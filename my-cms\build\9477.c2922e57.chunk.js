"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[9477],{9477:(N,P,s)=>{s.d(P,{ProtectedListPage:()=>ts});var _=s(92132),e=s(21272),D=s(94061),C=s(85963),h=s(88353),T=s(74773),A=s(4198),R=s(55356),M=s(38413),E=s(83997),L=s(35513),m=s(25641),K=s(26127),v=s(90361),l=s(33363),x=s(40216),i=s(30893),Q=s(98765),n=s(55506),X=s(90625),H=s(41909),S=s(5194),J=s(50612),Y=s(60256),Z=s(88761),$=s(54894),b=s(17703),F=s(43543),k=s(85071),z=s(66248),Ms=s(15126),rs=s(63299),Os=s(67014),Ps=s(59080),Cs=s(79275),hs=s(14718),Ts=s(82437),As=s(61535),Rs=s(5790),Ls=s(12083),ms=s(35223),gs=s(5409),vs=s(74930),Bs=s(2600),cs=s(48940),Ws=s(41286),Is=s(56336),Us=s(13426),Ks=s(84624),xs=s(77965),js=s(54257),fs=s(71210),ys=s(51187),us=s(39404),ps=s(58692),Ns=s(501),Ss=s(57646),$s=s(23120),Fs=s(44414),zs=s(25962),Gs=s(14664),Vs=s(42588),Qs=s(90325),Xs=s(62785),Hs=s(87443),Js=s(41032),Ys=s(22957),Zs=s(93179),bs=s(73055),ks=s(15747),ws=s(85306),qs=s(26509),s_=s(32058),__=s(81185),t_=s(82261),o_=s(55151),n_=s(79077);const w=({id:t,name:O,description:a,usersCount:B,icons:c,rowIndex:j,canUpdate:W})=>{const{formatMessage:I}=(0,$.A)(),[,g]=c,f=I({id:"Roles.RoleRow.user-count",defaultMessage:"{number, plural, =0 {#  user} one {#  user} other {# users}}"},{number:B});return(0,_.jsxs)(v.Tr,{"aria-rowindex":j,...W?(0,n.qM)({fn:g.onClick}):{},children:[(0,_.jsx)(l.Td,{maxWidth:(0,n.a8)(130),children:(0,_.jsx)(i.o,{ellipsis:!0,textColor:"neutral800",children:O})}),(0,_.jsx)(l.Td,{maxWidth:(0,n.a8)(250),children:(0,_.jsx)(i.o,{ellipsis:!0,textColor:"neutral800",children:a})}),(0,_.jsx)(l.Td,{children:(0,_.jsx)(i.o,{textColor:"neutral800",children:f})}),(0,_.jsx)(l.Td,{children:(0,_.jsx)(E.s,{justifyContent:"flex-end",...n.dG,children:c.map((d,U)=>d?(0,_.jsx)(D.a,{paddingLeft:U===0?0:1,children:(0,_.jsx)(h.K,{onClick:d.onClick,label:d.label,borderWidth:0,icon:d.icon})},d.label):null)})})]},t)},q=()=>{const{formatMessage:t}=(0,$.A)();(0,n.L4)();const O=(0,F.j)(z.s),{formatAPIError:a}=(0,n.wq)(),B=(0,n.hN)(),[c,j]=e.useState(!1),[{query:W}]=(0,n.sq)(),{isLoading:I,allowedActions:{canCreate:g,canDelete:f,canRead:d,canUpdate:U}}=(0,n.ec)(O.settings?.roles),{roles:G,refetch:os}=(0,k.u)({filters:W?._q?{name:{$containsi:W._q}}:void 0},{refetchOnMountOrArgChange:!0,skip:I||!d}),{push:y}=(0,b.W6)(),[{showModalConfirmButtonLoading:ns,roleToDelete:Es},u]=e.useReducer(_s,ss),{post:as}=(0,n.GD)(),ls=async()=>{try{u({type:"ON_REMOVE_ROLES"}),await as("/admin/roles/batch-delete",{ids:[Es]}),await os(),u({type:"RESET_DATA_TO_DELETE"})}catch(o){o instanceof Y.pe&&B({type:"warning",message:a(o)})}p()},V=()=>y("/settings/roles/new"),p=()=>j(o=>!o),ds=o=>r=>{r.preventDefault(),r.stopPropagation(),o.usersCount?B({type:"info",message:{id:"Roles.ListPage.notification.delete-not-allowed"}}):(u({type:"SET_ROLE_TO_DELETE",id:o.id}),p())},is=o=>r=>{r.preventDefault(),r.stopPropagation(),y(`/settings/roles/duplicate/${o.id}`)},es=G.length+1,Ds=6;return I?(0,_.jsx)(M.g,{children:(0,_.jsx)(n.Bl,{})}):(0,_.jsxs)(M.g,{children:[(0,_.jsx)(n.x7,{name:"Roles"}),(0,_.jsx)(R.Q,{primaryAction:g?(0,_.jsx)(C.$,{onClick:V,startIcon:(0,_.jsx)(S.A,{}),size:"S",children:t({id:"Settings.roles.list.button.add",defaultMessage:"Add new role"})}):null,title:t({id:"global.roles",defaultMessage:"roles"}),subtitle:t({id:"Settings.roles.list.description",defaultMessage:"List of roles"}),as:"h2"}),d&&(0,_.jsx)(T.B,{startActions:(0,_.jsx)(n.q7,{label:t({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:t({id:"global.roles",defaultMessage:"roles"})})})}),d&&(0,_.jsx)(A.s,{children:(0,_.jsxs)(L.X,{colCount:Ds,rowCount:es,footer:g?(0,_.jsx)(x.S,{onClick:V,icon:(0,_.jsx)(S.A,{}),children:t({id:"Settings.roles.list.button.add",defaultMessage:"Add new role"})}):null,children:[(0,_.jsx)(K.d,{children:(0,_.jsxs)(v.Tr,{"aria-rowindex":1,children:[(0,_.jsx)(l.Th,{children:(0,_.jsx)(i.o,{variant:"sigma",textColor:"neutral600",children:t({id:"global.name",defaultMessage:"Name"})})}),(0,_.jsx)(l.Th,{children:(0,_.jsx)(i.o,{variant:"sigma",textColor:"neutral600",children:t({id:"global.description",defaultMessage:"Description"})})}),(0,_.jsx)(l.Th,{children:(0,_.jsx)(i.o,{variant:"sigma",textColor:"neutral600",children:t({id:"global.users",defaultMessage:"Users"})})}),(0,_.jsx)(l.Th,{children:(0,_.jsx)(Q.s,{children:t({id:"global.actions",defaultMessage:"Actions"})})})]})}),(0,_.jsx)(m.N,{children:G?.map((o,r)=>(0,_.jsx)(w,{id:o.id,name:o.name,description:o.description,usersCount:o.usersCount,icons:[g&&{onClick:is(o),label:t({id:"app.utils.duplicate",defaultMessage:"Duplicate"}),icon:(0,_.jsx)(X.A,{})},U&&{onClick:()=>y(`/settings/roles/${o.id}`),label:t({id:"app.utils.edit",defaultMessage:"Edit"}),icon:(0,_.jsx)(H.A,{})},f&&{onClick:ds(o),label:t({id:"global.delete",defaultMessage:"Delete"}),icon:(0,_.jsx)(J.A,{})}].filter(Boolean),rowIndex:r+2,canUpdate:U},o.id))})]})}),(0,_.jsx)(n.TM,{isOpen:c,onConfirm:ls,isConfirmButtonLoading:ns,onToggleDialog:p})]})},ss={roleToDelete:null,showModalConfirmButtonLoading:!1,shouldRefetchData:!1},_s=(t,O)=>(0,Z.Ay)(t,a=>{switch(O.type){case"ON_REMOVE_ROLES":{a.showModalConfirmButtonLoading=!0;break}case"ON_REMOVE_ROLES_SUCCEEDED":{a.shouldRefetchData=!0,a.roleToDelete=null;break}case"RESET_DATA_TO_DELETE":{a.shouldRefetchData=!1,a.roleToDelete=null,a.showModalConfirmButtonLoading=!1;break}case"SET_ROLE_TO_DELETE":{a.roleToDelete=O.id;break}default:return a}}),ts=()=>{const t=(0,F.j)(z.s);return(0,_.jsx)(n.kz,{permissions:t.settings?.roles.main,children:(0,_.jsx)(q,{})})}},40216:(N,P,s)=>{s.d(P,{S:()=>M});var _=s(92132),e=s(63891),D=s(94061),C=s(48653),h=s(83997),T=s(30893);const A=(0,e.Ay)(D.a)`
  height: ${24/16}rem;
  width: ${24/16}rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    height: ${10/16}rem;
    width: ${10/16}rem;
  }

  svg path {
    fill: ${({theme:E})=>E.colors.primary600};
  }
`,R=(0,e.Ay)(D.a)`
  border-radius: 0 0 ${({theme:E})=>E.borderRadius} ${({theme:E})=>E.borderRadius};
  display: block;
  width: 100%;
  border: none;
`,M=({children:E,icon:L,...m})=>(0,_.jsxs)("div",{children:[(0,_.jsx)(C.c,{}),(0,_.jsx)(R,{as:"button",background:"primary100",padding:5,...m,children:(0,_.jsxs)(h.s,{children:[(0,_.jsx)(A,{"aria-hidden":!0,background:"primary200",children:L}),(0,_.jsx)(D.a,{paddingLeft:3,children:(0,_.jsx)(T.o,{variant:"pi",fontWeight:"bold",textColor:"primary600",children:E})})]})})]})},85071:(N,P,s)=>{s.d(P,{u:()=>h});var _=s(21272),e=s(55506),D=s(54894),C=s(43543);const h=(T={},A)=>{const{locale:R}=(0,D.A)(),M=(0,e.QM)(R,{sensitivity:"base"}),{data:E,error:L,isError:m,isLoading:K,refetch:v}=(0,C.z)(T,A);return{roles:_.useMemo(()=>[...E??[]].sort((x,i)=>M.compare(x.name,i.name)),[E,M]),error:L,isError:m,isLoading:K,refetch:v}}}}]);
