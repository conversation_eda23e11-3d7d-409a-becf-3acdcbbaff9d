"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4011],{54011:(p,n,u)=>{u.r(n),u.d(n,{Analytics:()=>E,Documentation:()=>t,Email:()=>e,Password:()=>o,Provider:()=>i,ResetPasswordToken:()=>r,Role:()=>a,Username:()=>c,Users:()=>g,default:()=>x});const E="Ph\xE2n T\xEDch",t="T\xE0i li\u1EC7u",e="Email",o="M\u1EADt Kh\u1EA9u",i="Nh\xE0 Cung C\u1EA5p",r="C\xE0i L\u1EA1i Chu\u1ED7i Kh\xF3a M\u1EADt Kh\u1EA9u",a="Vai tr\xF2",c="T\xEAn \u0111\u0103ng nh\u1EADp",g="Ng\u01B0\u1EDDi d\xF9ng",x={Analytics:E,"Auth.components.Oops.text":"T\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n \u0111\xE3 b\u1ECB kho\xE1","Auth.components.Oops.text.admin":"N\u1EBFu c\xF3 s\u1EF1 nh\u1EA7m l\u1EABn, h\xE3y li\xEAn h\u1EC7 ng\u01B0\u1EDDi qu\u1EA3n tr\u1ECB","Auth.form.button.forgot-password":"G\u1EEDi Email","Auth.form.button.go-home":"QUAY V\u1EC0 TRANG CH\u1EE6","Auth.form.button.login":"\u0110\u0103ng nh\u1EADp","Auth.form.button.login.providers.error":"Kh\xF4ng th\u1EC3 k\u1EBFt n\u1ED1i b\u1EA1n v\u1EDBi d\u1ECBch v\u1EE5 \u0111\xE3 ch\u1ECDn.","Auth.form.button.login.strapi":"\u0110\u0103ng nh\u1EADp v\u1EDBi Strapi","Auth.form.button.password-recovery":"Kh\xF4i ph\u1EE5c m\u1EADt kh\u1EA9u","Auth.form.button.register":"S\u1EB5n s\xE0ng \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u","Auth.form.confirmPassword.label":"Nh\u1EADp l\u1EA1i m\u1EADt kh\u1EA9u","Auth.form.currentPassword.label":"M\u1EADt kh\u1EA9u hi\u1EC7n t\u1EA1i","Auth.form.email.label":"Email","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"T\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n b\u1ECB kh\xF3a b\u1EDFi ng\u01B0\u1EDDi qu\u1EA3n tr\u1ECB.","Auth.form.error.code.provide":"M\xE3 \u0111\u01B0\u1EE3c cung c\u1EA5p kh\xF4ng ch\xEDnh x\xE1c.","Auth.form.error.confirmed":"T\xE0i kho\u1EA3n Email c\u1EE7a b\u1EA1n ch\u01B0a \u0111\u01B0\u1EE3c x\xE1c nh\u1EADn.","Auth.form.error.email.invalid":"Email sai.","Auth.form.error.email.provide":"Vui l\xF2ng cung c\u1EA5p t\xEAn \u0111\u0103ng nh\u1EADp ho\u1EB7c email.","Auth.form.error.email.taken":"Email \u0111\xE3 t\u1ED3n t\u1EA1i.","Auth.form.error.invalid":"\u0110\u1ECBnh danh ho\u1EB7c m\u1EADt kh\u1EA9u sai.","Auth.form.error.params.provide":"Tham s\u1ED1 \u0111\u01B0\u1EE3c cung c\u1EA5p kh\xF4ng ch\xEDnh x\xE1c.","Auth.form.error.password.format":"M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n kh\xF4ng th\u1EC3 ch\u1EE9a k\xFD t\u1EF1 `$` h\u01A1n ba l\u1EA7n","Auth.form.error.password.local":"Ng\u01B0\u1EDDi d\xF9ng n\xE0y ch\u01B0a t\u1EEBng \u0111\u1EB7t m\u1EADt kh\u1EA9u c\u1EE5c b\u1ED9, vui l\xF2ng \u0111\u0103ng nh\u1EADp th\xF4ng qua nh\xE0 cung c\u1EA5p \u0111\xE3 d\xF9ng trong l\xFAc t\u1EA1o t\xE0i kho\u1EA3n ban \u0111\u1EA7u","Auth.form.error.password.matching":"M\u1EADt kh\u1EA9u kh\xF4ng kh\u1EDBp.","Auth.form.error.password.provide":"Vui l\xF2ng cung c\u1EA5p m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n.","Auth.form.error.ratelimit":"Th\u1EED qu\xE1 nhi\u1EC1u l\u1EA7n, vui l\xF2ng th\u1EED l\u1EA1i sau m\u1ED9t ph\xFAt","Auth.form.error.user.not-exist":"Email n\xE0y ch\u01B0a t\u1ED3n t\u1EA1i.","Auth.form.error.username.taken":"T\xEAn \u0111\u0103ng nh\u1EADp \u0111\xE3 t\u1ED3n t\u1EA1i.","Auth.form.firstname.label":"H\u1ECD","Auth.form.firstname.placeholder":"v.d. Kai","Auth.form.forgot-password.email.label":"Nh\u1EADp email c\u1EE7a b\u1EA1n","Auth.form.forgot-password.email.label.success":"Email \u0111\xE3 g\u1EEDi th\xE0nh c\xF4ng \u0111\u1EBFn","Auth.form.lastname.label":"T\xEAn","Auth.form.lastname.placeholder":"v.d. Doe","Auth.form.password.hide-password":"\u1EA8n m\u1EADt kh\u1EA9u","Auth.form.password.hint":"M\u1EADt kh\u1EA9u ph\u1EA3i ch\u1EE9a \xEDt nh\u1EA5t 8 k\xFD t\u1EF1, 1 vi\u1EBFt hoa, 1 vi\u1EBFt th\u01B0\u1EDDng v\xE0 1 s\u1ED1","Auth.form.password.show-password":"Hi\u1EC3n th\u1ECB password","Auth.form.register.news.label":"C\u1EADp nh\u1EADt cho t\xF4i v\u1EC1 ch\u1EE9c n\u0103ng m\u1EDBi v\xE0 nh\u1EEFng c\u1EA3i thi\u1EC7n s\u1EAFp t\u1EDBi (th\xF4ng qua vi\u1EC7c n\xE0y b\u1EA1n \u0111\xE3 ch\u1EA5p nh\u1EADn {terms} v\xE0 {policy}).","Auth.form.register.subtitle":"Th\xF4ng tin c\u1EE7a b\u1EA1n ch\u1EC9 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng \u0111\u1EC3 \u0111\u0103ng nh\u1EADp v\xE0o trang qu\u1EA3n tr\u1ECB. T\u1EA5t c\u1EA3 c\xE1c d\u1EEF li\u1EC7u \u0111\u01B0\u1EE3c l\u01B0u \u1EDF c\u01A1 s\u1EDF d\u1EEF li\u1EC7u c\u1EE7a b\u1EA1n.","Auth.form.rememberMe.label":"Nh\u1EDB t\xF4i","Auth.form.username.label":"T\xEAn \u0111\u0103ng nh\u1EADp","Auth.form.username.placeholder":"Kai Doe","Auth.form.welcome.subtitle":"\u0110\u0103ng nh\u1EADp v\xE0o t\xE0i kho\u1EA3n Strapi c\u1EE7a b\u1EA1n","Auth.form.welcome.title":"Ch\xE0o m\u1EEBng!","Auth.link.forgot-password":"Qu\xEAn m\u1EADt kh\u1EA9u?","Auth.link.ready":"S\u1EB5n s\xE0ng \u0111\u0103ng nh\u1EADp?","Auth.link.signin":"\u0110\u0103ng nh\u1EADp","Auth.link.signin.account":"\u0110\xE3 c\xF3 t\xE0i kho\u1EA3n?","Auth.login.sso.divider":"Ho\u1EB7c \u0111\u0103ng nh\u1EADp v\u1EDBi","Auth.login.sso.loading":"\u0110ang t\u1EA3i c\xE1c d\u1ECBch v\u1EE5 cung c\u1EA5p...","Auth.login.sso.subtitle":"\u0110\u0103ng nh\u1EADp qua SSO","Auth.privacy-policy-agreement.policy":"ch\xEDnh s\xE1ch b\u1EA3o m\u1EADt","Auth.privacy-policy-agreement.terms":"c\xE1c \u0111i\u1EC1u kho\u1EA3n","Auth.reset-password.title":"\u0110\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u","Content Manager":"Qu\u1EA3n L\xFD N\u1ED9i Dung",Documentation:t,Email:e,"Files Upload":"T\u1EA3i T\xE2p Tin L\xEAn","HomePage.helmet.title":"Trang ch\u1EE7","HomePage.roadmap":"Xem l\u1ED9 tr\xECnh c\u1EE7a ch\xFAng t\xF4i","HomePage.welcome.congrats":"Ch\xFAc m\u1EEBng!","HomePage.welcome.congrats.content":"B\u1EA1n \u0111\xE3 \u0111\u0103ng nh\u1EADp nh\u01B0 l\xE0 ng\u01B0\u1EDDi qu\u1EA3n tr\u1ECB \u0111\u1EA7u ti\xEAn. \u0110\u1EC3 kh\xE1m ph\xE1 nh\u1EEFng t\xEDnh n\u0103ng m\u1EA1nh m\u1EBD \u0111\u01B0\u1EE3c cung c\u1EA5p b\u1EDFi Strapi.","New entry":"B\u1EA3n ghi m\u1EDBi",Password:o,Provider:i,ResetPasswordToken:r,Role:a,Username:c,Users:g,"Users & Permissions":"Ng\u01B0\u1EDDi d\xF9ng v\xE0 Ph\xE2n quy\u1EC1n","app.components.BlockLink.code":"Code v\xED d\u1EE5","app.components.Button.cancel":"H\u1EE7y b\u1ECF","app.components.ComingSoonPage.comingSoon":"S\u1EAFp c\xF3","app.components.DownloadInfo.download":"\u0110ang t\u1EA3i v\u1EC1","app.components.DownloadInfo.text":"Vi\u1EC7c n\xE0y c\xF3 th\u1EC3 t\u1ED1n m\u1ED9t ph\xFAt. C\u1EA3m \u01A1n b\u1EA1n v\xEC s\u1EF1 ki\xEAn nh\u1EABn.","app.components.EmptyAttributes.title":"Ch\u01B0a c\xF3 tr\u01B0\u1EDDng nh\u1EADp li\u1EC7u n\xE0o","app.components.HomePage.button.blog":"XEM TH\xCAM TR\xCAN BLOG","app.components.HomePage.community":"T\xECm c\u1ED9ng \u0111\u1ED3ng tr\xEAn web","app.components.HomePage.community.content":"Th\u1EA3o lu\u1EADn v\u1EDBi th\xE0nh vi\xEAn, ng\u01B0\u1EDDi \u0111\xF3ng g\xF3p v\xE0 l\u1EADp tr\xECnh vi\xEAn tr\xEAn nh\u1EEFng k\xEAnh kh\xE1c nhau.","app.components.HomePage.welcome":"Ch\xE0o m\u1EEBng b\u1EA1n \u0111\xE3 tham gia \u{1F44B}","app.components.HomePage.welcome.again":"Ch\xE0o m\u1EEBng \u{1F44B}","app.components.HomePage.welcomeBlock.content":"Ch\xFAng t\xF4i h\u1EA1nh ph\xFAc khi c\xF3 b\u1EA1n l\xE0 m\u1ED9t ph\u1EA7n c\u1EE7a c\u1ED9ng \u0111\u1ED3ng. Ch\xFAng t\xF4i li\xEAn t\u1EE5c t\xECm ki\u1EBFm s\u1EF1 ph\u1EA3n h\u1ED3i, do \u0111\xF3 b\u1EA1n c\xF3 th\u1EC3 Li\xEAn h\u1EC7 tr\u1EF1c ti\u1EBFp v\u1EDBi ch\xFAng t\xF4i tr\xEAn ","app.components.HomePage.welcomeBlock.content.again":"Ch\xFAng t\xF4i hi v\u1ECDng b\u1EA1n c\xF3 ti\u1EBFn tri\u1EC3n tr\xEAn d\u1EF1 \xE1n c\u1EE7a b\u1EA1n... B\u1EA1n t\u1EF1 do \u0111\u1ECDc tin t\u1EE9c m\u1EDBi nh\u1EA5t v\u1EC1 Strapi. Ch\xFAng t\xF4i \u0111ang c\u1ED1 g\u1EAFng h\u1EBFt m\u1EE9c \u0111\u1EC3 c\u1EA3i thi\u1EC7n s\u1EA3n ph\u1EA9m d\u1EF1a tr\xEAn ph\u1EA3n h\u1ED3i c\u1EE7a b\u1EA1n.","app.components.HomePage.welcomeBlock.content.issues":"v\u1EA5n \u0111\u1EC1.","app.components.HomePage.welcomeBlock.content.raise":" ho\u1EB7c n\xEAu l\xEAn ","app.components.ImgPreview.hint":"K\xE9o v\xE0 th\u1EA3 t\u1EADp tin c\u1EE7a b\u1EA1n v\xE0o khu v\u1EF1c n\xE0y ho\u1EB7c {browse} \u0111\u1EC3 t\xECm t\u1EADp tin t\u1EA3i l\xEAn","app.components.ImgPreview.hint.browse":"duy\u1EC7t","app.components.InputFile.newFile":"Th\xEAm t\u1EADp tin m\u1EDBi","app.components.InputFileDetails.open":"M\u1EDF ra \u1EDF th\u1EBB m\u1EDBi","app.components.InputFileDetails.originalName":"T\xEAn g\u1ED1c:","app.components.InputFileDetails.remove":"Xo\xE1 t\u1EADp tin n\xE0y","app.components.InputFileDetails.size":"K\xEDch th\u01B0\u1EDBc:","app.components.InstallPluginPage.Download.description":"N\xF3 s\u1EBD t\u1ED1n m\u1ED9t v\xE0i gi\xE2y \u0111\u1EC3 t\u1EA3i v\u1EC1 v\xE0 c\xE0i \u0111\u1EB7t plugin.","app.components.InstallPluginPage.Download.title":"\u0110ang t\u1EA3i v\u1EC1...","app.components.InstallPluginPage.description":"M\u1EDF r\u1ED9ng \u1EE9ng d\u1EE5ng c\u1EE7a b\u1EA1n d\u1EC5 d\xE0ng","app.components.LeftMenuFooter.help":"Gi\xFAp \u0111\u1EE1","app.components.LeftMenuFooter.poweredBy":"Cung c\u1EA5p b\u1EDFi ","app.components.LeftMenuLinkContainer.configuration":"C\xE1c c\u1EA5u h\xECnh","app.components.LeftMenuLinkContainer.general":"Chung","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Ch\u01B0a c\xF3 plugins n\xE0o \u0111\u01B0\u1EE3c c\xE0i \u0111\u1EB7t","app.components.LeftMenuLinkContainer.plugins":"Plugins","app.components.ListPluginsPage.description":"Danh s\xE1ch plugins \u0111\xE3 c\xE0i trong d\u1EF1 \xE1n.","app.components.ListPluginsPage.helmet.title":"Danh s\xE1ch plugins","app.components.Logout.logout":"\u0110\u0103ng xu\u1EA5t","app.components.Logout.profile":"H\u1ED3 s\u01A1","app.components.NotFoundPage.back":"Tr\u1EDF v\u1EC1 trang ch\u1EE7","app.components.NotFoundPage.description":"Kh\xF4ng T\xECm Th\u1EA5y","app.components.Official":"Ch\xEDnh Th\u1EE9c","app.components.Onboarding.label.completed":"% \u0111\xE3 ho\xE0n th\xE0nh","app.components.Onboarding.title":"Videos B\u1EAFt \u0110\u1EA7u","app.components.PluginCard.Button.label.download":"T\u1EA3i v\u1EC1","app.components.PluginCard.Button.label.install":"\u0110\xE3 \u0111\u01B0\u1EE3c c\xE0i \u0111\u1EB7t","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"Ch\u1EE9c n\u0103ng autoReload kh\xF4ng ho\u1EA1t \u0111\u1ED9ng. Vui l\xF2ng ch\u1EA1y \u1EE9ng d\u1EE5ng c\u1EE7a b\u1EA1n v\u1EDBi `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"T\xF4i hi\u1EC3u!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"V\xEC nh\u1EEFng l\xFD do b\u1EA3o m\u1EADt, ch\u1EC9 m\u1ED9t plugin c\xF3 th\u1EC3 \u0111\u01B0\u1EE3c t\u1EA3i v\u1EC1 trong m\xF4i tr\u01B0\u1EDDng ph\xE1t tri\u1EC3n.","app.components.PluginCard.PopUpWarning.install.impossible.title":"T\u1EA3i v\u1EC1 kh\xF4ng kh\u1EA3 thi","app.components.PluginCard.compatible":"T\u01B0\u01A1ng th\xEDch v\u1EDBi \u1EE9ng d\u1EE5ng c\u1EE7a b\u1EA1n","app.components.PluginCard.compatibleCommunity":"T\u01B0\u01A1ng th\xEDch v\u1EDBi c\u1ED9ng \u0111\u1ED3ng","app.components.PluginCard.more-details":"Th\xEAm chi ti\u1EBFt","app.components.listPlugins.button":"Th\xEAm m\u1EDBi Plugin","app.components.listPlugins.title.none":"Ch\u01B0a c\xF3 plugins n\xE0o \u0111\u01B0\u1EE3c c\xE0i \u0111\u1EB7t","app.components.listPluginsPage.deletePlugin.error":"M\u1ED9t l\u1ED7i x\u1EA3y ra trong khi g\u1EE1 b\u1ECF plugin","app.containers.App.notification.error.init":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi g\u1EEDi y\xEAu c\u1EA7u \u0111\u1EBFn API","app.utils.SelectOption.defaultMessage":" ","app.utils.defaultMessage":" ","app.utils.placeholder.defaultMessage":" ","components.AutoReloadBlocker.description":"Ch\u1EA1y Strapi v\u1EDBi m\u1ED9t trong c\xE1c l\u1EC7nh sau:","components.AutoReloadBlocker.header":"T\xEDnh n\u0103ng T\u1EA3i l\u1EA1i b\u1ECB b\u1EAFt bu\u1ED9c cho plugin n\xE0y.","components.ErrorBoundary.title":"\u0110i\u1EC1u g\xEC \u0111\xF3 kh\xF4ng \u1ED5n...","components.Input.error.attribute.key.taken":"Gi\xE1 tr\u1ECB n\xE0y \u0111\xE3 t\u1ED3n t\u1EA1i","components.Input.error.attribute.sameKeyAndName":"Kh\xF4ng th\u1EC3 b\u1EB1ng nhau","components.Input.error.attribute.taken":"Tr\u01B0\u1EDDng nh\u1EADp li\u1EC7u n\xE0y \u0111\xE3 t\u1ED3n t\u1EA1i","components.Input.error.contentTypeName.taken":"T\xEAn n\xE0y \u0111\xE3 t\u1ED3n t\u1EA1i","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"M\u1EADt kh\u1EA9u kh\xF4ng kh\u1EDBp","components.Input.error.validation.email":"Email kh\xF4ng h\u1EE3p l\u1EC7","components.Input.error.validation.json":"\u0110\xE2y kh\xF4ng ph\u1EA3i l\xE0 \u0111\u1ECBnh d\u1EA1ng JSON","components.Input.error.validation.max":"Gi\xE1 tr\u1ECB qu\xE1 cao {max}.","components.Input.error.validation.maxLength":"Gi\xE1 tr\u1ECB qu\xE1 d\xE0i {max}.","components.Input.error.validation.min":"Gi\xE1 tr\u1ECB qu\xE1 th\u1EA5p {min}.","components.Input.error.validation.minLength":"Gi\xE1 tr\u1ECB qu\xE1 ng\u1EAFn {min}.","components.Input.error.validation.minSupMax":"Kh\xF4ng th\u1EC3 l\xE0 tr\xEAn m\u0169","components.Input.error.validation.regex":"Gi\xE1 tr\u1ECB kh\xF4ng kh\u1EDBi v\u1EDBi regex.","components.Input.error.validation.required":"Gi\xE1 tr\u1ECB n\xE0y b\u1EAFt bu\u1ED9c.","components.InputSelect.option.placeholder":"Ch\u1ECDn \u1EDF \u0111\xE2y","components.ListRow.empty":"Kh\xF4ng c\xF3 d\u1EEF li\u1EC7u \u0111\u1EC3 hi\u1EC3n th\u1ECB.","components.OverlayBlocker.description":"B\u1EA1n \u0111ang d\xF9ng m\u1ED9t t\xEDnh n\u0103ng m\xE0 n\xF3 c\u1EA7n ph\u1EA3i kh\u1EDFi \u0111\u1ED9ng l\u1EA1i m\xE1y ch\u1EE7. Vui l\xF2ng ch\u1EDD cho m\xE1y ch\u1EE7 kh\u1EDFi \u0111\u1ED9ng xong.","components.OverlayBlocker.description.serverError":"M\xE1y ch\u1EE7 n\xEAn \u0111\u01B0\u1EE3c kh\u1EDFi \u0111\u1ED9ng l\u1EA1i, vui l\xF2ng ki\u1EC3m tra nh\u1EADt k\xFD c\u1EE7a b\u1EA1n trong c\u1EEDa s\u1ED5 l\u1EC7nh","components.OverlayBlocker.title":"\u0110ang \u0111\u1EE3i kh\u1EDFi \u0111\u1ED9ng l\u1EA1i...","components.OverlayBlocker.title.serverError":"Vi\u1EC7c kh\u1EDFi \u0111\u1ED9ng l\u1EA1i t\u1ED1n nhi\u1EC1u th\u1EDDi gian h\u01A1n mong \u0111\u1EE3i","components.PageFooter.select":"b\u1EA3n ghi tr\xEAn trang","components.ProductionBlocker.description":"V\xEC l\xFD do an to\xE0n ch\xFAng t\xF4i ph\u1EA3i v\xF4 hi\u1EC7u ho\xE1 plugin trong c\xE1c m\xF4i tr\u01B0\u1EDDng kh\xE1c","components.ProductionBlocker.header":"Plugin n\xE0y ch\u1EC9 hi\u1EC7u l\u1EE9c trong m\xF4i tr\u01B0\u1EDDng ph\xE1t tri\u1EC3n","components.Wysiwyg.collapse":"Thu l\u1EA1i","components.Wysiwyg.selectOptions.H1":"Ti\xEAu \u0111\u1EC1 H1","components.Wysiwyg.selectOptions.H2":"Ti\xEAu \u0111\u1EC1 H2","components.Wysiwyg.selectOptions.H3":"Ti\xEAu \u0111\u1EC1 H3","components.Wysiwyg.selectOptions.H4":"Ti\xEAu \u0111\u1EC1 H4","components.Wysiwyg.selectOptions.H5":"Ti\xEAu \u0111\u1EC1 H5","components.Wysiwyg.selectOptions.H6":"Ti\xEAu \u0111\u1EC1 H6","components.Wysiwyg.selectOptions.title":"Th\xEAm m\u1ED9t ti\xEAu \u0111\u1EC1","components.WysiwygBottomControls.charactersIndicators":"k\xFD t\u1EF1","components.WysiwygBottomControls.fullscreen":"M\u1EDF r\u1ED9ng","components.WysiwygBottomControls.uploadFiles":"K\xE9o v\xE0 th\u1EA3 c\xE1c t\u1EADp tin, d\xE1n t\u1EEB b\u1ED9 nh\u1EDB t\u1EA1m hay {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"ch\u1ECDn t\u1EC7p","components.popUpWarning.message":"B\u1EA1n c\xF3 ch\u1EAFc l\xE0 b\u1EA1n mu\u1ED1n xo\xE1 n\xF3?","components.popUpWarning.title":"Vui l\xF2ng x\xE1c nh\u1EADn","content-manager.EditRelations.title":"D\u1EEF Li\u1EC7u Quan H\u1EC7","content-manager.components.AddFilterCTA.add":"L\u1ECDc","content-manager.components.AddFilterCTA.hide":"L\u1ECDc","content-manager.components.DraggableAttr.edit":"Nh\u1EA5n \u0111\u1EC3 ch\u1EC9nh s\u1EEDa","content-manager.components.EmptyAttributesBlock.button":"\u0110\u1EBFn trang c\xE0i \u0111\u1EB7t","content-manager.components.EmptyAttributesBlock.description":"B\u1EA1n c\xF3 th\u1EC3 thay \u0111\u1ED5i c\xE0i \u0111\u1EB7t c\u1EE7a b\u1EA1n","content-manager.components.FilterOptions.button.apply":"\xC1p d\u1EE5ng","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"\xC1p d\u1EE5ng","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"X\xF3a t\u1EA5t c\u1EA3","content-manager.components.FiltersPickWrapper.PluginHeader.description":"C\xE0i \u0111\u1EB7t c\xE1c \u0111i\u1EC1u ki\u1EC7n \u0111\u1EC3 \xE1p d\u1EE5ng cho vi\u1EC7c l\u1ECDc c\xE1c b\u1EA3n ghi","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"C\xE1c b\u1ED9 l\u1ECDc","content-manager.components.FiltersPickWrapper.hide":"\u1EA8n \u0111i","content-manager.components.LimitSelect.itemsPerPage":"S\u1ED1 l\u01B0\u1EE3ng b\u1EA3n ghi trong trang","content-manager.components.Search.placeholder":"T\xECm m\u1ED9t b\u1EA3n ghi...","content-manager.components.TableDelete.delete":"X\xF3a t\u1EA5t c\u1EA3","content-manager.components.TableDelete.deleteSelected":"X\xF3a \u0111\xE3 ch\u1ECDn","content-manager.components.TableEmpty.withFilters":"Kh\xF4ng c\xF3 {contentType} v\u1EDBi b\u1ED9 l\u1ECDc \u0111\u01B0\u1EE3c d\xF9ng","content-manager.components.TableEmpty.withSearch":"Kh\xF4ng c\xF3 {contentType} t\u01B0\u01A1ng \u1EE9ng v\u1EDBi t\xECm ki\u1EBFm ({search})...","content-manager.components.TableEmpty.withoutFilter":"Kh\xF4ng c\xF3 {contentType}...","content-manager.containers.Edit.Link.Layout":"C\u1EA5u h\xECnh b\u1ED1 c\u1EE5c","content-manager.containers.Edit.addAnItem":"Th\xEAm m\u1ED9t b\u1EA3n ghi...","content-manager.containers.Edit.clickToJump":"Nh\u1EA5n \u0111\u1EC3 nh\u1EA3y v\xE0o b\u1EA3n ghi","content-manager.containers.Edit.delete":"X\xF3a","content-manager.containers.Edit.editing":"\u0110\u0103ng s\u1EEDa...","content-manager.containers.Edit.pluginHeader.title.new":"T\u1EA1o m\u1ED9t B\u1EA3n ghi","content-manager.containers.Edit.reset":"L\xE0m l\u1EA1i","content-manager.containers.Edit.returnList":"Tr\u1EDF v\u1EC1 danh s\xE1ch","content-manager.containers.Edit.seeDetails":"Chi ti\u1EBFt","content-manager.containers.Edit.submit":"L\u01B0u","content-manager.containers.EditView.notification.errors":"B\u1EA3ng nh\u1EADp li\u1EC7u c\xF3 v\xE0i l\u1ED7i","content-manager.containers.Home.introduction":"\u0110\u1EC3 ch\u1EC9nh s\u1EEDa c\xE1c b\u1EA3n ghi c\u1EE7a b\u1EA1n, \u0111i \u0111\u1EBFn li\xEAn k\u1EBFt \u1EDF menu b\xEAn tr\xE1i. Plugin n\xE0y ch\u01B0a c\xF3 c\xE1ch th\xEDch h\u1EE3p \u0111\u1EC3 ch\u1EC9nh s\u1EEDa c\xE1c c\xE0i \u0111\u1EB7t v\xE0 n\xF3 v\u1EABn \u0111ang \u0111\u01B0\u1EE3c ph\xE1t tri\u1EC3n.","content-manager.containers.Home.pluginHeaderDescription":"Qu\u1EA3n l\xFD c\xE1c b\u1EA3n ghi th\xF4ng qua m\u1ED9t giao di\u1EC7n m\u1EA1nh v\xE0 \u0111\u1EB9p.","content-manager.containers.Home.pluginHeaderTitle":"Qu\u1EA3n L\xFD N\u1ED9i Dung","content-manager.containers.List.errorFetchRecords":"L\u1ED7i","content-manager.containers.ListPage.displayedFields":"C\xE1c tr\u01B0\u1EDDng \u0111\xE3 \u0111\u01B0\u1EE3c tr\xECnh b\xE0y","content-manager.containers.SettingPage.attributes":"C\xE1c tr\u01B0\u1EDDng thu\u1ED9c t\xEDnh","content-manager.containers.SettingPage.attributes.description":"\u0110\u1ECBnh ngh\u0129a th\u1EE9 t\u1EF1 c\xE1c thu\u1ED9c t\xEDnh","content-manager.containers.SettingPage.editSettings.description":"K\xE9o & th\u1EA3 c\xE1c tr\u01B0\u1EDDng \u0111\u1EC3 x\xE2y d\u1EF1ng b\u1ED1 c\u1EE5c","content-manager.containers.SettingPage.editSettings.entry.title":"T\xEAn b\u1EA3n ghi","content-manager.containers.SettingPage.editSettings.entry.title.description":"C\xE0i \u0111\u1EB7t tr\u01B0\u1EDDng \u0111\u01B0\u1EE3c tr\xECnh b\xE0y trong b\u1EA3n ghi c\u1EE7a b\u1EA1n","content-manager.containers.SettingPage.editSettings.title":"Ch\u1EC9nh s\u1EEDa hi\u1EC3n th\u1ECB (c\xE1c c\xE0i \u0111\u1EB7t)","content-manager.containers.SettingPage.layout":"B\u1ED1 c\u1EE5c","content-manager.containers.SettingPage.listSettings.title":"Hi\u1EC3n th\u1ECB danh s\xE1ch (c\xE1c c\xE0i \u0111\u1EB7t)","content-manager.containers.SettingPage.settings":"C\xE1c c\xE0i \u0111\u1EB7t","content-manager.containers.SettingViewModel.pluginHeader.title":"Qu\u1EA3n L\xFD N\u1ED9i Dung - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"C\u1EA5u h\xECnh c\xE1c c\xE0i \u0111\u1EB7t ri\xEAng","content-manager.containers.SettingsPage.Block.generalSettings.title":"Chung","content-manager.containers.SettingsView.list.title":"C\xE1c c\u1EA5u h\xECnh v\u1EC1 Tr\xECnh b\xE0y","content-manager.emptyAttributes.title":"Ch\u01B0a c\xF3 tr\u01B0\u1EDDng n\xE0o h\u1EBFt","content-manager.error.attribute.key.taken":"Gi\xE1 tr\u1ECB n\xE0y \u0111\xE3 t\u1ED3n t\u1EA1i","content-manager.error.attribute.sameKeyAndName":"Kh\xF4ng th\u1EC3 b\u1EB1ng nhau","content-manager.error.attribute.taken":"T\xEAn tr\u01B0\u1EDDng n\xE0y \u0111\xE3 t\u1ED3n t\u1EA1i","content-manager.error.contentTypeName.taken":"T\xEAn n\xE0y \u0111\xE3 t\u1ED3n t\u1EA1i","content-manager.error.model.fetch":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi l\u1EA5y v\u1EC1 c\u1EA5u h\xECnh n\u1ED9i dung.","content-manager.error.record.create":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi t\u1EA1o b\u1EA3n ghi.","content-manager.error.record.delete":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi xo\xE1 b\u1EA3n ghi.","content-manager.error.record.fetch":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi l\u1EA5y v\u1EC1 b\u1EA3n ghi.","content-manager.error.record.update":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi c\u1EADp nh\u1EADt b\u1EA3n ghi.","content-manager.error.records.count":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi l\u1EA5y v\u1EC1 s\u1ED1 l\u01B0\u1EE3ng b\u1EA3n ghi.","content-manager.error.records.fetch":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi l\u1EA5y v\u1EC1 c\xE1c b\u1EA3n ghi.","content-manager.error.schema.generation":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi qu\xE1 tr\xECnh t\u1EA1o ra l\u01B0\u1EE3c \u0111\u1ED3.","content-manager.error.validation.json":"\u0110\xE2y kh\xF4ng ph\u1EA3i l\xE0 \u0111\u1ECBnh d\u1EA1ng JSON","content-manager.error.validation.max":"Gi\xE1 tr\u1ECB qu\xE1 cao.","content-manager.error.validation.maxLength":"Gi\xE1 tr\u1ECB qu\xE1 d\xE0i.","content-manager.error.validation.min":"Gi\xE1 tr\u1ECB qu\xE1 th\u1EA5p.","content-manager.error.validation.minLength":"Gi\xE1 tr\u1ECB qu\xE1 ng\u1EAFn.","content-manager.error.validation.minSupMax":"Kh\xF4ng th\u1EC3 l\xE0 tr\xEAn m\u0169","content-manager.error.validation.regex":"Gi\xE1 tr\u1ECB kh\xF4ng kh\u1EDBi v\u1EDBi regex.","content-manager.error.validation.required":"Gi\xE1 tr\u1ECB n\xE0y b\u1EAFt bu\u1ED9c.","content-manager.form.Input.bulkActions":"K\xEDch ho\u1EA1t ho\u1EA1t \u0111\u1ED9ng g\u1ED9p","content-manager.form.Input.defaultSort":"Thu\u1ED9c t\xEDnh s\u1EAFp x\u1EBFp m\u1EB7c \u0111\u1ECBnh","content-manager.form.Input.description":"M\xF4 t\u1EA3","content-manager.form.Input.description.placeholder":"T\xEAn hi\u1EC3n th\u1ECB trong h\u1ED3 s\u01A1","content-manager.form.Input.editable":"Tr\u01B0\u1EDDng ch\u1EC9nh s\u1EEDa \u0111\u01B0\u1EE3c","content-manager.form.Input.filters":"K\xEDch ho\u1EA1t c\xE1c b\u1ED9 l\u1ECDc","content-manager.form.Input.label":"Nh\xE3n","content-manager.form.Input.label.inputDescription":"Gi\xE1 tr\u1ECB n\xE0y ghi \u0111\xE8 l\xEAn nh\xE3n \u0111\u01B0\u1EE3c tr\xECnh b\xE0y trong ph\u1EA7n \u0111\u1EA7u c\u1EE7a b\u1EA3ng","content-manager.form.Input.pageEntries":"B\u1EA3n ghi trong trang","content-manager.form.Input.placeholder":"Ch\u1ED7 ch\u1EDD gi\xE1 tr\u1ECB","content-manager.form.Input.placeholder.placeholder":"Gi\xE1 tr\u1ECB tuy\u1EC7t v\u1EDDi c\u1EE7a t\xF4i","content-manager.form.Input.search":"K\xEDch ho\u1EA1t t\xECm ki\u1EBFm","content-manager.form.Input.search.field":"K\xEDch ho\u1EA1t t\xECm ki\u1EBFm cho tr\u01B0\u1EDDng n\xE0y","content-manager.form.Input.sort.field":"K\xEDch ho\u1EA1t s\u1EAFp x\u1EBFp tr\xEAn tr\u01B0\u1EDDng n\xE0y","content-manager.form.Input.wysiwyg":"Tr\xECnh b\xE0y nh\u01B0 l\xE0 WYSIWYG","content-manager.global.displayedFields":"C\xE1c Tr\u01B0\u1EDDng \u0110\xE3 \u0110\u01B0\u1EE3c Tr\xECnh B\xE0y","content-manager.groups":"Nh\xF3m","content-manager.groups.numbered":"Nh\xF3m ({number})","content-manager.notification.error.displayedFields":"B\u1EA1n c\u1EA7n tr\xECnh b\xE0y \xEDt nh\u1EA5t m\u1ED9t tr\u01B0\u1EDDng","content-manager.notification.error.relationship.fetch":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi l\u1EA5y v\u1EC1 m\u1ED1i quan h\u1EC7.","content-manager.notification.info.SettingPage.disableSort":"B\u1EA1n c\u1EA7n c\xF3 m\u1ED9t thu\u1ED9c t\xEDnh \u0111\u01B0\u1EE3c ph\xE9p s\u1EAFp x\u1EBFp","content-manager.notification.info.minimumFields":"B\u1EA1n c\u1EA7n hi\u1EC3n th\u1ECB \xEDt nh\u1EA5t m\u1ED9t tr\u01B0\u1EDDng","content-manager.notification.upload.error":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra trong khi t\u1EA3i l\xEAn c\xE1c t\u1EADp tin c\u1EE7a b\u1EA1n","content-manager.pageNotFound":"Kh\xF4ng T\xECm Th\u1EA5y Trang","content-manager.plugin.description.long":"C\xE1ch nhanh \u0111\u1EC3 xem, s\u1EEDa v\xE0 xo\xE1 d\u1EEF li\u1EC7u trong c\u01A1 s\u1EDF d\u1EEF li\u1EC7u c\u1EE7a b\u1EA1n.","content-manager.plugin.description.short":"C\xE1ch nhanh \u0111\u1EC3 xem, s\u1EEDa v\xE0 xo\xE1 d\u1EEF li\u1EC7u trong c\u01A1 s\u1EDF d\u1EEF li\u1EC7u c\u1EE7a b\u1EA1n.","content-manager.popUpWarning.bodyMessage.contentType.delete":"B\u1EA1n c\xF3 ch\u1EAFc l\xE0 mu\u1ED1n xo\xE1 b\u1EA3n ghi n\xE0y kh\xF4ng?","content-manager.popUpWarning.bodyMessage.contentType.delete.all":"B\u1EA1n c\xF3 ch\u1EAFc l\xE0 mu\u1ED1n xo\xE1 c\xE1c b\u1EA3n ghi n\xE0y kh\xF4ng?","content-manager.popUpWarning.warning.cancelAllSettings":"B\u1EA1n c\xF3 ch\u1EAFc l\xE0 mu\u1ED1n h\u1EE7y b\u1ECF c\xE1c thay \u0111\u1ED5i c\u1EE7a b\u1EA1n?","content-manager.popUpWarning.warning.updateAllSettings":"N\xF3 s\u1EBD thay \u0111\u1ED5i t\u1EA5t c\u1EA3 c\xE0i \u0111\u1EB7t c\u1EE7a b\u1EA1n","content-manager.success.record.delete":"\u0110\xE3 xo\xE1","content-manager.success.record.save":"\u0110\xE3 l\u01B0u","form.button.done":"Ho\xE0n th\xE0nh","notification.error":"M\u1ED9t l\u1ED7i \u0111\xE3 x\u1EA3y ra","notification.error.layout":"Kh\xF4ng th\u1EC3 kh\xF4i ph\u1EE5c","notification.form.error.fields":"B\u1EA3ng nh\u1EADp li\u1EC7u c\xF3 v\xE0i l\u1ED7i","request.error.model.unknown":"C\u1EA5u tr\xFAc n\xE0y kh\xF4ng t\u1ED3n t\u1EA1i"}}}]);
