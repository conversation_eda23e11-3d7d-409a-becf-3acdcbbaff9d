"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[7063],{27063:(o,D,_)=>{_.r(D),_.d(D,{EventsTableEE:()=>a});var E=_(92132),P=_(46153),t=_(21272),d=_(55506),v=_(43543),l=_(15126),C=_(63299),B=_(67014),A=_(59080),T=_(79275),R=_(14718),K=_(82437),L=_(61535),W=_(5790),I=_(12083),U=_(35223),n=_(5409),h=_(74930),m=_(2600),r=_(48940),g=_(41286),f=_(56336),j=_(13426),y=_(84624),x=_(77965),i=_(54257),H=_(71210),N=_(51187),S=_(39404),F=_(58692),z=_(501),V=_(57646),J=_(23120),G=_(44414),Q=_(25962),X=_(14664),Y=_(42588),Z=_(90325),$=_(62785),u=_(87443),c=_(41032),w=_(22957),k=_(93179),p=_(73055),e=_(15747),b=_(85306),q=_(26509),__=_(32058),E_=_(81185),P_=_(82261),s_=_(98898),D_=_(66248),M_=_(55151),O_=_(79077),a_=_(28026);const M={"review-workflows":{"review-workflows":["review-workflows.updateEntryStage"]},releases:{releases:["releases.publish"]}},O=s=>{switch(s){case"review-workflows":return()=>[{id:"review-workflows.updateEntryStage",defaultMessage:"Stage Change"}];case"releases":return()=>[{id:"releases.publish",defaultMessage:"Publish"}]}},a=()=>(0,E.jsxs)(P.a.Root,{children:[(0,E.jsx)(P.a.Headers,{}),(0,E.jsx)(P.a.Body,{}),Object.keys(M).map(s=>(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(P.a.Headers,{getHeaders:O(s)}),(0,E.jsx)(P.a.Body,{providedEvents:M[s]})]}))]})}}]);
