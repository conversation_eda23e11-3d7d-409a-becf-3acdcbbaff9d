"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[5543],{5543:(K,s,_)=>{_.r(s),_.d(s,{CreateActionEE:()=>M});var E=_(92132),O=_(85963),t=_(58805),a=_(83997),o=_(79739),d=_(34313),n=_(68994),C=_(67031),A=_(33544),l=_(54894),v=_(43543),U=_(55506),W=_(15126),h=_(63299),m=_(67014),i=_(59080),r=_(79275),g=_(14718),y=_(21272),f=_(82437),j=_(61535),x=_(5790),S=_(12083),N=_(35223),$=_(5409),z=_(74930),F=_(2600),G=_(48940),H=_(41286),J=_(56336),Q=_(13426),V=_(84624),X=_(77965),Y=_(54257),Z=_(71210),c=_(51187),p=_(39404),u=_(58692),k=_(501),w=_(57646),b=_(23120),q=_(44414),e=_(25962),__=_(14664),E_=_(42588),s_=_(90325),M_=_(62785),D_=_(87443),P_=_(41032),O_=_(22957),t_=_(93179),a_=_(73055),o_=_(15747),d_=_(85306),n_=_(26509),C_=_(32058),A_=_(81185),l_=_(82261);const M=({onClick:I})=>{const{formatMessage:D}=(0,l.A)(),{license:B,isError:L,isLoading:R}=(0,v.m)(),{permittedSeats:T,shouldStopCreate:P}=B??{};return L||R?null:(0,E.jsxs)(a.s,{gap:2,children:[!C(T)&&P&&(0,E.jsx)(o.m,{description:D({id:"Settings.application.admin-seats.at-limit-tooltip",defaultMessage:"At limit: add seats to invite more users"}),position:"left",children:(0,E.jsx)(t.I,{width:`${14/16}rem`,height:`${14/16}rem`,color:"danger500",as:n.A})}),(0,E.jsx)(O.$,{"data-testid":"create-user-button",onClick:I,startIcon:(0,E.jsx)(d.A,{}),size:"S",disabled:P,children:D({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"})})]})};M.propTypes={onClick:A.func.isRequired}}}]);
