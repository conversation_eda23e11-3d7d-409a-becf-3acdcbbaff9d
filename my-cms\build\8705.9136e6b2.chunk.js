"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[8705],{48705:(i,e,t)=>{t.r(e),t.d(e,{configurations:()=>o,default:()=>a,from:()=>n});const o="nastavenia",n="od",a={"attribute.boolean":"Logick\xE1 hodnota","attribute.boolean.description":"\xE1no/nie, 1/0, pravda/lo\u017E","attribute.component":"Komponent","attribute.component.description":"S<PERSON>pina pol\xED\u010Dok, ktor\xE9 je mo\u017En\xE9 opakovane pou\u017E\xEDva\u0165","attribute.date":"D\xE1tum a \u010Das","attribute.date.description":"Dial\xF3g pre v\xFDber d\xE1tumu a \u010Dasu","attribute.datetime":"D\xE1tum a \u010Das","attribute.dynamiczone":"Dynamick\xE1 z\xF3na","attribute.dynamiczone.description":"Umo\u017E\u0148uje dynamicky zvoli\u0165 komponenty po\u010Das \xFApravy obsahu","attribute.email":"E-mailov\xE1 adresa","attribute.email.description":"Pol\xED\u010Dko s automatickou valid\xE1ciou form\xE1tu e-mailovej adresy","attribute.enumeration":"Vymenovanie","attribute.enumeration.description":"Zoznam preddefinovan\xFDch hodn\xF4t s v\xFDberom jednej mo\u017Enosti","attribute.json":"JSON","attribute.json.description":"D\xE1ta vo form\xE1te JSON","attribute.media":"S\xFAbory","attribute.media.description":"Napr. obr\xE1zky, vide\xE1, at\u010F.","attribute.null":" ","attribute.number":"\u010C\xEDslo","attribute.number.description":"\u010C\xEDsla (cel\xE9, desatinn\xE9)","attribute.password":"Heslo","attribute.password.description":"Pol\xED\u010Dko pre zadanie hesla","attribute.relation":"Rel\xE1cia","attribute.relation.description":"Ur\u010Duje vz\u0165ah k in\xE9mu Typu obsahu","attribute.richtext":"Textov\xFD editor","attribute.richtext.description":"Textov\xE9 pole s mo\u017Enos\u0165ami form\xE1tovania","attribute.text":"Text","attribute.text.description":"Kr\xE1tky alebo dlh\u0161\xED text","attribute.time":"\u010Cas","attribute.timestamp":"Timestamp - \u010Casov\xFD odtla\u010Dok","attribute.uid":"UID","attribute.uid.description":"Unik\xE1tny identifik\xE1tor","button.attributes.add.another":"Prida\u0165 \u010Fal\u0161ie pol\xED\u010Dko","button.component.add":"Prida\u0165 komponent","button.component.create":"Vytvori\u0165 nov\xFD komponent","button.model.create":"Vytvori\u0165 nov\xFD Typ obsahu","button.single-types.create":"Vytvori\u0165 nov\xFD jednorazov\xFD typ","component.repeatable":"(opakuj\xFAce)","components.componentSelect.no-component-available":"U\u017E ste pridali v\u0161etky komponenty","components.componentSelect.no-component-available.with-search":"Nena\u0161iel sa \u017Eiaden komponent spl\u0148uj\xFAci v\xFDraz","components.componentSelect.value-component":"Ozna\u010Den\xE9 komponenty: {number} (zadajte h\u013Eadan\xFD text)","components.componentSelect.value-components":"Ozna\u010Den\xE9 komponenty: {number}",configurations:o,"contentType.collectionName.description":"U\u017Eito\u010Dn\xE9, ak m\xE1 by\u0165 n\xE1zov kolekcie (v dashboard) in\xFD ako meno tabu\u013Eky","contentType.collectionName.label":"Meno kolekcie","contentType.displayName.label":"N\xE1zov kolekcie","contentType.draftAndPublish.description":"Pred zverejnen\xEDm vytv\xE1ra\u0165 verziu n\xE1vrhu ka\u017Edej polo\u017Eky","contentType.draftAndPublish.label":"Draft/publish syst\xE9m","contentType.kind.change.warning":"Pr\xE1ve ste zmenili druh typu obsahu: API bude obnoven\xE9 (routes, controllers a services bud\xFA prep\xEDsan\xE9)","error.attributeName.reserved-name":"Tento n\xE1zov je vyhraden\xFD a nem\xF4\u017Ee by\u0165 pou\u017Eit\xFD (hroz\xED po\u0161kodenie in\xFDch funkcional\xEDt syst\xE9mu)","error.contentTypeName.reserved-name":"Tento n\xE1zov je vyhraden\xFD a nem\xF4\u017Ee by\u0165 pou\u017Eit\xFD (hroz\xED po\u0161kodenie in\xFDch funkcional\xEDt syst\xE9mu)","error.validation.enum-duplicate":"Duplicitn\xE9 hodnoty nie s\xFA povolen\xE9","error.validation.minSupMax":"Nem\xF4\u017Ee by\u0165 nadraden\xFD","error.validation.regex":"Vzor regul\xE1rneho v\xFDrazu (regex) je neplatn\xFD","error.validation.relation.targetAttribute-taken":"Tento n\xE1zov u\u017E v cie\u013Eovom objekte existuje","form.attribute.component.option.add":"Prida\u0165 komponent","form.attribute.component.option.create":"Vytvori\u0165 nov\xFD komponent","form.attribute.component.option.create.description":"Komponent je dostupn\xFD medzi v\u0161etk\xFDmi typmi a komponentami.","form.attribute.component.option.repeatable":"Znovu pou\u017Eite\u013En\xFD komponent","form.attribute.component.option.repeatable.description":"Ide\xE1lne pre viacpolo\u017Ekov\xE9 (polia) in\u0161tancie ako napr\xEDklad meta tagy, ingrediencie, at\u010F..","form.attribute.component.option.reuse-existing":"Pou\u017Ei\u0165 existuj\xFAci komponent","form.attribute.component.option.reuse-existing.description":"Pou\u017E\xEDvajte u\u017E vytvoren\xE9 komponenty pre uchovanie konzistentn\xFDch d\xE1t medzi Typmi obsahu.","form.attribute.component.option.single":"Jednorazov\xFD komponent","form.attribute.component.option.single.description":"Vhodn\xE9 pre zosk\xFApenie pol\xED\u010Dok, napr. cel\xE1 adresa","form.attribute.item.customColumnName":"Vlastn\xE9 n\xE1zvy st\u013Apcov","form.attribute.item.customColumnName.description":"Umo\u017E\u0148uje premenova\u0165 datab\xE1zov\xFD st\u013Ap\u010Dek pre potreby API","form.attribute.item.defineRelation.fieldName":"N\xE1zov pol\xED\u010Dka","form.attribute.item.enumeration.graphql":"N\xE1zov pol\xED\u010Dka pre GraphQL","form.attribute.item.enumeration.graphql.description":"Umo\u017E\u0148uje prep\xEDsa\u0165 predvolen\xE9 n\xE1zvy pre GraphQL","form.attribute.item.enumeration.placeholder":`Napr.:
r\xE1no
de\u0148
ve\u010Der`,"form.attribute.item.enumeration.rules":"Hodnoty (jedna na riadok)","form.attribute.item.maximum":"Maxim\xE1lna hodnota","form.attribute.item.maximumLength":"Maxim\xE1lna d\u013A\u017Eka","form.attribute.item.minimum":"Minim\xE1lna hodnota","form.attribute.item.minimumLength":"Minim\xE1lna d\u013A\u017Eka","form.attribute.item.number.type":"\u010C\xEDseln\xFD form\xE1t","form.attribute.item.number.type.biginteger":"ve\u013Ek\xE9 \u010D\xEDslo (napr.: 123456789)","form.attribute.item.number.type.decimal":"desatinn\xE9 \u010D\xEDslo (napr.: 2.22)","form.attribute.item.number.type.float":"desatinn\xE9 \u010D\xEDslo (napr.: 3.33333333)","form.attribute.item.number.type.integer":"cel\xE9 \u010D\xEDslo (napr.: 10)","form.attribute.item.privateField":"Skryt\xE9 pol\xED\u010Dko","form.attribute.item.privateField.description":"Toto pol\xED\u010Dko sa nebude zobrazova\u0165 v API","form.attribute.item.requiredField":"Povinn\xE9 pol\xED\u010Dko","form.attribute.item.requiredField.description":"Nedovol\xED vytvori\u0165 z\xE1znam ak ostane toto pol\xED\u010Dko pr\xE1zdne","form.attribute.item.text.regex":"Vzor regul\xE1rneho v\xFDrazu (RegExp)","form.attribute.item.text.regex.description":"Text regul\xE1rneho v\xFDrazu","form.attribute.item.uniqueField":"Unik\xE1tne pol\xED\u010Dko","form.attribute.item.uniqueField.description":"Nedovol\xED vytvori\u0165 z\xE1znam ak u\u017E existuje in\xFD z\xE1znam s rovnakou hodnotou","form.attribute.media.allowed-types":"Zvoli\u0165 povolen\xE9 typy s\xFAborov","form.attribute.media.allowed-types.option-files":"S\xFAbory","form.attribute.media.allowed-types.option-images":"Obr\xE1zky","form.attribute.media.allowed-types.option-videos":"Vide\xE1","form.attribute.media.option.multiple":"Viacero s\xFAborov","form.attribute.media.option.multiple.description":"Vhodn\xE9 pre gal\xE9riu, zoznam s\xFAborov na stiahnutie","form.attribute.media.option.single":"Jeden s\xFAbor","form.attribute.media.option.single.description":"Vhodn\xE9 pre profilov\xFA fotku alebo hlavn\xFD obr\xE1zok","form.attribute.settings.default":"Predvolen\xE1 hodnota","form.attribute.text.option.long-text":"Dlh\xFD text","form.attribute.text.option.long-text.description":"Vhodn\xE9 pre dlh\u0161ie popisy. Presn\xE9 vyh\u013Ead\xE1vanie je vypnut\xE9.","form.attribute.text.option.short-text":"Kr\xE1tky text","form.attribute.text.option.short-text.description":"Vhodn\xE9 pre nadpisy, n\xE1zvy, URL adresy. Presn\xE9 vyh\u013Ead\xE1vanie je zapnut\xE9.","form.button.add-components-to-dynamiczone":"Prida\u0165 komponenty do z\xF3ny","form.button.add-field":"Prida\u0165 \u010Fal\u0161ie pol\xED\u010Dko","form.button.add-first-field-to-created-component":"Prida\u0165 prv\xE9 pol\xED\u010Dko do komponentu","form.button.add.field.to.collectionType":"Prida\u0165 \u010Fal\u0161ie pol\xED\u010Dko do tejto kolekcie","form.button.add.field.to.component":"Prida\u0165 \u010Fal\u0161ie pol\xED\u010Dko do komponentu","form.button.add.field.to.contentType":"Prida\u0165 \u010Fal\u0161ie pol\xED\u010Dko do tohto typu obsahu","form.button.add.field.to.singleType":"Prida\u0165 \u010Fal\u0161ie pol\xED\u010Dko do tohto jednorazov\xE9ho typu","form.button.cancel":"Zru\u0161i\u0165","form.button.collection-type.description":"Ide\xE1lne pre viacn\xE1sobn\xE9 in\u0161tancie ako s\xFA napr\xEDklad \u010Dl\xE1nky, produkty, koment\xE1re, at\u010F.","form.button.configure-component":"Nastavi\u0165 komponent","form.button.configure-view":"Upravi\u0165 vzh\u013Ead","form.button.select-component":"Vybra\u0165 komponent","form.button.single-type.description":"Ide\xE1lne pre jednorazov\xE9 in\u0161tancie ako s\xFA napr\xEDklad domovsk\xE1 str\xE1nka, at\u010F.",from:n,"modalForm.attribute.form.base.name.description":"Medzery nie s\xFA povolen\xE9 v n\xE1zve pol\xED\u010Dka","modalForm.attribute.form.base.name.placeholder":"napr. slug, seoUrl, kanonick\xE1Url","modalForm.attribute.target-field":"Prilo\u017Een\xE9 pol\xED\u010Dko","modalForm.attributes.select-component":"Vyberte komponent","modalForm.attributes.select-components":"Vyberte komponenty","modalForm.component.header-create":"Vytvorte komponent","modalForm.components.create-component.category.label":"Vyberte kateg\xF3riu alebo zadajte n\xE1zov pre vytvorenie novej","modalForm.components.icon.label":"Ikona","modalForm.editCategory.base.name.description":"Medzery nie s\xFA povolen\xE9 v n\xE1zve kateg\xF3rie","modalForm.header-edit":"Upravi\u0165 {name}","modalForm.header.categories":"Kateg\xF3rie","modalForm.header.back":"sp\xE4\u0165","modalForm.singleType.header-create":"Vytvori\u0165 jednorazov\xFD typ","modalForm.sub-header.addComponentToDynamicZone":"Prida\u0165 nov\xFD komponent do dynamickej z\xF3ny","modalForm.sub-header.attribute.create":"Prida\u0165 nov\xE9 pol\xED\u010Dko {type}","modalForm.sub-header.attribute.create.step":"Prida\u0165 nov\xFD komponent ({step}/2)","modalForm.sub-header.attribute.edit":"Upravi\u0165 {name}","modalForm.sub-header.chooseAttribute.collectionType":"Vyberte typ pol\xED\u010Dka pre Typ obsahu","modalForm.sub-header.chooseAttribute.component":"Vyberte typ pol\xED\u010Dka pre komponent","modalForm.sub-header.chooseAttribute.singleType":"Vyberte typ pol\xED\u010Dka pre jednorazov\xFD typ","modelPage.attribute.relation-polymorphic":"Prepojenie (polymorfn\xE9)","modelPage.attribute.relationWith":"Prepojenie s","notification.info.autoreaload-disable":"Funkcionalita AutoReload je povinn\xE1 pre pou\u017Eitie tohto pluginu. Spustite V\xE1\u0161 server pomocou pr\xEDkazu `strapi develop`","notification.info.creating.notSaved":"Ulo\u017Ete zmeny pred vytvoren\xEDm nov\xE9ho Typu obsahu alebo komponentu","plugin.description.long":"Navrhnite \u0161trukt\xFAru webu jednoducho. Vytvorte nov\xE9 pol\xED\u010Dka a prepojenia behom p\xE1r sek\xFAnd. S\xFAbory sa automaticky vytvoria a upravia v r\xE1mci projektu.","plugin.description.short":"Navrhnite \u0161trukt\xFAru webu jednoducho.","popUpForm.navContainer.advanced":"Pokro\u010Dil\xE9 nastavenia","popUpForm.navContainer.base":"Z\xE1kladn\xE9 nastavenia","popUpWarning.bodyMessage.cancel-modifications":"Ste si ist\xFD, \u017Ee chcete zru\u0161i\u0165 \xFApravy?","popUpWarning.bodyMessage.cancel-modifications.with-components":"Ste si ist\xFD, \u017Ee chcete zru\u0161i\u0165 \xFApravy? Niektor\xE9 komponenty boli vytvoren\xE9 alebo upraven\xE9...","popUpWarning.bodyMessage.category.delete":"Ste si ist\xFD, \u017Ee chcete odstr\xE1ni\u0165 t\xFAto kateg\xF3riu? V\u0161etky komponentu bud\xFA takisto vymazan\xE9.","popUpWarning.bodyMessage.component.delete":"Ste si ist\xFD, \u017Ee chcete odstr\xE1ni\u0165 tento komponent?","popUpWarning.bodyMessage.contentType.delete":"Ste si ist\xFD, \u017Ee chcete odstr\xE1ni\u0165 tento Typ obsahu?","popUpWarning.draft-publish.button.confirm":"\xC1no, deaktivova\u0165","popUpWarning.draft-publish.message":"Ak deaktivujete Draft/Publish syst\xE9m, v\u0161etky Va\u0161e n\xE1vrhy (drafts) bud\xFA zmazan\xE9","popUpWarning.draft-publish.second-message":"Ste si ist\xFD, \u017Ee to chcete deaktivova\u0165?","prompt.unsaved":"Ste si ist\xFD, \u017Ee chcete od\xEDs\u0165? V\u0161etky \xFApravy bud\xFA straten\xE9.","relation.attributeName.placeholder":"Napr: autor, kategoria, tag","relation.manyToMany":"m\xE1 viacero a patr\xED viacer\xFDm","relation.manyToOne":"m\xE1 viacero","relation.manyWay":"m\xE1 viacero","relation.oneToMany":"patr\xED viacer\xFDm","relation.oneToOne":"m\xE1 jeden a patr\xED jedn\xE9mu","relation.oneWay":"m\xE1 jeden"}}}]);
