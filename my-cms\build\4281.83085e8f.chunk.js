"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4281],{62482:(ts,p,s)=>{s.d(p,{F:()=>R});var t=s(92132),d=s(21272),y=s(94061),E=s(85963),m=s(55506),c=s(28604),L=s(54894);const R=({displayedFilters:D})=>{const[v,B]=d.useState(!1),{formatMessage:S}=(0,L.A)(),u=d.useRef(null),W=()=>{B(z=>!z)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(y.a,{paddingTop:1,paddingBottom:1,children:[(0,t.jsx)(E.$,{variant:"tertiary",ref:u,startIcon:(0,t.jsx)(c.A,{}),onClick:W,size:"S",children:S({id:"app.utils.filters",defaultMessage:"Filters"})}),v&&(0,t.jsx)(m.LC,{displayedFilters:D,isVisible:v,onToggle:W,source:u})]}),(0,t.jsx)(m.cZ,{filtersSchema:D})]})}},74281:(ts,p,s)=>{s.d(p,{ProtectedListPage:()=>fs,e:()=>ls});var t=s(92132),d=s(21272),y=s(50215),E=s(94061),m=s(85963),c=s(90151),L=s(68074),R=s(88353),D=s(74773),v=s(4198),B=s(55356),S=s(38413),u=s(61485),W=s(99248),z=s(67030),Z=s(6239),r=s(83997),O=s(25641),F=s(90361),N=s(33363),f=s(30893),e=s(55506),T=s(5409),$=s(54894),q=s(17703),_=s(43543),es=s(62482),g=s(34313),_s=s(41909),ds=s(50612),Es=s(37679),ms=s(60043),Ms=s(61535),b=s(12083),is=s(82803),as=s(66248),Ts=s(15126),Is=s(63299),Us=s(67014),Rs=s(59080),Bs=s(79275),us=s(14718),Ws=s(82437),xs=s(5790),Ks=s(35223),js=s(74930),ps=s(2600),ys=s(48940),Ss=s(41286),Fs=s(56336),Ns=s(13426),$s=s(84624),Vs=s(77965),zs=s(54257),Ys=s(71210),Gs=s(51187),Hs=s(39404),Qs=s(58692),Js=s(501),Xs=s(57646),Zs=s(23120),bs=s(44414),ks=s(25962),ws=s(14664),qs=s(42588),st=s(90325),tt=s(62785),et=s(87443),at=s(41032),nt=s(22957),it=s(93179),ot=s(73055),lt=s(15747),rt=s(85306),_t=s(26509),dt=s(32058),Et=s(81185),mt=s(82261),Mt=s(85071),Pt=s(55151),ct=s(79077);const Ps=({onClick:a})=>{const{formatMessage:i}=(0,$.A)();return(0,t.jsx)(m.$,{onClick:a,startIcon:(0,t.jsx)(g.A,{}),size:"S",children:i({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"})})},cs=({onToggle:a})=>{const[i,x]=d.useState("create"),[Y,G]=d.useState(""),{formatMessage:M}=(0,$.A)(),A=(0,e.hN)(),{lockApp:H,unlockApp:I}=(0,e.MA)(),{_unstableFormatAPIError:Q,_unstableFormatValidationErrors:K}=(0,e.wq)(),n=(0,_.p)(Ds,async()=>(await s.e(9971).then(s.bind(s,79971))).ROLE_LAYOUT,{combine(o,U){return[...o,...U]},defaultValue:[]}),V=(0,_.p)(os,async()=>(await s.e(9971).then(s.bind(s,79971))).FORM_INITIAL_VALUES,{combine(o,U){return{...o,...U}},defaultValue:os}),h=(0,_.p)(is.M,async()=>(await s.e(3153).then(s.bind(s,43153))).MagicLinkEE),[J]=(0,_.H)(),j=M({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"}),k=async(o,{setErrors:U})=>{H();const P=await J({...o,roles:o.roles??[]});"data"in P?P.data.registrationToken?(G(P.data.registrationToken),ss()):A({type:"warning",message:{id:"notification.error",defaultMessage:"An error occured"}}):(A({type:"warning",message:Q(P.error)}),(0,_.x)(P.error)&&P.error.name==="ValidationError"&&U(K(P.error))),I()},ss=()=>{l?x(l):a()},{buttonSubmitLabel:w,isDisabled:X,next:l}=As[i];return h?(0,t.jsxs)(u.k,{onClose:a,labelledBy:"title",children:[(0,t.jsx)(W.r,{children:(0,t.jsx)(Es.B,{label:j,children:(0,t.jsx)(ms.m,{isCurrent:!0,children:j})})}),(0,t.jsx)(Ms.l1,{enableReinitialize:!0,initialValues:V??{},onSubmit:k,validationSchema:gs,validateOnChange:!1,children:({errors:o,handleChange:U,values:P,isSubmitting:rs})=>(0,t.jsxs)(e.lV,{children:[(0,t.jsx)(Z.c,{children:(0,t.jsxs)(r.s,{direction:"column",alignItems:"stretch",gap:6,children:[i!=="create"&&(0,t.jsx)(h,{registrationToken:Y}),(0,t.jsxs)(E.a,{children:[(0,t.jsx)(f.o,{variant:"beta",as:"h2",children:M({id:"app.components.Users.ModalCreateBody.block-title.details",defaultMessage:"User details"})}),(0,t.jsx)(E.a,{paddingTop:4,children:(0,t.jsx)(r.s,{direction:"column",alignItems:"stretch",gap:1,children:(0,t.jsx)(c.x,{gap:5,children:Os.map(ns=>ns.map(C=>(0,t.jsx)(L.E,{...C.size,children:(0,t.jsx)(e.ah,{...C,disabled:X,error:o[C.name],onChange:U,value:P[C.name]})},C.name)))})})})]}),(0,t.jsxs)(E.a,{children:[(0,t.jsx)(f.o,{variant:"beta",as:"h2",children:M({id:"global.roles",defaultMessage:"User's role"})}),(0,t.jsx)(E.a,{paddingTop:4,children:(0,t.jsxs)(c.x,{gap:5,children:[(0,t.jsx)(L.E,{col:6,xs:12,children:(0,t.jsx)(is.S,{disabled:X,error:o.roles,onChange:U,value:P.roles??[]})}),n.map(ns=>ns.map(C=>(0,t.jsx)(L.E,{...C.size,children:(0,t.jsx)(e.ah,{...C,disabled:X,onChange:U,value:P[C.name]})},C.name)))]})})]})]})}),(0,t.jsx)(z.j,{startActions:(0,t.jsx)(m.$,{variant:"tertiary",onClick:a,type:"button",children:M({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),endActions:i==="create"?(0,t.jsx)(m.$,{type:"submit",loading:rs,children:M(w)}):(0,t.jsx)(m.$,{type:"button",loading:rs,onClick:a,children:M(w)})})]})})]}):null},os={firstname:"",lastname:"",email:"",roles:[]},Ds=[],Os=[[{intlLabel:{id:"Auth.form.firstname.label",defaultMessage:"First name"},name:"firstname",placeholder:{id:"Auth.form.firstname.placeholder",defaultMessage:"e.g. Kai"},type:"text",size:{col:6,xs:12},required:!0},{intlLabel:{id:"Auth.form.lastname.label",defaultMessage:"Last name"},name:"lastname",placeholder:{id:"Auth.form.lastname.placeholder",defaultMessage:"e.g. Doe"},type:"text",size:{col:6,xs:12}}],[{intlLabel:{id:"Auth.form.email.label",defaultMessage:"Email"},name:"email",placeholder:{id:"Auth.form.email.placeholder",defaultMessage:"e.g. <EMAIL>"},type:"email",size:{col:6,xs:12},required:!0}]],gs=b.Ik().shape({firstname:b.Yj().trim().required(e.iW.required),lastname:b.Yj(),email:b.Yj().email(e.iW.email).required(e.iW.required),roles:b.YO().min(1,e.iW.required).required(e.iW.required)}),As={create:{buttonSubmitLabel:{id:"app.containers.Users.ModalForm.footer.button-success",defaultMessage:"Invite user"},isDisabled:!1,next:"magic-link"},"magic-link":{buttonSubmitLabel:{id:"global.finish",defaultMessage:"Finish"},isDisabled:!0,next:null}},hs=({canDelete:a,headers:i=[],entriesToDelete:x=[],onClickDelete:Y,onSelectRow:G,withMainAction:M,withBulkActions:A,rows:H=[]})=>{const{push:I,location:{pathname:Q}}=(0,q.W6)(),{formatMessage:K}=(0,$.A)();return(0,t.jsx)(O.N,{children:H.map(n=>{const V=x.findIndex(h=>h===n.id)!==-1;return(0,t.jsxs)(F.Tr,{...(0,e.qM)({fn:()=>I(`${Q}/${n.id}`),condition:A}),children:[M&&(0,t.jsx)(N.Td,{...e.dG,children:(0,t.jsx)(y.J,{"aria-label":K({id:"app.component.table.select.one-entry",defaultMessage:"Select {target}"},{target:(0,as.g)(n?.firstname??"",n.lastname)}),checked:V,onChange:()=>{G&&G({name:n.id,value:!V})}})}),i.map(({key:h,cellFormatter:J,name:j,...k})=>(0,t.jsx)(N.Td,{children:typeof J=="function"?J(n,{key:h,name:j,formatMessage:K,...k}):(0,t.jsx)(f.o,{textColor:"neutral800",children:n[j]||"-"})},h)),A&&(0,t.jsx)(N.Td,{children:(0,t.jsxs)(r.s,{justifyContent:"end",children:[(0,t.jsx)(R.K,{onClick:()=>I(`${Q}/${n.id}`),label:K({id:"app.component.table.edit",defaultMessage:"Edit {target}"},{target:(0,as.g)(n.firstname??"",n.lastname)}),noBorder:!0,icon:(0,t.jsx)(_s.A,{})}),a&&(0,t.jsx)(E.a,{paddingLeft:1,...e.dG,children:(0,t.jsx)(R.K,{onClick:()=>{Y&&Y(n.id)},label:K({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:(0,as.g)(n.firstname??"",n.lastname)}),noBorder:!0,icon:(0,t.jsx)(ds.A,{})})})]})})]},n.id)})})},ls=()=>{const{_unstableFormatAPIError:a}=(0,e.wq)(),[i,x]=d.useState(!1),Y=(0,_.j)(l=>l.admin_app.permissions),{allowedActions:{canCreate:G,canDelete:M,canRead:A}}=(0,e.ec)(Y.settings?.users),H=(0,e.hN)(),{formatMessage:I}=(0,$.A)(),{search:Q}=(0,q.zy)();(0,e.L4)();const{data:K,isError:n,isLoading:V}=(0,_.k)(T.parse(Q,{ignoreQueryPrefix:!0}),{skip:!A}),{pagination:h,users:J}=K??{},j=(0,_.p)(Ps,async()=>(await s.e(5543).then(s.bind(s,5543))).CreateActionEE),k=Cs.map(l=>({...l,metadatas:{...l.metadatas,label:I(l.metadatas.label)}})),ss=I({id:"global.users",defaultMessage:"Users"}),w=()=>{x(l=>!l)},[X]=(0,_.J)();return j?(0,t.jsxs)(S.g,{"aria-busy":V,children:[(0,t.jsx)(e.x7,{name:"Users"}),(0,t.jsx)(B.Q,{primaryAction:G&&(0,t.jsx)(j,{onClick:w}),title:ss,subtitle:I({id:"Settings.permissions.users.listview.header.subtitle",defaultMessage:"All the users who have access to the Strapi admin panel"})}),A&&(0,t.jsx)(D.B,{startActions:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(e.q7,{label:I({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:ss})}),(0,t.jsx)(es.F,{displayedFilters:Ls})]})}),(0,t.jsxs)(v.s,{children:[!A&&(0,t.jsx)(e.UW,{}),n&&(0,t.jsx)("div",{children:"TODO: An error occurred"}),A&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(e.Ee,{contentType:"Users",isLoading:V,onConfirmDeleteAll:async l=>{const o=await X({ids:l});"error"in o&&H({type:"warning",message:a(o.error)})},onConfirmDelete:async l=>{const o=await X({ids:[l]});"error"in o&&H({type:"warning",message:a(o.error)})},headers:k,rows:J,withBulkActions:!0,withMainAction:M,children:(0,t.jsx)(hs,{canDelete:M})}),h&&(0,t.jsx)(E.a,{paddingTop:4,children:(0,t.jsxs)(r.s,{alignItems:"flex-end",justifyContent:"space-between",children:[(0,t.jsx)(e._u,{}),(0,t.jsx)(e.W7,{pagination:h})]})})]})]}),i&&(0,t.jsx)(cs,{onToggle:w})]}):null},Cs=[{name:"firstname",key:"firstname",metadatas:{label:{id:"Settings.permissions.users.firstname",defaultMessage:"Firstname"},sortable:!0}},{name:"lastname",key:"lastname",metadatas:{label:{id:"Settings.permissions.users.lastname",defaultMessage:"Lastname"},sortable:!0}},{key:"email",name:"email",metadatas:{label:{id:"Settings.permissions.users.email",defaultMessage:"Email"},sortable:!0}},{key:"roles",name:"roles",metadatas:{label:{id:"Settings.permissions.users.roles",defaultMessage:"Roles"},sortable:!1},cellFormatter({roles:a},{formatMessage:i}){return(0,t.jsx)(f.o,{textColor:"neutral800",children:a.map(x=>i({id:`Settings.permissions.users.${x.code}`,defaultMessage:x.name})).join(`,
`)})}},{key:"username",name:"username",metadatas:{label:{id:"Settings.permissions.users.username",defaultMessage:"Username"},sortable:!0}},{key:"isActive",name:"isActive",metadatas:{label:{id:"Settings.permissions.users.user-status",defaultMessage:"User status"},sortable:!1},cellFormatter({isActive:a},{formatMessage:i}){return(0,t.jsxs)(r.s,{children:[(0,t.jsx)(e.nW,{variant:a?"success":"danger"}),(0,t.jsx)(f.o,{textColor:"neutral800",children:i({id:a?"Settings.permissions.users.active":"Settings.permissions.users.inactive",defaultMessage:a?"Active":"Inactive"})})]})}}],Ls=[{name:"firstname",metadatas:{label:"Firstname"},fieldSchema:{type:"string"}},{name:"lastname",metadatas:{label:"Lastname"},fieldSchema:{type:"string"}},{name:"email",metadatas:{label:"Email"},fieldSchema:{type:"email"}},{name:"username",metadatas:{label:"Username"},fieldSchema:{type:"string"}},{name:"isActive",metadatas:{label:"Active user"},fieldSchema:{type:"boolean"}}],vs=()=>{const a=(0,_.p)(ls,async()=>(await s.e(1160).then(s.bind(s,91160))).UserListPageEE);return a?(0,t.jsx)(a,{}):null},fs=()=>{const a=(0,_.j)(i=>i.admin_app.permissions.settings?.users.main);return(0,t.jsx)(e.kz,{permissions:a,children:(0,t.jsx)(vs,{})})}},82803:(ts,p,s)=>{s.d(p,{M:()=>S,S:()=>u,a:()=>B});var t=s(92132),d=s(54894),y=s(43543),E=s(88353),m=s(56654),c=s(55506),L=s(90625),R=s(84795),D=s(63891),v=s(85071);const B=({children:r,target:O})=>{const F=(0,c.hN)(),{formatMessage:N}=(0,d.A)(),{copy:f}=(0,c.iD)(),e=N({id:"app.component.CopyToClipboard.label",defaultMessage:"Copy to clipboard"}),T=async()=>{await f(O)&&F({type:"info",message:{id:"notification.link-copied"}})};return(0,t.jsx)(c.bQ,{endAction:(0,t.jsx)(E.K,{label:e,noBorder:!0,icon:(0,t.jsx)(L.A,{}),onClick:T}),title:O,titleEllipsis:!0,subtitle:r,icon:(0,t.jsx)("span",{style:{fontSize:32},children:"\u2709\uFE0F"}),iconBackground:"neutral100"})},S=({registrationToken:r})=>{const{formatMessage:O}=(0,d.A)(),F=`${window.location.origin}${(0,y.K)()}/auth/register?registrationToken=${r}`;return(0,t.jsx)(B,{target:F,children:O({id:"app.components.Users.MagicLink.connect",defaultMessage:"Copy and share this link to give access to this user"})})},u=({disabled:r,error:O,onChange:F,value:N})=>{const{isLoading:f,roles:e}=(0,v.u)(),{formatMessage:T}=(0,d.A)(),$=O?T({id:O,defaultMessage:O}):"",q=T({id:"app.components.Users.ModalCreateBody.block-title.roles",defaultMessage:"User's roles"}),_=T({id:"app.components.Users.ModalCreateBody.block-title.roles.description",defaultMessage:"A user can have one or several roles"}),es=T({id:"app.components.Select.placeholder",defaultMessage:"Select"});return(0,t.jsx)(m.KF,{id:"roles",disabled:r,error:$,hint:_,label:q,name:"roles",onChange:g=>{F({target:{name:"roles",value:g}})},placeholder:es,startIcon:f?(0,t.jsx)(Z,{}):void 0,value:N.map(g=>g.toString()),withTags:!0,required:!0,children:e.map(g=>(0,t.jsx)(m.fe,{value:g.id.toString(),children:T({id:`global.${g.code}`,defaultMessage:g.name})},g.id))})},W=(0,D.i7)`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
`,z=D.Ay.div`
  animation: ${W} 2s infinite linear;
`,Z=()=>(0,t.jsx)(z,{children:(0,t.jsx)(R.A,{})})},85071:(ts,p,s)=>{s.d(p,{u:()=>m});var t=s(21272),d=s(55506),y=s(54894),E=s(43543);const m=(c={},L)=>{const{locale:R}=(0,y.A)(),D=(0,d.QM)(R,{sensitivity:"base"}),{data:v,error:B,isError:S,isLoading:u,refetch:W}=(0,E.z)(c,L);return{roles:t.useMemo(()=>[...v??[]].sort((Z,r)=>D.compare(Z.name,r.name)),[v,D]),error:B,isError:S,isLoading:u,refetch:W}}}}]);
