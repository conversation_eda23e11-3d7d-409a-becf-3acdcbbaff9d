"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4384],{54384:(a,u,t)=>{t.r(u),t.d(u,{default:()=>i,from:()=>e});const e="from",i={"attribute.boolean":"Boolean","attribute.date":"Date","attribute.email":"Email","attribute.enumeration":"Enumeration","attribute.json":"JSON","attribute.media":"Media","attribute.password":"Password","attribute.relation":"Relation","attribute.text":"Text","form.attribute.item.customColumnName":"\u30AB\u30B9\u30BF\u30E0\u5217\u540D","form.attribute.item.customColumnName.description":"\u3053\u308C\u306F\u3001\u30C7\u30FC\u30BF\u30D9\u30FC\u30B9\u306E\u30AB\u30E9\u30E0\u540D\u3092API\u306E\u30EC\u30B9\u30DD\u30F3\u30B9\u306E\u3088\u308A\u5305\u62EC\u7684\u306A\u30D5\u30A9\u30FC\u30DE\u30C3\u30C8\u306B\u5909\u66F4\u3059\u308B\u5834\u5408\u306B\u4FBF\u5229\u3067\u3059","form.attribute.item.defineRelation.fieldName":"\u30D5\u30A3\u30FC\u30EB\u30C9\u540D","form.attribute.item.enumeration.graphql":"GraphQL\u306E\u540D\u524D\u306E\u4E0A\u66F8\u304D","form.attribute.item.enumeration.graphql.description":"GraphQL\u306E\u65E2\u5B9A\u306E\u751F\u6210\u540D\u3092\u30AA\u30FC\u30D0\u30FC\u30E9\u30A4\u30C9\u3067\u304D\u307E\u3059","form.attribute.item.enumeration.rules":"Values (one line per value)","form.attribute.item.maximum":"\u6700\u5927\u5024","form.attribute.item.maximumLength":"\u6700\u5927\u9577","form.attribute.item.minimum":"\u6700\u5C0F\u5024","form.attribute.item.minimumLength":"\u6700\u5C0F\u9577","form.attribute.item.number.type":"\u6570\u5024\u5F62\u5F0F","form.attribute.item.number.type.decimal":"decimal (ex: 2.22)","form.attribute.item.number.type.float":"float (ex: 3.33333333)","form.attribute.item.number.type.integer":"integer (ex: 10)","form.attribute.item.requiredField":"\u5FC5\u9808\u30D5\u30A3\u30FC\u30EB\u30C9","form.attribute.item.requiredField.description":"\u3053\u306E\u30D5\u30A3\u30FC\u30EB\u30C9\u304C\u7A7A\u306E\u5834\u5408\u3001\u30A8\u30F3\u30C8\u30EA\u3092\u4F5C\u6210\u3059\u308B\u3053\u3068\u306F\u3067\u304D\u307E\u305B\u3093","form.attribute.item.uniqueField":"\u4E00\u610F\u306E\u30D5\u30A3\u30FC\u30EB\u30C9","form.attribute.item.uniqueField.description":"\u540C\u3058\u5185\u5BB9\u306E\u65E2\u5B58\u306E\u30A8\u30F3\u30C8\u30EA\u304C\u3042\u308B\u5834\u5408\u3001\u30A8\u30F3\u30C8\u30EA\u3092\u4F5C\u6210\u3059\u308B\u3053\u3068\u306F\u3067\u304D\u307E\u305B\u3093","form.attribute.settings.default":"\u30C7\u30D5\u30A9\u30EB\u30C8\u5024","form.button.cancel":"\u30AD\u30E3\u30F3\u30BB\u30EB",from:e,"modelPage.attribute.relationWith":"\u95A2\u4FC2","plugin.description.long":"API\u306E\u30C7\u30FC\u30BF\u69CB\u9020\u3092\u30E2\u30C7\u30EB\u5316\u3057\u307E\u3059\u3002\u6570\u5206\u3067\u65B0\u3057\u3044\u30D5\u30A3\u30FC\u30EB\u30C9\u3068\u95A2\u4FC2\u3092\u4F5C\u6210\u3057\u307E\u3059\u3002\u30D5\u30A1\u30A4\u30EB\u306F\u30D7\u30ED\u30B8\u30A7\u30AF\u30C8\u3067\u81EA\u52D5\u7684\u306B\u4F5C\u6210\u3055\u308C\u3001\u66F4\u65B0\u3055\u308C\u307E\u3059\u3002","plugin.description.short":"API\u306E\u30C7\u30FC\u30BF\u69CB\u9020\u3092\u30E2\u30C7\u30EB\u5316\u3057\u307E\u3059\u3002","popUpForm.navContainer.advanced":"\u9AD8\u5EA6\u306A\u8A2D\u5B9A","popUpForm.navContainer.base":"\u57FA\u672C\u8A2D\u5B9A","popUpWarning.bodyMessage.contentType.delete":"\u3053\u306E\u30B3\u30F3\u30C6\u30F3\u30C4\u30BF\u30A4\u30D7\u3092\u524A\u9664\u3057\u3066\u3082\u3088\u308D\u3057\u3044\u3067\u3059\u304B\uFF1F","relation.attributeName.placeholder":"\u4F8B\uFF1Aauthor\u3001category\u3001tag","relation.manyToMany":"has and belongs to many","relation.manyToOne":"has many","relation.oneToMany":"belongs to many","relation.oneToOne":"has and belongs to one","relation.oneWay":"has one"}}}]);
