(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[1595],{4191:(x,U,t)=>{var s=t(87864),e=t(86386),l=t(45353),d=t(29884),M=t(74565),P=t(52689),g=t(48126),D=t(82388),A=t(82261);function y(B,C,b){C.length?C=s(C,function(I){return A(I)?function(_){return e(_,I.length===1?I[0]:I)}:I}):C=[D];var w=-1;C=s(C,P(l));var k=d(B,function(I,_,it){var Q=s(C,function(z){return z(I)});return{criteria:Q,index:++w,value:I}});return M(k,function(I,_){return g(I,_,b)})}x.exports=y},29884:(x,U,t)=>{var s=t(97449),e=t(91522);function l(d,M){var P=-1,g=e(d)?Array(d.length):[];return s(d,function(D,A,y){g[++P]=M(D,A,y)}),g}x.exports=l},45635:(x,U,t)=>{var s=t(87212),e=t(4191),l=t(39226),d=t(3956),M=l(function(P,g){if(P==null)return[];var D=g.length;return D>1&&d(P,g[0],g[1])?g=[]:D>2&&d(g[0],g[1],g[2])&&(g=[g[0]]),e(P,s(g,1),[])});x.exports=M},48126:(x,U,t)=>{var s=t(64958);function e(l,d,M){for(var P=-1,g=l.criteria,D=d.criteria,A=g.length,y=M.length;++P<A;){var B=s(g[P],D[P]);if(B){if(P>=y)return B;var C=M[P];return B*(C=="desc"?-1:1)}}return l.index-d.index}x.exports=e},51595:(x,U,t)=>{"use strict";t.r(U),t.d(U,{SettingsPage:()=>as,makeUniqueRoutes:()=>Dt});var s=t(92132),e=t(21272),l=t(94061),d=t(85963),M=t(19307),P=t(37789),g=t(1844),D=t(76547),A=t(52358),y=t(33468),B=t(98915),C=t(5239),b=t(11196),w=t(7981),k=t(48653),I=t(7153),_=t(74447),it=t(10229),Q=t(90151),z=t(68074),et=t(58805),dt=t(88353),lt=t(42455),Ot=t(4198),pt=t(55356),rt=t(64871),xt=t(38413),Ct=t(61485),ft=t(99248),q=t(67030),S=t(83997),tt=t(12408),st=t(24093),Lt=t(12081),vt=t(7537),j=t(30893),f=t(55506),At=t(14718),W=t(54894),G=t(17703),jt=t(71389),F=t(43543),Rt=t(45635),gt=t(82437),ct=t(66248),Bt=t(41156),It=t(32091),Tt=t(63996),Ut=t(87419),yt=t(29404),Kt=t(54514),Et=t(14595),Wt=t(66980),St=t(99576),zt=t(5194),Ft=t(70603),mt=t(63891),_t=t(89032),$t=t(5463),Nt=t(60256),os=t(15126),is=t(63299),es=t(67014),ds=t(59080),ls=t(79275),rs=t(61535),gs=t(5790),cs=t(12083),Es=t(35223),ms=t(5409),hs=t(74930),Ms=t(2600),Ps=t(48940),us=t(41286),Ds=t(56336),Os=t(13426),ps=t(84624),xs=t(77965),Cs=t(54257),fs=t(71210),Ls=t(51187),vs=t(39404),As=t(58692),js=t(501),Rs=t(57646),Bs=t(23120),Is=t(44414),Ts=t(25962),Us=t(14664),ys=t(42588),Ks=t(90325),Ws=t(62785),Ss=t(87443),zs=t(41032),Fs=t(22957),_s=t(93179),$s=t(73055),Ns=t(15747),Vs=t(85306),Gs=t(26509),Zs=t(32058),Xs=t(81185),Hs=t(82261),Qs=t(55151),Js=t(79077);const Vt=n=>n.map(a=>{const o=a.links.map(m=>({...m,isDisplayed:!1}));return{...a,links:o}}),Gt=()=>{const[{isLoading:n,menu:a},o]=e.useState({isLoading:!0,menu:[]}),{allPermissions:m}=(0,f.r5)(),{shouldUpdateStrapi:c}=(0,f.Xe)(),{settings:E}=(0,f.vD)(),u=(0,gt.d4)(ct.s),O=e.useMemo(()=>(0,F.y)(),[]),{admin:i,global:r}=(0,F.p)(O,async()=>(await t.e(178).then(t.bind(t,50178))).SETTINGS_LINKS_EE(),{combine(p,L){return{admin:[...L.admin,...p.admin],global:[...p.global,...L.global]}},defaultValue:{admin:[],global:[]}}),h=e.useCallback(p=>{if(!p.id)throw new Error("The settings menu item must have an id attribute.");return{...p,permissions:u.settings?.[p.id]?.main??[]}},[u.settings]);return e.useEffect(()=>{const p=async()=>{const J=await(H=>Promise.all(H.reduce((K,N,V)=>{const at=N.links.map(async(ot,Y)=>({hasPermission:await(0,f.v$)(m,ot.permissions),sectionIndex:V,linkIndex:Y}));return[...K,...at]},[])))(T);o(H=>({...H,isLoading:!1,menu:T.map((K,N)=>({...K,links:K.links.map((V,at)=>{const ot=J.find(Y=>Y.sectionIndex===N&&Y.linkIndex===at);return{...V,isDisplayed:Boolean(ot?.hasPermission)}})}))}))},{global:L,...R}=E,T=Vt([{...L,links:Rt([...L.links,...r.map(h)],v=>v.id).map(v=>({...v,hasNotification:v.id==="000-application-infos"&&c}))},{id:"permissions",intlLabel:{id:"Settings.permissions",defaultMessage:"Administration Panel"},links:i.map(h)},...Object.values(R)]);p()},[i,r,m,E,c,h]),{isLoading:n,menu:a.map(p=>({...p,links:p.links.filter(L=>L.isDisplayed)}))}},Zt=(0,mt.Ay)(et.I)`
  right: 15px;
  position: absolute;

  path {
    fill: ${({theme:n})=>n.colors.warning500};
  }
`,Xt=({menu:n})=>{const{formatMessage:a}=(0,W.A)(),{trackUsage:o}=(0,f.z1)(),{pathname:m}=(0,G.zy)(),E=n.filter(i=>!i.links.every(r=>r.isDisplayed===!1)).map(i=>({...i,title:i.intlLabel,links:i.links.map(r=>({...r,title:r.intlLabel,name:r.id}))})),u=a({id:"global.settings",defaultMessage:"Settings"}),O=i=>()=>{o("willNavigate",{from:m,to:i})};return(0,s.jsxs)(Bt.C,{ariaLabel:u,children:[(0,s.jsx)(It.X,{label:u}),(0,s.jsx)(yt.w,{children:E.map(i=>(0,s.jsx)(Ut.L,{label:a(i.intlLabel),children:i.links.map(r=>(0,s.jsxs)(Tt.u,{as:jt.k2,withBullet:r.hasNotification,to:r.to,onClick:O(r.to),children:[a(r.intlLabel),r?.lockIcon&&(0,s.jsx)(Zt,{width:`${15/16}rem`,height:`${15/16}rem`,as:Wt.A})]},r.id))},i.id))})]})},Ht=[{async Component(){const{ProtectedListPage:n}=await t.e(9477).then(t.bind(t,9477));return n},to:"/settings/roles",exact:!0},{async Component(){const{ProtectedCreatePage:n}=await Promise.all([t.e(6103),t.e(3884),t.e(1397)]).then(t.bind(t,61397));return n},to:"/settings/roles/duplicate/:id",exact:!0},{async Component(){const{ProtectedCreatePage:n}=await Promise.all([t.e(6103),t.e(3884),t.e(1397)]).then(t.bind(t,61397));return n},to:"/settings/roles/new",exact:!0},{async Component(){const{ProtectedEditPage:n}=await Promise.all([t.e(3884),t.e(309)]).then(t.bind(t,10309));return n},to:"/settings/roles/:id",exact:!0},{async Component(){const{ProtectedListPage:n}=await t.e(4281).then(t.bind(t,74281));return n},to:"/settings/users",exact:!0},{async Component(){const{ProtectedEditPage:n}=await t.e(632).then(t.bind(t,632));return n},to:"/settings/users/:id",exact:!0},{async Component(){const{ProtectedCreatePage:n}=await t.e(2466).then(t.bind(t,52466));return n},to:"/settings/webhooks/create",exact:!0},{async Component(){const{ProtectedEditPage:n}=await t.e(6153).then(t.bind(t,46153)).then(a=>a.b);return n},to:"/settings/webhooks/:id",exact:!0},{async Component(){const{ProtectedListPage:n}=await t.e(165).then(t.bind(t,50165));return n},to:"/settings/webhooks",exact:!0},{async Component(){const{ProtectedListView:n}=await t.e(7632).then(t.bind(t,27632));return n},to:"/settings/api-tokens",exact:!0},{async Component(){const{ProtectedCreateView:n}=await Promise.all([t.e(6103),t.e(8182),t.e(8324)]).then(t.bind(t,18324));return n},to:"/settings/api-tokens/create",exact:!0},{async Component(){const{ProtectedEditView:n}=await Promise.all([t.e(6103),t.e(8182),t.e(8595)]).then(t.bind(t,28595));return n},to:"/settings/api-tokens/:id",exact:!0},{async Component(){const{ProtectedCreateView:n}=await Promise.all([t.e(6103),t.e(8182),t.e(914)]).then(t.bind(t,90914));return n},to:"/settings/transfer-tokens/create",exact:!0},{async Component(){const{ProtectedListView:n}=await t.e(598).then(t.bind(t,10598));return n},to:"/settings/transfer-tokens",exact:!0},{async Component(){const{ProtectedEditView:n}=await Promise.all([t.e(6103),t.e(8182),t.e(3292)]).then(t.bind(t,13292));return n},to:"/settings/transfer-tokens/:id",exact:!0},{async Component(){const{PurchaseAuditLogs:n}=await t.e(6323).then(t.bind(t,96323));return n},to:"/settings/purchase-audit-logs",exact:!0},{async Component(){const{PurchaseReviewWorkflows:n}=await t.e(5904).then(t.bind(t,5904));return n},to:"/settings/purchase-review-workflows",exact:!0},{async Component(){const{PurchaseSingleSignOn:n}=await t.e(8848).then(t.bind(t,98848));return n},to:"/settings/purchase-single-sign-on",exact:!0}],$=750,Z=100,ht=["image/jpeg","image/png","image/svg+xml"],Qt={id:"Settings.application.customization.modal.upload.error-format",defaultMessage:"Wrong format uploaded (accepted formats only: jpeg, jpg, png, svg)."},Mt={id:"Settings.application.customization.modal.upload.error-size",defaultMessage:"The file uploaded is too large (max dimension: {dimension}x{dimension}, max file size: {size}KB)"},Pt=async n=>{if(!ht.includes(n.type))throw new X("File format",Qt);const o=await new Promise(u=>{const O=new FileReader;O.onload=()=>{const i=new Image;i.onload=()=>{u({width:i.width,height:i.height})},i.src=O.result},O.readAsDataURL(n)});if(!(o.width<=$&&o.height<=$))throw new X("File sizing",Mt);const c={ext:n.name.split(".").pop(),size:n.size/1e3,name:n.name,url:URL.createObjectURL(n),rawFile:n,width:o.width,height:o.height};if(!(c.size<=Z))throw new X("File sizing",Mt);return c};class X extends Error{displayMessage;constructor(a,o,m){super(a,m),this.displayMessage=o}}const[Jt,nt]=(0,_t.q)("LogoInput"),ut=({canUpdate:n,customLogo:a,defaultLogo:o,hint:m,label:c,onChangeLogo:E})=>{const[u,O]=e.useState(),[i,r]=e.useState(),{formatMessage:h}=(0,W.A)(),p=()=>{O(void 0),r(void 0)};return(0,s.jsxs)(Jt,{setLocalImage:O,localImage:u,goToStep:r,onClose:p,children:[(0,s.jsx)(C.Z,{label:c,selectedSlide:0,hint:m,previousLabel:"",nextLabel:"",onNext:()=>{},onPrevious:()=>{},secondaryLabel:a?.name||"logo.png",actions:(0,s.jsxs)(b.O,{children:[(0,s.jsx)(dt.K,{disabled:!n,onClick:()=>r("upload"),label:h({id:"Settings.application.customization.carousel.change-action",defaultMessage:"Change logo"}),icon:(0,s.jsx)(zt.A,{})}),a?.url&&(0,s.jsx)(dt.K,{disabled:!n,onClick:()=>E(null),label:h({id:"Settings.application.customization.carousel.reset-action",defaultMessage:"Reset logo"}),icon:(0,s.jsx)(Ft.A,{})})]}),children:(0,s.jsx)(w.o,{label:h({id:"Settings.application.customization.carousel-slide.label",defaultMessage:"Logo slide"}),children:(0,s.jsx)(l.a,{maxHeight:"40%",maxWidth:"40%",as:"img",src:a?.url||o,alt:h({id:"Settings.application.customization.carousel.title",defaultMessage:"Logo"})})})}),i?(0,s.jsxs)(Ct.k,{labelledBy:"modal",onClose:p,children:[(0,s.jsx)(ft.r,{children:(0,s.jsx)(j.o,{fontWeight:"bold",as:"h2",id:"modal",children:h(i==="upload"?{id:"Settings.application.customization.modal.upload",defaultMessage:"Upload logo"}:{id:"Settings.application.customization.modal.pending",defaultMessage:"Pending logo"})})}),i==="upload"?(0,s.jsx)(Yt,{}):(0,s.jsx)(qt,{onChangeLogo:E})]}):null]})},Yt=()=>{const{formatMessage:n}=(0,W.A)();return(0,s.jsxs)(Lt.f,{label:n({id:"Settings.application.customization.modal.tab.label",defaultMessage:"How do you want to upload your assets?"}),variant:"simple",children:[(0,s.jsxs)(l.a,{paddingLeft:8,paddingRight:8,children:[(0,s.jsxs)(tt.t,{children:[(0,s.jsx)(tt.o,{children:n({id:"Settings.application.customization.modal.upload.from-computer",defaultMessage:"From computer"})}),(0,s.jsx)(tt.o,{children:n({id:"Settings.application.customization.modal.upload.from-url",defaultMessage:"From url"})})]}),(0,s.jsx)(k.c,{})]}),(0,s.jsxs)(st.T,{children:[(0,s.jsx)(st.K,{children:(0,s.jsx)(wt,{})}),(0,s.jsx)(st.K,{children:(0,s.jsx)(bt,{})})]})]})},bt=()=>{const{formatMessage:n}=(0,W.A)(),[a,o]=e.useState(""),[m,c]=e.useState(),{setLocalImage:E,goToStep:u,onClose:O}=nt("URLForm"),i=h=>{o(h.target.value)},r=async h=>{h.preventDefault();const L=new FormData(h.target).get("logo-url");if(L)try{const R=await $t.A.get(L.toString(),{responseType:"blob",timeout:8e3}),T=new File([R.data],R.config.url??"",{type:R.headers["content-type"]}),v=await Pt(T);E(v),u("pending")}catch(R){if(R instanceof Nt.pe)c(n({id:"Settings.application.customization.modal.upload.error-network",defaultMessage:"Network error"}));else if(R instanceof X)c(n(R.displayMessage,{size:Z,dimension:$}));else throw R}};return(0,s.jsxs)("form",{onSubmit:r,children:[(0,s.jsx)(l.a,{paddingLeft:8,paddingRight:8,paddingTop:6,paddingBottom:6,children:(0,s.jsx)(vt.k,{label:n({id:"Settings.application.customization.modal.upload.from-url.input-label",defaultMessage:"URL"}),error:m,onChange:i,value:a,name:"logo-url"})}),(0,s.jsx)(q.j,{startActions:(0,s.jsx)(d.$,{onClick:O,variant:"tertiary",children:n({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),endActions:(0,s.jsx)(d.$,{type:"submit",children:n({id:"Settings.application.customization.modal.upload.next",defaultMessage:"Next"})})})]})},wt=()=>{const{formatMessage:n}=(0,W.A)(),[a,o]=e.useState(!1),[m,c]=e.useState(),E=e.useRef(null),u=e.useId(),{setLocalImage:O,goToStep:i,onClose:r}=nt("ComputerForm"),h=()=>o(!0),p=()=>o(!1),L=T=>{T.preventDefault(),E.current.click()},R=async()=>{if(p(),!E.current.files)return;const[T]=E.current.files;try{const v=await Pt(T);O(v),i("pending")}catch(v){if(v instanceof X)c(n(v.displayMessage,{size:Z,dimension:$})),E.current.focus();else throw v}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("form",{children:(0,s.jsx)(l.a,{paddingLeft:8,paddingRight:8,paddingTop:6,paddingBottom:6,children:(0,s.jsx)(I.D,{name:u,error:m,children:(0,s.jsxs)(S.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,s.jsxs)(S.s,{paddingTop:9,paddingBottom:7,hasRadius:!0,justifyContent:"center",direction:"column",background:a?"primary100":"neutral100",borderColor:a?"primary500":m?"danger600":"neutral300",borderStyle:"dashed",borderWidth:"1px",position:"relative",onDragEnter:h,onDragLeave:p,children:[(0,s.jsx)(et.I,{color:"primary600",width:(0,f.a8)(60),height:(0,f.a8)(60),as:St.A,"aria-hidden":!0}),(0,s.jsx)(l.a,{paddingTop:3,paddingBottom:5,children:(0,s.jsx)(j.o,{variant:"delta",as:"label",htmlFor:u,children:n({id:"Settings.application.customization.modal.upload.drag-drop",defaultMessage:"Drag and Drop here or"})})}),(0,s.jsx)(kt,{accept:ht.join(", "),type:"file",name:"files",tabIndex:-1,onChange:R,ref:E,id:u}),(0,s.jsx)(d.$,{type:"button",onClick:L,children:n({id:"Settings.application.customization.modal.upload.cta.browse",defaultMessage:"Browse files"})}),(0,s.jsx)(l.a,{paddingTop:6,children:(0,s.jsx)(j.o,{variant:"pi",textColor:"neutral600",children:n({id:"Settings.application.customization.modal.upload.file-validation",defaultMessage:"Max dimension: {dimension}x{dimension}, Max size: {size}KB"},{size:Z,dimension:$})})})]}),(0,s.jsx)(it.b,{})]})})})}),(0,s.jsx)(q.j,{startActions:(0,s.jsx)(d.$,{onClick:r,variant:"tertiary",children:n({id:"Settings.application.customization.modal.cancel",defaultMessage:"Cancel"})})})]})},kt=(0,mt.Ay)(_.T)`
  opacity: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
`,qt=({onChangeLogo:n})=>{const{formatMessage:a}=(0,W.A)(),{localImage:o,setLocalImage:m,goToStep:c,onClose:E}=nt("PendingLogoDialog"),u=()=>{m(void 0),c("upload")},O=()=>{o&&n(o),E()};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.a,{paddingLeft:8,paddingRight:8,paddingTop:6,paddingBottom:6,children:[(0,s.jsxs)(S.s,{justifyContent:"space-between",paddingBottom:6,children:[(0,s.jsxs)(S.s,{direction:"column",alignItems:"flex-start",children:[(0,s.jsx)(j.o,{variant:"pi",fontWeight:"bold",children:a({id:"Settings.application.customization.modal.pending.title",defaultMessage:"Logo ready to upload"})}),(0,s.jsx)(j.o,{variant:"pi",textColor:"neutral500",children:a({id:"Settings.application.customization.modal.pending.subtitle",defaultMessage:"Manage the chosen logo before uploading it"})})]}),(0,s.jsx)(d.$,{onClick:u,variant:"secondary",children:a({id:"Settings.application.customization.modal.pending.choose-another",defaultMessage:"Choose another logo"})})]}),(0,s.jsx)(l.a,{maxWidth:(0,f.a8)(180),children:o?.url?(0,s.jsx)(ts,{asset:o}):null})]}),(0,s.jsx)(q.j,{startActions:(0,s.jsx)(d.$,{onClick:E,variant:"tertiary",children:a({id:"Settings.application.customization.modal.cancel",defaultMessage:"Cancel"})}),endActions:(0,s.jsx)(d.$,{onClick:O,children:a({id:"Settings.application.customization.modal.pending.upload",defaultMessage:"Upload logo"})})})]})},ts=({asset:n})=>{const{formatMessage:a}=(0,W.A)();return(0,s.jsxs)(M.Z,{children:[(0,s.jsx)(y.a,{children:(0,s.jsx)(P.P,{size:"S",src:n.url})}),(0,s.jsxs)(D.b,{children:[(0,s.jsxs)(A.W,{children:[(0,s.jsx)(B.Z,{children:n.name}),(0,s.jsx)(B.j,{children:`${n.ext?.toUpperCase()} - ${n.width}\u2715${n.height}`})]}),(0,s.jsx)(g.S,{children:a({id:"Settings.application.customization.modal.pending.card-badge",defaultMessage:"image"})})]})]})},ss=()=>null,ns=()=>{const{trackUsage:n}=(0,f.z1)(),{formatMessage:a}=(0,W.A)(),{logos:o,updateProjectSettings:m}=(0,F.u)("ApplicationInfoPage"),[c,E]=e.useState({menu:o.menu,auth:o.auth}),{settings:u}=(0,gt.d4)(ct.s),{communityEdition:O,latestStrapiReleaseTag:i,nodeVersion:r,shouldUpdateStrapi:h,strapiVersion:p}=(0,f.Xe)(),L=(0,F.p)(ss,async()=>(await t.e(3440).then(t.bind(t,53440))).AdminSeatInfoEE),{allowedActions:{canRead:R,canUpdate:T}}=(0,f.ec)(u?u["project-settings"]:{});(0,f.L4)();const v=K=>{K.preventDefault(),m({authLogo:c.auth.custom??null,menuLogo:c.menu.custom??null})},J=K=>N=>{N===null&&n("didClickResetLogo",{logo:K}),E(V=>({...V,[K]:{...V[K],custom:N}}))};if(e.useEffect(()=>{E({menu:o.menu,auth:o.auth})},[o]),!L)return null;const H=c.auth.custom===o.auth.custom&&c.menu.custom===o.menu.custom;return(0,s.jsxs)(lt.P,{children:[(0,s.jsx)(f.x7,{name:a({id:"Settings.application.header",defaultMessage:"Application"})}),(0,s.jsx)(xt.g,{children:(0,s.jsxs)("form",{onSubmit:v,children:[(0,s.jsx)(pt.Q,{title:a({id:"Settings.application.title",defaultMessage:"Overview"}),subtitle:a({id:"Settings.application.description",defaultMessage:"Administration panel\u2019s global information"}),primaryAction:T&&(0,s.jsx)(d.$,{disabled:H,type:"submit",startIcon:(0,s.jsx)(Kt.A,{}),children:a({id:"global.save",defaultMessage:"Save"})})}),(0,s.jsx)(Ot.s,{children:(0,s.jsxs)(S.s,{direction:"column",alignItems:"stretch",gap:6,children:[(0,s.jsxs)(S.s,{direction:"column",alignItems:"stretch",gap:4,hasRadius:!0,background:"neutral0",shadow:"tableShadow",paddingTop:6,paddingBottom:6,paddingRight:7,paddingLeft:7,children:[(0,s.jsx)(j.o,{variant:"delta",as:"h3",children:a({id:"global.details",defaultMessage:"Details"})}),(0,s.jsxs)(Q.x,{gap:5,as:"dl",children:[(0,s.jsxs)(z.E,{col:6,s:12,children:[(0,s.jsx)(j.o,{variant:"sigma",textColor:"neutral600",as:"dt",children:a({id:"Settings.application.strapiVersion",defaultMessage:"strapi version"})}),(0,s.jsxs)(S.s,{gap:3,direction:"column",alignItems:"start",as:"dd",children:[(0,s.jsxs)(j.o,{children:["v",p]}),h&&(0,s.jsx)(rt.N,{href:`https://github.com/strapi/strapi/releases/tag/${i}`,endIcon:(0,s.jsx)(Et.A,{}),children:a({id:"Settings.application.link-upgrade",defaultMessage:"Upgrade your admin panel"})})]})]}),(0,s.jsxs)(z.E,{col:6,s:12,children:[(0,s.jsx)(j.o,{variant:"sigma",textColor:"neutral600",as:"dt",children:a({id:"Settings.application.edition-title",defaultMessage:"current plan"})}),(0,s.jsxs)(S.s,{gap:3,direction:"column",alignItems:"start",as:"dd",children:[(0,s.jsx)(j.o,{children:a({id:"Settings.application.ee-or-ce",defaultMessage:"{communityEdition, select, true {Community Edition} other {Enterprise Edition}}"},{communityEdition:O})}),(0,s.jsx)(rt.N,{href:"https://strapi.io/pricing-self-hosted",endIcon:(0,s.jsx)(Et.A,{}),children:a({id:"Settings.application.link-pricing",defaultMessage:"See all pricing plans"})})]})]}),(0,s.jsxs)(z.E,{col:6,s:12,children:[(0,s.jsx)(j.o,{variant:"sigma",textColor:"neutral600",as:"dt",children:a({id:"Settings.application.node-version",defaultMessage:"node version"})}),(0,s.jsx)(j.o,{as:"dd",children:r})]}),(0,s.jsx)(L,{})]})]}),R&&(0,s.jsxs)(l.a,{hasRadius:!0,background:"neutral0",shadow:"tableShadow",paddingTop:6,paddingBottom:6,paddingRight:7,paddingLeft:7,children:[(0,s.jsx)(j.o,{variant:"delta",as:"h3",children:a({id:"Settings.application.customization",defaultMessage:"Customization"})}),(0,s.jsx)(j.o,{variant:"pi",textColor:"neutral600",children:a({id:"Settings.application.customization.size-details",defaultMessage:"Max dimension: {dimension}\xD7{dimension}, Max file size: {size}KB"},{dimension:$,size:Z})}),(0,s.jsxs)(Q.x,{paddingTop:4,gap:4,children:[(0,s.jsx)(z.E,{col:6,s:12,children:(0,s.jsx)(ut,{canUpdate:T,customLogo:c.menu.custom,defaultLogo:c.menu.default,hint:a({id:"Settings.application.customization.menu-logo.carousel-hint",defaultMessage:"Replace the logo in the main navigation"}),label:a({id:"Settings.application.customization.carousel.menu-logo.title",defaultMessage:"Menu logo"}),onChangeLogo:J("menu")})}),(0,s.jsx)(z.E,{col:6,s:12,children:(0,s.jsx)(ut,{canUpdate:T,customLogo:c.auth.custom,defaultLogo:c.auth.default,hint:a({id:"Settings.application.customization.auth-logo.carousel-hint",defaultMessage:"Replace the logo in the authentication pages"}),label:a({id:"Settings.application.customization.carousel.auth-logo.title",defaultMessage:"Auth logo"}),onChangeLogo:J("auth")})})]})]})]})})]})})]})},as=()=>{const{settingId:n}=(0,G.g)(),{settings:a}=(0,f.vD)(),{formatMessage:o}=(0,W.A)(),{isLoading:m,menu:c}=Gt(),E=(0,F.p)(Ht,async()=>(await t.e(4755).then(t.bind(t,64755))).ROUTES_EE,{combine(i,r){return[...i,...r]},defaultValue:[]}),u=e.useMemo(()=>Dt(E).map(({to:i,Component:r,exact:h})=>(0,F.d)(r,i,h)),[E]),O=Object.values(a).flatMap(i=>{const{links:r}=i;return r.map(h=>(0,F.d)(h.Component,h.to,h.exact||!1))});return m?(0,s.jsx)(f.Bl,{}):n?(0,s.jsxs)(lt.P,{sideNav:(0,s.jsx)(Xt,{menu:c}),children:[(0,s.jsx)(At.m,{title:o({id:"global.settings",defaultMessage:"Settings"})}),(0,s.jsxs)(G.dO,{children:[(0,s.jsx)(G.qh,{path:"/settings/application-infos",component:ns,exact:!0}),u,O]})]}):(0,s.jsx)(G.rd,{to:"/settings/application-infos"})},Dt=n=>n.filter((a,o,m)=>m.findIndex(c=>c.to===a.to)===o)},64958:(x,U,t)=>{var s=t(91662);function e(l,d){if(l!==d){var M=l!==void 0,P=l===null,g=l===l,D=s(l),A=d!==void 0,y=d===null,B=d===d,C=s(d);if(!y&&!C&&!D&&l>d||D&&A&&B&&!y&&!C||P&&A&&B||!M&&B||!g)return 1;if(!P&&!D&&!C&&l<d||C&&M&&g&&!P&&!D||y&&M&&g||!A&&g||!B)return-1}return 0}x.exports=e},74565:x=>{function U(t,s){var e=t.length;for(t.sort(s);e--;)t[e]=t[e].value;return t}x.exports=U},75821:(x,U,t)=>{var s=t(91522);function e(l,d){return function(M,P){if(M==null)return M;if(!s(M))return l(M,P);for(var g=M.length,D=d?g:-1,A=Object(M);(d?D--:++D<g)&&P(A[D],D,A)!==!1;);return M}}x.exports=e},97449:(x,U,t)=>{var s=t(85373),e=t(75821),l=e(s);x.exports=l}}]);
