"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4475],{14475:(i,u,E)=>{E.r(u),E.d(u,{default:()=>n});const n={"BoundRoute.title":"\u0110\u1EBFn t\u1EDBi","EditForm.inputSelect.description.role":"\u0110\xEDnh k\xE8m ng\u01B0\u1EDDi d\xF9ng m\u1EDBi \u0111\xE3 \u0111\u01B0\u1EE3c x\xE1c th\u1EF1c v\xE0o quy\u1EC1n \u0111\u01B0\u1EE3c ch\u1ECDn.","EditForm.inputSelect.label.role":"Quy\u1EC1n m\u1EB7c \u0111\u1ECBnh cho c\xE1c ng\u01B0\u1EDDi d\xF9ng \u0111\xE3 \u0111\u01B0\u1EE3c ch\u1EE9ng th\u1EF1c","EditForm.inputToggle.description.email":"Kh\xF4ng cho ph\xE9p ng\u01B0\u1EDDi d\xF9ng t\u1EA1o nhi\u1EC1u t\xE0i kho\u1EA3n c\xF3 c\xF9ng \u0111\u1ECBa ch\u1EC9 email v\u1EDBi nhi\u1EC1u nh\xE0 cung c\u1EA5p ch\u1EE9ng th\u1EF1c.","EditForm.inputToggle.description.email-confirmation":"Khi \u0111\u01B0\u1EE3c k\xEDch ho\u1EA1t (ON), ng\u01B0\u1EDDi d\xF9ng \u0111\u0103ng k\xFD m\u1EDBi nh\u1EADn \u0111\u01B0\u1EE3c m\u1ED9t email x\xE1c nh\u1EADn.","EditForm.inputToggle.description.email-confirmation-redirection":"Sau khi x\xE1c nh\u1EADn email c\u1EE7a b\u1EA1n, ch\u1ECDn n\u01A1i b\u1EA1n s\u1EBD \u0111\u01B0\u1EE3c \u0111\u01B0a v\u1EC1.","EditForm.inputToggle.description.email-reset-password":"URL c\u1EE7a trang l\u1EA5y l\u1EA1i m\u1EADt kh\u1EA9u c\u1EE7a \u1EE9ng d\u1EE5ng c\u1EE7a b\u1EA1n","EditForm.inputToggle.description.sign-up":"Khi kh\xF4ng k\xEDch ho\u1EA1t (OFF), qu\xE1 tr\xECnh \u0111\u0103ng k\xFD b\u1ECB c\u1EA5m. Kh\xF4ng ai c\xF3 th\u1EC3 \u0111\u0103ng k\xFD th\xEAm d\xF9 d\xF9ng b\u1EA5t k\u1EF3 nh\xE0 cung c\u1EA5p n\xE0o.","EditForm.inputToggle.label.email":"M\u1ED9t t\xE0i kho\u1EA3n tr\xEAn m\u1ED9t \u0111\u1ECBa ch\u1EC9 email","EditForm.inputToggle.label.email-confirmation":"K\xEDch ho\u1EA1t email x\xE1c nh\u1EADn","EditForm.inputToggle.label.email-confirmation-redirection":"URL \u0111\u01B0a v\u1EC1","EditForm.inputToggle.label.email-reset-password":"Trang l\u1EA5y l\u1EA1i m\u1EADt kh\u1EA9u","EditForm.inputToggle.label.sign-up":"K\xEDch ho\u1EA1t \u0111\u0103ng k\xFD","HeaderNav.link.advancedSettings":"C\xE0i \u0111\u1EB7t n\xE2ng cao","HeaderNav.link.emailTemplates":"M\u1EABu email","HeaderNav.link.providers":"C\xE1c nh\xE0 cung c\u1EA5p","Plugin.permissions.plugins.description":"\u0110\u1ECBnh ngh\u0129a t\u1EA5t c\u1EA3 h\xE0nh \u0111\u1ED9ng \u0111\u01B0\u1EE3c ph\xE9p cho {name} plugin.","Plugins.header.description":"Ch\u1EC9 c\xE1c h\xE0nh \u0111\u1ED9ng \u0111\u1EBFn b\u1EDFi m\u1ED9t \u0111\u01B0\u1EDDng d\u1EABn \u0111\u01B0\u1EE3c li\u1EC7t k\xEA b\xEAn d\u01B0\u1EDBi.","Plugins.header.title":"C\xE1c Quy\u1EC1n","Policies.header.hint":"Ch\u1ECDn c\xE1c h\xE0nh \u0111\u1ED9ng c\u1EE7a \u1EE9ng d\u1EF1ng ho\u1EB7c h\xE0nh \u0111\u1ED9ng c\u1EE7a plugin v\xE0 nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng b\xE1nh r\u0103ng \u0111\u1EC3 hi\u1EC3n th\u1ECB \u0111\u01B0\u1EDDng \u0111\u1EBFn","Policies.header.title":"C\xE1c c\xE0i \u0111\u1EB7t n\xE2ng cao","PopUpForm.Email.email_templates.inputDescription":"N\u1EBFu b\u1EA1n kh\xF4ng ch\u1EAFc s\u1EED d\u1EE5ng c\xE1c bi\u1EBFn nh\u01B0 th\u1EBF n\xE0o, {link}","PopUpForm.Email.options.from.email.label":"Email ng\u01B0\u1EDDi g\u1EEDi","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"T\xEAn ng\u01B0\u1EDDi g\u1EEDi","PopUpForm.Email.options.from.name.placeholder":"Kai Doe","PopUpForm.Email.options.message.label":"Th\xF4ng \u0111i\u1EC7p","PopUpForm.Email.options.object.label":"Ch\u1EE7 \u0111\u1EC1","PopUpForm.Email.options.response_email.label":"Email ph\u1EA3n h\u1ED3i","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"N\u1EBFu kh\xF4ng k\xEDch ho\u1EA1t, ng\u01B0\u1EDDi d\xF9ng s\u1EBD kh\xF4ng th\u1EC3 d\xF9ng nh\xE0 cung c\u1EA5p n\xE0y.","PopUpForm.Providers.enabled.label":"K\xEDch ho\u1EA1t","PopUpForm.Providers.key.label":"Client ID","PopUpForm.Providers.key.placeholder":"V\u0102N B\u1EA2N","PopUpForm.Providers.redirectURL.front-end.label":"URL chuy\u1EC3n ti\u1EBFp \u0111\u1EBFn \u1EE9ng d\u1EE5ng b\xEAn ngo\xE0i c\u1EE7a b\u1EA1n","PopUpForm.Providers.secret.label":"Client Secret","PopUpForm.Providers.secret.placeholder":"V\u0102N B\u1EA2N","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"S\u1EEDa C\xE1c M\u1EABu Email","notification.success.submit":"C\xE1c c\u1EA5u h\xECnh \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt","plugin.description.long":"B\u1EA3o v\u1EC7 API c\u1EE7a b\u1EA1n v\u1EDBi qu\xE1 tr\xECnh ch\u1EE9ng th\u1EF1c \u0111\u1EA7y \u0111\u1EE7 d\u1EF1a tr\xEAn JWT. Plugin n\xE0y c\u0169ng k\xE8m v\u1EDBi chi\u1EBFn l\u01B0\u1EE3c ACL cho ph\xE9p b\u1EA1n qu\u1EA3n l\xFD quy\u1EC1n gi\u1EEFa c\xE1c nh\xF3m ng\u01B0\u1EDDi d\xF9ng.","plugin.description.short":"B\u1EA3o v\u1EC7 API c\u1EE7a b\u1EA1n v\u1EDBi qu\xE1 tr\xECnh ch\u1EE9ng th\u1EF1c \u0111\u1EA7y \u0111\u1EE7 d\u1EF1a tr\xEAn JWT","plugin.name":"Vai tr\xF2 v\xE0 Quy\u1EC1n"}}}]);
