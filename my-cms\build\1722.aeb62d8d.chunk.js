(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[1722],{4191:(f,T,s)=>{var e=s(87864),d=s(86386),n=s(45353),c=s(29884),l=s(74565),E=s(52689),p=s(48126),M=s(82388),j=s(82261);function y(A,P,I){P.length?P=e(P,function(B){return j(B)?function(U){return d(U,B.length===1?B[0]:B)}:B}):P=[M];var G=-1;P=e(P,E(n));var V=c(A,function(B,U,k){var S=e(P,function($){return $(B)});return{criteria:S,index:++G,value:B}});return l(V,function(B,U){return p(B,U,I)})}f.exports=y},6223:(f,T,s)=>{var e=s(60759),d=s(39226),n=s(56793),c=d(function(l,E){return n(l)?e(l,E):[]});f.exports=c},29884:(f,T,s)=>{var e=s(97449),d=s(91522);function n(c,l){var E=-1,p=d(c)?Array(c.length):[];return e(c,function(M,j,y){p[++E]=l(M,j,y)}),p}f.exports=n},34542:(f,T,s)=>{var e=s(87864),d=s(45353),n=s(29884),c=s(82261);function l(E,p){var M=c(E)?e:n;return M(E,d(p,3))}f.exports=l},45635:(f,T,s)=>{var e=s(87212),d=s(4191),n=s(39226),c=s(3956),l=n(function(E,p){if(E==null)return[];var M=p.length;return M>1&&c(E,p[0],p[1])?p=[]:M>2&&c(p[0],p[1],p[2])&&(p=[p[0]]),d(E,e(p,1),[])});f.exports=l},48126:(f,T,s)=>{var e=s(64958);function d(n,c,l){for(var E=-1,p=n.criteria,M=c.criteria,j=p.length,y=l.length;++E<j;){var A=e(p[E],M[E]);if(A){if(E>=y)return A;var P=l[E];return A*(P=="desc"?-1:1)}}return n.index-c.index}f.exports=d},51722:(f,T,s)=>{"use strict";s.r(T),s.d(T,{default:()=>ds});var e=s(92132),d=s(21272),n=s(55506),c=s(17703),l=s(99105),E=s(57237),p=s(44604),M=s(60888),j=s(94061),y=s(85963),A=s(4181),P=s(90151),I=s(68074),G=s(88353),V=s(42455),B=s(74773),U=s(4198),k=s(55356),S=s(64871),$=s(11273),H=s(38413),b=s(83997),Ce=s(35513),Re=s(25641),ve=s(26127),re=s(90361),N=s(33363),ae=s(7537),oe=s(5287),C=s(30893),le=s(98765),Le=s(46270),de=s(54514),Te=s(20415),Be=s(41909),be=s(5194),Ue=s(50612),ce=s(61535),K=s(54894),Q=s(74930),v=s(33544),We=s(39404),ue=s(45635),Z=s(2600),X=s(63891),ge=s(88761),ee=s(77965),Ie=s(6223),Se=s(34542),Ke=s(61468),he=s(48940),$e=s(501),se=s(12083);const pe=(0,d.createContext)({}),me=({children:t,value:i})=>(0,e.jsx)(pe.Provider,{value:i,children:t}),te=()=>(0,d.useContext)(pe);me.propTypes={children:v.node.isRequired,value:v.object.isRequired};function Ne(t){switch(t){case"application":return"Application";case"plugin::content-manager":return"Content manager";case"plugin::content-type-builder":return"Content types builder";case"plugin::documentation":return"Documentation";case"plugin::email":return"Email";case"plugin::i18n":return"i18n";case"plugin::upload":return"Upload";case"plugin::users-permissions":return"Users-permissions";default:return We(t.replace("api::","").replace("plugin::",""))}}const Ge=(t,i)=>{const r=Object.keys(i).sort().map(a=>({name:a,isOpen:!1}));return{...t,collapses:r}},Ee=(0,X.AH)`
  background: ${t=>t.theme.colors.primary100};
  svg {
    opacity: 1;
  }
`,Ve=(0,X.Ay)(j.a)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  svg {
    opacity: 0;
    path {
      fill: ${t=>t.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${t=>t.isActive&&Ee}
  &:hover {
    ${Ee}
  }
`,ze=X.Ay.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({theme:t})=>t.colors.neutral150};
`,xe=({subCategory:t})=>{const{formatMessage:i}=(0,K.A)(),{onChange:r,onChangeSelectAll:a,onSelectedAction:g,selectedAction:o,modifiedData:h}=te(),x=(0,d.useMemo)(()=>Z(h,t.name,{}),[h,t]),m=(0,d.useMemo)(()=>Object.values(x).every(u=>u.enabled===!0),[x]),_=(0,d.useMemo)(()=>Object.values(x).some(u=>u.enabled===!0)&&!m,[x,m]),D=(0,d.useCallback)(({target:{name:u}})=>{a({target:{name:u,value:!m}})},[m,a]),O=(0,d.useCallback)(u=>o===u,[o]);return(0,e.jsxs)(j.a,{children:[(0,e.jsxs)(b.s,{justifyContent:"space-between",alignItems:"center",children:[(0,e.jsx)(j.a,{paddingRight:4,children:(0,e.jsx)(C.o,{variant:"sigma",textColor:"neutral600",children:t.label})}),(0,e.jsx)(ze,{}),(0,e.jsx)(j.a,{paddingLeft:4,children:(0,e.jsx)(A.S,{name:t.name,value:m,onValueChange:u=>D({target:{name:t.name,value:u}}),indeterminate:_,children:i({id:"app.utils.select-all",defaultMessage:"Select all"})})})]}),(0,e.jsx)(b.s,{paddingTop:6,paddingBottom:6,children:(0,e.jsx)(P.x,{gap:2,style:{flex:1},children:t.actions.map(u=>{const R=`${u.name}.enabled`;return(0,e.jsx)(I.E,{col:6,children:(0,e.jsxs)(Ve,{isActive:O(u.name),padding:2,hasRadius:!0,children:[(0,e.jsx)(A.S,{value:Z(h,R,!1),name:R,onValueChange:W=>r({target:{name:R,value:W}}),children:u.label}),(0,e.jsxs)("button",{type:"button",onClick:()=>g(u.name),style:{display:"inline-flex",alignItems:"center"},children:[(0,e.jsx)(le.s,{as:"span",children:i({id:"app.utils.show-bound-route",defaultMessage:"Show bound route for {route}"},{route:u.name})}),(0,e.jsx)(Te.A,{})]})]})},u.name)})})})]})};xe.propTypes={subCategory:v.object.isRequired};const fe=({name:t,permissions:i})=>{const r=(0,d.useMemo)(()=>ue(Object.values(i.controllers).reduce((a,g,o)=>{const h=`${t}.controllers.${Object.keys(i.controllers)[o]}`,x=ue(Object.keys(g).reduce((m,_)=>[...m,{...g[_],label:_,name:`${h}.${_}`}],[]),"label");return[...a,{actions:x,label:Object.keys(i.controllers)[o],name:h}]},[]),"label"),[t,i]);return(0,e.jsx)(j.a,{padding:6,children:r.map(a=>(0,e.jsx)(xe,{subCategory:a},a.name))})};fe.propTypes={name:v.string.isRequired,permissions:v.object.isRequired};const Fe={collapses:[]},ke=(t,i)=>(0,ge.Ay)(t,r=>{switch(i.type){case"TOGGLE_COLLAPSE":{r.collapses=t.collapses.map((a,g)=>g===i.index?{...a,isOpen:!a.isOpen}:{...a,isOpen:!1});break}default:return r}}),He=()=>{const{modifiedData:t}=te(),{formatMessage:i}=(0,K.A)(),[{collapses:r},a]=(0,d.useReducer)(ke,Fe,o=>Ge(o,t)),g=o=>a({type:"TOGGLE_COLLAPSE",index:o});return(0,e.jsx)(b.s,{direction:"column",alignItems:"stretch",gap:1,children:r.map((o,h)=>(0,e.jsxs)(E.n,{expanded:o.isOpen,onToggle:()=>g(h),variant:h%2===0?"secondary":void 0,children:[(0,e.jsx)(M.P,{title:Ne(o.name),description:i({id:"users-permissions.Plugin.permissions.plugins.description",defaultMessage:"Define all allowed actions for the {name} plugin."},{name:o.name}),variant:h%2?"primary":"secondary"}),(0,e.jsx)(p.u,{children:(0,e.jsx)(fe,{permissions:t[o.name],name:o.name})})]},o.name))})},Qe=t=>{switch(t){case"POST":return{text:"success600",border:"success200",background:"success100"};case"GET":return{text:"secondary600",border:"secondary200",background:"secondary100"};case"PUT":return{text:"warning600",border:"warning200",background:"warning100"};case"DELETE":return{text:"danger600",border:"danger200",background:"danger100"};default:return{text:"neutral600",border:"neutral200",background:"neutral100"}}},we=(0,X.Ay)(j.a)`
  margin: -1px;
  border-radius: ${({theme:t})=>t.spaces[1]} 0 0 ${({theme:t})=>t.spaces[1]};
`;function ne({route:t}){const{formatMessage:i}=(0,K.A)(),{method:r,handler:a,path:g}=t,o=g?Ke(g.split("/")):[],[h="",x=""]=a?a.split("."):[],m=Qe(t.method);return(0,e.jsxs)(b.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,e.jsxs)(C.o,{variant:"delta",as:"h3",children:[i({id:"users-permissions.BoundRoute.title",defaultMessage:"Bound route to"}),"\xA0",(0,e.jsx)("span",{children:h}),(0,e.jsxs)(C.o,{variant:"delta",textColor:"primary600",children:[".",x]})]}),(0,e.jsxs)(b.s,{hasRadius:!0,background:"neutral0",borderColor:"neutral200",gap:0,children:[(0,e.jsx)(we,{background:m.background,borderColor:m.border,padding:2,children:(0,e.jsx)(C.o,{fontWeight:"bold",textColor:m.text,children:r})}),(0,e.jsx)(j.a,{paddingLeft:2,paddingRight:2,children:Se(o,_=>(0,e.jsxs)(C.o,{textColor:_.includes(":")?"neutral600":"neutral900",children:["/",_]},_))})]})]})}ne.defaultProps={route:{handler:"Nocontroller.error",method:"GET",path:"/there-is-no-path"}},ne.propTypes={route:v.shape({handler:v.string,method:v.string,path:v.string})};const Ye=()=>{const{formatMessage:t}=(0,K.A)(),{selectedAction:i,routes:r}=te(),a=Ie(i.split("."),"controllers"),g=Z(r,a[0]),o=a.slice(1).join("."),h=ee(g)?[]:g.filter(x=>x.handler.endsWith(o));return(0,e.jsx)(I.E,{col:5,background:"neutral150",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,style:{minHeight:"100%"},children:i?(0,e.jsx)(b.s,{direction:"column",alignItems:"stretch",gap:2,children:h.map((x,m)=>(0,e.jsx)(ne,{route:x},m))}):(0,e.jsxs)(b.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,e.jsx)(C.o,{variant:"delta",as:"h3",children:t({id:"users-permissions.Policies.header.title",defaultMessage:"Advanced settings"})}),(0,e.jsx)(C.o,{as:"p",textColor:"neutral600",children:t({id:"users-permissions.Policies.header.hint",defaultMessage:"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"})})]})})},Xe=(t,i,r)=>({...t,initialData:i,modifiedData:i,routes:r}),Ze={initialData:{},modifiedData:{},routes:{},selectedAction:"",policies:[]},Je=(t,i)=>(0,ge.Ay)(t,r=>{switch(i.type){case"ON_CHANGE":{const a=i.keys.length,g=i.keys[a-1]==="enabled";if(i.value&&g){const o=$e(i.keys,a-1).join(".");r.selectedAction=o}he(r,["modifiedData",...i.keys],i.value);break}case"ON_CHANGE_SELECT_ALL":{const a=["modifiedData",...i.keys],g=Z(t,a,{}),o=Object.keys(g).reduce((h,x)=>(h[x]={...g[x],enabled:i.value},h),{});he(r,a,o);break}case"ON_RESET":{r.modifiedData=t.initialData;break}case"ON_SUBMIT_SUCCEEDED":{r.initialData=t.modifiedData;break}case"SELECT_ACTION":{const{actionToSelect:a}=i;r.selectedAction=a===t.selectedAction?"":a;break}default:return r}}),Me=(0,d.forwardRef)(({permissions:t,routes:i},r)=>{const{formatMessage:a}=(0,K.A)(),[g,o]=(0,d.useReducer)(Je,Ze,D=>Xe(D,t,i));(0,d.useImperativeHandle)(r,()=>({getPermissions(){return{permissions:g.modifiedData}},resetForm(){o({type:"ON_RESET"})},setFormAfterSubmit(){o({type:"ON_SUBMIT_SUCCEEDED"})}}));const _={...g,onChange:({target:{name:D,value:O}})=>o({type:"ON_CHANGE",keys:D.split("."),value:O==="empty__string_value"?"":O}),onChangeSelectAll:({target:{name:D,value:O}})=>o({type:"ON_CHANGE_SELECT_ALL",keys:D.split("."),value:O}),onSelectedAction:D=>o({type:"SELECT_ACTION",actionToSelect:D})};return(0,e.jsx)(me,{value:_,children:(0,e.jsxs)(P.x,{gap:0,shadow:"filterShadow",hasRadius:!0,background:"neutral0",children:[(0,e.jsx)(I.E,{col:7,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,e.jsxs)(b.s,{direction:"column",alignItems:"stretch",gap:6,children:[(0,e.jsxs)(b.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,e.jsx)(C.o,{variant:"delta",as:"h2",children:a({id:(0,l.g)("Plugins.header.title"),defaultMessage:"Permissions"})}),(0,e.jsx)(C.o,{as:"p",textColor:"neutral600",children:a({id:(0,l.g)("Plugins.header.description"),defaultMessage:"Only actions bound by a route are listed below."})})]}),(0,e.jsx)(He,{})]})}),(0,e.jsx)(Ye,{})]})})});Me.propTypes={permissions:v.object.isRequired,routes:v.object.isRequired};const _e=(0,d.memo)(Me),je=se.Ik().shape({name:se.Yj().required(n.iW.required),description:se.Yj().required(n.iW.required)}),qe=t=>Object.keys(t).reduce((i,r)=>{const a=t[r].controllers,g=Object.keys(a).reduce((o,h)=>(ee(a[h])||(o[h]=a[h]),o),{});return ee(g)||(i[r]={controllers:g}),i},{}),Pe=()=>{const t=(0,n.hN)(),{get:i}=(0,n.ry)(),{formatAPIError:r}=(0,n.wq)(l.g),[{data:a,isLoading:g,error:o,refetch:h},{data:x,isLoading:m,error:_,refetch:D}]=(0,Q.useQueries)([{queryKey:["users-permissions","permissions"],async queryFn(){const{data:{permissions:R}}=await i("/users-permissions/permissions");return R}},{queryKey:["users-permissions","routes"],async queryFn(){const{data:{routes:R}}=await i("/users-permissions/routes");return R}}]),O=async()=>{await Promise.all([h(),D()])};(0,d.useEffect)(()=>{o&&t({type:"warning",message:r(o)})},[t,o,r]),(0,d.useEffect)(()=>{_&&t({type:"warning",message:r(_)})},[t,_,r]);const u=g||m;return{permissions:a?qe(a):{},routes:x??{},getData:O,isLoading:u}},es=()=>{const{formatMessage:t}=(0,K.A)(),i=(0,n.hN)(),{goBack:r}=(0,c.W6)(),{lockApp:a,unlockApp:g}=(0,n.MA)(),{isLoading:o,permissions:h,routes:x}=Pe(),{trackUsage:m}=(0,n.z1)(),_=d.useRef(),{post:D}=(0,n.ry)(),O=(0,Q.useMutation)(R=>D("/users-permissions/roles",R),{onError(){i({type:"warning",message:{id:"notification.error",defaultMessage:"An error occurred"}})},onSuccess(){m("didCreateRole"),i({type:"success",message:{id:(0,l.g)("Settings.roles.created"),defaultMessage:"Role created"}}),r()}}),u=async R=>{a();const W=_.current.getPermissions();await O.mutate({...R,...W,users:[]}),g()};return(0,e.jsxs)(H.g,{children:[(0,e.jsx)(n.x7,{name:"Roles"}),(0,e.jsx)(ce.l1,{enableReinitialize:!0,initialValues:{name:"",description:""},onSubmit:u,validationSchema:je,children:({handleSubmit:R,values:W,handleChange:w,errors:L})=>(0,e.jsxs)(n.lV,{noValidate:!0,onSubmit:R,children:[(0,e.jsx)(k.Q,{primaryAction:!o&&(0,e.jsx)(y.$,{type:"submit",loading:O.isLoading,startIcon:(0,e.jsx)(de.A,{}),children:t({id:"global.save",defaultMessage:"Save"})}),title:t({id:"Settings.roles.create.title",defaultMessage:"Create a role"}),subtitle:t({id:"Settings.roles.create.description",defaultMessage:"Define the rights given to the role"})}),(0,e.jsx)(U.s,{children:(0,e.jsxs)(b.s,{background:"neutral0",direction:"column",alignItems:"stretch",gap:7,hasRadius:!0,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,shadow:"filterShadow",children:[(0,e.jsxs)(b.s,{direction:"column",alignItems:"stretch",children:[(0,e.jsx)(C.o,{variant:"delta",as:"h2",children:t({id:(0,l.g)("EditPage.form.roles"),defaultMessage:"Role details"})}),(0,e.jsxs)(P.x,{gap:4,children:[(0,e.jsx)(I.E,{col:6,children:(0,e.jsx)(ae.k,{name:"name",value:W.name||"",onChange:w,label:t({id:"global.name",defaultMessage:"Name"}),error:L?.name?t({id:L.name,defaultMessage:"Name is required"}):!1,required:!0})}),(0,e.jsx)(I.E,{col:6,children:(0,e.jsx)(oe.T,{id:"description",value:W.description||"",onChange:w,label:t({id:"global.description",defaultMessage:"Description"}),error:L?.description?t({id:L.description,defaultMessage:"Description is required"}):!1,required:!0})})]})]}),!o&&(0,e.jsx)(_e,{ref:_,permissions:h,routes:x})]})})]})})]})},ss=()=>(0,e.jsx)(n.kz,{permissions:l.P.createRole,children:(0,e.jsx)(es,{})}),ts=()=>{const{formatMessage:t}=(0,K.A)(),i=(0,n.hN)(),{lockApp:r,unlockApp:a}=(0,n.MA)(),{params:{id:g}}=(0,c.W5)("/settings/users-permissions/roles/:id"),{get:o}=(0,n.ry)(),{isLoading:h,routes:x}=Pe(),{data:m,isLoading:_,refetch:D}=(0,Q.useQuery)(["users-permissions","role",g],async()=>{const{data:{role:L}}=await o(`/users-permissions/roles/${g}`);return L}),O=d.useRef(),{put:u}=(0,n.ry)(),{formatAPIError:R}=(0,n.wq)(),W=(0,Q.useMutation)(L=>u(`/users-permissions/roles/${g}`,L),{onError(L){i({type:"warning",message:R(L)})},async onSuccess(){i({type:"success",message:{id:(0,l.g)("Settings.roles.created"),defaultMessage:"Role edited"}}),await D()}}),w=async L=>{r();const z=O.current.getPermissions();await W.mutate({...L,...z,users:[]}),a()};return _?(0,e.jsx)(n.Bl,{}):(0,e.jsxs)(H.g,{children:[(0,e.jsx)(n.x7,{name:"Roles"}),(0,e.jsx)(ce.l1,{enableReinitialize:!0,initialValues:{name:m.name,description:m.description},onSubmit:w,validationSchema:je,children:({handleSubmit:L,values:z,handleChange:J,errors:Y})=>(0,e.jsxs)(n.lV,{noValidate:!0,onSubmit:L,children:[(0,e.jsx)(k.Q,{primaryAction:!h&&(0,e.jsx)(y.$,{disabled:m.code==="strapi-super-admin",type:"submit",loading:W.isLoading,startIcon:(0,e.jsx)(de.A,{}),children:t({id:"global.save",defaultMessage:"Save"})}),title:m.name,subtitle:m.description,navigationAction:(0,e.jsx)(n.N_,{startIcon:(0,e.jsx)(Le.A,{}),to:"/settings/users-permissions/roles",children:t({id:"global.back",defaultMessage:"Back"})})}),(0,e.jsx)(U.s,{children:(0,e.jsxs)(b.s,{background:"neutral0",direction:"column",alignItems:"stretch",gap:7,hasRadius:!0,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,shadow:"filterShadow",children:[(0,e.jsxs)(b.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,e.jsx)(C.o,{variant:"delta",as:"h2",children:t({id:(0,l.g)("EditPage.form.roles"),defaultMessage:"Role details"})}),(0,e.jsxs)(P.x,{gap:4,children:[(0,e.jsx)(I.E,{col:6,children:(0,e.jsx)(ae.k,{name:"name",value:z.name||"",onChange:J,label:t({id:"global.name",defaultMessage:"Name"}),error:Y?.name?t({id:Y.name,defaultMessage:"Name is required"}):!1,required:!0})}),(0,e.jsx)(I.E,{col:6,children:(0,e.jsx)(oe.T,{id:"description",value:z.description||"",onChange:J,label:t({id:"global.description",defaultMessage:"Description"}),error:Y?.description?t({id:Y.description,defaultMessage:"Description is required"}):!1,required:!0})})]})]}),!h&&(0,e.jsx)(_e,{ref:O,permissions:m.permissions,routes:x})]})})]})})]})},ns=()=>(0,e.jsx)(n.kz,{permissions:l.P.updateRole,children:(0,e.jsx)(ts,{})}),is=(0,X.Ay)(S.N)`
  align-items: center;
  height: ${(0,n.a8)(32)};
  display: flex;
  justify-content: center;
  padding: ${({theme:t})=>`${t.spaces[2]}}`};
  width: ${(0,n.a8)(32)};

  svg {
    height: ${(0,n.a8)(12)};
    width: ${(0,n.a8)(12)};

    path {
      fill: ${({theme:t})=>t.colors.neutral500};
    }
  }

  &:hover,
  &:focus {
    svg {
      path {
        fill: ${({theme:t})=>t.colors.neutral800};
      }
    }
  }
`,ie=({sortedRoles:t,canDelete:i,permissions:r,setRoleToDelete:a,onDelete:g})=>{const{formatMessage:o}=(0,K.A)(),{push:h}=(0,c.W6)(),[x,m]=g,_=u=>i&&!["public","authenticated"].includes(u.type),D=u=>{a(u),m(!x)},O=u=>{h(`/settings/users-permissions/roles/${u}`)};return(0,e.jsx)(Re.N,{children:t?.map(u=>(0,e.jsxs)(re.Tr,{...(0,n.qM)({fn:()=>O(u.id)}),children:[(0,e.jsx)(N.Td,{width:"20%",children:(0,e.jsx)(C.o,{children:u.name})}),(0,e.jsx)(N.Td,{width:"50%",children:(0,e.jsx)(C.o,{children:u.description})}),(0,e.jsx)(N.Td,{width:"30%",children:(0,e.jsx)(C.o,{children:o({id:"Roles.RoleRow.user-count",defaultMessage:"{number, plural, =0 {# user} one {# user} other {# users}}"},{number:u.nb_users})})}),(0,e.jsx)(N.Td,{children:(0,e.jsxs)(b.s,{justifyContent:"end",...n.dG,children:[(0,e.jsx)(n.d4,{permissions:r.updateRole,children:(0,e.jsx)(is,{to:`/settings/users-permissions/roles/${u.id}`,"aria-label":o({id:"app.component.table.edit",defaultMessage:"Edit {target}"},{target:`${u.name}`}),children:(0,e.jsx)(Be.A,{})})}),_(u)&&(0,e.jsx)(n.d4,{permissions:r.deleteRole,children:(0,e.jsx)(G.K,{onClick:()=>D(u.id),noBorder:!0,icon:(0,e.jsx)(Ue.A,{}),label:o({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:`${u.name}`})})})]})})]},u.name))})};ie.defaultProps={canDelete:!1},ie.propTypes={onDelete:v.array.isRequired,permissions:v.object.isRequired,setRoleToDelete:v.func.isRequired,sortedRoles:v.array.isRequired,canDelete:v.bool};const rs=async(t,i)=>{try{const{get:r}=(0,n.GD)(),{data:a}=await r("/users-permissions/roles");return i("The roles have loaded successfully"),a}catch(r){throw t({type:"warning",message:{id:"notification.error"}}),new Error(r)}},as=async(t,i)=>{try{const{del:r}=(0,n.GD)();await r(`/users-permissions/roles/${t}`)}catch{i({type:"warning",message:{id:"notification.error",defaultMessage:"An error occured"}})}},os=()=>{const{trackUsage:t}=(0,n.z1)(),{formatMessage:i,locale:r}=(0,K.A)(),a=(0,n.hN)(),{notifyStatus:g}=(0,$.W)(),[{query:o}]=(0,n.sq)(),h=o?._q||"",[x,m]=(0,d.useState)(!1),[_,D]=(0,d.useState)(!1),[O,u]=(0,d.useState)();(0,n.L4)();const{isLoading:R,allowedActions:{canRead:W,canDelete:w}}=(0,n.ec)({create:l.P.createRole,read:l.P.readRoles,update:l.P.updateRole,delete:l.P.deleteRole}),{isLoading:L,data:{roles:z},isFetching:J,refetch:Y}=(0,Q.useQuery)("get-roles",()=>rs(a,g),{initialData:{},enabled:W}),{includes:ye}=(0,n.U2)(r,{sensitivity:"base"}),De=(0,n.QM)(r,{sensitivity:"base"}),Ae=L||J,cs=()=>{m(!x)},us={roles:{id:(0,l.g)("Roles.empty"),defaultMessage:"You don't have any roles yet."},search:{id:(0,l.g)("Roles.empty.search"),defaultMessage:"No roles match the search."}},gs=i({id:"global.roles",defaultMessage:"Roles"}),hs=(0,Q.useMutation)(F=>as(F,a),{async onSuccess(){await Y()}}),ps=async()=>{D(!0),await hs.mutateAsync(O),m(!x),D(!1)},q=(z||[]).filter(F=>ye(F.name,h)||ye(F.description,h)).sort((F,Oe)=>De.compare(F.name,Oe.name)||De.compare(F.description,Oe.description)),ms=h&&!q.length?"search":"roles",Es=4,xs=(z?.length||0)+1;return(0,e.jsxs)(V.P,{children:[(0,e.jsx)(n.x7,{name:gs}),(0,e.jsxs)(H.g,{"aria-busy":Ae,children:[(0,e.jsx)(k.Q,{title:i({id:"global.roles",defaultMessage:"Roles"}),subtitle:i({id:"Settings.roles.list.description",defaultMessage:"List of roles"}),primaryAction:(0,e.jsx)(n.d4,{permissions:l.P.createRole,children:(0,e.jsx)(n.z9,{to:"/settings/users-permissions/roles/new",onClick:()=>t("willCreateRole"),startIcon:(0,e.jsx)(be.A,{}),size:"S",children:i({id:(0,l.g)("List.button.roles"),defaultMessage:"Add new role"})})})}),(0,e.jsx)(B.B,{startActions:(0,e.jsx)(n.q7,{label:i({id:"app.component.search.label",defaultMessage:"Search"})})}),(0,e.jsxs)(U.s,{children:[!W&&(0,e.jsx)(n.UW,{}),(Ae||R)&&(0,e.jsx)(n.Bl,{}),W&&q&&q?.length?(0,e.jsxs)(Ce.X,{colCount:Es,rowCount:xs,children:[(0,e.jsx)(ve.d,{children:(0,e.jsxs)(re.Tr,{children:[(0,e.jsx)(N.Th,{children:(0,e.jsx)(C.o,{variant:"sigma",textColor:"neutral600",children:i({id:"global.name",defaultMessage:"Name"})})}),(0,e.jsx)(N.Th,{children:(0,e.jsx)(C.o,{variant:"sigma",textColor:"neutral600",children:i({id:"global.description",defaultMessage:"Description"})})}),(0,e.jsx)(N.Th,{children:(0,e.jsx)(C.o,{variant:"sigma",textColor:"neutral600",children:i({id:"global.users",defaultMessage:"Users"})})}),(0,e.jsx)(N.Th,{children:(0,e.jsx)(le.s,{children:i({id:"global.actions",defaultMessage:"Actions"})})})]})}),(0,e.jsx)(ie,{sortedRoles:q,canDelete:w,permissions:l.P,setRoleToDelete:u,onDelete:[x,m]})]}):(0,e.jsx)(n.pA,{content:us[ms]})]}),(0,e.jsx)(n.TM,{isConfirmButtonLoading:_,onConfirm:ps,onToggleDialog:cs,isOpen:x})]})]})},ls=()=>(0,e.jsx)(n.kz,{permissions:l.P.accessRoles,children:(0,e.jsx)(os,{})}),ds=()=>(0,e.jsx)(n.kz,{permissions:l.P.accessRoles,children:(0,e.jsxs)(c.dO,{children:[(0,e.jsx)(c.qh,{path:"/settings/users-permissions/roles/new",component:ss,exact:!0}),(0,e.jsx)(c.qh,{path:"/settings/users-permissions/roles/:id",component:ns,exact:!0}),(0,e.jsx)(c.qh,{path:"/settings/users-permissions/roles",component:ls,exact:!0}),(0,e.jsx)(c.qh,{path:"",component:n.hH})]})})},60759:(f,T,s)=>{var e=s(48143),d=s(83289),n=s(96685),c=s(87864),l=s(52689),E=s(37879),p=200;function M(j,y,A,P){var I=-1,G=d,V=!0,B=j.length,U=[],k=y.length;if(!B)return U;A&&(y=c(y,l(A))),P?(G=n,V=!1):y.length>=p&&(G=E,V=!1,y=new e(y));e:for(;++I<B;){var S=j[I],$=A==null?S:A(S);if(S=P||S!==0?S:0,V&&$===$){for(var H=k;H--;)if(y[H]===$)continue e;U.push(S)}else G(y,$,P)||U.push(S)}return U}f.exports=M},61468:(f,T,s)=>{var e=s(52196);function d(n){var c=n==null?0:n.length;return c?e(n,1,c):[]}f.exports=d},64958:(f,T,s)=>{var e=s(91662);function d(n,c){if(n!==c){var l=n!==void 0,E=n===null,p=n===n,M=e(n),j=c!==void 0,y=c===null,A=c===c,P=e(c);if(!y&&!P&&!M&&n>c||M&&j&&A&&!y&&!P||E&&j&&A||!l&&A||!p)return 1;if(!E&&!M&&!P&&n<c||P&&l&&p&&!E&&!M||y&&l&&p||!j&&p||!A)return-1}return 0}f.exports=d},74565:f=>{function T(s,e){var d=s.length;for(s.sort(e);d--;)s[d]=s[d].value;return s}f.exports=T},75821:(f,T,s)=>{var e=s(91522);function d(n,c){return function(l,E){if(l==null)return l;if(!e(l))return n(l,E);for(var p=l.length,M=c?p:-1,j=Object(l);(c?M--:++M<p)&&E(j[M],M,j)!==!1;);return l}}f.exports=d},97449:(f,T,s)=>{var e=s(85373),d=s(75821),n=d(e);f.exports=n}}]);
