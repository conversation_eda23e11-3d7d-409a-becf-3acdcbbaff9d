"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[159],{159:(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i={"BoundRoute.title":"Bound route to","EditForm.inputSelect.description.role":"It will attach the new authenticated user to the selected role.","EditForm.inputSelect.label.role":"Default role for authenticated users","EditForm.inputToggle.description.email":"Disallow the user to create multiple accounts using the same email address with different authentication providers.","EditForm.inputToggle.description.email-confirmation":"When enabled (ON), new registered users receive a confirmation email.","EditForm.inputToggle.description.email-confirmation-redirection":"After you confirmed your email, choose where you will be redirected.","EditForm.inputToggle.description.email-reset-password":"URL of your application's reset password page","EditForm.inputToggle.description.sign-up":"When disabled (OFF), the registration process is forbidden. No one can subscribe anymore no matter the used provider.","EditForm.inputToggle.label.email":"One account per email address","EditForm.inputToggle.label.email-confirmation":"Enable email confirmation","EditForm.inputToggle.label.email-confirmation-redirection":"Redirection url","EditForm.inputToggle.label.email-reset-password":"Reset password page","EditForm.inputToggle.label.sign-up":"Enable sign-ups","EditForm.inputToggle.placeholder.email-confirmation-redirection":"ex: https://yourfrontend.com/email-confirmation-redirection","EditForm.inputToggle.placeholder.email-reset-password":"ex: https://yourfrontend.com/reset-password","EditPage.form.roles":"Role details","Email.template.data.loaded":"Email templates has been loaded","Email.template.email_confirmation":"Email address confirmation","Email.template.form.edit.label":"Edit a template","Email.template.table.action.label":"action","Email.template.table.icon.label":"icon","Email.template.table.name.label":"name","Form.advancedSettings.data.loaded":"Advanced settings data has been loaded","HeaderNav.link.advancedSettings":"Advanced settings","HeaderNav.link.emailTemplates":"Email templates","HeaderNav.link.providers":"Providers","Plugin.permissions.plugins.description":"Define all allowed actions for the {name} plugin.","Plugins.header.description":"Only actions bound by a route are listed below.","Plugins.header.title":"Permissions","Policies.header.hint":"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route","Policies.header.title":"Advanced settings","PopUpForm.Email.email_templates.inputDescription":"If you're unsure how to use variables, {link}","PopUpForm.Email.link.documentation":"check out our documentation.","PopUpForm.Email.options.from.email.label":"Shipper email","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Shipper name","PopUpForm.Email.options.from.name.placeholder":"Kai Doe","PopUpForm.Email.options.message.label":"Message","PopUpForm.Email.options.object.label":"Subject","PopUpForm.Email.options.object.placeholder":"Please confirm your email address for %APP_NAME%","PopUpForm.Email.options.response_email.label":"Response email","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"If disabled, users won't be able to use this provider.","PopUpForm.Providers.enabled.label":"Enable","PopUpForm.Providers.key.label":"Client ID","PopUpForm.Providers.key.placeholder":"TEXT","PopUpForm.Providers.redirectURL.front-end.label":"The redirect URL to your front-end app","PopUpForm.Providers.redirectURL.label":"The redirect URL to add in your {provider} application configurations","PopUpForm.Providers.secret.label":"Client Secret","PopUpForm.Providers.secret.placeholder":"TEXT","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Edit email template","PopUpForm.header.edit.providers":"Edit Provider","Providers.data.loaded":"Providers have been loaded","Providers.status":"Status","Roles.empty":"You don't have any roles yet.","Roles.empty.search":"No roles match the search.","Settings.roles.deleted":"Role deleted","Settings.roles.edited":"Role edited","Settings.section-label":"Users & Permissions plugin","components.Input.error.validation.email":"This is an invalid email","components.Input.error.validation.json":"This doesn't match the JSON format","components.Input.error.validation.max":"The value is too high.","components.Input.error.validation.maxLength":"The value is too long.","components.Input.error.validation.min":"The value is too low.","components.Input.error.validation.minLength":"The value is too short.","components.Input.error.validation.minSupMax":"Can't be superior","components.Input.error.validation.regex":"The value does not match the regex.","components.Input.error.validation.required":"This value is required.","components.Input.error.validation.unique":"This value is already used.","notification.success.submit":"Settings have been updated","page.title":"Settings - Roles","plugin.description.long":"Protect your API with a full authentication process based on JWT. This plugin comes also with an ACL strategy that allows you to manage the permissions between the groups of users.","plugin.description.short":"Protect your API with a full authentication process based on JWT.","plugin.name":"Users & Permissions Plugin","popUpWarning.button.cancel":"Cancel","popUpWarning.button.confirm":"Confirm","popUpWarning.title":"Please confirm","popUpWarning.warning.cancel":"Are you sure you want to cancel your modifications?"}}}]);
