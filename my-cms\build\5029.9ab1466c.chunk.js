"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[5029],{45029:(g,a,n)=>{n.r(a),n.d(a,{Analytics:()=>e,Documentation:()=>t,Email:()=>i,Password:()=>o,Provider:()=>r,ResetPasswordToken:()=>s,Role:()=>m,Username:()=>l,Users:()=>p,default:()=>u});const e="Analisis",t="Dokumen",i="Email",o="Kata Laluan",r="Penyedia",s="Token penetapan semula kata laluan",m="Peranan",l="Nama Penggguna",p="Para Pengguna",u={Analytics:e,"Auth.form.button.forgot-password":"Hantar e-mel","Auth.form.button.login":"Log masuk","Auth.form.button.register":"Sedia untuk mulakan","Auth.form.error.blocked":"<PERSON>kaun anda telah disekat oleh pengelola.","Auth.form.error.code.provide":"Kod yang salah terlah diberikan.","Auth.form.error.confirmed":"E-mel akaun anda tidak disahkan lagi.","Auth.form.error.email.invalid":"E-mel ini tidak sah.","Auth.form.error.email.provide":"Sila berikan nama pengguna atau e-mel anda.","Auth.form.error.email.taken":"E-mel sudah diambil.","Auth.form.error.invalid":"Username/Email atau kata laluan tidak tepat.","Auth.form.error.params.provide":"Parameter yang tidak tepat telah diberikan","Auth.form.error.password.format":"Kata laluan anda tidak boleh mengandungi simbol `$` lebih dari tiga kali.","Auth.form.error.password.local":"Pengguna ini tidak pernah menetapkan kata laluan local, sila log masuk melalui provider yang digunakan sewaktu pembuatan akaun.","Auth.form.error.password.matching":"Kata laluan tidak sepadan.","Auth.form.error.password.provide":"Sila isikan kata laluan anda.","Auth.form.error.ratelimit":"Terlalu banyak percubaan, sila cuba sebentar lagi.","Auth.form.error.user.not-exist":"E-mel ini tidak wujud.","Auth.form.error.username.taken":"Nama pengguna sudah diambil.","Auth.form.forgot-password.email.label":"Masukkan emel anda","Auth.form.forgot-password.email.label.success":"E-mel berjaya dihantar ke","Auth.form.register.news.label":"Ikuti perkembangan terkini mengenai ciri baru dan penambahbaikan yang akan datang (dengan melakukan ini, anda menerima {terms} dan {policy}).","Auth.link.forgot-password":"Lupa kata laluan anda ?","Auth.link.ready":"Sedia untuk log masuk?","Auth.privacy-policy-agreement.policy":"dasar privasi","Auth.privacy-policy-agreement.terms":"syarat","Content Manager":"Pengurus Kandungan","Content Type Builder":"Pembina Jenis Kandungan",Documentation:t,Email:i,"Files Upload":"Fail Muat Naik","HomePage.helmet.title":"Halaman Utama","HomePage.roadmap":"Lihat jadual kerja kami","HomePage.welcome.congrats":"Tahniah!","HomePage.welcome.congrats.content":"Anda telah masuk sebagai pengelola yang pertama. Untuk meneroka ciri yang bagus disediakan oleh Strapi,","HomePage.welcome.congrats.content.bold":"kami mengesyorkan anda untuk buat Jenis Koleksi yang pertama.","New entry":"Entri baru",Password:o,Provider:r,ResetPasswordToken:s,Role:m,"Roles & Permissions":"Peranan & Keizinan","Settings.error":"Ralat","Settings.global":"Tetapan global","Settings.webhooks.create":"Buat satu webhook","Settings.webhooks.create.header":"Buat header baru","Settings.webhooks.created":"Webhook telah dibuat","Settings.webhooks.events.create":"Cipta","Settings.webhooks.form.events":"Sewaktu","Settings.webhooks.form.headers":"Tajuk","Settings.webhooks.form.url":"Url","Settings.webhooks.key":"Kata Kunci","Settings.webhooks.list.button.add":"Tambah webhook baru","Settings.webhooks.list.description":"Dapatkan pemberitahuan perubahan untuk POST.","Settings.webhooks.list.empty.description":"Tambah satu dalam senarai.","Settings.webhooks.list.empty.link":"Lihat dokumen kami","Settings.webhooks.list.empty.title":"Belum ada webhook","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.trigger":"Cetus","Settings.webhooks.trigger.cancel":"Batalkan pencetusan","Settings.webhooks.trigger.pending":"Belum selesai\u2026","Settings.webhooks.trigger.save":"Sila simpan untuk cetuskan","Settings.webhooks.trigger.success":"Berjaya!","Settings.webhooks.trigger.success.label":"Cetusan berjaya","Settings.webhooks.trigger.test":"Uji Cetusan","Settings.webhooks.trigger.title":"Simpan sebelum cetus","Settings.webhooks.value":"Kandungan",Username:l,Users:p,"Users & Permissions":"Para Pengguna & Keizinan","app.components.BlockLink.code":"Contoh Kod","app.components.Button.cancel":"Batal","app.components.Button.reset":"Set Semula","app.components.ComingSoonPage.comingSoon":"Akan Datang","app.components.DownloadInfo.download":"Muat turun sedang dijalankan...","app.components.DownloadInfo.text":"Ini akan mengambil masa, terima kasih atas kesabaran anda.","app.components.EmptyAttributes.title":"Tiada ruang disini","app.components.HomePage.button.blog":"LIHAT LEBIH LAGI DI BLOG","app.components.HomePage.community":"Cari komuniti di web","app.components.HomePage.community.content":"Bincang dengan ahli kumpulan, penyumbang dan pembangun di saluran berbeza.","app.components.HomePage.create":"Cipta Jenis Kandungan anda","app.components.HomePage.welcome":"Selamat datang!","app.components.HomePage.welcome.again":"Selamat datang ","app.components.HomePage.welcomeBlock.content":"Kami mengalu-alukan kedatangan anda di komuniti. Kami sentiasa mencari penambahbaikan, jadi jangan segan silu untuk mesej kami di ","app.components.HomePage.welcomeBlock.content.again":"Kami harap anda membuat progress pada projek anda... Luangkan masa untuk membaca berita baru kami tentang strapi. Kami memberikan yang terbaik untuk menambah baik produk ini berdasarkan maklum balas anda.","app.components.HomePage.welcomeBlock.content.issues":"isu-isu.","app.components.HomePage.welcomeBlock.content.raise":" atau berikan ","app.components.ImgPreview.hint":"Tarik & Lepas fail anda kedalam kawasan ini atau {browse} fail untuk muat naik","app.components.ImgPreview.hint.browse":"pilih fail","app.components.InputFile.newFile":"Tambah fail baru","app.components.InputFileDetails.open":"Buka di tab baru","app.components.InputFileDetails.originalName":"Nama asal:","app.components.InputFileDetails.remove":"Buang fail ini","app.components.InputFileDetails.size":"Saiz:","app.components.InstallPluginPage.Download.description":"Ia mungkin mengambil beberapa saat untuk memuat turun dan memasang plugin.","app.components.InstallPluginPage.Download.title":"Memuat Turun...","app.components.InstallPluginPage.description":"Kembangkan aplikasi anda dengan mudah.","app.components.LeftMenuFooter.help":"Bantuan","app.components.LeftMenuFooter.poweredBy":"Dikuasakan oleh ","app.components.LeftMenuLinkContainer.collectionTypes":"Jenis Koleksi","app.components.LeftMenuLinkContainer.configuration":"Konfigurasi","app.components.LeftMenuLinkContainer.general":"Umum","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Belum ada plugin yang dipasang","app.components.LeftMenuLinkContainer.plugins":"Plugin-plugin","app.components.LeftMenuLinkContainer.singleTypes":"Jenis Tunggal","app.components.ListPluginsPage.description":"Senarai plugin yang dipasang didalam projek ini.","app.components.ListPluginsPage.helmet.title":"Senarai plugin","app.components.Logout.logout":"Log Keluar","app.components.Logout.profile":"Profil","app.components.NotFoundPage.back":"Kembali ke laman utama","app.components.NotFoundPage.description":"Tidak dijumpai","app.components.Official":"Rasmi","app.components.Onboarding.label.completed":"% siap","app.components.Onboarding.title":"Video-video untuk bermula","app.components.PluginCard.Button.label.download":"Muat turun","app.components.PluginCard.Button.label.install":"Sudah dipasang","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"Ciri AutoReload perlu diaktifkan. Sila mulakkan aplikasi anda dengan `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Saya Faham!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Untuk tujuan keselamatan, plugin hanya boleh di muat turun dalam environment 'development'.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Memuat turun adalah mustahil","app.components.PluginCard.compatible":"Serasi dengan applikasi anda","app.components.PluginCard.compatibleCommunity":"Serasi dengan komuniti","app.components.PluginCard.more-details":"Butiran selanjutnya","app.components.listPlugins.button":"Tambah Plugin Baru","app.components.listPlugins.title.none":"Tiada plugin dipasang","app.components.listPluginsPage.deletePlugin.error":"Satu ralat muncul ketika membuang plugin tersebut","app.containers.App.notification.error.init":"Ralat berlaku semasa permintaan API","app.links.configure-view":"Susun paparan","app.utils.SelectOption.defaultMessage":" ","app.utils.defaultMessage":" ","app.utils.filters":"Tapisan","app.utils.placeholder.defaultMessage":" ","component.Input.error.validation.integer":"Nilainya haruslah dalam integer","components.AutoReloadBlocker.description":"Jalankan Strapi dengan salah satu arahan berikut:","components.AutoReloadBlocker.header":"ciri Reload diperlukan untuk plugin ini.","components.ErrorBoundary.title":"Ada sesuatu yang tidak kena...","components.Input.error.attribute.key.taken":"Nilai ini sudah wujud","components.Input.error.attribute.sameKeyAndName":"Tak boleh sama","components.Input.error.attribute.taken":"Nama kotak ini sudah wujud","components.Input.error.contentTypeName.taken":"Nama ini sudah wujud","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"Kata laluan tidak sepadan","components.Input.error.validation.email":"Ini bukan email","components.Input.error.validation.json":"Ini tak sepadan dengan format JSON","components.Input.error.validation.max":"Nilai isinya terlalu tinggi {max}.","components.Input.error.validation.maxLength":"Panjang isinya terlalu panjang {max}.","components.Input.error.validation.min":"Nilai isinya terlalu rendah {min}.","components.Input.error.validation.minLength":"Panjang isinya terlalu pendek {min}.","components.Input.error.validation.minSupMax":"Tidak boleh lebih tinggi","components.Input.error.validation.regex":"Nilai isinya tidak sepadan dengan regex.","components.Input.error.validation.required":"Nilai ini adalah wajib.","components.Input.error.validation.unique":"Nilai ini telah digunakan.","components.InputSelect.option.placeholder":"Pilih disini","components.ListRow.empty":"Tiada data untuk pamirkan.","components.OverlayBlocker.description":"Anda menggunakan ciri yang memerlukan pelayan untuk dimulakan semula. Sila tunggu sehinggal pelayan habis.","components.OverlayBlocker.description.serverError":"Pelayan sepatutnya telah dimulakan semula, sila periksa log anda di terminal.","components.OverlayBlocker.title":"Menunggu untuk dimulakan semula...","components.OverlayBlocker.title.serverError":"Permulaan semula mengambil masa lebih lama daripada yang dijangkakan","components.PageFooter.select":"entri dipaparkan setiap halaman","components.ProductionBlocker.description":"Untuk tujuan keslamatan kami perlu menyahkan plugin didalam environment lain.","components.ProductionBlocker.header":"Plugin in hanya tersedia dalam pembangunan(development).","components.Search.placeholder":"Cari...","components.Wysiwyg.collapse":"Tutup","components.Wysiwyg.selectOptions.H1":"Tajuk H1","components.Wysiwyg.selectOptions.H2":"Tajuk H2","components.Wysiwyg.selectOptions.H3":"Tajuk H3","components.Wysiwyg.selectOptions.H4":"Tajuk H4","components.Wysiwyg.selectOptions.H5":"Tajuk H5","components.Wysiwyg.selectOptions.H6":"Tajuk H6","components.Wysiwyg.selectOptions.title":"Tambah tajuk","components.WysiwygBottomControls.charactersIndicators":"aksara","components.WysiwygBottomControls.fullscreen":"Besarkan","components.WysiwygBottomControls.uploadFiles":"Tarik & Lepas fail, tampal dari clipboard atau {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"pilih","components.popUpWarning.message":"Anda yakin untuk memadam?","components.popUpWarning.title":"Sila sahkan","content-manager.EditRelations.title":"Data Terhubung(Relational data)","content-manager.api.id":"ID API","content-manager.components.AddFilterCTA.add":"Penapis","content-manager.components.AddFilterCTA.hide":"Penapis","content-manager.components.DraggableAttr.edit":"Klik untuk mengedit","content-manager.components.DynamicZone.pick-compo":"Pilih satu komponen","content-manager.components.EmptyAttributesBlock.button":"Pergi ke halaman tetapan","content-manager.components.EmptyAttributesBlock.description":"Anda boleh ubah tetapan anda","content-manager.components.FieldItem.linkToComponentLayout":"Tetapkan susun atur komponen","content-manager.components.FilterOptions.button.apply":"Gunakan","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Gunakan","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Kosongkan semua","content-manager.components.FiltersPickWrapper.PluginHeader.description":"Tetapkan syarat yang akan digunakan untuk tapis entri","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Tapisan","content-manager.components.FiltersPickWrapper.hide":"Sembunyikan","content-manager.components.LimitSelect.itemsPerPage":"Item setiap halaman","content-manager.components.Search.placeholder":"Cari entri..","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Ubah paparan untuk pengeditan.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Tentukan tetapan paparan senarai.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Konfigurasikan paparan - {name}","content-manager.components.TableDelete.delete":"Padam semua","content-manager.components.TableDelete.deleteSelected":"Padam yang dipilih","content-manager.components.TableEmpty.withFilters":"Tiada {contentType} dalam tapisan...","content-manager.components.TableEmpty.withSearch":"Tiada {contentType} yang sesuai dengan carian ({search})...","content-manager.components.TableEmpty.withoutFilter":"Tiada {contentType}...","content-manager.components.empty-repeatable":"Belum ada entri. Klik pada butang di bawah untuk tambah satu","content-manager.components.notification.info.maximum-requirement":"Anda sudah mencapai bilangan ruang maksimum","content-manager.components.notification.info.minimum-requirement":"Ruang telah ditambahkan untuk memenuhi syarat minimum","content-manager.components.reset-entry":"Tetapkan semula entri","content-manager.components.uid.apply":"Gunakan","content-manager.components.uid.available":"Tersedia","content-manager.components.uid.regenerate":"jana semula","content-manager.components.uid.suggested":"yang dicadangkan","content-manager.components.uid.unavailable":"tidak tersedia","content-manager.containers.Edit.Link.Layout":"Konfigurasikan susun atur","content-manager.containers.Edit.Link.Model":"Edit jenis koleksi","content-manager.containers.Edit.addAnItem":"Tambah item...","content-manager.containers.Edit.clickToJump":"Klik untuk pergi ke entri","content-manager.containers.Edit.delete":"Padam","content-manager.containers.Edit.editing":"Menyunting...","content-manager.containers.Edit.pluginHeader.title.new":"Buat entri","content-manager.containers.Edit.reset":"Semula","content-manager.containers.Edit.returnList":"Kembali ke senarai","content-manager.containers.Edit.seeDetails":"Butiran","content-manager.containers.Edit.submit":"Simpan","content-manager.containers.EditSettingsView.modal-form.edit-field":"Edit ruang","content-manager.containers.EditView.notification.errors":"Borang mengandungi beberapa kesalahan","content-manager.containers.Home.introduction":"Untuk mengedit entri anda, pergi ke pautan yang ditetapkan di menu sebelah kiri. Plugin ini tidak mempunyai cara yang betul untuk mengedit tetapan dan masih dalam pembangunan yang aktif","content-manager.containers.Home.pluginHeaderDescription":"Uruskan entri anda melalui antaramuka yang hebat dan cantik.","content-manager.containers.Home.pluginHeaderTitle":"Pengurus Kandungan","content-manager.containers.List.errorFetchRecords":"Ralat","content-manager.containers.ListPage.displayedFields":"Ruang yang dipamirkan","content-manager.containers.ListSettingsView.modal-form.edit-label":"Edit label","content-manager.containers.SettingPage.add.field":"Tambah ruang lain","content-manager.containers.SettingPage.attributes":"Ruang ciri-ciri","content-manager.containers.SettingPage.attributes.description":"Tentukan tertib untuk ciri-ciri","content-manager.containers.SettingPage.editSettings.description":"Tarik & Lepas ruang untuk menyusun atur","content-manager.containers.SettingPage.editSettings.entry.title":"Pilihan nama entri","content-manager.containers.SettingPage.editSettings.entry.title.description":"Pilih ruang yang akan dipaparkan sebagai nama untuk entri anda.","content-manager.containers.SettingPage.editSettings.title":"Edit paparan (tetapan)","content-manager.containers.SettingPage.layout":"Susun atur","content-manager.containers.SettingPage.listSettings.description":"Tetapkan pilihan yang ada untuk jenis koleksi ini","content-manager.containers.SettingPage.listSettings.title":"Paparan senarai (tetapan)","content-manager.containers.SettingPage.pluginHeaderDescription":"Tetapkan tetapan khusus untuk Jenis Koleksi ini","content-manager.containers.SettingPage.settings":"Tetapan","content-manager.containers.SettingPage.view":"Sususnan","content-manager.containers.SettingViewModel.pluginHeader.title":"Pengurus Kandungan - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Tetapkan tetapan tertentu","content-manager.containers.SettingsPage.Block.contentType.title":"Jenis Koleksi(Collection Types)","content-manager.containers.SettingsPage.Block.generalSettings.description":"Tetapkan pilihan lalai untuk Jenis Koleksi anda","content-manager.containers.SettingsPage.Block.generalSettings.title":"Am(General)","content-manager.containers.SettingsPage.pluginHeaderDescription":"Tetapkan tetapan untuk semua Jenis koleksi dan Kumpulan anda","content-manager.containers.SettingsView.list.subtitle":"Tetapkan susun atur dan paparan Jenis koleksi dan kumpulan anda","content-manager.containers.SettingsView.list.title":"Paparan konfigurasi","content-manager.emptyAttributes.button":"Pergi ke pembina jenis koleksi","content-manager.emptyAttributes.description":"Tambah ruang pertama anda ke Jenis Koleksi anda","content-manager.emptyAttributes.title":"Belum ada ruang","content-manager.error.attribute.key.taken":"Nilai ini sudah ada","content-manager.error.attribute.sameKeyAndName":"Tidak boleh sama","content-manager.error.attribute.taken":"Nama ruang ini sudah ada","content-manager.error.contentTypeName.taken":"Nama ini sudah ada","content-manager.error.model.fetch":"Ralat berlaku sewaktu mendapatkan tetapan model ","content-manager.error.record.create":"Ralat berlaku sewaktu membuat rekod.","content-manager.error.record.delete":"Ralat berlaku sewaktu penghapusan rekod.","content-manager.error.record.fetch":"Ralat berlaku sewaktu mendapatkan rekod.","content-manager.error.record.update":"Ralat berlaku sewaktu mengemaskini rekod.","content-manager.error.records.count":"Ralat berlaku sewaktu mendapatkan kiraan rekod.","content-manager.error.records.fetch":"Ralat berlaku sewaktu mendapatkan rekod.","content-manager.error.schema.generation":"Ralat berlaku sewaktu penghasilan skema.","content-manager.error.validation.json":"Ini bukan JSON(This is not a JSON)","content-manager.error.validation.max":"Nilai isinya terlalu tinggi.","content-manager.error.validation.maxLength":"Panjang isinya terlalu panjang.","content-manager.error.validation.min":"Nilai isinya terlalu rendah.","content-manager.error.validation.minLength":"Panjang isinya terlalu pendek.","content-manager.error.validation.minSupMax":"Tidak boleh lebih tinggi","content-manager.error.validation.regex":"Nilai isinya tidak sepadan dengan regex.","content-manager.error.validation.required":"Nilai input ini adalah wajib","content-manager.form.Input.bulkActions":"Benarkan tindakan pukal","content-manager.form.Input.defaultSort":"Ciri tertib yang lalai","content-manager.form.Input.description":"Penerangan","content-manager.form.Input.description.placeholder":"Paparkan nama dalam profil","content-manager.form.Input.editable":"Ruang yang boleh diedit","content-manager.form.Input.filters":"Benarkan tapisan","content-manager.form.Input.label":"Label","content-manager.form.Input.label.inputDescription":"Isinya ini menggantikan label yang dipaparkan di tajuk jadual","content-manager.form.Input.pageEntries":"Entri setiap halaman","content-manager.form.Input.pageEntries.inputDescription":"Catatan: Anda boleh menukarkan nilai ini di halaman tetapan Jenis Koleksi","content-manager.form.Input.placeholder":"Pemegang Tempat","content-manager.form.Input.placeholder.placeholder":"My awesome value","content-manager.form.Input.search":"Benarkan carian","content-manager.form.Input.search.field":"Benarkan carian di ruang ini","content-manager.form.Input.sort.field":"Benarkan penyusunan di ruang ini","content-manager.form.Input.wysiwyg":"Paparkan sebagai WYSIWYG","content-manager.global.displayedFields":"Ruang yang dipaparkan","content-manager.groups":"Kumpulan","content-manager.groups.numbered":"Kumpulan ({number})","content-manager.models":"Jenis Koleksi","content-manager.models.numbered":"Jenis Koleksi ({number})","content-manager.notification.error.displayedFields":"Anda perlukan sekurang-kurangnya satu ruang yang dipaparkan","content-manager.notification.error.relationship.fetch":"Ralat berlaku sewaktu mendapatkan perhubungan data.","content-manager.notification.info.SettingPage.disableSort":"Anda mesti mempunyai satu attribute dengan penertib yang telah dibenarkan(You need to have one attribute with the sorting allowed)","content-manager.notification.info.minimumFields":"Anda mesti memaparkan sekurang-kurangnya satu ruang","content-manager.notification.upload.error":"Berlaku ralat sewaktu memuat naik fail anda","content-manager.pageNotFound":"Laman tidak dijumpai","content-manager.plugin.description.long":"Cara cepat untuk melihat, mengedit dan menghapus data dalam pangkalan data anda.","content-manager.plugin.description.short":"Cara cepat untuk melihat, mengedit dan menghapus data dalam pangkalan data anda.","content-manager.popUpWarning.bodyMessage.contentType.delete":"Adakah anda pasti mahu memadam entri ini?","content-manager.popUpWarning.bodyMessage.contentType.delete.all":"Adakah anda pasti mahu memadam entri ini?","content-manager.popUpWarning.warning.cancelAllSettings":"Adakah anda pasti mahu membatalkan pengubahsuaian anda?","content-manager.popUpWarning.warning.updateAllSettings":"Ini akan mengubah semua tetapan anda","content-manager.success.record.delete":"Telah Dipadam","content-manager.success.record.save":"Disimpan","form.button.done":"Siap","global.prompt.unsaved":"Adakah anda pasti untuk meninggalkan halaman ini? Segala perubahan anda akan hilang","notification.contentType.relations.conflict":"Jenis kandungan ada hubungan(relations) yang saling bertentangan","notification.error":"satu ralat muncul","notification.error.layout":"Tidak dapat kesan susunan atur","notification.form.error.fields":"Borang mengandungi beberapa kesalahan","notification.form.success.fields":"Perubahan disimpan","notification.success.delete":"Item telah dipadamkan","request.error.model.unknown":"model ini tidak wujud"}}}]);
