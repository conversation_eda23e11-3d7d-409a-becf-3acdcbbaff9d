"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[7632],{22185:(x,A,t)=>{t.d(A,{T:()=>F});var s=t(92132),i=t(21272),R=t(94061),U=t(88353),C=t(83997),B=t(25641),o=t(90361),E=t(33363),a=t(30893),l=t(21610),e=t(55506),W=t(83925),p=t(41909),I=t(50612),K=t(54894),j=t(17703),z=t(71389),V=t(63891);const F=({permissions:n,headers:d=[],contentType:M,isLoading:D=!1,tokens:O=[],onConfirmDelete:P,tokenType:r})=>{const{canDelete:g,canUpdate:T,canRead:y}=n;return(0,s.jsx)(e.Ee,{headers:d,contentType:M,rows:O,withBulkActions:g||T||y,isLoading:D,onConfirmDelete:P,children:(0,s.jsx)(Y,{tokenType:r,permissions:n,onConfirmDelete:P})})},Y=({tokenType:n,permissions:d,rows:M=[],withBulkActions:D,onConfirmDelete:O})=>{const{canDelete:P,canUpdate:r,canRead:g}=d,[{query:T}]=(0,e.sq)(),{formatMessage:y}=(0,K.A)(),[,Z]=T&&T.sort?T.sort.split(":"):[void 0,"ASC"],{push:w,location:{pathname:b}}=(0,j.W6)(),{trackUsage:q}=(0,e.z1)(),tt=[...M].sort((_,st)=>{const S=_.name.localeCompare(st.name);return Z==="DESC"?-S:S});return(0,s.jsx)(B.N,{children:tt.map(_=>(0,s.jsxs)(o.Tr,{...(0,e.qM)({fn(){q("willEditTokenFromList",{tokenType:n}),w(`${b}/${_.id}`)},condition:r}),children:[(0,s.jsx)(E.Td,{maxWidth:(0,e.a8)(250),children:(0,s.jsx)(a.o,{textColor:"neutral800",fontWeight:"bold",ellipsis:!0,children:_.name})}),(0,s.jsx)(E.Td,{maxWidth:(0,e.a8)(250),children:(0,s.jsx)(a.o,{textColor:"neutral800",ellipsis:!0,children:_.description})}),(0,s.jsx)(E.Td,{children:(0,s.jsx)(a.o,{textColor:"neutral800",children:(0,s.jsx)(e.sR,{timestamp:new Date(_.createdAt)})})}),(0,s.jsx)(E.Td,{children:_.lastUsedAt&&(0,s.jsx)(a.o,{textColor:"neutral800",children:(0,s.jsx)(e.sR,{timestamp:new Date(_.lastUsedAt),customIntervals:[{unit:"hours",threshold:1,text:y({id:"Settings.apiTokens.lastHour",defaultMessage:"last hour"})}]})})}),D&&(0,s.jsx)(E.Td,{children:(0,s.jsxs)(C.s,{justifyContent:"end",children:[r&&(0,s.jsx)(X,{tokenName:_.name,tokenId:_.id}),!r&&g&&(0,s.jsx)(J,{tokenName:_.name,tokenId:_.id}),P&&(0,s.jsx)(H,{tokenName:_.name,onClickDelete:()=>O?.(_.id),tokenType:n})]})})]},_.id))})},G={edit:{id:"app.component.table.edit",defaultMessage:"Edit {target}"},read:{id:"app.component.table.read",defaultMessage:"Read {target}"}},u=({tokenName:n,tokenId:d,buttonType:M="edit",children:D})=>{const{formatMessage:O}=(0,K.A)(),{location:{pathname:P}}=(0,j.W6)();return(0,s.jsx)(Q,{forwardedAs:z.k2,to:`${P}/${d}`,title:O(G[M],{target:n}),children:D})},Q=(0,V.Ay)(l.N)`
  svg {
    path {
      fill: ${({theme:n})=>n.colors.neutral500};
    }
  }

  &:hover,
  &:focus {
    svg {
      path {
        fill: ${({theme:n})=>n.colors.neutral800};
      }
    }
  }
`,H=({tokenName:n,onClickDelete:d,tokenType:M})=>{const{formatMessage:D}=(0,K.A)(),{trackUsage:O}=(0,e.z1)(),[P,r]=i.useState(!1),g=()=>{r(!1),O("willDeleteToken",{tokenType:M}),d()};return(0,s.jsxs)(R.a,{paddingLeft:1,onClick:T=>T.stopPropagation(),children:[(0,s.jsx)(U.K,{onClick:()=>{r(!0)},label:D({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:`${n}`}),name:"delete",borderWidth:0,icon:(0,s.jsx)(I.A,{})}),(0,s.jsx)(e.TM,{onToggleDialog:()=>r(!1),onConfirm:g,isOpen:P})]})},J=({tokenName:n,tokenId:d})=>(0,s.jsx)(u,{tokenName:n,tokenId:d,buttonType:"read",children:(0,s.jsx)(W.A,{})}),X=({tokenName:n,tokenId:d})=>(0,s.jsx)(u,{tokenName:n,tokenId:d,children:(0,s.jsx)(p.A,{width:12})})},27632:(x,A,t)=>{t.d(A,{ProtectedListView:()=>dt});var s=t(92132),i=t(21272),R=t(4198),U=t(55356),C=t(25815),B=t(38413),o=t(55506),E=t(5194),a=t(5409),l=t(54894),e=t(17703),W=t(43543),p=t(30740),I=t(99831),K=t(22185),j=t(15126),z=t(63299),V=t(67014),F=t(59080),Y=t(79275),G=t(14718),u=t(82437),Q=t(61535),H=t(5790),J=t(12083),X=t(35223),n=t(74930),d=t(2600),M=t(48940),D=t(41286),O=t(56336),P=t(13426),r=t(84624),g=t(77965),T=t(54257),y=t(71210),Z=t(51187),w=t(39404),b=t(58692),q=t(501),tt=t(57646),_=t(23120),st=t(44414),S=t(25962),Ot=t(14664),Tt=t(42588),At=t(90325),mt=t(62785),ct=t(87443),Ct=t(41032),It=t(22957),gt=t(93179),Lt=t(73055),ht=t(15747),vt=t(85306),Rt=t(26509),Ut=t(32058),Bt=t(81185),Wt=t(82261);const ot=[{name:"name",key:"name",metadatas:{label:{id:"Settings.apiTokens.ListView.headers.name",defaultMessage:"Name"},sortable:!0}},{name:"description",key:"description",metadatas:{label:{id:"Settings.apiTokens.ListView.headers.description",defaultMessage:"Description"},sortable:!1}},{name:"createdAt",key:"createdAt",metadatas:{label:{id:"Settings.apiTokens.ListView.headers.createdAt",defaultMessage:"Created at"},sortable:!1}},{name:"lastUsedAt",key:"lastUsedAt",metadatas:{label:{id:"Settings.apiTokens.ListView.headers.lastUsedAt",defaultMessage:"Last used"},sortable:!1}}],Et=()=>{(0,o.L4)();const{formatMessage:m}=(0,l.A)(),L=(0,o.hN)(),it=(0,W.j)(c=>c.admin_app.permissions.settings?.["api-tokens"]),{allowedActions:{canCreate:k,canDelete:rt,canUpdate:lt,canRead:h}}=(0,o.ec)(it),{push:at}=(0,e.W6)(),{trackUsage:f}=(0,o.z1)(),{startSection:et}=(0,o.Cx)(),{_unstableFormatAPIError:N}=(0,o.wq)();i.useEffect(()=>{et("apiTokens")},[et]),i.useEffect(()=>{at({search:a.stringify({sort:"name:ASC"},{encode:!1})})},[at]);const Pt=ot.map(c=>({...c,metadatas:{...c.metadatas,label:m(c.metadatas.label)}}));(0,W.b)(()=>{f("willAccessTokenList",{tokenType:I.A})});const{data:v=[],isLoading:nt,error:$}=(0,p.u)(void 0,{skip:!h});i.useEffect(()=>{$&&L({type:"warning",message:N($)})},[$,N,L]),i.useEffect(()=>{f("didAccessTokenList",{number:v.length,tokenType:I.A})},[v,f]);const[Mt]=(0,p.a)(),Dt=async c=>{try{const _t=await Mt(c);if("error"in _t){L({type:"warning",message:N(_t.error)});return}f("didDeleteToken")}catch{L({type:"warning",message:{id:"notification.error",defaultMessage:"Something went wrong"}})}};return(0,s.jsxs)(B.g,{"aria-busy":nt,children:[(0,s.jsx)(o.x7,{name:"API Tokens"}),(0,s.jsx)(U.Q,{title:m({id:"Settings.apiTokens.title",defaultMessage:"API Tokens"}),subtitle:m({id:"Settings.apiTokens.description",defaultMessage:"List of generated tokens to consume the API"}),primaryAction:k&&(0,s.jsx)(C.z,{"data-testid":"create-api-token-button",startIcon:(0,s.jsx)(E.A,{}),size:"S",onClick:()=>f("willAddTokenFromList",{tokenType:I.A}),to:"/settings/api-tokens/create",children:m({id:"Settings.apiTokens.create",defaultMessage:"Create new API Token"})})}),(0,s.jsxs)(R.s,{children:[!h&&(0,s.jsx)(o.UW,{}),h&&v.length>0&&(0,s.jsx)(K.T,{permissions:{canRead:h,canDelete:rt,canUpdate:lt},headers:Pt,contentType:"api-tokens",isLoading:nt,onConfirmDelete:Dt,tokens:v,tokenType:I.A}),h&&k&&v.length===0&&(0,s.jsx)(o.R1,{content:{id:"Settings.apiTokens.addFirstToken",defaultMessage:"Add your first API Token"},action:(0,s.jsx)(C.z,{variant:"secondary",startIcon:(0,s.jsx)(E.A,{}),to:"/settings/api-tokens/create",children:m({id:"Settings.apiTokens.addNewToken",defaultMessage:"Add new API Token"})})}),h&&!k&&v.length===0&&(0,s.jsx)(o.R1,{content:{id:"Settings.apiTokens.emptyStateLayout",defaultMessage:"You don\u2019t have any content yet..."}})]})]})},dt=()=>{const m=(0,W.j)(L=>L.admin_app.permissions.settings?.["api-tokens"].main);return(0,s.jsx)(o.kz,{permissions:m,children:(0,s.jsx)(Et,{})})}},30740:(x,A,t)=>{t.d(A,{a:()=>B,b:()=>U,c:()=>C,d:()=>o,u:()=>R});var s=t(43543);const i=s.n.injectEndpoints({endpoints:E=>({getAPITokens:E.query({query:()=>"/admin/api-tokens",transformResponse:a=>a.data,providesTags:(a,l)=>[...a?.map(({id:e})=>({type:"ApiToken",id:e}))??[],{type:"ApiToken",id:"LIST"}]}),getAPIToken:E.query({query:a=>`/admin/api-tokens/${a}`,transformResponse:a=>a.data,providesTags:(a,l,e)=>[{type:"ApiToken",id:e}]}),createAPIToken:E.mutation({query:a=>({url:"/admin/api-tokens",method:"POST",data:a}),transformResponse:a=>a.data,invalidatesTags:[{type:"ApiToken",id:"LIST"}]}),deleteAPIToken:E.mutation({query:a=>({url:`/admin/api-tokens/${a}`,method:"DELETE"}),transformResponse:a=>a.data,invalidatesTags:(a,l,e)=>[{type:"ApiToken",id:e}]}),updateAPIToken:E.mutation({query:({id:a,...l})=>({url:`/admin/api-tokens/${a}`,method:"PUT",data:l}),transformResponse:a=>a.data,invalidatesTags:(a,l,{id:e})=>[{type:"ApiToken",id:e}]})}),overrideExisting:!1}),{useGetAPITokensQuery:R,useGetAPITokenQuery:U,useCreateAPITokenMutation:C,useDeleteAPITokenMutation:B,useUpdateAPITokenMutation:o}=i},99831:(x,A,t)=>{t.d(A,{A:()=>s,T:()=>i});const s="api-token",i="transfer-token"}}]);
