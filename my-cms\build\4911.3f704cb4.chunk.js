"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4911],{54911:(b,e,n)=>{n.r(e),n.d(e,{Analytics:()=>t,Documentation:()=>a,Email:()=>i,Password:()=>o,Provider:()=>r,ResetPasswordToken:()=>l,Role:()=>u,Username:()=>s,Users:()=>p,anErrorOccurred:()=>m,clearLabel:()=>c,dark:()=>d,default:()=>x,light:()=>g,or:()=>F,skipToContent:()=>k,submit:()=>y});const t="Analizler",a="Dok\xFCmantasyon",i="E-posta",o="\u015Eifre",r="Sa\u011Flay\u0131c\u0131",l="\u015Eifre s\u0131f\u0131rlama anahtar\u0131",u="Rol",s="Kullan\u0131c\u0131 Ad\u0131",p="Kullan\u0131c\u0131lar",m="Haydaa! Bir \u015Feyler ters gitti. L\xFCtfen tekrar dene.",c="Temizle",d="Koyu",g="A\xE7\u0131k",F="YA DA",k="\u0130\xE7eri\u011Fe atla",y="G\xF6nder",x={Analytics:t,"Auth.components.Oops.text":"Hesab\u0131n donduruldu.","Auth.components.Oops.text.admin":"Hatal\u0131 oldu\u011Funu d\xFC\u015F\xFCn\xFCyorsan\u0131z l\xFCtfen y\xF6neticinize ula\u015F\u0131n.","Auth.components.Oops.title":"Haydaa...","Auth.form.active.label":"Aktif","Auth.form.button.forgot-password":"E-posta g\xF6nder","Auth.form.button.go-home":"ANASAYFAYA GER\u0130 D\xD6N","Auth.form.button.login":"Giri\u015F","Auth.form.button.password-recovery":"\u015Eifre Kurtarma","Auth.form.button.register":"Ba\u015Flamaya haz\u0131r","Auth.form.email.label":"E-posta","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"Hesab\u0131n\u0131z y\xF6netici taraf\u0131ndan engellendi.","Auth.form.error.code.provide":"Ge\xE7ersiz sa\u011Flanm\u0131\u015F kod.","Auth.form.error.confirmed":"Tan\u0131mlad\u0131\u011F\u0131n\u0131z e-posta onaylanmad\u0131.","Auth.form.error.email.invalid":"E-postas\u0131 ge\xE7ersiz.","Auth.form.error.email.provide":"L\xFCtfen kullan\u0131c\u0131 ad\u0131n\u0131z\u0131 veya e-postan\u0131z\u0131 belirtin.","Auth.form.error.email.taken":"E-posta zaten al\u0131nm\u0131\u015F","Auth.form.error.invalid":"Kimlik veya \u015Fifre ge\xE7ersiz.","Auth.form.error.params.provide":"Ge\xE7ersiz sa\u011Flanm\u0131\u015F kod parametresi.","Auth.form.error.password.format":"\u015Eifreniz `$` sembol\xFCn\xFC \xFC\xE7 kezden fazla i\xE7eremez.","Auth.form.error.password.local":"Bu kullan\u0131c\u0131 hi\xE7bir bir \u015Fifre belirlemedi; hesap olu\u015Fturma s\u0131ras\u0131nda kullan\u0131lan sa\u011Flay\u0131c\u0131 arac\u0131l\u0131\u011F\u0131yla l\xFCtfen giri\u015F yap\u0131n\u0131z..","Auth.form.error.password.matching":"Parolalar uyu\u015Fmuyor.","Auth.form.error.password.provide":"L\xFCtfen \u015Fifrenizi girin.","Auth.form.error.ratelimit":"\xC7ok fazla deneme var. L\xFCtfen bir dakika sonra tekrar deneyin.","Auth.form.error.user.not-exist":"Bu e-posta bulunmamaktad\u0131r..","Auth.form.error.username.taken":"Kullan\u0131c\u0131 ad\u0131 zaten al\u0131nm\u0131\u015F","Auth.form.firstname.label":"Ad\u0131n","Auth.form.firstname.placeholder":"\xF6r. Zeynep","Auth.form.forgot-password.email.label":"E-postan\u0131z\u0131 giriniz","Auth.form.forgot-password.email.label.success":"E-posta ba\u015Far\u0131yla g\xF6nderildi, ","Auth.form.lastname.label":"Soyad\u0131n","Auth.form.lastname.placeholder":"\xF6r. Y\u0131lmaz","Auth.form.password.hide-password":"\u015Eifreyi gizle","Auth.form.password.hint":"8 karakterden uzun olmal\u0131 ve en az 1 b\xFCy\xFCk harf, 1 k\xFC\xE7\xFCk harf ve 1 say\u0131 i\xE7ermeli","Auth.form.password.show-password":"\u015Eifreyi g\xF6ster","Auth.form.register.news.label":"Beni gelecekteki \xF6zellikler ve geli\u015Ftirmeler hakk\u0131nda bilgilendir (bunu se\xE7erek {terms} ve {policy}'leri kabul etmi\u015F say\u0131l\u0131rs\u0131n\u0131z)","Auth.form.register.subtitle":"Bilgiler yaln\u0131zca Strapi kimlik do\u011Frulamas\u0131 i\xE7in kullan\u0131lacak. T\xFCm veriler sizin veritaban\u0131n\u0131zda saklanacak.","Auth.form.rememberMe.label":"Beni hat\u0131rla","Auth.form.username.label":"Kullan\u0131c\u0131 Ad\u0131","Auth.form.username.placeholder":"Kai Doe","Auth.form.welcome.subtitle":"Strapi hesab\u0131na giri\u015F yap","Auth.form.welcome.title":"Strapi'ye ho\u015Fgeldiniz!","Auth.link.forgot-password":"Parolan\u0131z\u0131 m\u0131 unuttunuz ?","Auth.link.ready":"Zaten kay\u0131tl\u0131 m\u0131s\u0131n\u0131z?","Auth.link.signin":"Giri\u015F yap","Auth.link.signin.account":"Hesab\u0131n var m\u0131?","Auth.login.sso.divider":"Ya da bunlarla giri\u015F yap","Auth.login.sso.loading":"Sa\u011Flay\u0131c\u0131lar y\xFCkleniyor...","Auth.login.sso.subtitle":"Hesab\u0131na SSO ile giri\u015F yap","Auth.privacy-policy-agreement.policy":"gizlilik s\xF6zle\u015Fmesi","Auth.privacy-policy-agreement.terms":"ko\u015Fullar","Auth.reset-password.title":"\u015Eifreni s\u0131f\u0131rla","Content Manager":"\u0130\xE7erik Y\xF6netimi","Content Type Builder":"\u0130\xE7erik-Tipi Kurucusu",Documentation:a,Email:i,"Files Upload":"Dosya y\xFCkleme","HomePage.helmet.title":"Anasayfa","HomePage.roadmap":"Yol haritam\u0131z\u0131 g\xF6r\xFCnt\xFCleyin","HomePage.welcome.congrats":"Tebrikler!","HomePage.welcome.congrats.content":"\u0130lk y\xF6netici olarak giri\u015F yapt\u0131n\u0131z. Strapi'nin g\xFC\xE7l\xFC \xF6zelliklerini ke\u015Ffetmek i\xE7in,","HomePage.welcome.congrats.content.bold":"ilk \u0130\xE7erik-Tipi'ni yaratman\u0131z\u0131 \xF6neriyoruz.","Media Library":"Ortam K\xFCt\xFCphanesi","New entry":"Yeni kay\u0131t",Password:o,Provider:r,ResetPasswordToken:l,Role:u,"Settings.PageTitle":"Ayarlar - {name}","Settings.tokens.Button.cancel":"\u0130ptal","Settings.tokens.Button.regenerate":"Yeniden \xFCret","Settings.apiTokens.ListView.headers.createdAt":"Olu\u015Fturuldu","Settings.apiTokens.ListView.headers.description":"Tan\u0131m","Settings.apiTokens.ListView.headers.lastUsedAt":"En son kullan\u0131ld\u0131","Settings.apiTokens.ListView.headers.name":"\u0130sim","Settings.apiTokens.ListView.headers.type":"Token tipi","Settings.tokens.RegenerateDialog.title":"Token\u0131 yeniden \xFCret.","Settings.apiTokens.addFirstToken":"\u0130lk API Token\u0131n\u0131n\u0131 ekle","Settings.apiTokens.addNewToken":"Yeni API Token\u0131 ekle","Settings.tokens.copy.editMessage":"G\xFCvenlik sebebiyle, token\u0131 yaln\u0131zca bir kere g\xF6rebilirsin.","Settings.tokens.copy.editTitle":"Bu tokena art\u0131k eri\u015Filemez.","Settings.tokens.copy.lastWarning":"Bu token\u0131 kopyalamay\u0131 unutma. Bir daha eri\u015Femeyeceksin!","Settings.apiTokens.create":"Yeni API Token\u0131 olu\u015Ftur","Settings.apiTokens.createPage.permissions.description":"Sadece bir yol ile ba\u011Flanm\u0131\u015F eylemler listelenmektedir.","Settings.apiTokens.createPage.permissions.title":"\u0130zinler","Settings.apiTokens.description":"API'\u0131 kullanmak i\xE7in olu\u015Fturulmu\u015F token listesi","Settings.tokens.duration.30-days":"30 g\xFCn","Settings.tokens.duration.7-days":"7 g\xFCn","Settings.tokens.duration.90-days":"90 g\xFCn","Settings.tokens.duration.expiration-date":"Sona erme tarihi","Settings.tokens.duration.unlimited":"S\u0131n\u0131rs\u0131z","Settings.apiTokens.emptyStateLayout":"Hen\xFCz hi\xE7 i\xE7eri\u011Fin yok...","Settings.tokens.form.duration":"Token s\xFCresi","Settings.tokens.form.type":"Token tipi","Settings.tokens.notification.copied":"Token panoya kopyaland\u0131.","Settings.tokens.popUpWarning.message":"Bu token\u0131 yeniden \xFCretmek istedi\u011Finden emin misin?","Settings.apiTokens.title":"API Tokenlar\u0131","Settings.tokens.types.full-access":"Tam yetki","Settings.tokens.types.read-only":"Salt-okunur","Settings.application.customization":"\xD6zelle\u015Ftirme","Settings.application.customization.carousel-hint":"Y\xF6netim paneli logosunu de\u011Fi\u015Ftir. (G\xF6rsel boyutu s\u0131n\u0131r\u0131: {dimension}x{dimension}, Dosya boyutu s\u0131n\u0131r\u0131: {size}KB)","Settings.application.customization.carousel-slide.label":"Logo slayt\u0131","Settings.application.customization.carousel.change-action":"Logoyu de\u011Fi\u015Ftir","Settings.application.customization.carousel.reset-action":"Logoyu s\u0131f\u0131rla","Settings.application.customization.carousel.title":"Logo","Settings.application.customization.modal.cancel":"\u0130ptal","Settings.application.customization.modal.pending":"Bekleyen logo","Settings.application.customization.modal.pending.card-badge":"g\xF6rsel","Settings.application.customization.modal.pending.choose-another":"Ba\u015Fka bir logo se\xE7","Settings.application.customization.modal.pending.subtitle":"Y\xFCklemeden \xF6nce se\xE7ilen logoyu y\xF6net","Settings.application.customization.modal.pending.title":"Logo y\xFCklemeye haz\u0131r","Settings.application.customization.modal.pending.upload":"Logo y\xFCkle","Settings.application.customization.modal.tab.label":"Dosyalar\u0131 nas\u0131l y\xFCklemek istersin?","Settings.application.customization.modal.upload":"Logo y\xFCkle","Settings.application.customization.modal.upload.cta.browse":"Dosyalara g\xF6zat","Settings.application.customization.modal.upload.drag-drop":"Buraya s\xFCr\xFCkle b\u0131rak ya da","Settings.application.customization.modal.upload.error-format":"Desteklenmeyen bi\xE7im alg\u0131land\u0131 (desteklenen bi\xE7imler: jpeg, jpg, png, svg).","Settings.application.customization.modal.upload.error-network":"A\u011F hatas\u0131","Settings.application.customization.modal.upload.error-size":"Y\xFCklenen dosya \xE7ok b\xFCy\xFCk (G\xF6rsel boyutu s\u0131n\u0131r\u0131: {dimension}x{dimension}, Dosya boyutu s\u0131n\u0131r\u0131: {size}KB)","Settings.application.customization.modal.upload.file-validation":"G\xF6rsel boyutu s\u0131n\u0131r\u0131: {dimension}x{dimension}, Dosya boyutu s\u0131n\u0131r\u0131: {size}KB","Settings.application.customization.modal.upload.from-computer":"Bilgisayar\u0131mdan","Settings.application.customization.modal.upload.from-url":"URLden","Settings.application.customization.modal.upload.from-url.input-label":"URL","Settings.application.customization.modal.upload.next":"\u0130leri","Settings.application.description":"Y\xF6netim panelinin t\xFCm bilgileri","Settings.application.edition-title":"mevcut plan","Settings.application.get-help":"Yard\u0131m al","Settings.application.link-pricing":"T\xFCm \xFCcret planlar\u0131n\u0131 g\xF6r","Settings.application.link-upgrade":"Admin panelini y\xFCkselt","Settings.application.node-version":"node versiyonu","Settings.application.strapi-version":"strapi versiyonu","Settings.application.strapiVersion":"strapi versiyonu","Settings.application.title":"Ku\u015Fbak\u0131\u015F\u0131","Settings.error":"Hata","Settings.global":"Genel Ayarlar","Settings.permissions":"Y\xF6netim paneli","Settings.permissions.category":"{category} i\xE7in izin ayarlar\u0131","Settings.permissions.category.plugins":"{category} eklentisi i\xE7in izin ayarlar\u0131","Settings.permissions.conditions.anytime":"Her zaman","Settings.permissions.conditions.apply":"Uygula","Settings.permissions.conditions.can":"Yapabilir","Settings.permissions.conditions.conditions":"Ko\u015Fullar","Settings.permissions.conditions.links":"Ba\u011Flant\u0131lar","Settings.permissions.conditions.no-actions":"Ko\u015Fullar\u0131 belirtmeden \xF6nce eylemleri (olu\u015Ftur, oku, g\xFCncelle, ...) se\xE7melisin.","Settings.permissions.conditions.none-selected":"Her zaman","Settings.permissions.conditions.or":"YA DA","Settings.permissions.conditions.when":"Oldu\u011Funda","Settings.permissions.select-all-by-permission":"T\xFCm {label} izinlerini se\xE7","Settings.permissions.select-by-permission":"{label} iznini se\xE7","Settings.permissions.users.active":"Aktif","Settings.permissions.users.create":"Kullan\u0131c\u0131 davet et","Settings.permissions.users.email":"E-Posta","Settings.permissions.users.firstname":"Ad\u0131","Settings.permissions.users.form.sso":"SSO ile ba\u011Flan","Settings.permissions.users.form.sso.description":"A\xE7\u0131ld\u0131\u011F\u0131nda kullan\u0131c\u0131lar SSO ile giri\u015F yapabilir","Settings.permissions.users.inactive":"Pasif","Settings.permissions.users.lastname":"Soyad\u0131","Settings.permissions.users.listview.header.subtitle":"Strapi y\xF6netim paneline eri\u015Fimi olan kullan\u0131c\u0131lar","Settings.permissions.users.roles":"Roller","Settings.permissions.users.strapi-author":"Yazar","Settings.permissions.users.strapi-editor":"Edit\xF6r","Settings.permissions.users.strapi-super-admin":"S\xFCper Y\xF6netici","Settings.permissions.users.tabs.label":"Sekme \u0130zinleri","Settings.permissions.users.user-status":"Kullan\u0131c\u0131 durumu","Settings.permissions.users.username":"Kullan\u0131c\u0131 ad\u0131","Settings.profile.form.notify.data.loaded":"Profil verilerin y\xFCklendi","Settings.profile.form.section.experience.clear.select":"Se\xE7ilmi\u015F aray\xFCz dilini temizle","Settings.profile.form.section.experience.here":"buradan","Settings.profile.form.section.experience.interfaceLanguage":"Aray\xFCz dili","Settings.profile.form.section.experience.interfaceLanguage.hint":"Bu yaln\u0131zca senin aray\xFCz\xFCn\xFC se\xE7ilen dilde g\xF6sterecek.","Settings.profile.form.section.experience.interfaceLanguageHelp":"Tercih de\u011Fi\u015Fiklikleri yaln\u0131zca sana uygulan\u0131r. Daha fazla bilgi i\xE7in {here}.","Settings.profile.form.section.experience.mode.hint":"Aray\xFCz\xFCn\xFC se\xE7ilen modda g\xF6sterir.","Settings.profile.form.section.experience.mode.label":"Aray\xFCz modu","Settings.profile.form.section.experience.mode.option-label":"{name} modu","Settings.profile.form.section.experience.title":"Deneyim","Settings.profile.form.section.helmet.title":"Kullan\u0131c\u0131 profili","Settings.profile.form.section.profile.page.title":"Profil sayfas\u0131","Settings.roles.create.description":"Role verilen haklar\u0131 tan\u0131mla","Settings.roles.create.title":"Rol olu\u015Ftur","Settings.roles.created":"Rol olu\u015Fturuldu","Settings.roles.edit.title":"Rol\xFC d\xFCzenle","Settings.roles.form.button.users-with-role":"Bu rolde {number} kullan\u0131c\u0131","Settings.roles.form.created":"Olu\u015Fturuldu","Settings.roles.form.description":"Rol\xFCn ad\u0131 ve tan\u0131m\u0131","Settings.roles.form.permission.property-label":"{label} izinleri","Settings.roles.form.permissions.attributesPermissions":"Alanlar\u0131n izinleri","Settings.roles.form.permissions.create":"Olu\u015Ftur","Settings.roles.form.permissions.delete":"Sil","Settings.roles.form.permissions.publish":"Yay\u0131nla","Settings.roles.form.permissions.read":"Oku","Settings.roles.form.permissions.update":"G\xFCncelle","Settings.roles.list.button.add":"Yeni rol ekle","Settings.roles.list.description":"Rollerin listesi","Settings.roles.title.singular":"rol","Settings.sso.description":"Single Sign-On (SSO) \xF6zelli\u011Fini ayarla.","Settings.sso.form.registration.description":"SSO giri\u015Fi s\u0131ras\u0131nda hesab\u0131 olmayanlara yeni hesap olu\u015Ftur","Settings.sso.form.registration.label":"Otomatik kay\u0131t","Settings.sso.title":"Single Sign-On","Settings.webhooks.create":"Webhook olu\u015Ftur","Settings.webhooks.create.header":"Yeni ba\u015Fl\u0131k yarat","Settings.webhooks.created":"Webhook olu\u015Fturuldu","Settings.webhooks.event.publish-tooltip":"Bu eylem yaln\u0131zca Taslak/Yay\u0131mla sistemi a\xE7\u0131k oldu\u011Funda vard\u0131r","Settings.webhooks.events.create":"Olu\u015Ftur","Settings.webhooks.events.update":"G\xFCncelle","Settings.webhooks.form.events":"Etkinlikler","Settings.webhooks.form.headers":"Ba\u015Fl\u0131klar","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"{number} ba\u015Fl\u0131k sat\u0131r\u0131n\u0131 kald\u0131r","Settings.webhooks.key":"Anahtar","Settings.webhooks.list.button.add":"Yeni webhook ekle","Settings.webhooks.list.description":"POST de\u011Fi\u015Fiklikleri bildirimi al.","Settings.webhooks.list.empty.description":"\u0130lkini bu listeye ekleyin.","Settings.webhooks.list.empty.link":"D\xF6k\xFCmantasyonumuzu g\xF6r\xFCnt\xFCleyin","Settings.webhooks.list.empty.title":"Hen\xFCz bir webhook yok","Settings.webhooks.list.th.actions":"eylemler","Settings.webhooks.list.th.status":"durum","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooklar","Settings.webhooks.to.delete":"{webhooksToDeleteLength} dosya se\xE7ildi","Settings.webhooks.trigger":"Tetikleyici","Settings.webhooks.trigger.cancel":"Tetikleyiciyi iptal et","Settings.webhooks.trigger.pending":"Bekleniyor...","Settings.webhooks.trigger.save":"L\xFCtfen tetikleyiciyi kaydedin","Settings.webhooks.trigger.success":"Ba\u015Far\u0131l\u0131!","Settings.webhooks.trigger.success.label":"Tetikleyici ba\u015Far\u0131l\u0131","Settings.webhooks.trigger.test":"Test-tetikleyici","Settings.webhooks.trigger.title":"Tetikleyiciden \xF6nce kaydet","Settings.webhooks.value":"De\u011Fer","Usecase.back-end":"Arkay\xFCz Geli\u015Ftiricisi","Usecase.button.skip":"Bu soruyu atla","Usecase.content-creator":"\u0130\xE7erik \xDCreticisi","Usecase.front-end":"\xD6ny\xFCz Geli\u015Ftiricisi","Usecase.full-stack":"T\xFCmy\xFCz Geli\u015Ftiricisi","Usecase.input.work-type":"Ne i\u015Fle me\u015Fgulsun?","Usecase.notification.success.project-created":"Proje ba\u015Far\u0131yla olu\u015Fturuldu","Usecase.other":"Di\u011Fer","Usecase.title":"Biraz kendinden bahset",Username:s,Users:p,"Users & Permissions":"Kullan\u0131c\u0131lar & \u0130zinler","admin.pages.MarketPlacePage.filters.categories":"Kategoriler","admin.pages.MarketPlacePage.filters.categoriesSelected":"{count} kategori se\xE7ildi","admin.pages.MarketPlacePage.filters.collections":"Koleksiyonlar","admin.pages.MarketPlacePage.filters.collectionsSelected":"{count} koleksiyon se\xE7ildi","admin.pages.MarketPlacePage.helmet":"Pazaryeri - Eklentiler","admin.pages.MarketPlacePage.missingPlugin.description":"Bize arad\u0131\u011F\u0131n eklentiyi anlat ki biz de eklenti geli\u015Ftirici toplulu\u011Fumuzdaki ilham arayanlara iletelim!","admin.pages.MarketPlacePage.missingPlugin.title":"Arad\u0131\u011F\u0131n eklentiyi bulamad\u0131n m\u0131?","admin.pages.MarketPlacePage.offline.subtitle":"Strapi Market'e eri\u015Fmek i\xE7in Internet'e ba\u011Fl\u0131 olmal\u0131s\u0131n.","admin.pages.MarketPlacePage.offline.title":"\xC7evrimd\u0131\u015F\u0131s\u0131n","admin.pages.MarketPlacePage.plugin.copy":"Y\xFCkleme komutunu kopyala","admin.pages.MarketPlacePage.plugin.copy.success":"Y\xFCkleme komutu terminaline yap\u0131\u015Ft\u0131r\u0131lmak i\xE7in haz\u0131r","admin.pages.MarketPlacePage.plugin.downloads":"Bu eklenti haftada {downloadsCount} kez indirilmi\u015F","admin.pages.MarketPlacePage.plugin.githubStars":"Bu eklenti Github'da {starsCount} y\u0131ld\u0131z alm\u0131\u015F","admin.pages.MarketPlacePage.plugin.info":"Daha fazla","admin.pages.MarketPlacePage.plugin.info.label":"{pluginName} hakk\u0131nda daha fazla \xF6\u011Fren","admin.pages.MarketPlacePage.plugin.info.text":"Daha","admin.pages.MarketPlacePage.plugin.installed":"Y\xFCklendi","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"Strapi taraf\u0131ndan geli\u015Ftirildi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"Eklenti Strapi taraf\u0131ndan onayland\u0131","admin.pages.MarketPlacePage.plugin.version":`Strapi'yi "{strapiAppVersion}" versiyonundan "{versionRange}" versiyonuna y\xFCkselt`,"admin.pages.MarketPlacePage.plugin.version.null":'Y\xFCkl\xFC olan "{strapiAppVersion}" Strapi versiyonu ile uyumlulu\u011Fu do\u011Frulanam\u0131yor',"admin.pages.MarketPlacePage.plugins":"Eklentiler","admin.pages.MarketPlacePage.provider.downloads":"Bu sa\u011Flay\u0131c\u0131 haftada {downloadsCount} kez indirilmi\u015F","admin.pages.MarketPlacePage.provider.githubStars":"Bu sa\u011Flay\u0131c\u0131 Github'da {starsCount} y\u0131ld\u0131z alm\u0131\u015F","admin.pages.MarketPlacePage.providers":"Sa\u011Flay\u0131c\u0131lar","admin.pages.MarketPlacePage.search.clear":"Aramay\u0131 temizle","admin.pages.MarketPlacePage.search.empty":'"{target}" i\xE7in sonu\xE7 yok',"admin.pages.MarketPlacePage.search.placeholder":"Arama","admin.pages.MarketPlacePage.sort.alphabetical":"Alfabetik s\u0131raya diz","admin.pages.MarketPlacePage.sort.alphabetical.selected":"Alfabetik s\u0131ra","admin.pages.MarketPlacePage.sort.newest":"Yeniden eskiye diz","admin.pages.MarketPlacePage.sort.newest.selected":"Yeniden eskiye","admin.pages.MarketPlacePage.submit.plugin.link":"Eklenti g\xF6nder","admin.pages.MarketPlacePage.submit.provider.link":"Sa\u011Flay\u0131c\u0131 g\xF6nder","admin.pages.MarketPlacePage.subtitle":"Strapi'den daha fazlas\u0131n\u0131 al","admin.pages.MarketPlacePage.tab-group.label":"Strapi Eklenti ve Sa\u011Flay\u0131c\u0131lar\u0131",anErrorOccurred:m,"app.component.CopyToClipboard.label":"Panoya kopyala","app.component.search.label":"{target} i\xE7in arama yap","app.component.table.duplicate":"{target} kayd\u0131n\u0131 yinele","app.component.table.edit":"{target} kayd\u0131n\u0131 d\xFCzenle","app.component.table.select.one-entry":"{target} kayd\u0131n\u0131 se\xE7","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Strapi ve ekosistemi hakk\u0131ndaki son haberleri oku.","app.components.BlockLink.code":"Kod \xF6rnekleri","app.components.BlockLink.documentation.content":"Ba\u015Fl\u0131ca konseptleri, rehberleri ve talimatlar\u0131 ke\u015Ffet.","app.components.BlockLink.tutorial":"E\u011Fitimler","app.components.BlockLink.tutorial.content":"Strapi'yi kullanmak ve \xF6zelle\u015Ftirmek i\xE7in ad\u0131m ad\u0131m talimatlar\u0131 takip et.","app.components.Button.cancel":"\u0130ptal","app.components.Button.confirm":"Onayla","app.components.Button.reset":"S\u0131f\u0131rla","app.components.ComingSoonPage.comingSoon":"\xC7ok Yak\u0131nda","app.components.ConfirmDialog.title":"Onay","app.components.DownloadInfo.download":"\u0130ndirme devam ediyor...","app.components.DownloadInfo.text":"Bu birka\xE7 dakika s\xFCrebilir. Sabr\u0131n\u0131z i\xE7in te\u015Fekk\xFCrler.","app.components.EmptyAttributes.title":"Alan hen\xFCz yok","app.components.EmptyStateLayout.content-document":"\u0130\xE7erik bulunamad\u0131","app.components.EmptyStateLayout.content-permissions":"Bu i\xE7eri\u011Fe eri\u015Fim yetkiniz yok.","app.components.GuidedTour.CM.create.content":"<p>Buradaki t\xFCm i\xE7erikleri \u0130\xE7erik Y\xF6neticisi ile olu\u015Ftur ve y\xF6net.</p><p>\xD6r: Blog websitesi \xF6rne\u011Fini bir ad\u0131m daha \xF6teye g\xF6t\xFCr\xFCrsek, ki\u015Filer burada istedikleri gibi Makale yazabilir, kaydedip yay\u0131mlayabilir.</p><p>\u{1F4A1} Bir ipucu - Olu\u015Fturdu\u011Fun i\xE7eriklerde yay\u0131nla butonuna basmay\u0131 unutma.</p>","app.components.GuidedTour.CM.create.title":"\u26A1\uFE0F \u0130\xE7erik olu\u015Ftur","app.components.GuidedTour.CM.success.content":"<p>M\xFCthi\u015F! Son bir ad\u0131m kald\u0131.</p><b>\u{1F680} \u0130\xE7eri\u011Fi \xE7al\u0131\u015F\u0131rken g\xF6r</b>","app.components.GuidedTour.CM.success.cta.title":"APIyi test et","app.components.GuidedTour.CM.success.title":"Ad\u0131m 2: Tamamland\u0131 \u2705","app.components.GuidedTour.CTB.create.content":"<p>Koleksiyon tipleri birden \xE7ok girdiyi y\xF6netmene yard\u0131mc\u0131 olur. Tekil tipler tek bir girdiyi y\xF6netmek i\xE7in uygundur.</p> <p>\xD6r: Bir blog sayfas\u0131 i\xE7in, Makaleler Koleksiyon tipinde olabilecekken, Ana Sayfa Tekil tipte olacakt\u0131r.</p>","app.components.GuidedTour.CTB.create.cta.title":"Bir Koleksiyon tipi kur","app.components.GuidedTour.CTB.create.title":"\u{1F9E0} \u0130lk Koleksiyon tipini olu\u015Ftur","app.components.GuidedTour.CTB.success.content":"<p>\u0130yi gidiyorsun!</p><b>\u26A1\uFE0F D\xFCnya ile ne payla\u015Fmak isterdin?</b>","app.components.GuidedTour.CTB.success.title":"Ad\u0131m 1: Tamamland\u0131 \u2705","app.components.GuidedTour.apiTokens.create.content":"<p>Bir kimlik do\u011Frulama token\u0131 \xFCret ve yeni olu\u015Fturdu\u011Fun i\xE7eri\u011Fe ula\u015F.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"Bir API Token \xFCret","app.components.GuidedTour.apiTokens.create.title":"\u{1F680} \u0130\xE7eri\u011Fi \xE7al\u0131\u015F\u0131rken g\xF6r","app.components.GuidedTour.apiTokens.success.content":"<p>Bir HTTP istei\u011Fi yaparak i\xE7eri\u011Fi \xE7al\u0131\u015F\u0131rlen g\xF6r:</p><ul><li><p>URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>With the header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>\u0130\xE7eriklerle etkile\u015Fimin farkl\u0131 y\xF6ntemler i\xE7in <documentationLink>dok\xFCmantasyonu</documentationLink> oku.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Ana sayfaya geri d\xF6n","app.components.GuidedTour.apiTokens.success.title":"Ad\u0131m 3: Tamamland\u0131 \u2705","app.components.GuidedTour.create-content":"\u0130\xE7erik olu\u015Ftur","app.components.GuidedTour.home.CM.title":"\u26A1\uFE0F D\xFCnya ile ne payla\u015Fmak isterdin?","app.components.GuidedTour.home.CTB.cta.title":"\u0130\xE7erik tipi kurucusuna git","app.components.GuidedTour.home.CTB.title":"\u{1F9E0} \u0130\xE7erik yap\u0131s\u0131n\u0131 kur","app.components.GuidedTour.home.apiTokens.cta.title":"APIyi test et","app.components.GuidedTour.skip":"Turu atla","app.components.GuidedTour.title":"Ba\u015Flamak i\xE7in 3 ad\u0131m","app.components.HomePage.button.blog":"BLOG SAYFASINDA DAHA FAZLASINI G\xD6R\xDCN","app.components.HomePage.community":"Toplulu\u011Fumuza ula\u015F\u0131n","app.components.HomePage.community.content":"Farkl\u0131 kanallarda tak\u0131m \xFCyeleri, katk\u0131da bulunanlar ve geli\u015Ftiricilere ula\u015F\u0131n.","app.components.HomePage.create":"\u0130lk i\xE7erik tipini olu\u015Ftur","app.components.HomePage.welcome":"Panele ho\u015Fgeldiniz.","app.components.HomePage.welcome.again":"Ho\u015Fgeldiniz ","app.components.HomePage.welcomeBlock.content":"Sizi topluluk \xFCyelerinden biri olarak g\xF6rmekten mutluyuz. S\xFCrekli olarak geri bildirim alabilmemiz i\xE7in bize do\u011Frudan mesaj g\xF6ndermeye \xE7ekinmeyin ","app.components.HomePage.welcomeBlock.content.again":"Projenizde ilerleme kaydedece\u011Finizi umuyoruz... Strapi ile ilgili en yeni yenilikleri okumaktan \xE7ekinmeyin. \xDCr\xFCn\xFC geri bildirimlerinize g\xF6re geli\u015Ftirmek i\xE7in elimizden geleni yap\u0131yoruz.","app.components.HomePage.welcomeBlock.content.issues":"sorunlar","app.components.HomePage.welcomeBlock.content.raise":" yada y\xFCkselt ","app.components.ImgPreview.hint":"Dosyan\u0131z\u0131 bu alana s\xFCr\xFCkleyip b\u0131rak\u0131n ya da bir dosya y\xFCklemek i\xE7in {browse}","app.components.ImgPreview.hint.browse":"g\xF6zat","app.components.InputFile.newFile":"Yeni dosya ekle","app.components.InputFileDetails.open":"Yeni sekmede a\xE7","app.components.InputFileDetails.originalName":"Orjinal isim:","app.components.InputFileDetails.remove":"Bu dosyay\u0131 sil","app.components.InputFileDetails.size":"Boyut:","app.components.InstallPluginPage.Download.description":"Eklentiyi indirmek ve y\xFCklemek bir ka\xE7 saniye s\xFCrebilir.","app.components.InstallPluginPage.Download.title":"\u0130ndiriliyor...","app.components.InstallPluginPage.description":"Uygulaman\u0131z\u0131 rahatl\u0131kla geni\u015Fletin.","app.components.LeftMenu.collapse":"Men\xFCy\xFC ufalt","app.components.LeftMenu.expand":"Men\xFCy\xFC b\xFCy\xFCt","app.components.LeftMenu.general":"Genel","app.components.LeftMenu.logo.alt":"Uygulama logosu","app.components.LeftMenu.logout":"\xC7\u0131k\u0131\u015F","app.components.LeftMenu.navbrand.title":"Strapi Panosu","app.components.LeftMenu.navbrand.workplace":"\u0130\u015F Yeri","app.components.LeftMenu.plugins":"Eklentiler","app.components.LeftMenuFooter.help":"Yard\u0131m","app.components.LeftMenuFooter.poweredBy":"Gururla sunar ","app.components.LeftMenuLinkContainer.collectionTypes":"Koleksiyon Tipleri","app.components.LeftMenuLinkContainer.configuration":"Yap\u0131land\u0131rma","app.components.LeftMenuLinkContainer.general":"Genel","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Y\xFCklenen eklenti bulunmamaktad\u0131r.","app.components.LeftMenuLinkContainer.plugins":"Eklentiler","app.components.LeftMenuLinkContainer.singleTypes":"Tekil Tipler","app.components.ListPluginsPage.deletePlugin.description":"Eklentiyi kald\u0131rmak bir ka\xE7 saniye alabilir.","app.components.ListPluginsPage.deletePlugin.title":"Kald\u0131r\u0131l\u0131yor","app.components.ListPluginsPage.description":"Projedeki y\xFCklenen eklentiler.","app.components.ListPluginsPage.helmet.title":"Eklenti Listesi","app.components.Logout.logout":"\xC7\u0131k\u0131\u015F Yap","app.components.Logout.profile":"Profil","app.components.MarketplaceBanner":"Strapi Awesome'da projeni hayata ge\xE7irmek i\xE7in harika \u015Feyleri ve topluluk taraf\u0131ndan geli\u015Ftirilmi\u015F eklentileri ke\u015Ffet.","app.components.MarketplaceBanner.image.alt":"bir strapi roket logosu","app.components.MarketplaceBanner.link":"\u015Eimdi g\xF6zden ge\xE7ir","app.components.NotFoundPage.back":"Anasayfaya geri d\xF6n","app.components.NotFoundPage.description":"Bulunamad\u0131","app.components.Official":"Resmi","app.components.Onboarding.help.button":"Yard\u0131m butonu","app.components.Onboarding.label.completed":"% tamamland\u0131","app.components.Onboarding.title":"Ba\u015Flang\u0131\xE7 Videolaro","app.components.PluginCard.Button.label.download":"\u0130ndir","app.components.PluginCard.Button.label.install":"Zaten y\xFCklenmi\u015F","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"autoReload \xF6zelli\u011Fi aktif edilmeli. L\xFCtfen uygulamay\u0131 `yarn develop` ile ba\u015Flat\u0131n.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Anlad\u0131m!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"G\xFCvenlik nedeniyle bir eklenti yaln\u0131zca geli\u015Ftirme ortam\u0131nda indirilebilir.","app.components.PluginCard.PopUpWarning.install.impossible.title":"\u0130ndirme imkans\u0131z","app.components.PluginCard.compatible":"Uygulaman\u0131zla uyumlu","app.components.PluginCard.compatibleCommunity":"Toplulukla uyumlu","app.components.PluginCard.more-details":"Daha fazla detay","app.components.ToggleCheckbox.off-label":"Yanl\u0131\u015F","app.components.ToggleCheckbox.on-label":"Do\u011Fru","app.components.Users.ModalCreateBody.block-title.roles.description":"Bir kullan\u0131c\u0131 bir ya da daha fazla role sahip olabilir","app.components.listPlugins.button":"Yeni eklenti ekle","app.components.listPlugins.title.none":"Y\xFCklenen eklenti bulunmamaktad\u0131r.","app.components.listPluginsPage.deletePlugin.error":"Eklenti kald\u0131r\u0131l\u0131rken bir hata olu\u015Ftu","app.containers.App.notification.error.init":"API iste\u011Fi s\u0131ras\u0131nda bir hata olu\u015Ftu","app.links.configure-view":"Ekran\u0131 d\xFCzenle","app.page.not.found":"Haydaa! Arad\u0131\u011F\u0131n sayfay\u0131 bulam\u0131yor gibiyiz...","app.static.links.cheatsheet":"\xD6zetYard\u0131m","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Filtre ekle","app.utils.close-label":"Kapat","app.utils.defaultMessage":" ","app.utils.delete":"Sil","app.utils.duplicate":"Yinele","app.utils.edit":"D\xFCzenle","app.utils.errors.file-too-big.message":"Dosya \xE7ok b\xFCy\xFCk","app.utils.filter-value":"De\u011Feri filtrele","app.utils.filters":"Filtreler","app.utils.notify.data-loaded":"{target} y\xFCklendi","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Yay\u0131nla","app.utils.select-all":"T\xFCm\xFCn\xFC se\xE7","app.utils.select-field":"Alan\u0131 se\xE7","app.utils.select-filter":"Filtreyi se\xE7","app.utils.unpublish":"Yay\u0131ndan Kald\u0131r",clearLabel:c,"coming.soon":"Bu i\xE7erik \u015Fuanda d\xFCzenleniyor. Bir ka\xE7 hafta sonra yay\u0131nda olacak!","component.Input.error.validation.integer":"De\u011Fer say\u0131 olmal\u0131","components.AutoReloadBlocker.description":"Strapi'yi a\u015Fa\u011F\u0131daki komutlardan biri ile \xE7al\u0131\u015Ft\u0131r\u0131n:","components.AutoReloadBlocker.header":"Bu eklenti i\xE7in tekrar y\xFCkleme \xF6zelli\u011Fi gerekiyor.","components.ErrorBoundary.title":"Bir \u015Feyler yanl\u0131\u015F gitti...","components.FilterOptions.FILTER_TYPES.$contains":"i\xE7erir","components.FilterOptions.FILTER_TYPES.$containsi":"i\xE7erir (b\xFCy\xFCk/k\xFC\xE7\xFCk harfe duyars\u0131z)","components.FilterOptions.FILTER_TYPES.$endsWith":"ile biter","components.FilterOptions.FILTER_TYPES.$endsWithi":"ile biter (b\xFCy\xFCk/k\xFC\xE7\xFCk harfe duyars\u0131z)","components.FilterOptions.FILTER_TYPES.$eq":"e\u015Fittir","components.FilterOptions.FILTER_TYPES.$eqi":"e\u015Fittir (b\xFCy\xFCk/k\xFC\xE7\xFCk harfe duyars\u0131z)","components.FilterOptions.FILTER_TYPES.$gt":"b\xFCy\xFCkt\xFCr","components.FilterOptions.FILTER_TYPES.$gte":"b\xFCy\xFCk e\u015Fittir","components.FilterOptions.FILTER_TYPES.$lt":"k\xFC\xE7\xFCkt\xFCr","components.FilterOptions.FILTER_TYPES.$lte":"k\xFC\xE7\xFCk e\u015Fittir","components.FilterOptions.FILTER_TYPES.$ne":"e\u015Fit de\u011Fildir","components.FilterOptions.FILTER_TYPES.$nei":"e\u015Fit de\u011Fildir (b\xFCy\xFCk/k\xFC\xE7\xFCk harfe duyars\u0131z)","components.FilterOptions.FILTER_TYPES.$notContains":"i\xE7ermez","components.FilterOptions.FILTER_TYPES.$notContainsi":"i\xE7ermez (b\xFCy\xFCk/k\xFC\xE7\xFCk harfe duyars\u0131z)","components.FilterOptions.FILTER_TYPES.$notNull":"null de\u011Fildir","components.FilterOptions.FILTER_TYPES.$null":"null'dur","components.FilterOptions.FILTER_TYPES.$startsWith":"ba\u015Flar","components.FilterOptions.FILTER_TYPES.$startsWithi":"ba\u015Flar (b\xFCy\xFCk/k\xFC\xE7\xFCk harfe duyars\u0131z)","components.Input.error.attribute.key.taken":"Bu de\u011Fer zaten var.","components.Input.error.attribute.sameKeyAndName":"E\u015Fit olamaz","components.Input.error.attribute.taken":"Bu alan ismi zaten var.","components.Input.error.contain.lowercase":"\u015Eifre en az bir k\xFC\xE7\xFCk harf i\xE7ermelidir","components.Input.error.contain.number":"\u015Eifre en az bir say\u0131 i\xE7ermelidir","components.Input.error.contain.uppercase":"\u015Eifre en az bir b\xFCy\xFCk harf i\xE7ermelidir","components.Input.error.contentTypeName.taken":"Bu isim zaten var.","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"\u015Eifreler uyu\u015Fmuyor","components.Input.error.validation.email":"Ge\xE7ersiz e-posta adresi.","components.Input.error.validation.json":"Bu JSON bi\xE7imi ile e\u015Fle\u015Fmiyor","components.Input.error.validation.lowercase":"De\u011Ferin tamam\u0131 k\xFC\xE7\xFCk harf olmal\u0131d\u0131r","components.Input.error.validation.max":"De\u011Fer \xE7ok y\xFCksek {max}.","components.Input.error.validation.maxLength":"De\u011Fer \xE7ok uzun {max}.","components.Input.error.validation.min":"De\u011Fer \xE7ok az {min}.","components.Input.error.validation.minLength":"De\u011Fer \xE7ok k\u0131sa {min}.","components.Input.error.validation.minSupMax":"\xDCst\xFC olamaz","components.Input.error.validation.regex":"Regex ile e\u015Fle\u015Fmiyor.","components.Input.error.validation.required":"Zorunlu aland\u0131r.","components.Input.error.validation.unique":"De\u011Fer zaten kullan\u0131lm\u0131\u015F.","components.InputSelect.option.placeholder":"Buradan se\xE7in","components.ListRow.empty":"G\xF6sterilecek veri bulunmamaktad\u0131r.","components.NotAllowedInput.text":"Bu alan\u0131 g\xF6rmek i\xE7in yetkin yok","components.OverlayBlocker.description":"Sunucunun yeniden ba\u015Flat\u0131lmas\u0131 gereken bir \xF6zellik kullan\u0131yorsunuz. L\xFCtfen sunucu \xE7al\u0131\u015Fana kadar bekleyin.","components.OverlayBlocker.description.serverError":"Sunucu yeniden ba\u015Flat\u0131lmal\u0131, l\xFCtfen terminal \xFCzerinden loglar\u0131 kontrol edin.","components.OverlayBlocker.title":"Yeniden ba\u015Flat\u0131lmay\u0131 bekliyor...","components.OverlayBlocker.title.serverError":"Yeniden ba\u015Flatma beklendi\u011Finden uzun s\xFCr\xFCyor","components.PageFooter.select":"sayfa ba\u015F\u0131na kay\u0131t","components.ProductionBlocker.description":"G\xFCvenlik nedeniyle, bu eklentiyi di\u011Fer ortamlarda devre d\u0131\u015F\u0131 b\u0131rakmam\u0131z gerekir.","components.ProductionBlocker.header":"Bu eklenti yaln\u0131zca geli\u015Ftirme a\u015Famas\u0131nda mevcuttur.","components.Search.placeholder":"Arama...","components.TableHeader.sort":"\u015Euna g\xF6re diz: {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Markdown modu","components.Wysiwyg.ToggleMode.preview-mode":"\xD6nizleme modu","components.Wysiwyg.collapse":"Daralt","components.Wysiwyg.selectOptions.H1":"H1 ba\u015Fl\u0131k","components.Wysiwyg.selectOptions.H2":"H2 ba\u015Fl\u0131k","components.Wysiwyg.selectOptions.H3":"H3 ba\u015Fl\u0131k","components.Wysiwyg.selectOptions.H4":"H4 ba\u015Fl\u0131k","components.Wysiwyg.selectOptions.H5":"H5 ba\u015Fl\u0131k","components.Wysiwyg.selectOptions.H6":"H6 ba\u015Fl\u0131k","components.Wysiwyg.selectOptions.title":"Ba\u015Fl\u0131k ekle","components.WysiwygBottomControls.charactersIndicators":"karakter","components.WysiwygBottomControls.fullscreen":"Geni\u015Flet","components.WysiwygBottomControls.uploadFiles":"Dosyan\u0131z\u0131 bu alana s\xFCr\xFCkleyip b\u0131rak\u0131n ya da bir dosya y\xFCklemek i\xE7in {browse}","components.WysiwygBottomControls.uploadFiles.browse":"Bunlar\u0131 se\xE7","components.pagination.go-to":"{page} nolu sayfaya git","components.pagination.go-to-next":"Sonraki sayfaya git","components.pagination.go-to-previous":"\xD6nceki sayfaya git","components.pagination.remaining-links":"Ve {number} di\u011Fer ba\u011Flant\u0131","components.popUpWarning.button.cancel":"Hay\u0131r, iptal et","components.popUpWarning.button.confirm":"Evet, onayla","components.popUpWarning.message":"Bunu silmek istedi\u011Finizden emin misiniz?","components.popUpWarning.title":"L\xFCtfen onaylay\u0131n","content-manager.App.schemas.data-loaded":"\u015Eemalar ba\u015Far\u0131yla y\xFCklendi","content-manager.ListViewTable.relation-loaded":"\u0130li\u015Fkiler y\xFCklendi","content-manager.ListViewTable.relation-loading":"\u0130li\u015Fkiler y\xFCkleniyor","content-manager.ListViewTable.relation-more":"Bu ili\u015Fki g\xF6sterilenden daha \xE7ok kay\u0131t i\xE7eriyor","content-manager.EditRelations.title":"\u0130li\u015Fkili Data","content-manager.HeaderLayout.button.label-add-entry":"Yeni bir girdi olu\u015Ftur","content-manager.api.id":"API K\u0130ML\u0130K NO","content-manager.apiError.This attribute must be unique":"{field} benzersiz olmal\u0131","content-manager.components.AddFilterCTA.add":"Filtreler","content-manager.components.AddFilterCTA.hide":"Filtreler","content-manager.components.DragHandle-label":"S\xFCr\xFCkle","content-manager.components.DraggableAttr.edit":"D\xFCzenlemek i\xE7in t\u0131klay\u0131n","content-manager.components.DraggableCard.delete.field":"Sil: {item}","content-manager.components.DraggableCard.edit.field":"D\xFCzenle: {item}","content-manager.components.DraggableCard.move.field":"Ta\u015F\u0131: {item}","content-manager.components.ListViewTable.row-line":"\xF6\u011Fe sat\u0131r {number}","content-manager.components.DynamicZone.ComponentPicker-label":"Bir bile\u015Fen se\xE7","content-manager.components.DynamicZone.add-component":"{componentName}'e bir bile\u015Fen ekle","content-manager.components.DynamicZone.delete-label":"Sil: {name}","content-manager.components.DynamicZone.error-message":"Bile\u015Fen bir ya da daha fazla hata i\xE7eriyor","content-manager.components.DynamicZone.missing-components":"{number} eksik bile\u015Fen var","content-manager.components.DynamicZone.move-down-label":"Bile\u015Feni a\u015Fa\u011F\u0131 ta\u015F\u0131","content-manager.components.DynamicZone.move-up-label":"Bile\u015Feni yukar\u0131 ta\u015F\u0131","content-manager.components.DynamicZone.pick-compo":"Bir bile\u015Fen se\xE7","content-manager.components.DynamicZone.required":"Bile\u015Fen zorunlu","content-manager.components.EmptyAttributesBlock.button":"Ayarlar sayfas\u0131na git","content-manager.components.EmptyAttributesBlock.description":"Ayarlar\u0131n\u0131z\u0131 de\u011Fi\u015Ftirebilirsiniz","content-manager.components.FieldItem.linkToComponentLayout":"Bile\u015Fenin d\xFCzenini belirle","content-manager.components.FieldSelect.label":"Bir alan ekle","content-manager.components.FilterOptions.button.apply":"Uygula","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Uygula","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Hepsini temizle","content-manager.components.FiltersPickWrapper.PluginHeader.description":"Filtrelemek i\xE7in uygulanacak \u015Fartlar\u0131 ayarlay\u0131n","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Filtreler","content-manager.components.FiltersPickWrapper.hide":"Gizle","content-manager.components.LeftMenu.Search.label":"Bir i\xE7erik tipi ara","content-manager.components.LeftMenu.collection-types":"Koleksiyon Tipleri","content-manager.components.LeftMenu.single-types":"Tekil Tipler","content-manager.components.LimitSelect.itemsPerPage":"Sayfa ba\u015F\u0131","content-manager.components.NotAllowedInput.text":"Bu alan\u0131 g\xF6rmek i\xE7in iznin yok","content-manager.components.RepeatableComponent.error-message":"Bile\u015Fen ya da bile\u015Fenler bir ya da daha \xE7ok hata i\xE7eriyor","content-manager.components.Search.placeholder":"Kay\u0131t aramak i\xE7in...","content-manager.components.Select.draft-info-title":"Durum: Taslak","content-manager.components.Select.publish-info-title":"Durum: Yay\u0131nda","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"D\xFCzenleme g\xF6r\xFCn\xFCm\xFCn\xFC \xF6zelle\u015Ftir","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Liste g\xF6r\xFCn\xFCm\xFCn\xFCn ayarlar\u0131n\u0131 belirle.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"G\xF6r\xFCn\xFCm\xFC ayarla - {name}","content-manager.components.TableDelete.delete":"Hepsini sil","content-manager.components.TableDelete.deleteSelected":"Silme se\xE7ildi","content-manager.components.TableDelete.label":"{number} girdi se\xE7ildi","content-manager.components.TableEmpty.withFilters":"Uygulanan filtrelerle {contentType} yoktur...","content-manager.components.TableEmpty.withSearch":"Aramaya kar\u015F\u0131l\u0131k gelen {contentType} yoktur ({search})...","content-manager.components.TableEmpty.withoutFilter":"{contentType} yoktur...","content-manager.components.empty-repeatable":"Hen\xFCz bir girdi yok. A\u015Fa\u011F\u0131daki butona bas ve ekle.","content-manager.components.notification.info.maximum-requirement":"Alan say\u0131 s\u0131n\u0131r\u0131na ula\u015Ft\u0131n\u0131z.","content-manager.components.notification.info.minimum-requirement":"Minimum gerekleri sa\u011Flayacak bir alan eklendi","content-manager.components.repeatable.reorder.error":"Bile\u015Fenin alanlar\u0131n\u0131n s\u0131ras\u0131n\u0131 de\u011Fi\u015Ftirirken bir hata olu\u015Ftu. L\xFCtfen tekrar deneyin.","content-manager.components.reset-entry":"Girdiyi s\u0131f\u0131rla","content-manager.components.uid.apply":"uygula","content-manager.components.uid.available":"M\xFCsait","content-manager.components.uid.regenerate":"Yeniden \xDCret","content-manager.components.uid.suggested":"\xF6nerilen","content-manager.components.uid.unavailable":"M\xFCsait De\u011Fil","content-manager.containers.Edit.Link.Layout":"D\xFCzeni ayarla","content-manager.containers.Edit.Link.Model":"Koleksiyon-tipini d\xFCzenle","content-manager.containers.Edit.addAnItem":"Bir \xF6\u011Fe ekle...","content-manager.containers.Edit.clickToJump":"Kay\u0131ta atlamak i\xE7in t\u0131klay\u0131n","content-manager.containers.Edit.delete":"Sil","content-manager.containers.Edit.editing":"D\xFCzenleniyor...","content-manager.containers.Edit.information":"Bilgi","content-manager.containers.Edit.information.by":"Taraf\u0131ndan","content-manager.containers.Edit.information.created":"Olu\u015Fturuldu","content-manager.containers.Edit.information.draftVersion":"taslak versiyonu","content-manager.containers.Edit.information.editing":"D\xFCzenleniyor","content-manager.containers.Edit.information.lastUpdate":"Son g\xFCncelleme","content-manager.containers.Edit.information.publishedVersion":"yay\u0131nlanan versiyonu","content-manager.containers.Edit.pluginHeader.title.new":"Bir girdi olu\u015Ftur","content-manager.containers.Edit.reset":"Reset","content-manager.containers.Edit.returnList":"Listeye d\xF6n","content-manager.containers.Edit.seeDetails":"Detaylar","content-manager.containers.Edit.submit":"Kaydet","content-manager.containers.EditSettingsView.modal-form.edit-field":"Alan\u0131 d\xFCzenle","content-manager.containers.EditView.add.new-entry":"Bir girdi ekle","content-manager.containers.Home.introduction":"Giri\u015Flerinizi d\xFCzenlemek i\xE7in soldaki men\xFCdeki ilgili ba\u011Flant\u0131ya gidin. Bu eklentinin ayarlar\u0131 d\xFCzenlemek i\xE7in uygun bir yol bulunmamaktad\u0131r ve halen aktif geli\u015Ftirme a\u015Famas\u0131ndad\u0131r.","content-manager.containers.Home.pluginHeaderDescription":"G\xFC\xE7l\xFC ve g\xFCzel bir aray\xFCz arac\u0131l\u0131\u011F\u0131yla giri\u015Flerinizi y\xF6netin.","content-manager.containers.Home.pluginHeaderTitle":"\u0130\xE7erik Y\xF6netimi","content-manager.containers.List.draft":"Taslak","content-manager.containers.List.errorFetchRecords":"Hata","content-manager.containers.List.published":"Yay\u0131nland\u0131","content-manager.containers.ListPage.displayedFields":"G\xF6r\xFCnt\xFClenen Alanlar","content-manager.containers.ListPage.items":"{number} \xF6\u011Fe","content-manager.containers.ListPage.table-headers.publishedAt":"Durum","content-manager.containers.ListSettingsView.modal-form.edit-label":"D\xFCzenle: {fieldName}","content-manager.containers.SettingPage.add.field":"Bir ba\u015Fka Alan ekle","content-manager.containers.SettingPage.attributes":"Nitelik alanlar\u0131","content-manager.containers.SettingPage.attributes.description":"Niteliklerin s\u0131ras\u0131n\u0131 tan\u0131mlay\u0131n","content-manager.containers.SettingPage.editSettings.description":"Yerle\u015Fimi olu\u015Fturmak i\xE7in alanlar\u0131 s\xFCr\xFCkleyip b\u0131rak\u0131n","content-manager.containers.SettingPage.editSettings.entry.title":"Girdi ba\u015Fl\u0131\u011F\u0131","content-manager.containers.SettingPage.editSettings.entry.title.description":"Girdinin g\xF6r\xFCnt\xFClenen alan\u0131n\u0131 belirle","content-manager.containers.SettingPage.editSettings.relation-field.description":"D\xFCzenleme ve listeleme g\xF6r\xFCn\xFCm\xFCnde g\xF6r\xFCnt\xFClenecek alan\u0131 belirle","content-manager.containers.SettingPage.editSettings.title":"D\xFCzenle (ayarlar)","content-manager.containers.SettingPage.layout":"D\xFCzen","content-manager.containers.SettingPage.listSettings.description":"Bu koleksiyon tipi i\xE7in se\xE7enekleri d\xFCzenle","content-manager.containers.SettingPage.listSettings.title":"Liste (ayarlar)","content-manager.containers.SettingPage.pluginHeaderDescription":"Bu koleksiyon tipi i\xE7in \xF6zel ayarl\u0131 d\xFCzenle","content-manager.containers.SettingPage.settings":"Ayarlar","content-manager.containers.SettingPage.view":"G\xF6r\xFCnt\xFCle","content-manager.containers.SettingViewModel.pluginHeader.title":"\u0130\xE7erik Y\xF6neticisi - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Belirli ayarlar\u0131 yap\u0131land\u0131r\u0131n","content-manager.containers.SettingsPage.Block.contentType.title":"Koleksiyon Tipleri","content-manager.containers.SettingsPage.Block.generalSettings.description":"Koleksiyon tiplerin i\xE7in varsay\u0131lan se\xE7enekleri d\xFCzenle","content-manager.containers.SettingsPage.Block.generalSettings.title":"Genel","content-manager.containers.SettingsPage.pluginHeaderDescription":"T\xFCm Koleksiyon Tiplerin ve Gruplar\u0131n\u0131n ayarlar\u0131n\u0131 d\xFCzenle","content-manager.containers.SettingsView.list.subtitle":"Koleksiyon ve Gruplar\u0131n\u0131n d\xFCzen ve g\xF6r\xFCn\xFCmlerini d\xFCzenle","content-manager.containers.SettingsView.list.title":"D\xFCzenlemelerini g\xF6r\xFCnt\xFCle","content-manager.edit-settings-view.link-to-ctb.components":"Bile\u015Feni d\xFCzenle","content-manager.edit-settings-view.link-to-ctb.content-types":"\u0130\xE7erik tipini d\xFCzenle","content-manager.emptyAttributes.button":"Koleksiyon Tipi kurucusuna git","content-manager.emptyAttributes.description":"Koleksiyon Tipine ilk alan\u0131 ekle","content-manager.emptyAttributes.title":"Hen\xFCz bir alan yok","content-manager.error.attribute.key.taken":"Bu de\u011Fer zaten var.","content-manager.error.attribute.sameKeyAndName":"E\u015Fit olamaz","content-manager.error.attribute.taken":"Bu alan ismi zaten var.","content-manager.error.contentTypeName.taken":"Bu alan ismi zaten var.","content-manager.error.model.fetch":"Modellerin yap\u0131land\u0131rmas\u0131 getirilirken bir hata olu\u015Ftu.","content-manager.error.record.create":"Kay\u0131t olu\u015Fturulurken bir hata olu\u015Ftu.","content-manager.error.record.delete":"Kay\u0131t silinirken bir hata olu\u015Ftu.","content-manager.error.record.fetch":"Kay\u0131t getirilirken bir hata olu\u015Ftu.","content-manager.error.record.update":"Kay\u0131t g\xFCncelleme s\u0131ras\u0131nda bir hata olu\u015Ftu.","content-manager.error.records.count":"Say\u0131m kay\u0131tlar\u0131 getirilinceye kadar","content-manager.error.records.fetch":"Kay\u0131tlar getirilirken bir hata olu\u015Ftu.","content-manager.error.schema.generation":"\u015Eema olu\u015Fturma s\u0131ras\u0131nda bir hata olu\u015Ftu.","content-manager.error.validation.json":"Bu JSON bi\xE7imi ile e\u015Fle\u015Fmiyor","content-manager.error.validation.max":"De\u011Fer \xE7ok y\xFCksek.","content-manager.error.validation.maxLength":"De\u011Fer \xE7ok uzun.","content-manager.error.validation.min":"De\u011Fer \xE7ok az.","content-manager.error.validation.minLength":"De\u011Fer \xE7ok k\u0131sa.","content-manager.error.validation.minSupMax":"\xDCst\xFC olamaz","content-manager.error.validation.regex":"Regex ile e\u015Fle\u015Fmiyor.","content-manager.error.validation.required":"Zorunlu aland\u0131r.","content-manager.form.Input.bulkActions":"Toplu i\u015Flemleri etkinle\u015Ftir","content-manager.form.Input.defaultSort":"Varsay\u0131lan s\u0131ralama \xF6zelli\u011Fi","content-manager.form.Input.description":"A\xE7\u0131klama","content-manager.form.Input.description.placeholder":"Profildeki g\xF6r\xFCnen ad","content-manager.form.Input.editable":"D\xFCzenlenebilir alan","content-manager.form.Input.filters":"Filtreleri etkinle\u015Ftir","content-manager.form.Input.label":"Etiket","content-manager.form.Input.label.inputDescription":"Bu de\u011Fer, tablonun ba\u015F\u0131nda g\xF6r\xFCnt\xFClenen etiketi ge\xE7ersiz k\u0131lar","content-manager.form.Input.pageEntries":"Sayfa ba\u015F\u0131na kay\u0131tlar","content-manager.form.Input.placeholder":"Placeholder","content-manager.form.Input.placeholder.placeholder":"M\xFCthi\u015F de\u011Ferim","content-manager.form.Input.search":"Aramay\u0131 etkinle\u015Ftir","content-manager.form.Input.search.field":"Bu alanda aramay\u0131 etkinle\u015Ftir","content-manager.form.Input.sort.field":"Bu alana g\xF6re s\u0131ralamay\u0131 etkinle\u015Ftir","content-manager.form.Input.sort.order":"Varsay\u0131lan s\u0131ralama","content-manager.form.Input.wysiwyg":"WYSIWYG olarak g\xF6r\xFCnt\xFCle","content-manager.global.displayedFields":"G\xF6r\xFCnt\xFClenen Alanlar","content-manager.groups":"Gruplar","content-manager.groups.numbered":"Gruplar ({number})","content-manager.header.name":"\u0130\xE7erik","content-manager.link-to-ctb":"Modeli d\xFCzenle","content-manager.models":"Koleksiyon Tipleri","content-manager.models.numbered":"Koleksiyon Tipleri ({number})","content-manager.notification.error.displayedFields":"En az bir g\xF6r\xFCnt\xFClenen alana ihtiyac\u0131n\u0131z var","content-manager.notification.error.relationship.fetch":"\u0130li\u015Fki getirme s\u0131ras\u0131nda bir hata olu\u015Ftu.","content-manager.notification.info.SettingPage.disableSort":"S\u0131ralamaya izin verilen tek bir \xF6zelli\u011Fe sahip olman\u0131z gerekir","content-manager.notification.info.minimumFields":"En az bir alan g\xF6r\xFCnt\xFClenebilir olmal\u0131","content-manager.notification.upload.error":"Dosyalar\u0131n\u0131 y\xFCklerken bir hata olu\u015Ftu","content-manager.pageNotFound":"Sayfa bulunamad\u0131","content-manager.pages.ListView.header-subtitle":"{number} girdi bulundu","content-manager.pages.NoContentType.button":"\u0130lk \u0130\xE7erik Tipini olu\u015Ftur","content-manager.pages.NoContentType.text":"Hen\xFCz hi\xE7 i\xE7eri\u011Fin yok. Bir \u0130\xE7erik Tipi olu\u015Fturarak i\u015Fe ba\u015Flaman\u0131 \xF6neririz.","content-manager.permissions.not-allowed.create":"Belge olu\u015Fturma iznin yok","content-manager.permissions.not-allowed.update":"Bu belgeyi g\xF6rme iznin yok","content-manager.plugin.description.long":"Veritaban\u0131ndaki verileri g\xF6rmek, d\xFCzenlemek ve silmek i\xE7in h\u0131zl\u0131 bir yol.","content-manager.plugin.description.short":"Veritaban\u0131ndaki verileri g\xF6rmek, d\xFCzenlemek ve silmek i\xE7in h\u0131zl\u0131 bir yol.","content-manager.popUpWarning.warning.has-draft-relations.title":"Onay","content-manager.popUpWarning.warning.publish-question":"Hala yay\u0131nlamak istiyor musun?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Evet, yay\u0131nla","content-manager.popUpwarning.warning.has-draft-relations.message":"<b>{count} ili\u015Fki hen\xFCz yay\u0131nlanmad\u0131 ve bu beklenmedik bir davran\u0131\u015Fa yol a\xE7abilir.","content-manager.popover.display-relations.label":"\u0130li\u015Fkileri g\xF6ster","content-manager.select.currently.selected":"{count} tane se\xE7ili","content-manager.success.record.delete":"Silindi","content-manager.success.record.publish":"Yay\u0131nland\u0131","content-manager.success.record.save":"Kaydedildi","content-manager.success.record.unpublish":"Yay\u0131ndan Kald\u0131r\u0131ld\u0131","content-manager.utils.data-loaded":"{number} girdi ba\u015Far\u0131yla y\xFCklendi",dark:d,"form.button.continue":"Devam","form.button.done":"Tamam","global.actions":"Eylemler","global.back":"Geri","global.cancel":"\u0130ptal","global.change-password":"\u015Eifreyi de\u011Fi\u015Ftir","global.content-manager":"\u0130\xE7erik Y\xF6neticisi","global.continue":"Devam","global.delete":"Sil","global.delete-target":"Sil: {target}","global.description":"Tan\u0131m","global.details":"Detaylar","global.disabled":"Devred\u0131\u015F\u0131","global.documentation":"Dok\xFCmantasyon","global.enabled":"Etkin","global.finish":"Bitir","global.marketplace":"Pazaryeri","global.name":"\u0130sim","global.none":"Hi\xE7biri","global.password":"\u015Eifre","global.plugins":"Eklentiler","global.plugins.content-manager":"\u0130\xE7erik Y\xF6neticisi","global.plugins.content-manager.description":"Veritaban\u0131ndaki verileri g\xF6r\xFCnt\xFCleme, d\xFCzenleme ve silmenin kolay yolu.","global.plugins.content-type-builder":"\u0130\xE7erik Tipi Kurucusu","global.plugins.content-type-builder.description":"APInin veri yap\u0131s\u0131n\u0131 modelle. Sadece bir iki dakikada yeni alanlar ve ili\u015Fkiler olu\u015Ftur. Projendeki dosyalar otomatik olarak olu\u015Fturulur ve g\xFCncellenir.","global.plugins.documentation":"Dok\xFCmantasyon","global.plugins.documentation.description":"Bir OpenAPI Dok\xFCman\u0131 olu\u015Ftur ve SWAGGER UI ile APIni g\xF6rselle\u015Ftir.","global.plugins.email":"E-Posta","global.plugins.email.description":"Uygulaman\u0131 e-posta g\xF6nderecek \u015Fekilde ayarla.","global.plugins.graphql":"GraphQL","global.plugins.graphql.description":"Varsay\u0131lan API metodlar\u0131 ile bir GraphQL u\xE7 noktas\u0131 ekler.","global.plugins.i18n":"Uluslararas\u0131la\u015Ft\u0131rma","global.plugins.i18n.description":"Bu eklenti, hem Y\xF6netim paneli hem de API \xFCzerinden, farkl\u0131 dillerdeki i\xE7eri\u011Fi olu\u015Fturma, okuma ve g\xFCncelleme imkan\u0131 sa\u011Flar.","global.plugins.sentry":"Sentry","global.plugins.sentry.description":"Strapi hata olaylar\u0131n\u0131 Sentry'e ilet.","global.plugins.upload":"Ortam K\xFCt\xFCphanesi","global.plugins.upload.description":"Ortam dosyalar\u0131 y\xF6netimi.","global.plugins.users-permissions":"Roller ve \u0130zinler","global.plugins.users-permissions.description":"Servisinizi JWT'ye dayal\u0131 tam bir kimlik do\u011Frulama i\u015Flemi ile koruyun. Bu eklenti, kullan\u0131c\u0131 gruplar\u0131 aras\u0131ndaki izinleri y\xF6netmenize izin veren bir ACL stratejisiyle de gelir.","global.profile":"Profil","global.prompt.unsaved":"Bu sayfadan ayr\u0131lmak istedi\u011Finize emin misiniz? T\xFCm d\xFCzenlemeleriniz kaybolacak","global.reset-password":"\u015Eifreni s\u0131f\u0131rla","global.roles":"Roller","global.save":"Kaydet","global.search":"Arama","global.see-more":"Daha fazla","global.select":"Se\xE7","global.select-all-entries":"T\xFCm girdileri se\xE7","global.settings":"Ayarlar","global.type":"Tip","global.users":"Kullan\u0131c\u0131lar",light:g,"notification.contentType.relations.conflict":"\u0130\xE7erik tipinde \xE7ak\u0131\u015Fan ili\u015Fkiler var","notification.default.title":"Bilgi:","notification.error":"Bir hata olu\u015Ftu","notification.error.layout":"D\xFCzen al\u0131namad\u0131","notification.form.error.fields":"Form birden fazla hata i\xE7eriyor","notification.form.success.fields":"De\u011Fi\u015Fiklikler kaydedildi","notification.link-copied":"Ba\u011Flant\u0131 panoya kopyaland\u0131","notification.permission.not-allowed-read":"Bu dok\xFCman\u0131 g\xF6rme yetkin yok","notification.success.delete":"\xD6\u011Fe silindi","notification.success.saved":"Kaydedildi","notification.success.title":"Ba\u015Far\u0131l\u0131:","notification.success.apitokencreated":"API Token ba\u015Far\u0131yla olu\u015Fturuldu","notification.success.apitokenedited":"API Token ba\u015Far\u0131yla d\xFCzenlendi","notification.version.update.message":"Strapi'nin yeni versiyonu \xE7\u0131kt\u0131!","notification.warning.404":"404 - Bulunamad\u0131","notification.warning.title":"Dikkat:",or:F,"request.error.model.unknown":"Bu model bulunmamaktad\u0131r.",skipToContent:k,submit:y}}}]);
