(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[8595],{20920:(D,v,e)=>{var s=e(87864),r=e(80151),_=e(29327),P=e(52689),U=e(76299),h=Array.prototype,B=h.splice;function C(o,E,m,J){var b=J?_:r,I=-1,y=E.length,c=o;for(o===E&&(E=U(E)),m&&(c=s(o,P(m)));++I<y;)for(var S=0,L=E[I],G=m?m(L):L;(S=b(c,G,S,J))>-1;)c!==o&&B.call(c,S,1),B.call(o,S,1);return o}D.exports=C},28595:(D,v,e)=>{"use strict";e.d(v,{ProtectedEditView:()=>Ke,w:()=>ie});var s=e(92132),r=e(21272),_=e(57237),P=e(44604),U=e(60888),h=e(94061),B=e(85963),C=e(4181),o=e(90151),E=e(68074),m=e(4198),J=e(55356),b=e(38413),I=e(83997),y=e(30893),c=e(55506),S=e(61535),L=e(54894),G=e(17703),Y=e(43543),q=e(30740),F=e(99831),k=e(81590),le=e(89032),de=e(54514),ce=e(20415),_e=e(34542),Ee=e(61468),X=e(63891),pe=e(24092),H=e(12083),Pe=e(88761),ae=e(92173),He=e(15126),ze=e(63299),Qe=e(67014),Je=e(59080),be=e(79275),Xe=e(14718),Ze=e(82437),we=e(5790),qe=e(35223),es=e(5409),ss=e(74930),ts=e(2600),ns=e(48940),as=e(41286),os=e(56336),is=e(13426),rs=e(84624),ls=e(77965),ds=e(54257),cs=e(71210),_s=e(51187),Es=e(39404),ps=e(58692),Ps=e(501),us=e(57646),As=e(23120),gs=e(44414),hs=e(25962),ms=e(14664),Ms=e(42588),Os=e(90325),Ts=e(62785),Ds=e(87443),Cs=e(41032),ys=e(22957),Is=e(93179),xs=e(73055),fs=e(15747),vs=e(85306),Ls=e(26509),Rs=e(32058),Us=e(81185),js=e(82261);const ue=Y.n.injectEndpoints({endpoints:n=>({getPermissions:n.query({query:()=>"/admin/content-api/permissions",transformResponse:t=>t.data}),getRoutes:n.query({query:()=>"/admin/content-api/routes",transformResponse:t=>t.data})}),overrideExisting:!1}),{useGetPermissionsQuery:Ae,useGetRoutesQuery:ge}=ue,[he,me]=(0,le.q)("ApiTokenPermissionsContext"),Me=({children:n,...t})=>(0,s.jsx)(he,{...t,children:n}),ee=()=>me("useApiTokenPermissions"),Oe=({errors:n={},onChange:t,canEditInputs:a,isCreating:d,values:i={},apiToken:u={},onDispatch:l,setHasChangedPermissions:W})=>{const{formatMessage:M}=(0,L.A)(),j=({target:{value:x}})=>{W(!1),x==="full-access"&&l({type:"SELECT_ALL_ACTIONS"}),x==="read-only"&&l({type:"ON_CHANGE_READ_ONLY"})},z=[{value:"read-only",label:{id:"Settings.tokens.types.read-only",defaultMessage:"Read-only"}},{value:"full-access",label:{id:"Settings.tokens.types.full-access",defaultMessage:"Full access"}},{value:"custom",label:{id:"Settings.tokens.types.custom",defaultMessage:"Custom"}}];return(0,s.jsx)(h.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,s.jsxs)(I.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,s.jsx)(y.o,{variant:"delta",as:"h2",children:M({id:"global.details",defaultMessage:"Details"})}),(0,s.jsxs)(o.x,{gap:5,children:[(0,s.jsx)(E.E,{col:6,xs:12,children:(0,s.jsx)(k.T,{error:n.name,value:i.name,canEditInputs:a,onChange:t})},"name"),(0,s.jsx)(E.E,{col:6,xs:12,children:(0,s.jsx)(k.a,{error:n.description,value:i.description,canEditInputs:a,onChange:t})},"description"),(0,s.jsx)(E.E,{col:6,xs:12,children:(0,s.jsx)(k.L,{isCreating:d,error:n.lifespan,value:i.lifespan,onChange:t,token:u})},"lifespan"),(0,s.jsx)(E.E,{col:6,xs:12,children:(0,s.jsx)(k.b,{value:i.type,error:n.type,label:{id:"Settings.tokens.form.type",defaultMessage:"Token type"},onChange:x=>{j({target:{value:x}}),t({target:{name:"type",value:x}})},options:z,canEditInputs:a})},"type")]})]})})},Te=({apiTokenName:n=null})=>{const{formatMessage:t}=(0,L.A)();return(0,c.L4)(),(0,s.jsxs)(b.g,{"aria-busy":"true",children:[(0,s.jsx)(c.x7,{name:"API Tokens"}),(0,s.jsx)(J.Q,{primaryAction:(0,s.jsx)(B.$,{disabled:!0,startIcon:(0,s.jsx)(de.A,{}),type:"button",size:"L",children:t({id:"global.save",defaultMessage:"Save"})}),title:n||t({id:"Settings.apiTokens.createPage.title",defaultMessage:"Create API Token"})}),(0,s.jsx)(m.s,{children:(0,s.jsx)(c.Bl,{})})]})},De=n=>{switch(n){case"POST":return{text:"success600",border:"success200",background:"success100"};case"GET":return{text:"secondary600",border:"secondary200",background:"secondary100"};case"PUT":return{text:"warning600",border:"warning200",background:"warning100"};case"DELETE":return{text:"danger600",border:"danger200",background:"danger100"};default:return{text:"neutral600",border:"neutral200",background:"neutral100"}}},Ce=(0,X.Ay)(h.a)`
  margin: -1px;
  border-radius: ${({theme:n})=>n.spaces[1]} 0 0 ${({theme:n})=>n.spaces[1]};
`,ye=({route:n={handler:"Nocontroller.error",method:"GET",path:"/there-is-no-path"}})=>{const{formatMessage:t}=(0,L.A)(),{method:a,handler:d,path:i}=n,u=i?Ee(i.split("/")):[],[l="",W=""]=d?d.split("."):[],M=De(n.method);return(0,s.jsxs)(I.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,s.jsxs)(y.o,{variant:"delta",as:"h3",children:[t({id:"Settings.apiTokens.createPage.BoundRoute.title",defaultMessage:"Bound route to"}),"\xA0",(0,s.jsx)("span",{children:l}),(0,s.jsxs)(y.o,{variant:"delta",textColor:"primary600",children:[".",W]})]}),(0,s.jsxs)(I.s,{hasRadius:!0,background:"neutral0",borderColor:"neutral200",gap:0,children:[(0,s.jsx)(Ce,{background:M.background,borderColor:M.border,padding:2,children:(0,s.jsx)(y.o,{fontWeight:"bold",textColor:M.text,children:a})}),(0,s.jsx)(h.a,{paddingLeft:2,paddingRight:2,children:_e(u,j=>(0,s.jsxs)(y.o,{textColor:j.includes(":")?"neutral600":"neutral900",children:["/",j]},j))})]})]})},Ie=()=>{const{value:{selectedAction:n,routes:t}}=ee(),{formatMessage:a}=(0,L.A)(),d=n?.split(".")[0];return(0,s.jsx)(E.E,{col:5,background:"neutral150",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,style:{minHeight:"100%"},children:n?(0,s.jsx)(I.s,{direction:"column",alignItems:"stretch",gap:2,children:d&&d in t&&t[d].map(i=>i.config.auth?.scope?.includes(n)||i.handler===n?(0,s.jsx)(ye,{route:i},i.handler):null)}):(0,s.jsxs)(I.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,s.jsx)(y.o,{variant:"delta",as:"h3",children:a({id:"Settings.apiTokens.createPage.permissions.header.title",defaultMessage:"Advanced settings"})}),(0,s.jsx)(y.o,{as:"p",textColor:"neutral600",children:a({id:"Settings.apiTokens.createPage.permissions.header.hint",defaultMessage:"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"})})]})})},oe=(0,X.AH)`
  background: ${n=>n.theme.colors.primary100};
  svg {
    opacity: 1;
  }
`,xe=(0,X.Ay)(h.a)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  svg {
    opacity: 0;
    path {
      fill: ${n=>n.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${n=>n.isActive&&oe}
  &:hover {
    ${oe}
  }
`,fe=X.Ay.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({theme:n})=>n.colors.neutral150};
`,ve=({controllers:n=[],label:t,orderNumber:a=0,disabled:d=!1,onExpanded:i=()=>null,indexExpandendCollapsedContent:u=null})=>{const{value:{onChangeSelectAll:l,onChange:W,selectedActions:M,setSelectedAction:j,selectedAction:z}}=ee(),[x,Z]=r.useState(!1),{formatMessage:Q}=(0,L.A)(),O=()=>{Z(g=>!g),i(a)};r.useEffect(()=>{u!==null&&u!==a&&x&&Z(!1)},[u,a,x]);const re=g=>g===z;return(0,s.jsxs)(_.n,{expanded:x,onToggle:O,variant:a%2?"primary":"secondary",children:[(0,s.jsx)(U.P,{title:pe(t)}),(0,s.jsx)(P.u,{children:n?.map(g=>{const f=g.actions.every(T=>M.includes(T.actionId)),R=g.actions.some(T=>M.includes(T.actionId));return(0,s.jsxs)(h.a,{children:[(0,s.jsxs)(I.s,{justifyContent:"space-between",alignItems:"center",padding:4,children:[(0,s.jsx)(h.a,{paddingRight:4,children:(0,s.jsx)(y.o,{variant:"sigma",textColor:"neutral600",children:g?.controller})}),(0,s.jsx)(fe,{}),(0,s.jsx)(h.a,{paddingLeft:4,children:(0,s.jsx)(C.S,{value:f,indeterminate:!f&&R,onValueChange:()=>{l({target:{value:[...g.actions]}})},disabled:d,children:Q({id:"app.utils.select-all",defaultMessage:"Select all"})})})]}),(0,s.jsx)(o.x,{gap:4,padding:4,children:g?.actions&&g?.actions.map(T=>(0,s.jsx)(E.E,{col:6,children:(0,s.jsxs)(xe,{isActive:re(T.actionId),padding:2,hasRadius:!0,children:[(0,s.jsx)(C.S,{value:M.includes(T.actionId),name:T.actionId,onValueChange:()=>{W({target:{value:T.actionId}})},disabled:d,children:T.action}),(0,s.jsx)("button",{type:"button","data-testid":"action-cog",onClick:()=>j({target:{value:T.actionId}}),style:{display:"inline-flex",alignItems:"center"},children:(0,s.jsx)(ce.A,{})})]})},T.actionId))})]},`${t}.${g?.controller}`)})})]})},Le=({section:n=null,...t})=>{const[a,d]=r.useState(null),i=u=>d(u);return(0,s.jsx)(h.a,{padding:4,background:"neutral0",children:n&&n.map((u,l)=>(0,s.jsx)(ve,{label:u.label,controllers:u.controllers,orderNumber:l,indexExpandendCollapsedContent:a,onExpanded:i,...t},u.apiId))})},Re=({...n})=>{const{value:{data:t}}=ee(),{formatMessage:a}=(0,L.A)();return(0,s.jsxs)(o.x,{gap:0,shadow:"filterShadow",hasRadius:!0,background:"neutral0",children:[(0,s.jsxs)(E.E,{col:7,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:[(0,s.jsxs)(I.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,s.jsx)(y.o,{variant:"delta",as:"h2",children:a({id:"Settings.apiTokens.createPage.permissions.title",defaultMessage:"Permissions"})}),(0,s.jsx)(y.o,{as:"p",textColor:"neutral600",children:a({id:"Settings.apiTokens.createPage.permissions.description",defaultMessage:"Only actions bound by a route are listed below."})})]}),t?.permissions&&(0,s.jsx)(Le,{section:t?.permissions,...n})]}),(0,s.jsx)(Ie,{})]})},Ue=H.Ik().shape({name:H.Yj().max(100).required(c.iW.required),type:H.Yj().oneOf(["read-only","full-access","custom"]).required(c.iW.required),description:H.Yj().nullable(),lifespan:H.ai().integer().min(0).nullable().defined(c.iW.required)}),je=n=>{const t={allActionsIds:[],permissions:[]};return t.permissions=Object.entries(n).map(([a,d])=>({apiId:a,label:a.split("::")[1],controllers:Object.keys(d.controllers).map(i=>({controller:i,actions:i in d.controllers?d.controllers[i].map(u=>{const l=`${a}.${i}.${u}`;return a.includes("api::")&&t.allActionsIds.push(l),{action:u,actionId:l}}).flat():[]})).flat()})),t},Be={data:{allActionsIds:[],permissions:[]},routes:{},selectedAction:"",selectedActions:[]},We=(n,t)=>(0,Pe.Ay)(n,a=>{switch(t.type){case"ON_CHANGE":{a.selectedActions.includes(t.value)?ae(a.selectedActions,t.value):a.selectedActions.push(t.value);break}case"SELECT_ALL_IN_PERMISSION":{t.value.every(i=>a.selectedActions.includes(i.actionId))?t.value.forEach(i=>{ae(a.selectedActions,i.actionId)}):t.value.forEach(i=>{a.selectedActions.push(i.actionId)});break}case"SELECT_ALL_ACTIONS":{a.selectedActions=[...a.data.allActionsIds];break}case"ON_CHANGE_READ_ONLY":{const d=a.data.allActionsIds.filter(i=>i.includes("find")||i.includes("findOne"));a.selectedActions=[...d];break}case"UPDATE_PERMISSIONS_LAYOUT":{a.data=je(t.value);break}case"UPDATE_ROUTES":{a.routes={...t.value};break}case"UPDATE_PERMISSIONS":{a.selectedActions=[...t.value];break}case"SET_SELECTED_ACTION":{a.selectedAction=t.value;break}default:return a}}),ie=()=>{(0,c.L4)();const{formatMessage:n}=(0,L.A)(),t=(0,c.hN)(),{lockApp:a,unlockApp:d}=(0,c.MA)(),{state:i}=(0,G.zy)(),u=(0,Y.j)(p=>p.admin_app.permissions),[l,W]=r.useState(i?.apiToken?.accessKey?{...i.apiToken}:null),{trackUsage:M}=(0,c.z1)(),{setCurrentStep:j}=(0,c.Cx)(),{allowedActions:{canCreate:z,canUpdate:x,canRegenerate:Z}}=(0,c.ec)(u.settings?.["api-tokens"]),[Q,O]=r.useReducer(We,Be),g=(0,G.W5)("/settings/api-tokens/:id")?.params?.id,f=g==="create",{_unstableFormatAPIError:R,_unstableFormatValidationErrors:T}=(0,c.wq)(),Se=(0,G.W6)(),N=Ae(),$=ge();r.useEffect(()=>{N.error&&t({type:"warning",message:R(N.error)})},[N.error,R,t]),r.useEffect(()=>{$.error&&t({type:"warning",message:R($.error)})},[$.error,R,t]),r.useEffect(()=>{N.data&&O({type:"UPDATE_PERMISSIONS_LAYOUT",value:N.data})},[N.data]),r.useEffect(()=>{$.data&&O({type:"UPDATE_ROUTES",value:$.data})},[$.data]),r.useEffect(()=>{l&&(l.type==="read-only"&&O({type:"ON_CHANGE_READ_ONLY"}),l.type==="full-access"&&O({type:"SELECT_ALL_ACTIONS"}),l.type==="custom"&&O({type:"UPDATE_PERMISSIONS",value:l?.permissions}))},[l]),r.useEffect(()=>{M(f?"didAddTokenFromList":"didEditTokenFromList",{tokenType:F.A})},[f,M]);const{data:K,error:se,isLoading:ke}=(0,q.b)(g,{skip:!g||f||!!l});r.useEffect(()=>{se&&t({type:"warning",message:R(se)})},[se,R,t]),r.useEffect(()=>{K&&(W(K),K.type==="read-only"&&O({type:"ON_CHANGE_READ_ONLY"}),K.type==="full-access"&&O({type:"SELECT_ALL_ACTIONS"}),K.type==="custom"&&O({type:"UPDATE_PERMISSIONS",value:K?.permissions}))},[K]);const[Ne]=(0,q.c)(),[$e]=(0,q.d)(),Ve=async(p,V)=>{M(f?"willCreateToken":"willEditToken",{tokenType:F.A}),a();try{if(f){const A=await Ne({...p,lifespan:p?.lifespan||null,permissions:p.type==="custom"?Q.selectedActions:null});if("error"in A){(0,Y.x)(A.error)&&A.error.name==="ValidationError"?V.setErrors(T(A.error)):t({type:"warning",message:R(A.error)});return}t({type:"success",message:n({id:"notification.success.apitokencreated",defaultMessage:"API Token successfully created"})}),M("didCreateToken",{type:A.data.type,tokenType:F.A}),Se.replace(`/settings/api-tokens/${A.data.id}`,{apiToken:A.data}),j("apiTokens.success")}else{const A=await $e({id:g,name:p.name,description:p.description,type:p.type,permissions:p.type==="custom"?Q.selectedActions:null});if("error"in A){(0,Y.x)(A.error)&&A.error.name==="ValidationError"?V.setErrors(T(A.error)):t({type:"warning",message:R(A.error)});return}t({type:"success",message:n({id:"notification.success.apitokenedited",defaultMessage:"API Token successfully edited"})}),M("didEditToken",{type:A.data.type,tokenType:F.A})}}catch{t({type:"warning",message:{id:"notification.error",defaultMessage:"Something went wrong"}})}finally{d()}},[Ge,te]=r.useState(!1),Ye={...Q,onChange:({target:{value:p}})=>{te(!0),O({type:"ON_CHANGE",value:p})},onChangeSelectAll:({target:{value:p}})=>{te(!0),O({type:"SELECT_ALL_IN_PERMISSION",value:p})},setSelectedAction:({target:{value:p}})=>{O({type:"SET_SELECTED_ACTION",value:p})}},ne=x&&!f||z&&f;return ke?(0,s.jsx)(Te,{apiTokenName:l?.name}):(0,s.jsx)(Me,{value:Ye,children:(0,s.jsxs)(b.g,{children:[(0,s.jsx)(c.x7,{name:"API Tokens"}),(0,s.jsx)(S.l1,{validationSchema:Ue,validateOnChange:!1,initialValues:{name:l?.name||"",description:l?.description||"",type:l?.type,lifespan:l?.lifespan},enableReinitialize:!0,onSubmit:(p,V)=>Ve(p,V),children:({errors:p,handleChange:V,isSubmitting:A,values:w,setFieldValue:Fe})=>(Ge&&w?.type!=="custom"&&Fe("type","custom"),(0,s.jsxs)(c.lV,{children:[(0,s.jsx)(k.F,{backUrl:"/settings/api-tokens",title:{id:"Settings.apiTokens.createPage.title",defaultMessage:"Create API Token"},token:l,setToken:W,canEditInputs:ne,canRegenerate:Z,isSubmitting:A,regenerateUrl:"/admin/api-tokens/"}),(0,s.jsx)(m.s,{children:(0,s.jsxs)(I.s,{direction:"column",alignItems:"stretch",gap:6,children:[Boolean(l?.name)&&(0,s.jsx)(k.c,{token:l?.accessKey,tokenType:F.A}),(0,s.jsx)(Oe,{errors:p,onChange:V,canEditInputs:ne,isCreating:f,values:w,apiToken:l,onDispatch:O,setHasChangedPermissions:te}),(0,s.jsx)(Re,{disabled:!ne||w?.type==="read-only"||w?.type==="full-access"})]})})]}))})]})})},Ke=()=>{const n=(0,Y.j)(t=>t.admin_app.permissions.settings?.["api-tokens"].read);return(0,s.jsx)(c.kz,{permissions:n,children:(0,s.jsx)(ie,{})})}},29327:D=>{function v(e,s,r,_){for(var P=r-1,U=e.length;++P<U;)if(_(e[P],s))return P;return-1}D.exports=v},30740:(D,v,e)=>{"use strict";e.d(v,{a:()=>h,b:()=>P,c:()=>U,d:()=>B,u:()=>_});var s=e(43543);const r=s.n.injectEndpoints({endpoints:C=>({getAPITokens:C.query({query:()=>"/admin/api-tokens",transformResponse:o=>o.data,providesTags:(o,E)=>[...o?.map(({id:m})=>({type:"ApiToken",id:m}))??[],{type:"ApiToken",id:"LIST"}]}),getAPIToken:C.query({query:o=>`/admin/api-tokens/${o}`,transformResponse:o=>o.data,providesTags:(o,E,m)=>[{type:"ApiToken",id:m}]}),createAPIToken:C.mutation({query:o=>({url:"/admin/api-tokens",method:"POST",data:o}),transformResponse:o=>o.data,invalidatesTags:[{type:"ApiToken",id:"LIST"}]}),deleteAPIToken:C.mutation({query:o=>({url:`/admin/api-tokens/${o}`,method:"DELETE"}),transformResponse:o=>o.data,invalidatesTags:(o,E,m)=>[{type:"ApiToken",id:m}]}),updateAPIToken:C.mutation({query:({id:o,...E})=>({url:`/admin/api-tokens/${o}`,method:"PUT",data:E}),transformResponse:o=>o.data,invalidatesTags:(o,E,{id:m})=>[{type:"ApiToken",id:m}]})}),overrideExisting:!1}),{useGetAPITokensQuery:_,useGetAPITokenQuery:P,useCreateAPITokenMutation:U,useDeleteAPITokenMutation:h,useUpdateAPITokenMutation:B}=r},34542:(D,v,e)=>{var s=e(87864),r=e(45353),_=e(29884),P=e(82261);function U(h,B){var C=P(h)?s:_;return C(h,r(B,3))}D.exports=U},61468:(D,v,e)=>{var s=e(52196);function r(_){var P=_==null?0:_.length;return P?s(_,1,P):[]}D.exports=r},76610:(D,v,e)=>{var s=e(20920);function r(_,P){return _&&_.length&&P&&P.length?s(_,P):_}D.exports=r},92173:(D,v,e)=>{var s=e(39226),r=e(76610),_=s(r);D.exports=_}}]);
