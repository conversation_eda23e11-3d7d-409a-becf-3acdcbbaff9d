"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[1314],{51314:(J,A,s)=>{s.r(A),s.d(A,{ProfilePage:()=>ts});var e=s(92132),d=s(21272),P=s(94061),R=s(85963),v=s(88875),L=s(90151),E=s(68074),F=s(4198),N=s(55356),X=s(11273),V=s(38413),O=s(83997),D=s(48323),H=s(7537),C=s(30893),i=s(55506),b=s(54514),j=s(83925),W=s(93337),k=s(61535),q=s(39404),z=s(14718),I=s(54894),$=s(63891),B=s(12083),l=s(43543),ss=s(66248),es=s(97146),Ds=s(15126),ps=s(63299),us=s(67014),As=s(59080),vs=s(79275),Ls=s(82437),Cs=s(5790),Is=s(35223),Ts=s(5409),xs=s(74930),Rs=s(2600),js=s(48940),Ws=s(41286),Bs=s(56336),Us=s(13426),Ks=s(84624),ys=s(77965),ws=s(54257),Ss=s(71210),Ys=s(51187),Fs=s(58692),Ns=s(501),Vs=s(57646),Hs=s(23120),zs=s(44414),$s=s(25962),Zs=s(14664),Qs=s(42588),Gs=s(90325),Js=s(62785),Xs=s(87443),bs=s(41032),ks=s(22957),qs=s(93179),se=s(73055),ee=s(15747),ae=s(85306),te=s(26509),oe=s(32058),re=s(81185),ne=s(82261),ie=s(55151),de=s(79077);const as=B.Ik().shape({...es.C,currentPassword:B.Yj().when(["password","confirmPassword"],(t,o,n)=>t||o?n.required(i.iW.required):n),preferedLanguage:B.Yj().nullable()}),ts=()=>{const t=(0,l.j)(c=>c.admin_app.language.localeNames),{formatMessage:o}=(0,I.A)(),{trackUsage:n}=(0,i.z1)(),a=(0,i.hN)(),{lockApp:g,unlockApp:r}=(0,i.MA)(),{notifyStatus:M}=(0,X.W)(),K=(0,l.j)(c=>c.admin_app.theme.currentTheme),p=(0,l.s)(),{_unstableFormatValidationErrors:y,_unstableFormatAPIError:h}=(0,i.wq)();(0,i.L4)();const{user:m}=(0,l.a)("ProfilePage");d.useEffect(()=>{m?M(o({id:"Settings.profile.form.notify.data.loaded",defaultMessage:"Your profile data has been loaded"})):a({type:"warning",message:{id:"notification.error",defaultMessage:"An error occured"}})},[o,M,a,m]);const[is,{isLoading:ds}]=(0,l.t)(),{isLoading:ls,data:_s,error:Q}=(0,l.v)(void 0,{skip:!(window.strapi.isEE&&window.strapi.features.isEnabled("sso"))});d.useEffect(()=>{Q&&a({type:"warning",message:{id:"Settings.permissions.users.sso.provider.error"}})},[Q,a]);const Es=async(c,{setErrors:w})=>{g();const{confirmPassword:S,currentTheme:u,...T}=c;let f=T;if(f.password===""){const{password:G,currentPassword:x,...Y}=f;f=Y}const _=await is(f);"data"in _&&(p((0,l.w)(u)),n("didChangeMode",{newMode:u}),a({type:"success",message:{id:"notification.success.saved",defaultMessage:"Saved"}})),"error"in _&&((0,l.x)(_.error)&&(_.error.name==="ValidationError"||_.error.message==="ValidationError")?w(y(_.error)):(0,l.x)(_.error)?a({type:"warning",message:h(_.error)}):a({type:"warning",message:{id:"notification.error",defaultMessage:"An error occured"}})),r?.()};if(ls)return(0,e.jsxs)(V.g,{"aria-busy":"true",children:[(0,e.jsx)(z.m,{title:o({id:"Settings.profile.form.section.helmet.title",defaultMessage:"User profile"})}),(0,e.jsx)(N.Q,{title:o({id:"Settings.profile.form.section.profile.page.title",defaultMessage:"Profile page"})}),(0,e.jsx)(F.s,{children:(0,e.jsx)(i.Bl,{})})]});const ms=_s?.isSSOLocked??!1,{email:Ms,firstname:cs,lastname:Ps,username:gs,preferedLanguage:hs}=m??{},fs={email:Ms,firstname:cs,lastname:Ps,username:gs,preferedLanguage:hs,currentTheme:K,confirmPassword:"",password:""};return(0,e.jsxs)(V.g,{"aria-busy":ds,children:[(0,e.jsx)(z.m,{title:o({id:"Settings.profile.form.section.helmet.title",defaultMessage:"User profile"})}),(0,e.jsx)(k.l1,{onSubmit:Es,initialValues:fs,validateOnChange:!1,validationSchema:as,enableReinitialize:!0,children:({errors:c,values:{email:w,firstname:S,lastname:u,username:T,preferedLanguage:f,currentTheme:_,...G},handleChange:x,isSubmitting:Y,dirty:Os})=>(0,e.jsxs)(i.lV,{children:[(0,e.jsx)(N.Q,{title:T||(0,ss.g)(S??"",u),primaryAction:(0,e.jsx)(R.$,{startIcon:(0,e.jsx)(b.A,{}),loading:Y,type:"submit",disabled:!Os,children:o({id:"global.save",defaultMessage:"Save"})})}),(0,e.jsx)(P.a,{paddingBottom:10,children:(0,e.jsx)(F.s,{children:(0,e.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:6,children:[(0,e.jsx)(ns,{errors:c,onChange:x,values:{firstname:S,lastname:u,username:T,email:w}}),!ms&&(0,e.jsx)(os,{errors:c,onChange:x,values:G}),(0,e.jsx)(rs,{onChange:x,values:{preferedLanguage:f,currentTheme:_},localeNames:t})]})})})]})})]})},os=({errors:t,onChange:o,values:n})=>{const{formatMessage:a}=(0,I.A)(),[g,r]=d.useState(!1),[M,K]=d.useState(!1),[p,y]=d.useState(!1);return(0,e.jsx)(P.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,e.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,e.jsx)(C.o,{variant:"delta",as:"h2",children:a({id:"global.change-password",defaultMessage:"Change password"})}),(0,e.jsx)(L.x,{gap:5,children:(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsx)(H.k,{error:t.currentPassword?a({id:t.currentPassword,defaultMessage:t.currentPassword}):"",onChange:o,value:n.currentPassword,label:a({id:"Auth.form.currentPassword.label",defaultMessage:"Current Password"}),name:"currentPassword",type:g?"text":"password",endAction:(0,e.jsx)(U,{onClick:h=>{h.stopPropagation(),r(m=>!m)},label:a(g?{id:"Auth.form.password.show-password",defaultMessage:"Show password"}:{id:"Auth.form.password.hide-password",defaultMessage:"Hide password"}),children:g?(0,e.jsx)(j.A,{}):(0,e.jsx)(W.A,{})})})})}),(0,e.jsxs)(L.x,{gap:5,children:[(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsx)(Z,{error:t.password?a({id:t.password,defaultMessage:t.password}):"",onChange:o,value:n.password,label:a({id:"global.password",defaultMessage:"Password"}),name:"password",type:M?"text":"password",autoComplete:"new-password",endAction:(0,e.jsx)(U,{onClick:h=>{h.stopPropagation(),K(m=>!m)},label:a(M?{id:"Auth.form.password.show-password",defaultMessage:"Show password"}:{id:"Auth.form.password.hide-password",defaultMessage:"Hide password"}),children:M?(0,e.jsx)(j.A,{}):(0,e.jsx)(W.A,{})})})}),(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsx)(Z,{error:t.confirmPassword?a({id:t.confirmPassword,defaultMessage:t.confirmPassword}):"",onChange:o,value:n.confirmPassword,label:a({id:"Auth.form.confirmPassword.label",defaultMessage:"Confirm Password"}),name:"confirmPassword",type:p?"text":"password",autoComplete:"new-password",endAction:(0,e.jsx)(U,{onClick:h=>{h.stopPropagation(),y(m=>!m)},label:a(p?{id:"Auth.form.password.show-password",defaultMessage:"Show password"}:{id:"Auth.form.password.hide-password",defaultMessage:"Hide password"}),children:p?(0,e.jsx)(j.A,{}):(0,e.jsx)(W.A,{})})})})]})]})})},Z=(0,$.Ay)(H.k)`
  ::-ms-reveal {
    display: none;
  }
`,U=(0,$.Ay)(v.T)`
  svg {
    height: ${(0,i.a8)(16)};
    width: ${(0,i.a8)(16)};
    path {
      fill: ${({theme:t})=>t.colors.neutral600};
    }
  }
`,rs=({onChange:t,values:o,localeNames:n})=>{const{formatMessage:a}=(0,I.A)(),g=(0,l.j)(r=>r.admin_app.theme.availableThemes);return(0,e.jsx)(P.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,e.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,e.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:1,children:[(0,e.jsx)(C.o,{variant:"delta",as:"h2",children:a({id:"Settings.profile.form.section.experience.title",defaultMessage:"Experience"})}),(0,e.jsx)(C.o,{children:a({id:"Settings.profile.form.section.experience.interfaceLanguageHelp",defaultMessage:"Preference changes will apply only to you. More information is available {here}."},{here:(0,e.jsx)(P.a,{as:"a",color:"primary600",target:"_blank",rel:"noopener noreferrer",href:"https://docs.strapi.io/developer-docs/latest/development/admin-customization.html#locales",children:a({id:"Settings.profile.form.section.experience.here",defaultMessage:"here"})})})})]}),(0,e.jsxs)(L.x,{gap:5,children:[(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsx)(D.Z,{label:a({id:"Settings.profile.form.section.experience.interfaceLanguage",defaultMessage:"Interface language"}),placeholder:a({id:"global.select",defaultMessage:"Select"}),hint:a({id:"Settings.profile.form.section.experience.interfaceLanguage.hint",defaultMessage:"This will only display your own interface in the chosen language."}),onClear:()=>{t({target:{name:"preferedLanguage",value:null}})},clearLabel:a({id:"Settings.profile.form.section.experience.clear.select",defaultMessage:"Clear the interface language selected"}),value:o.preferedLanguage,onChange:r=>{t({target:{name:"preferedLanguage",value:r}})},children:Object.entries(n).map(([r,M])=>(0,e.jsx)(D.eY,{value:r,children:M},r))})}),(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsxs)(D.Z,{label:a({id:"Settings.profile.form.section.experience.mode.label",defaultMessage:"Interface mode"}),placeholder:a({id:"components.Select.placeholder",defaultMessage:"Select"}),hint:a({id:"Settings.profile.form.section.experience.mode.hint",defaultMessage:"Displays your interface in the chosen mode."}),value:o.currentTheme,onChange:r=>{t({target:{name:"currentTheme",value:r}})},children:[(0,e.jsx)(D.eY,{value:"system",children:a({id:"Settings.profile.form.section.experience.mode.option-system-label",defaultMessage:"Use system settings"})}),g.map(r=>(0,e.jsx)(D.eY,{value:r,children:a({id:"Settings.profile.form.section.experience.mode.option-label",defaultMessage:"{name} mode"},{name:a({id:r,defaultMessage:q(r)})})},r))]})})]})]})})},ns=({errors:t,onChange:o,values:n})=>{const{formatMessage:a}=(0,I.A)();return(0,e.jsx)(P.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,e.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,e.jsx)(C.o,{variant:"delta",as:"h2",children:a({id:"global.profile",defaultMessage:"Profile"})}),(0,e.jsxs)(L.x,{gap:5,children:[(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsx)(i.ah,{intlLabel:{id:"Auth.form.firstname.label",defaultMessage:"First name"},error:t.firstname,onChange:o,value:n.firstname,type:"text",name:"firstname",required:!0})}),(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsx)(i.ah,{intlLabel:{id:"Auth.form.lastname.label",defaultMessage:"Last name"},error:t.lastname,onChange:o,value:n.lastname,type:"text",name:"lastname"})}),(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsx)(i.ah,{intlLabel:{id:"Auth.form.email.label",defaultMessage:"Email"},error:t.email,onChange:o,value:n.email,type:"email",name:"email",required:!0})}),(0,e.jsx)(E.E,{s:12,col:6,children:(0,e.jsx)(i.ah,{intlLabel:{id:"Auth.form.username.label",defaultMessage:"Username"},error:t.username,onChange:o,value:n.username,type:"text",name:"username"})})]})]})})}},97146:(J,A,s)=>{s.d(A,{C:()=>P});var e=s(55506),d=s(12083);const P={firstname:d.Yj().trim().required(e.iW.required),lastname:d.Yj(),email:d.Yj().email(e.iW.email).lowercase().required(e.iW.required),username:d.Yj().nullable(),password:d.Yj().min(8,e.iW.minLength).matches(/[a-z]/,"components.Input.error.contain.lowercase").matches(/[A-Z]/,"components.Input.error.contain.uppercase").matches(/\d/,"components.Input.error.contain.number"),confirmPassword:d.Yj().min(8,e.iW.minLength).oneOf([d.KR("password"),null],"components.Input.error.password.noMatch").when("password",(R,v)=>R?v.required(e.iW.required):v)}}}]);
