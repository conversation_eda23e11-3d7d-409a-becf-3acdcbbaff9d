"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3854],{83854:(Te,S,s)=>{s.r(S),s.d(S,{ProvidersPage:()=>H,default:()=>fe});var e=s(92132),y=s(21272),z=s(85963),w=s(90151),Z=s(68074),ee=s(88353),se=s(42455),ie=s(4198),te=s(55356),ae=s(38413),re=s(61485),ne=s(99248),de=s(67030),le=s(6239),oe=s(83997),ue=s(35513),ce=s(25641),_e=s(26127),q=s(90361),P=s(33363),me=s(7537),ge=s(7441),b=s(30893),pe=s(98765),r=s(55506),Ee=s(41909),he=s(39404),x=s(54894),T=s(74930),Pe=s(37679),Me=s(60043),ve=s(61535),i=s(33544),n=s(99105),Re=s(77965),t=s(12083);const R=({description:l,disabled:M,intlLabel:p,error:_,name:c,onChange:v,placeholder:d,providerToEditName:E,type:g,value:u})=>{const{formatMessage:o}=(0,x.A)(),h=c==="noName"?`${window.strapi.backendURL}/api/connect/${E}/callback`:u,f=o({id:p.id,defaultMessage:p.defaultMessage},{provider:E,...p.values}),m=l?o({id:l.id,defaultMessage:l.defaultMessage},{provider:E,...l.values}):"";if(g==="bool")return(0,e.jsx)(ge.l,{"aria-label":c,checked:u,disabled:M,hint:m,label:f,name:c,offLabel:o({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:o({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:D=>{v({target:{name:c,value:D.target.checked}})}});const I=d?o({id:d.id,defaultMessage:d.defaultMessage},{...d.values}):"",B=_?o({id:_,defaultMessage:_}):"";return(0,e.jsx)(me.k,{"aria-label":c,disabled:M,error:B,label:f,name:c,onChange:v,placeholder:I,type:g,value:h})};R.defaultProps={description:null,disabled:!1,error:"",placeholder:null,value:""},R.propTypes={description:i.shape({id:i.string.isRequired,defaultMessage:i.string.isRequired,values:i.object}),disabled:i.bool,error:i.string,intlLabel:i.shape({id:i.string.isRequired,defaultMessage:i.string.isRequired,values:i.object}).isRequired,name:i.string.isRequired,onChange:i.func.isRequired,placeholder:i.shape({id:i.string.isRequired,defaultMessage:i.string.isRequired,values:i.object}),providerToEditName:i.string.isRequired,type:i.string.isRequired,value:i.oneOfType([i.bool,i.string])};const C=({headerBreadcrumbs:l,initialData:M,isSubmiting:p,layout:_,isOpen:c,onSubmit:v,onToggle:d,providerToEditName:E})=>{const{formatMessage:g}=(0,x.A)();return c?(0,e.jsxs)(re.k,{onClose:d,labelledBy:"title",children:[(0,e.jsx)(ne.r,{children:(0,e.jsx)(Pe.B,{label:l.join(", "),children:l.map((u,o,h)=>(0,e.jsx)(Me.m,{isCurrent:o===h.length-1,children:u},u))})}),(0,e.jsx)(ve.l1,{onSubmit:u=>v(u),initialValues:M,validationSchema:_.schema,validateOnChange:!1,children:({errors:u,handleChange:o,values:h})=>(0,e.jsxs)(r.lV,{children:[(0,e.jsx)(le.c,{children:(0,e.jsx)(oe.s,{direction:"column",alignItems:"stretch",gap:1,children:(0,e.jsx)(w.x,{gap:5,children:_.form.map(f=>f.map(m=>(0,e.jsx)(Z.E,{col:m.size,xs:12,children:(0,e.jsx)(R,{...m,error:u[m.name],onChange:o,value:h[m.name],providerToEditName:E})},m.name)))})})}),(0,e.jsx)(de.j,{startActions:(0,e.jsx)(z.$,{variant:"tertiary",onClick:d,type:"button",children:g({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),endActions:(0,e.jsx)(z.$,{type:"submit",loading:p,children:g({id:"global.save",defaultMessage:"Save"})})})]})})]}):null};C.defaultProps={initialData:null,providerToEditName:null},C.propTypes={headerBreadcrumbs:i.arrayOf(i.string).isRequired,initialData:i.object,layout:i.shape({form:i.arrayOf(i.array),schema:i.object}).isRequired,isOpen:i.bool.isRequired,isSubmiting:i.bool.isRequired,onSubmit:i.func.isRequired,onToggle:i.func.isRequired,providerToEditName:i.string};const F={id:(0,n.g)("PopUpForm.Providers.redirectURL.front-end.label"),defaultMessage:"The redirect URL to your front-end app"},N={id:"http://www.client-app.com",defaultMessage:"http://www.client-app.com"},U={id:(0,n.g)("PopUpForm.Providers.enabled.description"),defaultMessage:"If disabled, users won't be able to use this provider."},A={id:(0,n.g)("PopUpForm.Providers.enabled.label"),defaultMessage:"Enable"},Q={id:(0,n.g)("PopUpForm.Providers.key.label"),defaultMessage:"Client ID"},$={id:(0,n.g)("PopUpForm.Providers.redirectURL.label"),defaultMessage:"The redirect URL to add in your {provider} application configurations"},O={id:(0,n.g)("PopUpForm.Providers.key.placeholder"),defaultMessage:"TEXT"},k={id:(0,n.g)("PopUpForm.Providers.secret.label"),defaultMessage:"Client Secret"},W={email:{form:[[{intlLabel:A,name:"enabled",type:"bool",description:U,size:6}]],schema:t.Ik().shape({enabled:t.lc().required(r.iW.required)})},providers:{form:[[{intlLabel:A,name:"enabled",type:"bool",description:U,size:6,validations:{required:!0}}],[{intlLabel:Q,name:"key",type:"text",placeholder:O,size:12,validations:{required:!0}}],[{intlLabel:k,name:"secret",type:"text",placeholder:O,size:12,validations:{required:!0}}],[{intlLabel:F,placeholder:N,name:"callback",type:"text",size:12,validations:{required:!0}}],[{intlLabel:$,name:"noName",type:"text",validations:{},size:12,disabled:!0}]],schema:t.Ik().shape({enabled:t.lc().required(r.iW.required),key:t.Yj().when("enabled",{is:!0,then:t.Yj().required(r.iW.required),otherwise:t.Yj()}),secret:t.Yj().when("enabled",{is:!0,then:t.Yj().required(r.iW.required),otherwise:t.Yj()}),callback:t.Yj().when("enabled",{is:!0,then:t.Yj().required(r.iW.required),otherwise:t.Yj()})})},providersWithSubdomain:{form:[[{intlLabel:A,name:"enabled",type:"bool",description:U,size:6,validations:{required:!0}}],[{intlLabel:Q,name:"key",type:"text",placeholder:O,size:12,validations:{required:!0}}],[{intlLabel:k,name:"secret",type:"text",placeholder:O,size:12,validations:{required:!0}}],[{intlLabel:{id:(0,n.g)({id:"PopUpForm.Providers.jwksurl.label"}),defaultMessage:"JWKS URL"},name:"jwksurl",type:"text",placeholder:O,size:12,validations:{required:!1}}],[{intlLabel:{id:(0,n.g)("PopUpForm.Providers.subdomain.label"),defaultMessage:"Host URI (Subdomain)"},name:"subdomain",type:"text",placeholder:{id:(0,n.g)("PopUpForm.Providers.subdomain.placeholder"),defaultMessage:"my.subdomain.com"},size:12,validations:{required:!0}}],[{intlLabel:F,placeholder:N,name:"callback",type:"text",size:12,validations:{required:!0}}],[{intlLabel:$,name:"noName",type:"text",validations:{},size:12,disabled:!0}]],schema:t.Ik().shape({enabled:t.lc().required(r.iW.required),key:t.Yj().when("enabled",{is:!0,then:t.Yj().required(r.iW.required),otherwise:t.Yj()}),secret:t.Yj().when("enabled",{is:!0,then:t.Yj().required(r.iW.required),otherwise:t.Yj()}),subdomain:t.Yj().when("enabled",{is:!0,then:t.Yj().required(r.iW.required),otherwise:t.Yj()}),callback:t.Yj().when("enabled",{is:!0,then:t.Yj().required(r.iW.required),otherwise:t.Yj()})})}},H=()=>{const{formatMessage:l,locale:M}=(0,x.A)(),p=(0,T.useQueryClient)(),{trackUsage:_}=(0,r.z1)(),[c,v]=y.useState(!1),[d,E]=y.useState(null),g=(0,r.hN)(),{lockApp:u,unlockApp:o}=(0,r.MA)(),{get:h,put:f}=(0,r.ry)(),{formatAPIError:m}=(0,r.wq)(),I=(0,r.QM)(M,{sensitivity:"base"});(0,r.L4)();const{isLoading:B,allowedActions:{canUpdate:D}}=(0,r.ec)({update:n.P.updateProviders}),{isLoading:be,data:K}=(0,T.useQuery)(["users-permissions","get-providers"],async()=>{const{data:a}=await h("/users-permissions/providers");return a},{initialData:{}}),V=(0,T.useMutation)(a=>f("/users-permissions/providers",a),{async onSuccess(){await p.invalidateQueries(["users-permissions","get-providers"]),g({type:"success",message:{id:(0,n.g)("notification.success.submit")}}),_("didEditAuthenticationProvider"),Y(),o()},onError(a){g({type:"warning",message:m(a)}),o()},refetchActive:!1}),L=Object.entries(K).reduce((a,[j,ye])=>{const{icon:J,enabled:Le,subdomain:xe}=ye;return a.push({name:j,icon:J==="envelope"?["fas","envelope"]:["fab",J],enabled:Le,subdomain:xe}),a},[]).sort((a,j)=>I.compare(a.name,j.name)),Oe=be||B,X=y.useMemo(()=>d?!!L.find(j=>j.name===d)?.subdomain:!1,[L,d]),De=y.useMemo(()=>d==="email"?W.email:X?W.providersWithSubdomain:W.providers,[d,X]),Y=()=>{v(a=>!a)},G=a=>{D&&(E(a.name),Y())},je=async a=>{u(),_("willEditAuthenticationProvider"),V.mutate({providers:{...K,[d]:a}})};return(0,e.jsxs)(se.P,{children:[(0,e.jsx)(r.x7,{name:l({id:(0,n.g)("HeaderNav.link.providers"),defaultMessage:"Providers"})}),(0,e.jsxs)(ae.g,{children:[(0,e.jsx)(te.Q,{title:l({id:(0,n.g)("HeaderNav.link.providers"),defaultMessage:"Providers"})}),Oe?(0,e.jsx)(r.Bl,{}):(0,e.jsx)(ie.s,{children:(0,e.jsxs)(ue.X,{colCount:3,rowCount:L.length+1,children:[(0,e.jsx)(_e.d,{children:(0,e.jsxs)(q.Tr,{children:[(0,e.jsx)(P.Th,{children:(0,e.jsx)(b.o,{variant:"sigma",textColor:"neutral600",children:l({id:"global.name",defaultMessage:"Name"})})}),(0,e.jsx)(P.Th,{children:(0,e.jsx)(b.o,{variant:"sigma",textColor:"neutral600",children:l({id:(0,n.g)("Providers.status"),defaultMessage:"Status"})})}),(0,e.jsx)(P.Th,{children:(0,e.jsx)(b.o,{variant:"sigma",children:(0,e.jsx)(pe.s,{children:l({id:"global.settings",defaultMessage:"Settings"})})})})]})}),(0,e.jsx)(ce.N,{children:L.map(a=>(0,e.jsxs)(q.Tr,{...(0,r.qM)({fn:()=>G(a),condition:D}),children:[(0,e.jsx)(P.Td,{width:"45%",children:(0,e.jsx)(b.o,{fontWeight:"semiBold",textColor:"neutral800",children:a.name})}),(0,e.jsx)(P.Td,{width:"65%",children:(0,e.jsx)(b.o,{textColor:a.enabled?"success600":"danger600","data-testid":`enable-${a.name}`,children:a.enabled?l({id:"global.enabled",defaultMessage:"Enabled"}):l({id:"global.disabled",defaultMessage:"Disabled"})})}),(0,e.jsx)(P.Td,{...r.dG,children:D&&(0,e.jsx)(ee.K,{onClick:()=>G(a),noBorder:!0,icon:(0,e.jsx)(Ee.A,{}),label:"Edit"})})]},a.name))})]})})]}),(0,e.jsx)(C,{initialData:K[d],isOpen:c,isSubmiting:V.isLoading,layout:De,headerBreadcrumbs:[l({id:(0,n.g)("PopUpForm.header.edit.providers"),defaultMessage:"Edit Provider"}),he(d)],onToggle:Y,onSubmit:je,providerToEditName:d})]})},fe=()=>(0,e.jsx)(r.kz,{permissions:n.P.readProviders,children:(0,e.jsx)(H,{})})}}]);
