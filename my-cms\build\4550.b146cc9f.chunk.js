"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4550],{24550:(n,e,i)=>{i.r(e),i.d(e,{default:()=>a,link:()=>t});const t="Ba\u011Flant\u0131",a={link:t,"Settings.email.plugin.button.test-email":"Deneme e-postas\u0131 g\xF6nder","Settings.email.plugin.label.defaultFrom":"Varsay\u0131lan g\xF6nderim adresi","Settings.email.plugin.label.defaultReplyTo":"Varsay\u0131lan yan\u0131t adresi","Settings.email.plugin.label.provider":"E-Posta sa\u011Flay\u0131c\u0131","Settings.email.plugin.label.testAddress":"Al\u0131c\u0131 e-posta adresi","Settings.email.plugin.notification.config.error":"E-posta ayarlar\u0131n\u0131 okuma hatas\u0131","Settings.email.plugin.notification.data.loaded":"E-posta ayarlar\u0131 y\xFCklendi","Settings.email.plugin.notification.test.error":"{to} adresine deneme e-postas\u0131 g\xF6nderimi ba\u015Far\u0131s\u0131z oldu","Settings.email.plugin.notification.test.success":"E-posta denemesi ba\u015Far\u0131l\u0131. {to} adresinin posta kutusunu kontrol edin","Settings.email.plugin.placeholder.defaultFrom":"\xF6r: Strapi Yan\u0131tlama <<EMAIL>>","Settings.email.plugin.placeholder.defaultReplyTo":"\xF6r: Strapi <<EMAIL>>","Settings.email.plugin.placeholder.testAddress":"\xF6r: <EMAIL>","Settings.email.plugin.subTitle":"E-posta eklentisinin ayarlar\u0131n\u0131 deneyin","Settings.email.plugin.text.configuration":"Plugin {file} dosyas\u0131 \xFCzerinden ayarlan\u0131yor. Detaylar i\xE7in \u015Fu ba\u011Flant\u0131ya bak\u0131n: {link}","Settings.email.plugin.title":"Kurulum","Settings.email.plugin.title.config":"Kurulum","Settings.email.plugin.title.test":"E-posta g\xF6nderimini dene","SettingsNav.link.settings":"Ayarlar","SettingsNav.section-label":"E-posta eklentisi","components.Input.error.validation.email":"Ge\xE7ersiz e-posta adresi"}}}]);
