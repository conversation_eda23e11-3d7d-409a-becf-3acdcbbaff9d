"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[9971],{79971:(n,s,e)=>{e.r(s),e.d(s,{FORM_INITIAL_VALUES:()=>t,ROLE_LAYOUT:()=>i});const t={...window.strapi.features.isEnabled(window.strapi.features.SSO)?{useSSORegistration:!0}:{}},i=[...window.strapi.features.isEnabled(window.strapi.features.SSO)?[[{intlLabel:{id:"Settings.permissions.users.form.sso",defaultMessage:"Connect with SSO"},name:"useSSORegistration",type:"bool",size:{col:6,xs:12}}]]:[]]}}]);
