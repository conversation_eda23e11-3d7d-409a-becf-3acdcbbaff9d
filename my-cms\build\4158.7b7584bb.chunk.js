"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4158],{24158:(l,a,_)=>{_.d(a,{ReviewWorkflowsAssigneeEE:()=>i,ReviewWorkflowsStageEE:()=>S});var E=_(92132),t=_(94061),T=_(83997),D=_(30893),s=_(55506),P=_(25524),o=_(35658),O=_(54894),n=_(43543),W=_(15126),R=_(63299),C=_(67014),r=_(59080),L=_(79275),v=_(14718),I=_(21272),d=_(82437),K=_(61535),U=_(5790),B=_(12083),h=_(35223),g=_(5409),e=_(74930),f=_(2600),N=_(48940),G=_(41286),y=_(56336),F=_(13426),c=_(84624),j=_(77965),x=_(54257),Y=_(71210),w=_(51187),z=_(39404),H=_(58692),V=_(501),X=_(57646),J=_(23120),Q=_(44414),Z=_(25962),$=_(14664),u=_(42588),k=_(90325),p=_(62785),b=_(87443),q=_(41032),__=_(22957),E_=_(93179),s_=_(73055),O_=_(15747),t_=_(85306),o_=_(26509),D_=_(32058),P_=_(81185),n_=_(82261);const S=({color:M=P.b,name:A})=>{const{themeColorName:m}=(0,o.g)(M)??{};return(0,E.jsxs)(T.s,{alignItems:"center",gap:2,maxWidth:(0,s.a8)(300),children:[(0,E.jsx)(t.a,{height:2,background:M,borderColor:m==="neutral0"?"neutral150":void 0,hasRadius:!0,shrink:0,width:2}),(0,E.jsx)(D.o,{fontWeight:"regular",textColor:"neutral700",ellipsis:!0,children:A})]})},i=({user:M})=>{const{formatMessage:A}=(0,O.A)();return(0,E.jsx)(D.o,{textColor:"neutral800",children:(0,n.l)(M,A)})}},25524:(l,a,_)=>{_.d(a,{A:()=>o,C:()=>U,D:()=>K,R:()=>t,S:()=>I,a:()=>B,b:()=>d,c:()=>O,d:()=>W,e:()=>T,f:()=>D,g:()=>P,h:()=>s,i:()=>v,j:()=>R,k:()=>n,l:()=>C,m:()=>r,n:()=>L});var E=_(57438);const t="settings_review-workflows",T="Settings/Review_Workflows/RESET_WORKFLOW",D="Settings/Review_Workflows/SET_CONTENT_TYPES",s="Settings/Review_Workflows/SET_IS_LOADING",P="Settings/Review_Workflows/SET_ROLES",o="Settings/Review_Workflows/SET_WORKFLOW",O="Settings/Review_Workflows/SET_WORKFLOWS",n="Settings/Review_Workflows/WORKFLOW_DELETE_STAGE",W="Settings/Review_Workflows/WORKFLOW_ADD_STAGE",R="Settings/Review_Workflows/WORKFLOW_CLONE_STAGE",C="Settings/Review_Workflows/WORKFLOW_UPDATE_STAGE",r="Settings/Review_Workflows/WORKFLOW_UPDATE_STAGES",L="Settings/Review_Workflows/WORKFLOW_UPDATE_STAGE_POSITION",v="Settings/Review_Workflows/WORKFLOW_UPDATE",I={primary600:"Blue",primary200:"Lilac",alternative600:"Violet",alternative200:"Lavender",success600:"Green",success200:"Pale Green",danger500:"Cherry",danger200:"Pink",warning600:"Orange",warning200:"Yellow",secondary600:"Teal",secondary200:"Baby Blue",neutral400:"Gray",neutral0:"White"},d=E._.colors.primary600,K={STAGE:"stage"},U="numberOfWorkflows",B="stagesPerWorkflow"},35658:(l,a,_)=>{_.d(a,{a:()=>D,g:()=>T});var E=_(57438),t=_(25524);function T(s){if(!s)return null;const o=Object.entries(E._.colors).filter(([,O])=>O.toUpperCase()===s.toUpperCase()).reduce((O,[n])=>(t.S?.[n]&&(O=n),O),null);return o?{themeColorName:o,name:t.S[o]}:null}function D(){return Object.entries(t.S).map(([s,P])=>({hex:E._.colors[s].toUpperCase(),name:P}))}}}]);
