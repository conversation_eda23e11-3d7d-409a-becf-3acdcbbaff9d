"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4799],{84799:(S,e,t)=>{t.r(e),t.d(e,{Analytics:()=>n,Documentation:()=>o,Email:()=>r,Password:()=>s,Provider:()=>i,ResetPasswordToken:()=>a,Role:()=>l,Username:()=>c,Users:()=>p,anErrorOccurred:()=>u,clearLabel:()=>m,dark:()=>d,default:()=>b,light:()=>g,or:()=>E,selectButtonTitle:()=>x,skipToContent:()=>f,submit:()=>h});const n="Statistiques",o="Documentation",r="E-mail",s="Mot de passe",i="Provider",a="ResetPasswordToken",l="R\xF4le",c="Nom d'utilisateur",p="Utilisateurs",u="Oups ! Une erreur s'est produite. Veuillez r\xE9essayer.",m="Vider",d="Sombre",g="Clair",E="OU",x="S\xE9lectionner",f="Aller au contenu",h="Soumettre",b={Analytics:n,"Auth.components.Oops.text":"Votre compte a \xE9t\xE9 suspendu.","Auth.components.Oops.text.admin":"Si c'est une erreur, veuillez contacter votre administrateur.","Auth.components.Oops.title":"Oups !","Auth.form.active.label":"Actif","Auth.form.button.forgot-password":"Envoyer \xE0 nouveau","Auth.form.button.go-home":"Retour \xE0 l'accueil","Auth.form.button.login":"Se connecter","Auth.form.button.login.providers.error":"Nous ne pouvons pas vous connecter via le fournisseur s\xE9lectionn\xE9","Auth.form.button.login.strapi":"Se connecter avec Strapi","Auth.form.button.password-recovery":"R\xE9cup\xE9ration de mot de passe","Auth.form.button.register":"Pr\xEAt \xE0 commencer","Auth.form.confirmPassword.label":"Confirmation du mot de passe","Auth.form.currentPassword.label":"Mot de passe actuel","Auth.form.email.label":"Email","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"Votre compte a \xE9t\xE9 bloqu\xE9 par l'administrateur.","Auth.form.error.code.provide":"Le code est incorrect.","Auth.form.error.confirmed":"L'e-mail de votre compte n'est pas confirm\xE9.","Auth.form.error.email.invalid":"Cette e-mail n'est pas valide.","Auth.form.error.email.provide":"Votre identifiant est manquant.","Auth.form.error.email.taken":"Cet e-mail est d\xE9j\xE0 utilis\xE9","Auth.form.error.invalid":"Votre identifiant ou mot de passe est incorrect.","Auth.form.error.params.provide":"Les informations sont incorrectes.","Auth.form.error.password.format":"Votre mot de passe ne peut pas contenir trois fois le symbole `$`.","Auth.form.error.password.local":"Ce compte n'a pas de mot de passe.","Auth.form.error.password.matching":"Les mots de passe ne sont pas identique.","Auth.form.error.password.provide":"Votre mot de passe est manquant.","Auth.form.error.ratelimit":"Trop de tentatives, veuillez r\xE9essayer dans une minute.","Auth.form.error.user.not-exist":"Cette e-mail n'existe pas.","Auth.form.error.username.taken":"Ce nom est d\xE9j\xE0 utilis\xE9","Auth.form.firstname.label":"Pr\xE9nom","Auth.form.firstname.placeholder":"John","Auth.form.forgot-password.email.label":"Entrez votre e-mail","Auth.form.forgot-password.email.label.success":"E-mail envoy\xE9 avec succ\xE8s \xE0 l'adresse suivante","Auth.form.lastname.label":"Nom","Auth.form.lastname.placeholder":"Doe","Auth.form.password.hide-password":"Cacher le mot de passe","Auth.form.password.hint":"Le mot de passe doit contenir au moins 8 caract\xE8res, 1 majuscule, 1 minuscule et 1 chiffre.","Auth.form.password.show-password":"Afficher le mot de passe","Auth.form.register.news.label":"Me tenir au courant des nouvelles fonctionnalit\xE9s et am\xE9liorations \xE0 venir (en faisant cela vous acceptez les {terms} et {policy}).","Auth.form.register.subtitle":"Vos identifiants sont utilis\xE9 uniquement pour vous authentifier sur l'interface d'administration. Toutes les donn\xE9es sauvegard\xE9es seront stock\xE9es dans votre propre base de donn\xE9es.","Auth.form.rememberMe.label":"Se souvenir de moi","Auth.form.username.label":"Nom d'utilisateur","Auth.form.username.placeholder":"Kai Doe","Auth.form.welcome.subtitle":"Connectez-vous \xE0 votre compte Strapi","Auth.form.welcome.title":"Bienvenue !","Auth.link.forgot-password":"Mot de passe oubli\xE9 ?","Auth.link.ready":"Pr\xEAt \xE0 vous connecter ?","Auth.link.signin":"Connexion","Auth.link.signin.account":"Vous avez d\xE9j\xE0 un compte ?","Auth.login.sso.divider":"Ou connectez-vous avec","Auth.login.sso.loading":"Chargement des fournisseurs","Auth.login.sso.subtitle":"Vous connecter via SSO","Auth.privacy-policy-agreement.policy":"la politique de confidentialit\xE9","Auth.privacy-policy-agreement.terms":"termes","Auth.reset-password.title":"R\xE9initialiser le mot de passe","Content Manager":"Content Manager","Content Type Builder":"Content Types Builder",Documentation:o,Email:r,"Files Upload":"T\xE9l\xE9versement de fichiers","HomePage.helmet.title":"Accueil","HomePage.roadmap":"Voir la roadmap","HomePage.welcome.congrats":"Bravo !","HomePage.welcome.congrats.content":"Vous \xEAtes connect\xE9 en tant que premier Administrateur. Afin de d\xE9couvrir les fonctionnalit\xE9s propos\xE9es par Strapi,","HomePage.welcome.congrats.content.bold":"nous vous conseillons de cr\xE9er votre premi\xE8re Collection.","Media Library":"Mediath\xE8que","New entry":"Nouvelle entr\xE9e",Password:s,Provider:i,ResetPasswordToken:a,Role:l,"Roles & Permissions":"R\xF4les & Permissions","Roles.ListPage.notification.delete-all-not-allowed":"Certains r\xF4les n'ont pas pu \xEAtre supprim\xE9s car ils sont associ\xE9s \xE0 des utilisateurs.","Roles.ListPage.notification.delete-not-allowed":"Un r\xF4le ne peu pas \xEAtre supprim\xE9 s'il est associ\xE9 \xE0 des utilisateurs.","Roles.RoleRow.select-all":"S\xE9lectionner {name} pour action group\xE9e","Roles.RoleRow.user-count":"{number, plural, =0 {# utilisateur} one {# utilisateur} other {# utilisateurs}}","Roles.components.List.empty.withSearch":"Il n'y a pas de r\xF4les correspondant \xE0 la recherche ({search})...","Settings.PageTitle":"R\xE9glages - {name}","Settings.apiTokens.ListView.headers.createdAt":"Cr\xE9\xE9 le","Settings.apiTokens.ListView.headers.description":"Description","Settings.apiTokens.ListView.headers.lastUsedAt":"Derni\xE8re utilisation le","Settings.apiTokens.ListView.headers.name":"Nom","Settings.apiTokens.ListView.headers.type":"Type de jeton","Settings.apiTokens.regenerate":"R\xE9g\xE9n\xE9rer","Settings.apiTokens.createPage.title":"Cr\xE9er un jeton d'API","Settings.transferTokens.createPage.title":"Cr\xE9er un jeton de transfert","Settings.tokens.RegenerateDialog.title":"R\xE9g\xE9n\xE9rer le jeton","Settings.apiTokens.addFirstToken":"Ajouter votre premier jeton d'API","Settings.apiTokens.addNewToken":"Ajouter un nouveau jeton d'API","Settings.tokens.copy.editMessage":"Pour des raisons de s\xE9curit\xE9, vous ne pouvoir voir votre jeton qu'une seule fois","Settings.tokens.copy.editTitle":"Ce jeton n'est d\xE9sormais plus accessible","Settings.tokens.copy.lastWarning":"Assurez-vous de copier ce jeton, vous ne pourrez plus le revoir par la suite !","Settings.apiTokens.create":"Ajouter une entr\xE9e","Settings.apiTokens.createPage.permissions.description":"Seules les actions rattach\xE9es \xE0 une route sont list\xE9es ci-dessous.","Settings.apiTokens.createPage.permissions.title":"Permissions","Settings.apiTokens.description":"Liste des jetons g\xE9n\xE9r\xE9s pour consommer l'API","Settings.apiTokens.createPage.BoundRoute.title":"Route rattach\xE9e \xE0","Settings.apiTokens.createPage.permissions.header.title":"Param\xE8tres avanc\xE9s","Settings.apiTokens.createPage.permissions.header.hint":"S\xE9lectionner les actions de l'application ou du plugin et sur l'ic\xF4ne de la roue crant\xE9e pour afficher la route rattach\xE9e","Settings.apiTokens.lastHour":"derni\xE8re heure","Settings.tokens.duration.30-days":"30 jours","Settings.tokens.duration.7-days":"7 jours","Settings.tokens.duration.90-days":"90 jours","Settings.tokens.duration.expiration-date":"Date d'expiration","Settings.tokens.duration.unlimited":"Illimit\xE9","Settings.apiTokens.emptyStateLayout":"Vous n'avez pas encore de contenu...","Settings.tokens.form.duration":"Dur\xE9e de vie du jeton","Settings.tokens.form.type":"Type de jeton","Settings.tokens.form.name":"Nom","Settings.tokens.form.description":"Description","Settings.tokens.notification.copied":"Jeton copi\xE9 dans le press-papiers.","Settings.tokens.popUpWarning.message":"\xCAtes-vous s\xFBr(e) de vouloir r\xE9g\xE9n\xE9rer ce jeton ?","Settings.tokens.Button.cancel":"Annuler","Settings.tokens.Button.regenerate":"R\xE9g\xE9n\xE9rer","Settings.tokens.types.full-access":"Acc\xE8s total","Settings.tokens.types.read-only":"Lecture seule","Settings.tokens.types.custom":"Custom","Settings.tokens.regenerate":"R\xE9g\xE9n\xE9rer","Settings.transferTokens.title":"Jetons de transfert","Settings.transferTokens.description":"Liste des jetons de transfert g\xE9n\xE9r\xE9s","Settings.transferTokens.create":"Cr\xE9er un nouveau jeton de transfert","Settings.transferTokens.addFirstToken":"Ajouter votre premier jeton de transfert","Settings.transferTokens.addNewToken":"Ajouter un nouveau jeton de transfert","Settings.transferTokens.emptyStateLayout":"Vous n'avez aucun contenu pour le moment...","Settings.tokens.ListView.headers.name":"Nom","Settings.tokens.ListView.headers.description":"Description","Settings.transferTokens.ListView.headers.type":"Type de jeton","Settings.tokens.ListView.headers.createdAt":"Cr\xE9\xE9 le","Settings.tokens.ListView.headers.lastUsedAt":"Derni\xE8re utilisation","Settings.application.ee.admin-seats.count":"<text>{enforcementUserCount}</text>/{permittedSeats}","Settings.application.ee.admin-seats.at-limit-tooltip":"Limite atteinte : ajouter des places pour inviter d'autres utilisateurs","Settings.application.ee.admin-seats.add-seats":"{isHostedOnStrapiCloud, select, true {AJouter des places} other {Contacter le service clients}}","Settings.application.customization":"Customisation","Settings.application.customization.auth-logo.carousel-hint":"Remplacer le logo dans la page de connexion","Settings.application.customization.carousel-hint":"Changer le logo dans l'interface d'administration (dimensions maximales: {dimension}x{dimension}, poids maximal du fichier : {size}KB)","Settings.application.customization.carousel-slide.label":"Logo slide","Settings.application.customization.carousel.auth-logo.title":"Logo de connexion","Settings.application.customization.carousel.change-action":"Changer le logo","Settings.application.customization.carousel.menu-logo.title":"Logo du menu","Settings.application.customization.carousel.reset-action":"R\xE9initialiser le logo","Settings.application.customization.carousel.title":"Logo","Settings.application.customization.menu-logo.carousel-hint":"Remplacer le logo dans la navigation principale","Settings.application.customization.modal.cancel":"Annuler","Settings.application.customization.modal.pending":"T\xE9l\xE9chargement du logo","Settings.application.customization.modal.pending.card-badge":"image","Settings.application.customization.modal.pending.choose-another":"Choisir un autre logo","Settings.application.customization.modal.pending.subtitle":"G\xE9rer le logo choisi avant de le t\xE9l\xE9charger","Settings.application.customization.modal.pending.title":"Logo pr\xEAt pour le t\xE9l\xE9chargement","Settings.application.customization.modal.pending.upload":"T\xE9l\xE9chargement du logo","Settings.application.customization.modal.tab.label":"Comment voulez-vous t\xE9l\xE9charger vos medias ?","Settings.application.customization.modal.upload":"T\xE9l\xE9charger le logo","Settings.application.customization.modal.upload.cta.browse":"Explorer les fichiers","Settings.application.customization.modal.upload.drag-drop":"Glisser-d\xE9poser ici ou","Settings.application.customization.modal.upload.error-format":"Mauvais format charg\xE9 (formats accept\xE9s : jpeg, jpg, png, svg).","Settings.application.customization.modal.upload.error-network":"Erreur r\xE9seau","Settings.application.customization.modal.upload.error-size":"Le fichier t\xE9l\xE9charg\xE9 est trop grand (dimensions max : {dimension}x{dimension}, poids max: {size}KB)","Settings.application.customization.modal.upload.file-validation":"Dimensions maximales : {dimension}x{dimension}, poids maximal : {size}KB","Settings.application.customization.modal.upload.from-computer":"Depuis l'ordinateur","Settings.application.customization.modal.upload.from-url":"Depuis une URL","Settings.application.customization.modal.upload.from-url.input-label":"URL","Settings.application.customization.modal.upload.next":"Suivant","Settings.application.customization.size-details":"Dimensions maximales : {dimension}\xD7{dimension}, poids maximal : {size}KB","Settings.application.description":"Informations globales du panneau d'administration","Settings.application.edition-title":"plan actuel","Settings.application.ee-or-ce":"{communityEdition, select, true {\xC9dition Communaut\xE9} other {\xC9dition Entreprise}}","Settings.application.get-help":"Obtenir de l'aide","Settings.application.link-pricing":"Voir tous les tarifs","Settings.application.link-upgrade":"Mettez \xE0 niveau votre panneau d'administration","Settings.application.node-version":"version de node","Settings.application.strapi-version":"version de strapi","Settings.application.strapiVersion":"version de strapi","Settings.application.title":"Aper\xE7u","Settings.error":"Erreur","Settings.global":"Param\xE8tre Globaux","Settings.permissions":"Panneau d'aministration","Settings.permissions.category":"Param\xE8tres de permissions pour la cat\xE9gorie {category}","Settings.permissions.category.plugins":"Param\xE8tres de permissions pour le plugin {plugin}","Settings.permissions.conditions.anytime":"N'importe quand","Settings.permissions.conditions.apply":"Appliquer","Settings.permissions.conditions.can":"Peut","Settings.permissions.conditions.conditions":"D\xE9finir les conditions","Settings.permissions.conditions.links":"Liens","Settings.permissions.conditions.no-actions":"Vous devez d'abord s\xE9lectionner des actions (cr\xE9er, lire, mettre \xE0 jour, ...) avant de d\xE9finir des conditions sur celles-ci.","Settings.permissions.conditions.none-selected":"N'importe quand","Settings.permissions.conditions.or":"OU","Settings.permissions.conditions.when":"Quand","Settings.permissions.select-all-by-permission":"S\xE9lectionner toutes les permissions de {label}","Settings.permissions.select-by-permission":"S\xE9lectionner la permission de {label}","Settings.permissions.users.create":"Cr\xE9er un nouvel Utilisateur","Settings.permissions.users.email":"Email","Settings.permissions.users.firstname":"Pr\xE9nom","Settings.permissions.users.lastname":"Nom","Settings.permissions.users.form.sso":"Se connecter via SSO","Settings.permissions.users.form.sso.description":"Quand activ\xE9, les  utilisateurs peuvent se connecter via SSO","Settings.permissions.users.listview.header.subtitle":"Tous les utilisateurs ayant acc\xE8s au panneau d'administration de Strapi","Settings.permissions.users.tabs.label":"Onglet Autorisations","Settings.profile.form.notify.data.loaded":"Les donn\xE9es de votre profil ont \xE9t\xE9 charg\xE9es","Settings.profile.form.section.experience.clear.select":"Vider la langue de l'interface s\xE9lectionn\xE9e","Settings.profile.form.section.experience.here":"documentation","Settings.profile.form.section.experience.interfaceLanguage":"Langue de l'interface","Settings.profile.form.section.experience.interfaceLanguage.hint":"Cela affichera seulement votre propre interface dans la langue s\xE9lectionn\xE9e","Settings.profile.form.section.experience.interfaceLanguageHelp":"La s\xE9lection changera la langue de l'interface uniquement pour vous. Veuillez vous r\xE9f\xE9rer \xE0 cette {here} pour rendre d'autres langues disponibles pour votre \xE9quipe.","Settings.profile.form.section.experience.title":"Exp\xE9rience","Settings.profile.form.section.helmet.title":"Profil utilisateur","Settings.profile.form.section.profile.page.title":"Page de profil","Settings.roles.create.description":"D\xE9finir les droits attribu\xE9s au r\xF4le","Settings.roles.create.title":"Cr\xE9er un r\xF4le","Settings.roles.created":"R\xF4le cr\xE9\xE9","Settings.roles.edit.title":"Editer un r\xF4le","Settings.roles.form.button.users-with-role":"{number, plural, =0 {# utilisateurs} one {# utilisateur} other {# utilisateurs}} poss\xE9dant ce r\xF4le","Settings.roles.form.created":"Cr\xE9\xE9","Settings.roles.form.description":"Nom et description du r\xF4le","Settings.roles.form.permission.property-label":"permissions de {label}","Settings.roles.form.permissions.attributesPermissions":"Permissions de champs","Settings.roles.form.permissions.create":"Cr\xE9er","Settings.roles.form.permissions.delete":"Supprimer","Settings.roles.form.permissions.publish":"Publier","Settings.roles.form.permissions.read":"Lire","Settings.roles.form.permissions.update":"Mettre \xE0 jour","Settings.roles.list.button.add":"Ajouter un r\xF4le","Settings.roles.list.description":"Liste des r\xF4les","Settings.roles.title.singular":"r\xF4le","Settings.sso.description":"Configurer les param\xE8tres de la fonctionnalit\xE9 Single Sign-On.","Settings.sso.form.defaultRole.description":"Cela attribuera le nouvel utilisateur authentifi\xE9 au r\xF4le s\xE9lectionn\xE9","Settings.sso.form.defaultRole.description-not-allowed":"Vous devez avec la permission de lire les r\xF4les administateurs","Settings.sso.form.defaultRole.label":"R\xF4le par d\xE9faut","Settings.sso.form.registration.description":"Cr\xE9er un nouvel utilisateur lors de la connexion via SSO si aucun compte n'existe","Settings.sso.form.registration.label":"Enregistrement automatique","Settings.sso.title":"Single Sign-On","Settings.webhooks.create":"Cr\xE9er un webhook","Settings.webhooks.create.header":"Cr\xE9er un nouvel en-t\xEAte","Settings.webhooks.created":"Webhook cr\xE9\xE9","Settings.webhooks.event.publish-tooltip":"Cet \xE9v\xE9nement n'existe que pour les contenus avec le syst\xE8me Brouillon/Publier activ\xE9","Settings.webhooks.events.create":"Cr\xE9er","Settings.webhooks.events.update":"Mettre \xE0 jour","Settings.webhooks.form.events":"Ev\xE9nements","Settings.webhooks.form.headers":"En-t\xEAtes","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"Supprimer l'en-t\xEAte ligne {number}","Settings.webhooks.key":"Cl\xE9","Settings.webhooks.list.button.add":"Cr\xE9er un nouveau webhook","Settings.webhooks.list.description":"Recevoir des notifications de modifications en POST","Settings.webhooks.list.empty.description":"Aucun webhook trouv\xE9","Settings.webhooks.list.empty.link":"Voir notre documentation","Settings.webhooks.list.empty.title":"Il n'y a pas encore de webhooks","Settings.webhooks.list.th.actions":"actions","Settings.webhooks.list.th.status":"statut","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# \xE9l\xE9ment} other {# \xE9l\xE9ments}} s\xE9lectionn\xE9","Settings.webhooks.trigger":"D\xE9clencheur","Settings.webhooks.trigger.cancel":"Annuler le d\xE9clencheur","Settings.webhooks.trigger.pending":"En attente...","Settings.webhooks.trigger.save":"Veuillez sauvegarder pour d\xE9clencher","Settings.webhooks.trigger.success":"Succ\xE8s !","Settings.webhooks.trigger.success.label":"D\xE9clenchement r\xE9ussi","Settings.webhooks.trigger.test":"D\xE9clencheur de test","Settings.webhooks.trigger.title":"Sauvegarder avant de d\xE9clencher","Settings.webhooks.value":"Valeur",Username:c,Users:p,"Users & Permissions":"Utilisateurs et autorisations","Users.components.List.empty":"Aucun utilisateur...","Users.components.List.empty.withFilters":"Aucun utilisateur avec les filtres appliqu\xE9s...","Users.components.List.empty.withSearch":"Aucun utilisateur correspondant \xE0 la recherche ({search})...","admin.pages.MarketPlacePage.helmet":"Marketplace - Plugins","admin.pages.MarketPlacePage.submit.plugin.link":"Soumettez votre plugin","admin.pages.MarketPlacePage.subtitle":"Tirez le meilleur de Strapi",anErrorOccurred:u,"app.component.CopyToClipboard.label":"Copier dans le presse-papier","app.component.search.label":"Rechercher {target}","app.component.table.duplicate":"Dupliquer {target}","app.component.table.edit":"Modifier {target}","app.component.table.select.one-entry":"S\xE9lectionner {target}","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Lire les derni\xE8res actualit\xE9s \xE0 propos de Strapi et de son \xE9cosyst\xE8me","app.components.BlockLink.code":"Apps d'exemple","app.components.BlockLink.code.content":"Apprenez en testant des projets r\xE9els d\xE9velopp\xE9s par la communaut\xE9.","app.components.BlockLink.documentation.content":"D\xE9couvrir les concepts essentials, guides et instructions.","app.components.BlockLink.tutorial":"Tutoriels","app.components.BlockLink.tutorial.content":"Suivre les instructions \xE9tapes par \xE9tapes pour utiliser et personnaliser Strapi.","app.components.Button.cancel":"Annuler","app.components.Button.confirm":"Confirmer","app.components.Button.reset":"Annuler","app.components.ComingSoonPage.comingSoon":"Bient\xF4t disponible","app.components.ConfirmDialog.title":"Confirmation","app.components.DownloadInfo.download":"T\xE9l\xE9chargement en cours...","app.components.DownloadInfo.text":"Cela peut prendre une minute. Merci de patienter.","app.components.EmptyAttributes.title":"Il n'y a pas encore de champ","app.components.EmptyStateLayout.content-document":"Vous n'avez pas encore de contenu...","app.components.EmptyStateLayout.content-permissions":"Vous n'avez pas les permissions pour acc\xE9der \xE0 ce contenu","app.components.HomePage.button.blog":"Voir plus sur le blog","app.components.HomePage.community":"Rejoignez la communaut\xE9","app.components.HomePage.community.content":"Discutez avec les membres de l'\xE9quipe, contributeurs et d\xE9veloppeurs sur diff\xE9rent supports.","app.components.HomePage.create":"Cr\xE9ez votre premi\xE8re Collection","app.components.HomePage.roadmap":"Voir notre roadmap","app.components.HomePage.welcome":"Bienvenue \xE0 bord !","app.components.HomePage.welcome.again":"Bienvenue ","app.components.HomePage.welcomeBlock.content":"F\xE9licitations ! Vous \xEAtes connect\xE9 en tant que tout premier administrateur. Pour d\xE9couvrir les puissantes fonctionnalit\xE9s fournies par Strapi, nous vous recommandons de cr\xE9er votre premier Type de Contenu !","app.components.HomePage.welcomeBlock.content.again":"Nous esp\xE9rons que votre projet avance bien... D\xE9couvrez les derniers articles \xE0 propos de Strapi. Nous faisons de notre mieux pour am\xE9liorer le produit selon vos retours.","app.components.HomePage.welcomeBlock.content.issues":"issues","app.components.HomePage.welcomeBlock.content.raise":" ou soumettez des ","app.components.ImgPreview.hint":"Glissez-d\xE9posez dans cette zone ou {browse} un fichier \xE0 t\xE9l\xE9charger","app.components.ImgPreview.hint.browse":"recherchez","app.components.InputFile.newFile":"Ajouter un nouveau fichier","app.components.InputFileDetails.open":"Ouvrir dans une nouvelle fen\xEAtre","app.components.InputFileDetails.originalName":"Nom d'origine :","app.components.InputFileDetails.remove":"Supprimer ce fichier","app.components.InputFileDetails.size":"Taille:","app.components.InstallPluginPage.Download.description":"L'installation d'un plugin peut prendre quelques secondes.","app.components.InstallPluginPage.Download.title":"T\xE9l\xE9chargement en cours...","app.components.InstallPluginPage.description":"Am\xE9liorez votre app sans efforts","app.components.LeftMenu.collapse":"R\xE9duire la barre de navigation","app.components.LeftMenu.expand":"D\xE9velopper la barre de navigation","app.components.LeftMenu.logout":"D\xE9connexion","app.components.LeftMenuFooter.help":"Aide","app.components.LeftMenuFooter.poweredBy":"Propuls\xE9 par ","app.components.LeftMenuLinkContainer.collectionTypes":"Types de collection","app.components.LeftMenuLinkContainer.configuration":"Configurations","app.components.LeftMenuLinkContainer.general":"G\xE9n\xE9ral","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Aucun plugin install\xE9","app.components.LeftMenuLinkContainer.plugins":"Plugins","app.components.LeftMenuLinkContainer.singleTypes":"Types uniques","app.components.ListPluginsPage.deletePlugin.description":"La d\xE9sinstallation du plugin peut prendre quelques secondes.","app.components.ListPluginsPage.deletePlugin.title":"D\xE9sinstallation","app.components.ListPluginsPage.description":"Liste des plugins install\xE9s dans le projet.","app.components.ListPluginsPage.helmet.title":"List plugins","app.components.Logout.logout":"Se d\xE9connecter","app.components.Logout.profile":"Profil","app.components.MarketplaceBanner":"D\xE9couvrez les plugins construits par la communaut\xE9, et bien d'autres choses g\xE9niales pour d\xE9marrer votre projet, sur Strapi Awesome.","app.components.MarketplaceBanner.image.alt":"un logo fus\xE9e de strapi","app.components.MarketplaceBanner.link":"Aller voir \xE7a maintenant","app.components.NotFoundPage.back":"Retourner \xE0 la page d'accueil","app.components.NotFoundPage.description":"Page introuvable","app.components.Official":"Officiel","app.components.Onboarding.help.button":"Bouton d'aide","app.components.Onboarding.label.completed":"% compl\xE9t\xE9es","app.components.Onboarding.title":"D\xE9marrons ensemble","app.components.PluginCard.Button.label.download":"T\xE9l\xE9charger","app.components.PluginCard.Button.label.install":"D\xE9j\xE0 install\xE9","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"La configuration d'autoReload a besoin d'\xEAtre activ\xE9e pour t\xE9l\xE9charger un plugin. Veuillez d\xE9marrer votre application avec `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"J'ai compris !","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Pour des raisoins de s\xE9curit\xE9, un plugin ne peut \xEAtre install\xE9 qu'en d\xE9velopment.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Le t\xE9l\xE9chargement est impossible","app.components.PluginCard.compatible":"Compatible avec votre app","app.components.PluginCard.compatibleCommunity":"Compatible avec la communaut\xE9","app.components.PluginCard.more-details":"Plus de d\xE9tails","app.components.ToggleCheckbox.off-label":"D\xE9sactiv\xE9","app.components.ToggleCheckbox.on-label":"Activ\xE9","app.components.Users.MagicLink.connect":"Envoyez ce lien \xE0 l'utilisateur pour qu'il se connecte.","app.components.Users.MagicLink.connect.sso":"Envoyez ce lien \xE0 l'utilisateur, la premi\xE8re connexion peut \xEAtre effectu\xE9 via un fournisseur SSO","app.components.Users.ModalCreateBody.block-title.details":"D\xE9tails","app.components.Users.ModalCreateBody.block-title.roles":"R\xF4les de l'utilisateur","app.components.Users.ModalCreateBody.block-title.roles.description":"Un utilisateur peut avoir un ou plusieurs r\xF4les","app.components.Users.SortPicker.button-label":"Trier par","app.components.Users.SortPicker.sortby.email_asc":"Email (A \xE0 Z)","app.components.Users.SortPicker.sortby.email_desc":"Email (Z \xE0 A)","app.components.Users.SortPicker.sortby.firstname_asc":"Pr\xE9nom (A \xE0 Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Pr\xE9nom (Z \xE0 A)","app.components.Users.SortPicker.sortby.lastname_asc":"Nom (A \xE0 Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Nom (Z \xE0 A)","app.components.Users.SortPicker.sortby.username_asc":"Nom d'utilisateur (A \xE0 Z)","app.components.Users.SortPicker.sortby.username_desc":"Nom d'utilisateur (Z \xE0 A)","app.components.listPlugins.button":"Ajouter un Nouveau Plugin","app.components.listPlugins.title.none":"Aucun plugin n'est install\xE9","app.components.listPluginsPage.deletePlugin.error":"Une erreur est survenue pendant la d\xE9sintallation","app.containers.App.notification.error.init":"Une erreur est survenue en requ\xEAtant l'API","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Si vous ne recevez pas ce lien, veuillez contacter votre administrateur.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"La r\xE9ception de votre lien de r\xE9cup\xE9ration de mot de passe peut prendre quelques minutes.","app.containers.AuthPage.ForgotPasswordSuccess.title":"Email envoy\xE9","app.containers.Users.EditPage.form.active.label":"Actif","app.containers.Users.EditPage.header.label":"Modifier {name}","app.containers.Users.EditPage.header.label-loading":"Modifier l'utilisateur","app.containers.Users.EditPage.roles-bloc-title":"R\xF4les attribu\xE9s","app.containers.Users.ModalForm.footer.button-success":"Cr\xE9er l'utilisateur","app.links.configure-view":"Configurez la vue","app.static.links.cheatsheet":"Aide-m\xE9moire","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Ajouter un filtre","app.utils.close-label":"Fermer","app.utils.defaultMessage":" ","app.utils.duplicate":"Dupliquer","app.utils.edit":"Modifier","app.utils.delete":"Supprimer","app.utils.errors.file-too-big.message":"Le fichier est trop lourd","app.utils.filter-value":"Valeur du filtre","app.utils.filters":"Filtres","app.utils.notify.data-loaded":"{target} est charg\xE9e","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Publier","app.utils.select-all":"Tout s\xE9lectionner","app.utils.select-field":"S\xE9lectionner un champ","app.utils.select-filter":"S\xE9lectionner un filtre","app.utils.unpublish":"Annuler la publication",clearLabel:m,"coming.soon":"Ce contenu est actuellement en construction et sera de retour dans quelques semaines !","component.Input.error.validation.integer":"La valeur doit \xEAtre un nombre entier","components.AutoReloadBlocker.description":"D\xE9marrez Strapi avec l'une des commandes suivantes:","components.AutoReloadBlocker.header":"L'autoReload doit \xEAtre activ\xE9 pour ce plugin.","components.ErrorBoundary.title":"Une erreur est survenue...","components.FilterOptions.FILTER_TYPES.$contains":"contient","components.FilterOptions.FILTER_TYPES.$containsi":"contient (insensible \xE0 la casse)","components.FilterOptions.FILTER_TYPES.$endsWith":"termine par","components.FilterOptions.FILTER_TYPES.$endsWithi":"termine par (insensible \xE0 la casse)","components.FilterOptions.FILTER_TYPES.$eq":"est","components.FilterOptions.FILTER_TYPES.$eqi":"est (insensible \xE0 la casse)","components.FilterOptions.FILTER_TYPES.$gt":"est plus grand que","components.FilterOptions.FILTER_TYPES.$gte":"est plus grand ou \xE9gal \xE0","components.FilterOptions.FILTER_TYPES.$lt":"est plus petit que","components.FilterOptions.FILTER_TYPES.$lte":"est plus petit ou \xE9gal \xE0","components.FilterOptions.FILTER_TYPES.$ne":"n'est pas","components.FilterOptions.FILTER_TYPES.$nei":"n'est pas (insensible \xE0 la casse)","components.FilterOptions.FILTER_TYPES.$notContains":"ne contient pas","components.FilterOptions.FILTER_TYPES.$notContainsi":"ne contient pas (insensible \xE0 la casse)","components.FilterOptions.FILTER_TYPES.$notNull":"n'est pas nul","components.FilterOptions.FILTER_TYPES.$null":"est nul","components.FilterOptions.FILTER_TYPES.$startsWith":"commence par","components.FilterOptions.FILTER_TYPES.$startsWithi":"commence par (insensible \xE0 la casse)","components.Input.error.attribute.key.taken":"Cette valeur existe d\xE9j\xE0","components.Input.error.attribute.sameKeyAndName":"Ne peuvent pas \xEAtre \xE9gaux","components.Input.error.attribute.taken":"Ce champ existe d\xE9j\xE0","components.Input.error.contain.lowercase":"Le mot de passe doit contenir au moins une lettre minuscule","components.Input.error.contain.number":"Le mot de passe doit contenir au moins un chiffre","components.Input.error.contain.uppercase":"Le mot de passe doit contenir au moins une lettre majuscule","components.Input.error.contentTypeName.taken":"Ce nom existe d\xE9j\xE0","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"Le mot de passe ne correspond pas","components.Input.error.validation.email":"Le format n'est pas de type e-mail","components.Input.error.validation.json":"Le format JSON n'est pas respect\xE9","components.Input.error.validation.max":"La valeur est trop grande {max}.","components.Input.error.validation.maxLength":"La valeur est trop longue {max}.","components.Input.error.validation.min":"La valeur est trop basse {min}.","components.Input.error.validation.minLength":"La valeur est trop courte {min}.","components.Input.error.validation.minSupMax":"Ne peut pas \xEAtre plus grand.","components.Input.error.validation.regex":"La valeur ne correspond pas au format attendu.","components.Input.error.validation.required":"Ce champ est obligatoire.","components.Input.error.validation.unique":"Cette valeur est d\xE9j\xE0 prise","components.InputSelect.option.placeholder":"Choisissez ici","components.ListRow.empty":"Il n'y a pas de donn\xE9es \xE0 afficher.","components.NotAllowedInput.text":"Vous n'\xEAtes pas autoris\xE9 \xE0 voir ce champ","components.OverlayBlocker.description":"Vous utilisez une fonctionnalit\xE9 qui n\xE9cessite le red\xE9marrage du server. Merci d'attendre que celui-ci ait red\xE9marr\xE9.","components.OverlayBlocker.description.serverError":"Le serveur aurait d\xE9j\xE0 du red\xE9marrer, vous devriez regarder les messages dans le terminal.","components.OverlayBlocker.title":"Le serveur est en train de red\xE9marrer","components.OverlayBlocker.title.serverError":"Le serveur aurait d\xE9j\xE0 du red\xE9marrer","components.PageFooter.select":"entr\xE9es par page","components.ProductionBlocker.description":"Pour des raisons de s\xE9curit\xE9 il est d\xE9sactiv\xE9 dans les autres environnements.","components.ProductionBlocker.header":"Ce plugin est disponible uniquement en d\xE9veloppement.","components.Search.placeholder":"Rechercher...","components.TableHeader.sort":"Trier par {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Mode Markdown","components.Wysiwyg.ToggleMode.preview-mode":"Mode Aper\xE7u","components.Wysiwyg.collapse":"Fermer","components.Wysiwyg.selectOptions.H1":"Titre H1","components.Wysiwyg.selectOptions.H2":"Titre H2","components.Wysiwyg.selectOptions.H3":"Titre H3","components.Wysiwyg.selectOptions.H4":"Titre H4","components.Wysiwyg.selectOptions.H5":"Titre H5","components.Wysiwyg.selectOptions.H6":"Titre H6","components.Wysiwyg.selectOptions.title":"Ajouter un titre","components.WysiwygBottomControls.charactersIndicators":"caract\xE8res","components.WysiwygBottomControls.fullscreen":"Plein \xE9cran","components.WysiwygBottomControls.uploadFiles":"Ajouter des fichiers en les 'glissant-d\xE9posant', {browse}, ou en les collant depuis le presse-papier","components.WysiwygBottomControls.uploadFiles.browse":"en les selectionnant","components.pagination.go-to":"Aller \xE0 la page {page}","components.pagination.go-to-next":"Aller \xE0 la page suivante","components.pagination.go-to-previous":"Aller \xE0 la page pr\xE9c\xE9dente","components.pagination.remaining-links":"Et {number} autres liens","components.popUpWarning.button.cancel":"Non, annuler","components.popUpWarning.button.confirm":"Oui, confirmer","components.popUpWarning.message":"Etes-vous sure de vouloir le supprimer ?","components.popUpWarning.title":"Merci de confirmer","content-manager.App.schemas.data-loaded":"Les sch\xE9ma ont \xE9t\xE9 charg\xE9s avec succ\xE8s","content-manager.ListViewTable.relation-loaded":"Les relations on \xE9t\xE9 charg\xE9es","content-manager.EditRelations.title":"Donn\xE9es associ\xE9es","content-manager.HeaderLayout.button.label-add-entry":"Cr\xE9er une nouvelle entr\xE9e","content-manager.api.id":"API ID","content-manager.components.AddFilterCTA.add":"Filtres","content-manager.components.AddFilterCTA.hide":"Filtres","content-manager.components.DragHandle-label":"Glisser","content-manager.components.DraggableAttr.edit":"Cliquez pour modifier","content-manager.components.DraggableCard.delete.field":"Supprimer {item}","content-manager.components.DraggableCard.edit.field":"Modifier {item}","content-manager.components.DraggableCard.move.field":"D\xE9placer {item}","content-manager.components.ListViewTable.row-line":"ligne {number}","content-manager.components.DynamicZone.ComponentPicker-label":"Choisir un composant","content-manager.components.DynamicZone.add-component":"Ajouter un composant \xE0 {componentName}","content-manager.components.DynamicZone.delete-label":"Supprimer {name}","content-manager.components.DynamicZone.error-message":"Le composant contient une ou des erreurs","content-manager.components.DynamicZone.missing-components":"Il y a {number, plural, =0 {# composants manquants} one {# composant manquant} other {# composants manquants}}","content-manager.components.DynamicZone.move-down-label":"D\xE9placer le composant vers le bas","content-manager.components.DynamicZone.move-up-label":"D\xE9placer le composant vers le haut","content-manager.components.DynamicZone.pick-compo":"Choisir un composant","content-manager.components.DynamicZone.required":"Composant requis","content-manager.components.EmptyAttributesBlock.button":"Voir la page des configurations","content-manager.components.EmptyAttributesBlock.description":"Vous pouvez modifiez vos param\xE8tres","content-manager.components.FieldItem.linkToComponentLayout":"Modifier le layout du composant","content-manager.components.FieldSelect.label":"Ajouter un champ","content-manager.components.FilterOptions.button.apply":"Appliquer","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Appliquer","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Tout supprimer","content-manager.components.FiltersPickWrapper.PluginHeader.description":"D\xE9finissez les conditions des filtres \xE0 appliquer","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Filtres","content-manager.components.FiltersPickWrapper.hide":"Fermer","content-manager.components.LeftMenu.Search.label":"Chercher un type de contenu","content-manager.components.LeftMenu.collection-types":"Types de Collections","content-manager.components.LeftMenu.single-types":"Types uniques","content-manager.components.LimitSelect.itemsPerPage":"\xC9l\xE9ments par page","content-manager.components.NotAllowedInput.text":"Vous n'avez pas la permission de voir ce champ","content-manager.components.RepeatableComponent.error-message":"Le composant contient une ou des erreurs","content-manager.components.Search.placeholder":"Rechercher une entr\xE9e...","content-manager.components.Select.draft-info-title":"Statut: Brouillon","content-manager.components.Select.publish-info-title":"Statut: Publi\xE9","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"D\xE9finissez l'apparence de la vue edit.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"D\xE9finir les param\xE8tres de la vue liste.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Configurer la vue - {name}","content-manager.components.TableDelete.delete":"Tout supprimer","content-manager.components.TableDelete.deleteSelected":"Supprimer les \xE9l\xE9ments s\xE9lectionn\xE9s","content-manager.components.TableDelete.label":"{number, plural, one {# entr\xE9e s\xE9lectionn\xE9e} other {# entr\xE9es s\xE9lectionn\xE9es}}","content-manager.components.TableEmpty.withFilters":"Aucun {contentType} n'a \xE9t\xE9 trouv\xE9 avec ces filtres...","content-manager.components.TableEmpty.withSearch":"Aucun {contentType} n'a \xE9t\xE9 trouv\xE9 avec cette recherche ({search})...","content-manager.components.TableEmpty.withoutFilter":"Aucun {contentType} n'a \xE9t\xE9 trouv\xE9...","content-manager.components.empty-repeatable":"Il n'a pas encore d'entr\xE9e. Cliquez sur le bouton pour en ajouter une.","content-manager.components.notification.info.maximum-requirement":"Le nombre maximal de champs est atteint","content-manager.components.notification.info.minimum-requirement":"Un champ a \xE9t\xE9 rajout\xE9 pour remplir les conditions minimales","content-manager.components.repeatable.reorder.error":"Une erreur s'est produite lors de la r\xE9organisation du champ de votre composant, veuillez r\xE9essayer","content-manager.components.reset-entry":"Supprimer l'entr\xE9e","content-manager.components.uid.apply":"appliquer","content-manager.components.uid.available":"disponible","content-manager.components.uid.regenerate":"reg\xE9n\xE9rer","content-manager.components.uid.suggested":"sugg\xE9r\xE9","content-manager.components.uid.unavailable":"indisponible","content-manager.containers.Edit.Link.Layout":"Param\xE9trer la vue","content-manager.containers.Edit.Link.Model":"\xC9diter le mod\xE8le","content-manager.containers.Edit.addAnItem":"Ajouter un \xE9l\xE9ment...","content-manager.containers.Edit.clickToJump":"Cliquer pour voir l'entr\xE9e","content-manager.containers.Edit.delete":"Supprimer","content-manager.containers.Edit.delete-entry":"Supprimer cette entr\xE9e","content-manager.containers.Edit.editing":"\xC9dition en cours...","content-manager.containers.Edit.information":"Informations","content-manager.containers.Edit.information.by":"Par","content-manager.containers.Edit.information.created":"Cr\xE9\xE9","content-manager.containers.Edit.information.draftVersion":"version brouillon","content-manager.containers.Edit.information.editing":"\xC9dition :","content-manager.containers.Edit.information.lastUpdate":"Derni\xE8re modification","content-manager.containers.Edit.information.publishedVersion":"version publi\xE9e","content-manager.containers.Edit.pluginHeader.title.new":"Cr\xE9er un document","content-manager.containers.Edit.reset":"Annuler","content-manager.containers.Edit.returnList":"Retourner \xE0 la liste","content-manager.containers.Edit.seeDetails":"D\xE9tails","content-manager.containers.Edit.submit":"Valider","content-manager.containers.EditSettingsView.modal-form.edit-field":"Editer le champ","content-manager.containers.EditView.add.new-entry":"Ajouter une nouvelle entr\xE9e","content-manager.containers.EditView.notification.errors":"Le formulaire contient des erreurs","content-manager.containers.Home.introduction":"Pour \xE9diter du contenu, choisissez un type de donn\xE9es dans le menu de gauche.","content-manager.containers.Home.pluginHeaderDescription":"Cr\xE9er et modifier votre type de contenu","content-manager.containers.Home.pluginHeaderTitle":"Type de contenu","content-manager.containers.List.draft":"Brouillon","content-manager.containers.List.errorFetchRecords":"Erreur","content-manager.containers.List.published":"Publi\xE9","content-manager.containers.ListPage.displayedFields":"Champs affich\xE9s","content-manager.containers.ListPage.items":"{number, plural, =0 {\xE9lements} one {\xE9lement} other {\xE9lements}}","content-manager.containers.ListPage.table-headers.publishedAt":"Statut","content-manager.containers.ListSettingsView.modal-form.edit-label":"Editer le label","content-manager.containers.SettingPage.add.field":"Ins\xE9rer un autre champ","content-manager.containers.SettingPage.attributes":"Attributs","content-manager.containers.SettingPage.attributes.description":"Organisez les attributs du mod\xE8le","content-manager.containers.SettingPage.editSettings.description":"Glissez & d\xE9posez les champs pour construire le layout","content-manager.containers.SettingPage.editSettings.entry.title":"Nom de l'entr\xE9e","content-manager.containers.SettingPage.editSettings.entry.title.description":"D\xE9finissez quel champ sera affich\xE9","content-manager.containers.SettingPage.editSettings.relation-field.description":"D\xE9finir le champ affich\xE9 dans les vues d'\xE9dition et de liste","content-manager.containers.SettingPage.editSettings.title":"Vue edit (param\xE8tres)","content-manager.containers.SettingPage.layout":"Layout","content-manager.containers.SettingPage.listSettings.description":"Configurez les options de ce mod\xE8le","content-manager.containers.SettingPage.listSettings.title":"Vue liste (param\xE8tres)","content-manager.containers.SettingPage.pluginHeaderDescription":"Configurez les param\xE8tres de ce mod\xE8le","content-manager.containers.SettingPage.settings":"Param\xE8tres","content-manager.containers.SettingPage.view":"Vue","content-manager.containers.SettingViewModel.pluginHeader.title":"Gestion du contenu - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Configurez les param\xE8tres sp\xE9cifiques","content-manager.containers.SettingsPage.Block.contentType.title":"Types de collection","content-manager.containers.SettingsPage.Block.generalSettings.description":"Configurez les options par d\xE9fault de vos mod\xE8les","content-manager.containers.SettingsPage.Block.generalSettings.title":"G\xE9n\xE9ral","content-manager.containers.SettingsPage.pluginHeaderDescription":"Configurez les param\xE8tres de vos mod\xE8les et groupes","content-manager.containers.SettingsView.list.subtitle":"Configurez le layout et l'affichage de vos types de collection et groupes","content-manager.containers.SettingsView.list.title":"Param\xE8tres d'affichage","content-manager.edit-settings-view.link-to-ctb.components":"Modifier le composant","content-manager.edit-settings-view.link-to-ctb.content-types":"Modifier le type de contenu","content-manager.emptyAttributes.button":"Ouvrir le constructeur de types de contenu","content-manager.emptyAttributes.description":"Ajoutez votre premier champ a votre mod\xE8le","content-manager.emptyAttributes.title":"Il n'y a pas encore de champs","content-manager.error.attribute.key.taken":"Cette valeur existe d\xE9j\xE0","content-manager.error.attribute.sameKeyAndName":"Ne peuvent pas \xEAtre \xE9gaux","content-manager.error.attribute.taken":"Ce champ existe d\xE9j\xE0","content-manager.error.contentTypeName.taken":"Ce nom existe d\xE9j\xE0","content-manager.error.model.fetch":"Une erreur est survenue lors de la r\xE9ception des mod\xE8les.","content-manager.error.record.create":"Une erreur est survenue lors de la cr\xE9ation de l'entr\xE9e.","content-manager.error.record.delete":"Une erreur est survenue lors de la suppression de l'entr\xE9e.","content-manager.error.record.fetch":"Une erreur est survenue lors de la r\xE9ception de l'entr\xE9e.","content-manager.error.record.update":"Une erreur est survenue lors de la modification de l'entr\xE9e.","content-manager.error.records.count":"Une erreur est survenue lors de la r\xE9ception du nombre d'entr\xE9es.","content-manager.error.records.fetch":"Une erreur est survenue lors de la r\xE9ception des entr\xE9es.","content-manager.error.schema.generation":"Une erreur est survenue lors de la g\xE9n\xE9ration du sch\xE9ma.","content-manager.error.validation.json":"Le format JSON n'est pas respect\xE9","content-manager.error.validation.max":"La valeur est trop grande.","content-manager.error.validation.maxLength":"La valeur est trop longue.","content-manager.error.validation.min":"La valeur est trop basse.","content-manager.error.validation.minLength":"La valeur est trop courte.","content-manager.error.validation.minSupMax":"Ne peut pas \xEAtre plus grand","content-manager.error.validation.regex":"La valeur ne correspond pas au format attendu.","content-manager.error.validation.required":"Ce champ est obligatoire.","content-manager.form.Input.bulkActions":"Autoriser les actions group\xE9es","content-manager.form.Input.defaultSort":"Attribut de tri par d\xE9fault","content-manager.form.Input.description":"Description","content-manager.form.Input.description.placeholder":"Afficher le nom dans le profil","content-manager.form.Input.editable":"Champ editable","content-manager.form.Input.filters":"Autoriser les filtres","content-manager.form.Input.label":"Label","content-manager.form.Input.label.inputDescription":"Cette valeur modifie celle du champs de la table","content-manager.form.Input.pageEntries":"Nombre d'entr\xE9es par page","content-manager.form.Input.pageEntries.inputDescription":"Note : Vous pouvez modifier ces valeurs par mod\xE8le","content-manager.form.Input.placeholder":"Placeholder","content-manager.form.Input.placeholder.placeholder":"Mon super placeholder","content-manager.form.Input.search":"Autoriser la recherche","content-manager.form.Input.search.field":"Autoriser la recherche sur ce champs","content-manager.form.Input.sort.field":"Autoriser le tri sur ce champs","content-manager.form.Input.sort.order":"Ordre de tri par d\xE9faut","content-manager.form.Input.wysiwyg":"Afficher comme WYSIWYG","content-manager.global.displayedFields":"Champs affich\xE9s","content-manager.groups":"Groupes","content-manager.groups.numbered":"Groupes ({number})","content-manager.header.name":"Contenu","content-manager.link-to-ctb":"Editer le mod\xE8le","content-manager.models":"Types de Collection","content-manager.models.numbered":"Types de Collection ({number})","content-manager.notification.error.displayedFields":"Vous devez avoir au moins un champ d'affich\xE9","content-manager.notification.error.relationship.fetch":"Une erreur est survenue en r\xE9cup\xE9rant les relations.","content-manager.notification.info.SettingPage.disableSort":"Vous devez avoir au moins un attribut de tri par d\xE9faut","content-manager.notification.info.minimumFields":"Vous devez avoir au moins un champ d'affich\xE9","content-manager.notification.upload.error":"Une erreur est survenues en t\xE9l\xE9chargeant vos fichiers","content-manager.pageNotFound":"Page non trouv\xE9e","content-manager.pages.ListView.header-subtitle":"{number, plural, =0 {# entr\xE9es trouv\xE9e} one {# entr\xE9e trouv\xE9e} other {# entr\xE9es trouv\xE9es}}","content-manager.pages.NoContentType.button":"Cr\xE9er votre premier Type de Contenu","content-manager.pages.NoContentType.text":"Vous n'avez encore aucun contenu, nous vous recommandons de cr\xE9er votre premier Type de Contenu","content-manager.permissions.not-allowed.create":"Vous n'\xEAtes pas autoris\xE9 \xE0 cr\xE9er un document","content-manager.permissions.not-allowed.update":"Vous n'\xEAtes pas autoris\xE9 \xE0 voir ce document","content-manager.plugin.description.long":"Visualisez, modifiez et supprimez les donn\xE9es de votre base de donn\xE9es.","content-manager.plugin.description.short":"Visualisez, modifiez et supprimez les donn\xE9es de votre base de donn\xE9es.","content-manager.popover.display-relations.label":"Afficher les relations","content-manager.relation.add":"Ajouter une relation","content-manager.relation.disconnect":"Supprimer","content-manager.relation.isLoading":"Chargement des relations en cours","content-manager.relation.loadMore":"Charger davantage","content-manager.relation.notAvailable":"Aucune relation disponible","content-manager.relation.publicationState.draft":"Brouillon","content-manager.relation.publicationState.published":"Publi\xE9e","content-manager.select.currently.selected":"{count} actuellement s\xE9lectionn\xE9es","content-manager.success.record.delete":"Supprim\xE9","content-manager.success.record.publish":"Publi\xE9","content-manager.success.record.save":"Sauvegard\xE9","content-manager.success.record.unpublish":"Publication annul\xE9e","content-manager.utils.data-loaded":"{number, plural, =1 {L'entr\xE9e a \xE9t\xE9 charg\xE9e} other {Les entr\xE9es on \xE9t\xE9 charg\xE9es} avec  succ\xE8s","content-manager.apiError.This attribute must be unique":"Le champ {field} doit \xEAtre unique","content-manager.popUpWarning.warning.publish-question":"\xCAtes-vous s\xFBr de vouloir le publier ?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Oui, publier","content-manager.popUpwarning.warning.has-draft-relations.message":"<b>{count, plural, =0 { des relations de votre contenu n'est} one { des relations de votre contenu n'est} other { des relations de votre contenu ne sont}}</b> pas publi\xE9 actuellement.<br></br>Cela peut engendrer des liens cass\xE9s ou des erreurs dans votre projet.",dark:d,"form.button.continue":"Continuer","global.search":"Rechercher","global.actions":"Actions","global.auditLogs":"Journaux d'audit","global.back":"Retour","global.cancel":"Annuler","global.change-password":"Modifier le mot de passe","global.content-manager":"Gestion du contenu","global.continue":"Continuer","global.delete":"Supprimer","global.delete-target":"Supprimer {target}","global.description":"Description","global.details":"D\xE9tails","global.disabled":"D\xE9sactiv\xE9","global.documentation":"Documentation","global.enabled":"Activ\xE9","global.finish":"Terminer","global.marketplace":"Marketplace","global.name":"Nom","global.none":"Aucun","global.password":"Mot de passe","global.plugins":"Plugins","global.profile":"Profil","global.reset-password":"R\xE9initialiser le mot de passe","global.roles":"R\xF4les","global.save":"Enregistrer","global.see-more":"Voir plus","global.select":"S\xE9lectionner","global.select-all-entries":"S\xE9lectionner toutes les entr\xE9es","global.settings":"Param\xE8tres","global.type":"Type","global.users":"Utilisateurs",light:g,"form.button.done":"Terminer","global.prompt.unsaved":"\xCAtes-vous s\xFBr de vouloir quitter cette page? Toutes vos modifications seront perdues","notification.contentType.relations.conflict":"Le Type de Contenu \xE0 des relations qui rentrent en conflit","notification.default.title":"Information:","notification.error":"Une erreur est survenue","notification.error.layout":"Impossible de r\xE9cup\xE9rer le layout de l'admin","notification.form.error.fields":"Le formulaire contient des erreurs","notification.form.success.fields":"Modifications enregistr\xE9es","notification.link-copied":"Lien copi\xE9 dans le presse-papier","notification.permission.not-allowed-read":"Vous n'\xEAtes pas autoris\xE9 \xE0 voir ce document","notification.success.delete":"Cet \xE9l\xE9ment a \xE9t\xE9 supprim\xE9","notification.success.saved":"Sauvegard\xE9","notification.success.title":"Succ\xE8s :","notification.version.update.message":"Une nouvelle version de Strapi est disponible !","notification.warning.title":"Attention :","notification.warning.404":"404 - Introuvable",or:E,"request.error.model.unknown":"Le model n'existe pas",selectButtonTitle:x,skipToContent:f,submit:h}}}]);
