"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[6085],{6085:(F,e,o)=>{o.r(e),o.d(e,{Analytics:()=>n,Documentation:()=>a,Email:()=>t,Password:()=>i,Provider:()=>r,ResetPasswordToken:()=>s,Role:()=>c,Username:()=>l,Users:()=>p,anErrorOccurred:()=>d,clearLabel:()=>m,dark:()=>f,default:()=>E,light:()=>x,or:()=>u,skipToContent:()=>g,submit:()=>b});const n="Analytics",a="Documentaci\xF3n",t="Email",i="Contrase\xF1a",r="Proveedor",s="Restablecer Token de Contrase\xF1a",c="Rol",l="Nombre de usuario",p="Usuarios",d="\xA1Ups! Algo sali\xF3 mal. Int\xE9ntalo de nuevo.",m="Limpiar",u="O",g="Saltar al contenido",b="Enviar",x="Claro",f="Oscuro",E={Analytics:n,"Auth.components.Oops.text":"Tu cuenta ha sido suspendida","Auth.components.Oops.text.admin":"Si se trata de un error, comun\xEDquese con su administrador.","Auth.components.Oops.title":"Ups...","Auth.form.button.forgot-password":"Enviar Email","Auth.form.button.go-home":"REGRESAR A CASA","Auth.form.button.login":"Iniciar sesi\xF3n","Auth.form.button.login.providers.error":"No podemos conectarlo a trav\xE9s del proveedor seleccionado.","Auth.form.button.login.strapi":"Iniciar sesi\xF3n a trav\xE9s de Strapi","Auth.form.button.password-recovery":"Recuperaci\xF3n de Contrase\xF1a","Auth.form.button.register":"Listo para comenzar","Auth.form.confirmPassword.label":"Confirmaci\xF3n de contrase\xF1a","Auth.form.currentPassword.label":"Contrase\xF1a actual","Auth.form.email.label":"Correo electr\xF3nico","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"Su cuenta ha sido bloqueada por el administrador.","Auth.form.error.code.provide":"C\xF3digo incorrecto proporcionado.","Auth.form.error.confirmed":"Su cuenta de correo no ha sido confirmada.","Auth.form.error.email.invalid":"Este email es inv\xE1lido.","Auth.form.error.email.provide":"Por favor proporcione su nombre de usuario o su correo electr\xF3nico.","Auth.form.error.email.taken":"El email ya est\xE1 registrado","Auth.form.error.invalid":"Identificador o contrase\xF1a inv\xE1lidos.","Auth.form.error.params.provide":"Parametros incorrectos proporcionados.","Auth.form.error.password.format":"Su contrase\xF1a no puede contener el s\xEDmbolo `$` m\xE1s de tres veces.","Auth.form.error.password.local":"Este usuario nunca estableci\xF3 una contrase\xF1a local, por favor ingrese a trav\xE9s de su proveedor utilizado durante la creaci\xF3n de la cuenta.","Auth.form.error.password.matching":"Las contrase\xF1as no coinciden.","Auth.form.error.password.provide":"Por favor, introduzca su contrase\xF1a.","Auth.form.error.ratelimit":"Demasiados intentos. Por favor vuelva a intentarlo dentro de un minuto.","Auth.form.error.user.not-exist":"Este email no existe.","Auth.form.error.username.taken":"El nombre de usuario ya est\xE1 registrado","Auth.form.firstname.label":"Nombre","Auth.form.firstname.placeholder":"Juan","Auth.form.forgot-password.email.label":"Introduce tu email","Auth.form.forgot-password.email.label.success":"Email enviado con \xE9xito a","Auth.form.lastname.label":"Apellido","Auth.form.lastname.placeholder":"P\xE9rez","Auth.form.password.hide-password":"Ocultar contrase\xF1a","Auth.form.password.hint":"La contrase\xF1a debe contener al menos 8 caracteres, 1 may\xFAscula, 1 min\xFAscula y 1 n\xFAmero","Auth.form.password.show-password":"Mostrar contrase\xF1a","Auth.form.register.news.label":"Mantenerme informado sobre las nuevas funciones y las pr\xF3ximas mejoras (al hacer esto, acepta las {terms} y la {policy}).","Auth.form.register.subtitle":"Sus credenciales solo se utilizan para autenticarse en el panel de administraci\xF3n. Todos los datos guardados se almacenar\xE1n en su propia base de datos.","Auth.form.rememberMe.label":"Recu\xE9rdame","Auth.form.username.label":"Usuario","Auth.form.username.placeholder":"Kai Doe","Auth.form.welcome.subtitle":"Inicie sesi\xF3n en su cuenta de Strapi","Auth.form.welcome.title":"Bienvenido!","Auth.link.forgot-password":"\xBFOlvid\xF3 su contrase\xF1a?","Auth.link.ready":"\xBFListo para iniciar sesi\xF3n?","Auth.link.signin":"Registrarse","Auth.link.signin.account":"\xBFYa tienes una cuenta?","Auth.login.sso.divider":"O inicia sesi\xF3n con","Auth.login.sso.loading":"Cargando proveedores...","Auth.login.sso.subtitle":"Inicie sesi\xF3n en su cuenta a trav\xE9s de SSO","Auth.privacy-policy-agreement.policy":"pol\xEDtica de privacidad","Auth.privacy-policy-agreement.terms":"condiciones","Content Manager":"Gestor de Contenidos","Content Type Builder":"Constructor de Tipos de Contenido",Documentation:a,Email:t,"Files Upload":"Subida de archivos","HomePage.helmet.title":"P\xE1gina principal","HomePage.roadmap":"Vea nuestra hoja de ruta","HomePage.welcome.congrats":"\xA1Felicidades!","HomePage.welcome.congrats.content":`Est\xE1 registrado como el primer administrador. 
Para descubrir las potentes funciones que ofrece Strapi,`,"HomePage.welcome.congrats.content.bold":"le recomendamos que cree su primer Tipo de Colecci\xF3n.","Media Library":"Biblioteca de Multimedia","New entry":"Entrada nueva",Password:i,Provider:r,ResetPasswordToken:s,Role:c,"Roles & Permissions":"Roles y Permisos","Roles.ListPage.notification.delete-all-not-allowed":"Algunos roles no se pudieron eliminar porque est\xE1n asociados a usuarios","Roles.ListPage.notification.delete-not-allowed":"No se puede eliminar un rol si est\xE1 asociado a usuarios","Roles.RoleRow.select-all":"Seleccione {name} para acciones en bloque","Roles.components.List.empty.withSearch":"No hay rol correspondiente a la b\xFAsqueda ({search})...","Settings.PageTitle":"Configuraci\xF3n - {name}","Settings.apiTokens.addFirstToken":"Agrega tu primer token de API","Settings.apiTokens.addNewToken":"Agregar nuevo token de API","Settings.tokens.copy.editMessage":"Por razones de seguridad, solo puede ver su token una vez.","Settings.tokens.copy.editTitle":"Este token ya no es accesible.","Settings.tokens.copy.lastWarning":"\xA1Aseg\xFArate de copiar este token, no podr\xE1s volver a verlo!","Settings.apiTokens.create":"A\xF1adir entrada","Settings.apiTokens.description":"Lista de tokens generados para consumir la API","Settings.apiTokens.emptyStateLayout":"A\xFAn no tienes ning\xFAn contenido ...","Settings.tokens.notification.copied":"Token copiado al portapapeles.","Settings.apiTokens.title":"Tokens de API","Settings.apiTokens.lastHour":"\xFAltima hora","Settings.tokens.ListView.headers.createdAt":"Creado en","Settings.tokens.ListView.headers.description":"Descripci\xF3n","Settings.tokens.ListView.headers.lastUsedAt":"\xDAltimo uso","Settings.tokens.ListView.headers.name":"Nombre","Settings.tokens.types.full-access":"Acceso completo","Settings.tokens.types.read-only":"Solo lectura","Settings.application.description":"Informaci\xF3n global del panel de administraci\xF3n","Settings.application.edition-title":"plan actual","Settings.application.get-help":"Consigue ayuda","Settings.application.link-pricing":"Ver todos los planes","Settings.application.link-upgrade":"Actualiza tu panel de administraci\xF3n","Settings.application.node-version":"versi\xF3n de node","Settings.application.strapi-version":"versi\xF3n de strapi","Settings.application.strapiVersion":"versi\xF3n de strapi","Settings.application.title":"Descripci\xF3n general","Settings.error":"Error","Settings.global":"Configuraci\xF3n global","Settings.permissions":"Panel de administraci\xF3n","Settings.permissions.category":"Configuraci\xF3n de permisos para {category}","Settings.permissions.category.plugins":"Configuraci\xF3n de permisos para el plugin de {category}","Settings.permissions.conditions.anytime":"En cualquier momento","Settings.permissions.conditions.apply":"Aplicar","Settings.permissions.conditions.can":"Poder","Settings.permissions.conditions.conditions":"Definir condiciones","Settings.permissions.conditions.links":"Enlaces","Settings.permissions.conditions.no-actions":"No hay acci\xF3n","Settings.permissions.conditions.none-selected":"En cualquier momento","Settings.permissions.conditions.or":"O","Settings.permissions.conditions.when":"Cuando","Settings.permissions.select-all-by-permission":"Seleccionar todos los permisos de {label}","Settings.permissions.select-by-permission":"Seleccionar permiso de {label}","Settings.permissions.users.create":"Crear nuevo usuario","Settings.permissions.users.email":"Correo electr\xF3nico","Settings.permissions.users.firstname":"Nombre","Settings.permissions.users.lastname":"Apellido","Settings.permissions.users.form.sso":"connect with sso","Settings.permissions.users.form.sso.description":"when enabled (on), users can login via sso","Settings.permissions.users.listview.header.subtitle":"Todos los usuarios que tienen acceso al panel de administraci\xF3n de strapi","Settings.permissions.users.tabs.label":"Permisos de pesta\xF1as","Settings.profile.form.notify.data.loaded":"Se han cargado los datos de tu perfil","Settings.profile.form.section.experience.clear.select":"Borrar el idioma de interfaz seleccionado","Settings.profile.form.section.experience.here":"documentaci\xF3n","Settings.profile.form.section.experience.interfaceLanguage":"Idioma de interfaz","Settings.profile.form.section.experience.interfaceLanguage.hint":"Esto solo mostrar\xE1 su propia interfaz en el idioma elegido.","Settings.profile.form.section.experience.interfaceLanguageHelp":"La selecci\xF3n cambiar\xE1 el idioma de la interfaz solo para usted. Consulte esta {here} para que otros idiomas est\xE9n disponibles para su equipo.","Settings.profile.form.section.experience.title":"Experiencia","Settings.profile.form.section.helmet.title":"Perfil de usuario","Settings.profile.form.section.profile.page.title":"P\xE1gina de perfil","Settings.roles.create.description":"Definir los derechos otorgados al rol","Settings.roles.create.title":"Crea un rol","Settings.roles.created":"Rol creado","Settings.roles.edit.title":"Editar un rol","Settings.roles.form.button.users-with-role":"Usuarios con este rol","Settings.roles.form.created":"Creado","Settings.roles.form.description":"Nombre y descripci\xF3n del rol","Settings.roles.form.permission.property-label":"permisos de {label}","Settings.roles.form.permissions.attributesPermissions":"Permisos de los campos","Settings.roles.form.permissions.create":"Crear","Settings.roles.form.permissions.delete":"Eliminar","Settings.roles.form.permissions.publish":"Publicar","Settings.roles.form.permissions.read":"Leer","Settings.roles.form.permissions.update":"Actualizar","Settings.roles.list.button.add":"Agregar nuevo rol","Settings.roles.list.description":"Lista de roles","Settings.roles.title.singular":"rol","Settings.sso.description":"Configure los ajustes para la funci\xF3n de inicio de sesi\xF3n \xFAnico (SSO).","Settings.sso.form.defaultRole.description":"Asociar\xE1 al nuevo usuario autenticado al rol seleccionado","Settings.sso.form.defaultRole.description-not-allowed":"Debes tener permiso para leer los roles de administrador.","Settings.sso.form.defaultRole.label":"Rol predeterminado","Settings.sso.form.registration.description":"Crear un nuevo usuario en el inicio de sesi\xF3n (SSO) si no existe una cuenta","Settings.sso.form.registration.label":"Auto-registro","Settings.sso.title":"Inicio de sesi\xF3n \xFAnico (SSO)","Settings.webhooks.create":"Crea un webhook","Settings.webhooks.create.header":"Crea un nuevo encabezado","Settings.webhooks.created":"Webhook creado","Settings.webhooks.event.publish-tooltip":"Este evento solo existe para contenidos con el sistema Borrador/Publicaci\xF3n habilitado","Settings.webhooks.events.create":"Crear","Settings.webhooks.events.update":"Actualizar","Settings.webhooks.form.events":"Eventos","Settings.webhooks.form.headers":"Encabezados","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"eliminar fila de encabezado {number}","Settings.webhooks.key":"Clave","Settings.webhooks.list.button.add":"Agregar nuevo webhook","Settings.webhooks.list.description":"Recibe notificaciones de cambios POST.","Settings.webhooks.list.empty.description":"Agregue el primero a esta lista.","Settings.webhooks.list.empty.link":"Ver nuestra documentaci\xF3n","Settings.webhooks.list.empty.title":"Todav\xEDa no hay webhooks","Settings.webhooks.list.th.actions":"acciones","Settings.webhooks.list.th.status":"estado","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# recurso seleccionado} other {# recursos seleccionados}}","Settings.webhooks.trigger":"Desencadenante","Settings.webhooks.trigger.cancel":"Cancelar disparador","Settings.webhooks.trigger.pending":"Pendiente\u2026","Settings.webhooks.trigger.save":"Guarde para desencadenar","Settings.webhooks.trigger.success":"\xA1\xC9xito!","Settings.webhooks.trigger.success.label":"Desencadenante \xE9xitoso","Settings.webhooks.trigger.test":"Probar desencadenante","Settings.webhooks.trigger.title":"Guardar antes de desencadenar","Settings.webhooks.value":"Valor",Username:l,Users:p,"Users & Permissions":"Usuarios y permisos","Users.components.List.empty":"No hay usuarios...","Users.components.List.empty.withFilters":"No hay usuarios con los filtros aplicados...","Users.components.List.empty.withSearch":"No hay usuarios correspondientes a la b\xFAsqueda ({search})...","admin.pages.MarketPlacePage.helmet":"Marketplace - Plugins","admin.pages.MarketPlacePage.submit.plugin.link":"Env\xEDe su plugin","admin.pages.MarketPlacePage.subtitle":"Saca m\xE1s partido a Strapi",anErrorOccurred:d,"app.component.CopyToClipboard.label":"Copiar al portapapeles","app.component.search.label":"Buscar {target}","app.component.table.duplicate":"Copiar {target}","app.component.table.edit":"Editar {target}","app.component.table.select.one-entry":"Seleccionar {target}","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Lea las \xFAltimas noticias sobre Strapi y el ecosistema.","app.components.BlockLink.code":"Ejemplos de c\xF3digo","app.components.BlockLink.code.content":"Aprenda probando proyectos reales desarrollados por la comunidad.","app.components.BlockLink.documentation.content":"Descubra los conceptos esenciales, gu\xEDas e instrucciones.","app.components.BlockLink.tutorial":"Tutoriales","app.components.BlockLink.tutorial.content":"Siga las instrucciones paso a paso para usar y personalizar Strapi.","app.components.Button.cancel":"Cancelar","app.components.Button.confirm":"Confirmar","app.components.Button.reset":"Reiniciar","app.components.ComingSoonPage.comingSoon":"Pr\xF3ximamente","app.components.ConfirmDialog.title":"Confirmaci\xF3n","app.components.DownloadInfo.download":"Descarga en curso...","app.components.DownloadInfo.text":"Esto puede tardar un minuto. Gracias por su paciencia.","app.components.EmptyAttributes.title":"A\xFAn no hay campos","app.components.EmptyStateLayout.content-document":"No se encontr\xF3 contenido","app.components.EmptyStateLayout.content-permissions":"No tienes los permisos para acceder a ese contenido.","app.components.HomePage.button.blog":"VER M\xC1S EN EL BLOG","app.components.HomePage.community":"Encuentre la comunidad en la web","app.components.HomePage.community.content":"Hable con los miembros del equipo, colaboradores y desarrolladores en diferentes canales.","app.components.HomePage.create":"Crea tu primer Tipo de Contenido","app.components.HomePage.roadmap":"Vea nuestros pr\xF3ximos objetivos","app.components.HomePage.welcome":"\xA1Bienvenido a bordo!","app.components.HomePage.welcome.again":"\xA1Bienvenido ","app.components.HomePage.welcomeBlock.content":"Estamos felices de tenerlo como miembro de la comunidad. Estamos constantemente en busca de comentarios as\xED que no dude en enviarnos un DM en ","app.components.HomePage.welcomeBlock.content.again":"Esperamos que est\xE9s progresando en tu proyecto.... Si\xE9ntase libre de leer las \xFAltimas novedades sobre Strapi. Estamos dando lo mejor de nosotros mismos para mejorar el producto bas\xE1ndonos en sus comentarios.","app.components.HomePage.welcomeBlock.content.issues":"problema.","app.components.HomePage.welcomeBlock.content.raise":" o reportar cualquier ","app.components.ImgPreview.hint":"Arrastre y suelte el archivo en esta \xE1rea o {browse} para subir un archivo.","app.components.ImgPreview.hint.browse":"buscar","app.components.InputFile.newFile":"A\xF1adir nuevo archivo","app.components.InputFileDetails.open":"Abrir en una nueva pesta\xF1a","app.components.InputFileDetails.originalName":"Nombre original:","app.components.InputFileDetails.remove":"Eliminar este archivo","app.components.InputFileDetails.size":"Tama\xF1o:","app.components.InstallPluginPage.Download.description":"La descarga e instalaci\xF3n del plugin podr\xEDa llevar unos segundos.","app.components.InstallPluginPage.Download.title":"Descargando...","app.components.InstallPluginPage.description":"Extienda su aplicaci\xF3n sin esfuerzo.","app.components.LeftMenu.collapse":"Contraer la barra de navegaci\xF3n","app.components.LeftMenu.expand":"Expandir la barra de navegaci\xF3n","app.components.LeftMenu.logout":"Cerrar sesi\xF3n","app.components.LeftMenuFooter.help":"Ayuda","app.components.LeftMenuFooter.poweredBy":"Potenciado por ","app.components.LeftMenuLinkContainer.collectionTypes":"Tipos de Colecci\xF3n","app.components.LeftMenuLinkContainer.configuration":"Configuraciones","app.components.LeftMenuLinkContainer.general":"General","app.components.LeftMenuLinkContainer.noPluginsInstalled":"No hay plugins instalados todav\xEDa","app.components.LeftMenuLinkContainer.plugins":"Plugins","app.components.LeftMenuLinkContainer.singleTypes":"Tipos \xDAnicos","app.components.ListPluginsPage.deletePlugin.description":"Es posible que la desinstalaci\xF3n del plugin tarde unos segundos.","app.components.ListPluginsPage.deletePlugin.title":"Desinstalar","app.components.ListPluginsPage.description":"Lista de los plugins instalados en el proyecto.","app.components.ListPluginsPage.helmet.title":"Lista de plugins","app.components.Logout.logout":"Cerrar sesi\xF3n","app.components.Logout.profile":"Perfil","app.components.MarketplaceBanner":"Descubra los plugins creados por la comunidad y muchas m\xE1s cosas incre\xEDbles para impulsar su proyecto, en Strapi Awesome.","app.components.MarketplaceBanner.image.alt":"un logo de cohete strapi","app.components.MarketplaceBanner.link":"\xC9chale un vistazo ahora","app.components.NotFoundPage.back":"Volver a la p\xE1gina de inicio","app.components.NotFoundPage.description":"No encontrado","app.components.Official":"Oficial","app.components.Onboarding.help.button":"Boton de ayuda","app.components.Onboarding.label.completed":"% completado","app.components.Onboarding.title":"V\xEDdeos introductorios","app.components.PluginCard.Button.label.download":"Descargar","app.components.PluginCard.Button.label.install":"Ya instalado","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"La funci\xF3n de recarga autom\xE1tica debe estar desactivada. Por favor, inicie su aplicaci\xF3n con `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"\xA1Entendido!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Por motivos de seguridad, el plugin s\xF3lo puede descargarse en entorno de desarrollo.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Imposible descargar","app.components.PluginCard.compatible":"Compatible con su aplicaci\xF3n","app.components.PluginCard.compatibleCommunity":"Compatible con la comunidad","app.components.PluginCard.more-details":"M\xE1s detalles","app.components.ToggleCheckbox.off-label":"Apagado","app.components.ToggleCheckbox.on-label":"Encendido","app.components.Users.MagicLink.connect":"Env\xEDe este enlace al usuario para que se conecte.","app.components.Users.MagicLink.connect.sso":"Env\xEDe este enlace al usuario, el primer inicio de sesi\xF3n se puede realizar a trav\xE9s de un proveedor de SSO","app.components.Users.ModalCreateBody.block-title.details":"Detalles","app.components.Users.ModalCreateBody.block-title.roles":"Roles del usuario","app.components.Users.ModalCreateBody.block-title.roles.description":"Un usuario puede tener uno o varios roles.","app.components.Users.SortPicker.button-label":"Ordenar por","app.components.Users.SortPicker.sortby.email_asc":"Correo electr\xF3nico (de la A a la Z)","app.components.Users.SortPicker.sortby.email_desc":"Correo electr\xF3nico (de la Z a la A)","app.components.Users.SortPicker.sortby.firstname_asc":"Nombre (de la A a la Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Nombre (de la Z a la A)","app.components.Users.SortPicker.sortby.lastname_asc":"Apellido (de la A a la Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Apellido (de la Z a la A)","app.components.Users.SortPicker.sortby.username_asc":"Nombre de usuario (de la A a la Z)","app.components.Users.SortPicker.sortby.username_desc":"Nombre de usuario (de la Z a la A)","app.components.listPlugins.button":"A\xF1adir nuevo plugin","app.components.listPlugins.title.none":"No hay plugins instalados","app.components.listPluginsPage.deletePlugin.error":"Se ha producido un error al desinstalar el plugin","app.containers.App.notification.error.init":"Se produjo un error al solicitar la API","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Si no recibe este enlace, comun\xEDquese con su administrador.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"Es posible que tarde unos minutos en recibir el enlace de recuperaci\xF3n de contrase\xF1a.","app.containers.AuthPage.ForgotPasswordSuccess.title":"Correo electr\xF3nico enviado","app.containers.Users.EditPage.form.active.label":"Activo","app.containers.Users.EditPage.header.label":"Editar {name}","app.containers.Users.EditPage.header.label-loading":"Editar usuario","app.containers.Users.EditPage.roles-bloc-title":"Roles atribuidos","app.containers.Users.ModalForm.footer.button-success":"Crear usuario","app.links.configure-view":"Configurar la vista","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"A\xF1adir filtro","app.utils.close-label":"Cerrar","app.utils.defaultMessage":" ","app.utils.duplicate":"Duplicar","app.utils.edit":"Editar","app.utils.errors.file-too-big.message":"El archivo es demasiado grande","app.utils.filter-value":"Filtro","app.utils.filters":"Filtros","app.utils.notify.data-loaded":"{target} se ha cargado","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Publicar","app.utils.select-all":"Seleccionar todo","app.utils.select-field":"Seleccionar campo","app.utils.select-filter":"Seleccionar filtro","app.utils.unpublish":"Anular publicaci\xF3n",clearLabel:m,"coming.soon":"\xA1Este contenido est\xE1 actualmente en construcci\xF3n y estar\xE1 de regreso en unas semanas!","component.Input.error.validation.integer":"El valor debe ser un n\xFAmero entero","components.AutoReloadBlocker.description":"Inicia Strapi con uno de los siguientes comandos:","components.AutoReloadBlocker.header":"Es necesario recargar para este plugin.","components.ErrorBoundary.title":"Algo sali\xF3 mal...","components.FilterOptions.FILTER_TYPES.$contains":"contiene","components.FilterOptions.FILTER_TYPES.$containsi":"contiene (insensible a may\xFAsculas y min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$endsWith":"termina con","components.FilterOptions.FILTER_TYPES.$endsWithi":"termina con (insensible a may\xFAsculas y min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$eq":"es","components.FilterOptions.FILTER_TYPES.$eqi":"es (insensible a may\xFAsculas y min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$gt":"es mayor que","components.FilterOptions.FILTER_TYPES.$gte":"es mayor o igual a","components.FilterOptions.FILTER_TYPES.$lt":"es menor que","components.FilterOptions.FILTER_TYPES.$lte":"es menor o igual a","components.FilterOptions.FILTER_TYPES.$ne":"no es","components.FilterOptions.FILTER_TYPES.$nei":"no es (insensible a may\xFAsculas y min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$notContains":"no contiene","components.FilterOptions.FILTER_TYPES.$notContainsi":"no contiene (insensible a may\xFAsculas y min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$notNull":"is not null","components.FilterOptions.FILTER_TYPES.$null":"is null","components.FilterOptions.FILTER_TYPES.$startsWith":"comienza con","components.FilterOptions.FILTER_TYPES.$startsWithi":"comienza con (insensible a may\xFAsculas y min\xFAsculas)","components.Input.error.attribute.key.taken":"Este valor ya existe","components.Input.error.attribute.sameKeyAndName":"No puede ser igual","components.Input.error.attribute.taken":"Este nombre de campo ya existe","components.Input.error.contain.lowercase":"La contrase\xF1a debe contener al menos un car\xE1cter en min\xFAscula","components.Input.error.contain.number":"La contrase\xF1a debe contener al menos un n\xFAmero","components.Input.error.contain.uppercase":"La contrase\xF1a debe contener al menos un car\xE1cter en may\xFAscula","components.Input.error.contentTypeName.taken":"Este nombre ya existe","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"Las contrase\xF1as no coinciden","components.Input.error.validation.email":"Esto no es un email","components.Input.error.validation.json":"Esto no coincide con el formato JSON","components.Input.error.validation.max":"El valor es demasiado alto {max}.","components.Input.error.validation.maxLength":"El valor es demasiado largo {max}.","components.Input.error.validation.min":"El valor es demasiado bajo {min}.","components.Input.error.validation.minLength":"El valor es demasiado corto {min}.","components.Input.error.validation.minSupMax":"No puede ser superior","components.Input.error.validation.regex":"El valor no coincide con el de regex.","components.Input.error.validation.required":"Este valor es obligatorio.","components.Input.error.validation.unique":"Este valor ya se utiliza.","components.InputSelect.option.placeholder":"Elige aqu\xED","components.ListRow.empty":"No hay datos que mostrar.","components.NotAllowedInput.text":"Sin permisos para ver este campo.","components.OverlayBlocker.description":"Est\xE1 utilizando una funci\xF3n que necesita que el servidor se reinicie. Por favor, espere hasta que el servidor est\xE9 listo..","components.OverlayBlocker.description.serverError":"El servidor deber\xEDa haberse reiniciado, compruebe sus logs en el terminal.","components.OverlayBlocker.title":"Esperando el reinicio...","components.OverlayBlocker.title.serverError":"El reinicio est\xE1 llevando m\xE1s tiempo de lo esperado","components.PageFooter.select":"entradas por p\xE1gina","components.ProductionBlocker.description":"Por razones de seguridad tenemos que desactivar este plugin en otros entornos.","components.ProductionBlocker.header":"Este plugin s\xF3lo est\xE1 disponible en entornos de desarrollo.","components.Search.placeholder":"Buscar...","components.TableHeader.sort":"Ordenar por {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Modo de Markdown","components.Wysiwyg.ToggleMode.preview-mode":"Modo de vista previa","components.Wysiwyg.collapse":"Contraer men\xFA","components.Wysiwyg.selectOptions.H1":"T\xEDtulo H1","components.Wysiwyg.selectOptions.H2":"T\xEDtulo H2","components.Wysiwyg.selectOptions.H3":"T\xEDtulo H3","components.Wysiwyg.selectOptions.H4":"T\xEDtulo H4","components.Wysiwyg.selectOptions.H5":"T\xEDtulo H5","components.Wysiwyg.selectOptions.H6":"T\xEDtulo H6","components.Wysiwyg.selectOptions.title":"A\xF1adir un t\xEDtulo","components.WysiwygBottomControls.charactersIndicators":"caracteres","components.WysiwygBottomControls.fullscreen":"Expandir","components.WysiwygBottomControls.uploadFiles":"Arrastrar y soltar archivos, pegar desde el portapapeles o {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"seleccionarlos","components.pagination.go-to":"Ir a la p\xE1gina {page}","components.pagination.go-to-next":"Ir a la p\xE1gina siguiente","components.pagination.go-to-previous":"Regresar a la p\xE1gina anterior","components.pagination.remaining-links":"Y {number} enlaces m\xE1s","components.popUpWarning.button.cancel":"No, cancelar","components.popUpWarning.button.confirm":"S\xED, confirmar","components.popUpWarning.message":"\xBFEst\xE1s seguro de que quieres borrar esto?","components.popUpWarning.title":"Por favor, confirme","content-manager.App.schemas.data-loaded":"Los esquemas se han cargado correctamente.","content-manager.ListViewTable.relation-loaded":"Las relaciones se han cargado","content-manager.EditRelations.title":"Datos relacionados","content-manager.HeaderLayout.button.label-add-entry":"Crear nueva entrada","content-manager.api.id":"ID de API","content-manager.components.AddFilterCTA.add":"Filtros","content-manager.components.AddFilterCTA.hide":"Filtros","content-manager.components.DragHandle-label":"Arrastrar","content-manager.components.DraggableAttr.edit":"Click para editar","content-manager.components.DraggableCard.delete.field":"Borrar {item}","content-manager.components.DraggableCard.edit.field":"Editar {item}","content-manager.components.DraggableCard.move.field":"Mover {item}","content-manager.components.ListViewTable.row-line":"n\xFAmero de elemento {number}","content-manager.components.DynamicZone.ComponentPicker-label":"Elija un componente","content-manager.components.DynamicZone.add-component":"Agregue un componente a {componentName}","content-manager.components.DynamicZone.delete-label":"Eliminar {name}","content-manager.components.DynamicZone.error-message":"El componente contiene errore(s)","content-manager.components.DynamicZone.missing-components":"Hay {number, plural, =0 {# componentes faltantes} one {# componente faltante} other {# componentes faltantes}}","content-manager.components.DynamicZone.move-down-label":"Mover componente hacia abajo","content-manager.components.DynamicZone.move-up-label":"Mover componente hacia arriba","content-manager.components.DynamicZone.pick-compo":"Elija un componente","content-manager.components.DynamicZone.required":"Se requiere un componente","content-manager.components.EmptyAttributesBlock.button":"Ir a la p\xE1gina de configuraciones","content-manager.components.EmptyAttributesBlock.description":"Usted puede cambiar sus configuraciones","content-manager.components.FieldItem.linkToComponentLayout":"Establecer el dise\xF1o del componente","content-manager.components.FieldSelect.label":"Agregar un campo","content-manager.components.FilterOptions.button.apply":"Aplicar","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Aplicar","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Limpiar todo","content-manager.components.FiltersPickWrapper.PluginHeader.description":"Establece las condiciones a aplicar para filtrar registros","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Filtros","content-manager.components.FiltersPickWrapper.hide":"Ocultar","content-manager.components.LeftMenu.Search.label":"Buscar un tipo de contenido","content-manager.components.LeftMenu.collection-types":"Tipos de colecci\xF3n","content-manager.components.LeftMenu.single-types":"Tipos individuales","content-manager.components.LimitSelect.itemsPerPage":"registros por p\xE1gina","content-manager.components.NotAllowedInput.text":"Sin permisos para ver este campo","content-manager.components.RepeatableComponent.error-message":"Los componentes contienen errores","content-manager.components.Search.placeholder":"Buscar un registro...","content-manager.components.Select.draft-info-title":"Estado: Borrador","content-manager.components.Select.publish-info-title":"Estado: Publicado","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Personaliza c\xF3mo se ver\xE1 la vista de edici\xF3n.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Defina la configuraci\xF3n de la vista de lista.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Configurar la vista - {name}","content-manager.components.TableDelete.delete":"Eliminar todo","content-manager.components.TableDelete.deleteSelected":"Eliminar seleccionados","content-manager.components.TableDelete.label":"{number, plural, one {# entrada seleccionada} other {# entradas seleccionads}}","content-manager.components.TableEmpty.withFilters":"No hay {contentType} con los filtros aplicados...","content-manager.components.TableEmpty.withSearch":"No hay {contentType} que coincida con la b\xFAsqueda ({search})...","content-manager.components.TableEmpty.withoutFilter":"No hay {contentType}...","content-manager.components.empty-repeatable":"A\xFAn no hay entrada. Haga clic en el bot\xF3n de abajo para agregar uno.","content-manager.components.notification.info.maximum-requirement":"Ya has alcanzado el n\xFAmero m\xE1ximo de campos","content-manager.components.notification.info.minimum-requirement":"Se ha agregado un campo para cumplir con el requisito m\xEDnimo","content-manager.components.repeatable.reorder.error":"Se produjo un error al reordenar el campo de su componente. Vuelva a intentarlo.","content-manager.components.reset-entry":"Restablecer entrada","content-manager.components.uid.apply":"aplicar","content-manager.components.uid.available":"disponible","content-manager.components.uid.regenerate":"regenerado","content-manager.components.uid.suggested":"sugerido","content-manager.components.uid.unavailable":"no disponible","content-manager.containers.Edit.Link.Layout":"Configurar el layout","content-manager.containers.Edit.Link.Model":"Edita el Tipo de Colecci\xF3n","content-manager.containers.Edit.addAnItem":"Agregar un registro...","content-manager.containers.Edit.clickToJump":"Click para ir al registro","content-manager.containers.Edit.delete":"Eliminar","content-manager.containers.Edit.delete-entry":"Eliminar esta entrada","content-manager.containers.Edit.editing":"Editando...","content-manager.containers.Edit.information":"Informaci\xF3n","content-manager.containers.Edit.information.by":"Por","content-manager.containers.Edit.information.created":"Creado","content-manager.containers.Edit.information.draftVersion":"versi\xF3n preliminar","content-manager.containers.Edit.information.editing":"Edici\xF3n","content-manager.containers.Edit.information.lastUpdate":"\xDAltima actualizaci\xF3n","content-manager.containers.Edit.information.publishedVersion":"versi\xF3n publicada","content-manager.containers.Edit.pluginHeader.title.new":"Crea una entrada","content-manager.containers.Edit.reset":"Reiniciar","content-manager.containers.Edit.returnList":"Regresar a la lista","content-manager.containers.Edit.seeDetails":"Detalles","content-manager.containers.Edit.submit":"Guardar","content-manager.containers.EditSettingsView.modal-form.edit-field":"Edita el campo","content-manager.containers.EditView.add.new-entry":"Agregar una entrada","content-manager.containers.EditView.notification.errors":"El formulario contiene algunos errores","content-manager.containers.Home.introduction":"Para editar sus registros vaya al link en espec\xEDfico en el menu de la izquierda. Este plugin no tiene una manera de editar configuraciones y a\xFAn esta en continuo desarrollo.","content-manager.containers.Home.pluginHeaderDescription":"Gestiona sus registros en una bella y poderoza interfaz.","content-manager.containers.Home.pluginHeaderTitle":"Gestor de Contenido","content-manager.containers.List.draft":"Borrador","content-manager.containers.List.errorFetchRecords":"Error","content-manager.containers.List.published":"Publicado","content-manager.containers.ListPage.displayedFields":"Campos mostrados","content-manager.containers.ListPage.items":"{number, plural, =0 {elementos} one {elemento} other {elementos}}","content-manager.containers.ListPage.table-headers.publishedAt":"Estado","content-manager.containers.ListSettingsView.modal-form.edit-label":"Edita la etiqueta","content-manager.containers.SettingPage.add.field":"Insertar otro campo","content-manager.containers.SettingPage.attributes":"Campos de atributos","content-manager.containers.SettingPage.attributes.description":"Defina el orden de sus atributos","content-manager.containers.SettingPage.editSettings.description":"Arrastra y suelta los campos para construir el dise\xF1o","content-manager.containers.SettingPage.editSettings.entry.title":"T\xEDtulo de la entrada","content-manager.containers.SettingPage.editSettings.entry.title.description":"Establece el campo para mostar en tu entrada","content-manager.containers.SettingPage.editSettings.relation-field.description":"Establece el campo mostrado en las vistas de lista y edici\xF3n","content-manager.containers.SettingPage.editSettings.title":"Editar (configuraciones)","content-manager.containers.SettingPage.layout":"Layout","content-manager.containers.SettingPage.listSettings.description":"Configure las opciones para este Tipo de Colecci\xF3n","content-manager.containers.SettingPage.listSettings.title":"Lista (configuraciones)","content-manager.containers.SettingPage.pluginHeaderDescription":"Configure los ajustes espec\xEDficos para este Tipo de Colecci\xF3n","content-manager.containers.SettingPage.settings":"Ajustes","content-manager.containers.SettingPage.view":"Ver","content-manager.containers.SettingViewModel.pluginHeader.title":"Administrador de contenido - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Configuraciones espec\xEDficas","content-manager.containers.SettingsPage.Block.contentType.title":"Tipos de Colecci\xF3n","content-manager.containers.SettingsPage.Block.generalSettings.description":"Configure las opciones predeterminadas para sus Tipos de Colecci\xF3n","content-manager.containers.SettingsPage.Block.generalSettings.title":"General","content-manager.containers.SettingsPage.pluginHeaderDescription":"Configure los ajustes para todos sus Tipos y Grupos de Colecciones","content-manager.containers.SettingsView.list.subtitle":"Configure el dise\xF1o y la visualizaci\xF3n de sus Tipos y Grupos de Colecciones","content-manager.containers.SettingsView.list.title":"Configuraciones de pantalla","content-manager.edit-settings-view.link-to-ctb.components":"Edita el componente","content-manager.edit-settings-view.link-to-ctb.content-types":"Edita el tipo de contenido","content-manager.emptyAttributes.button":"Ir al constructor de Tipo de Colecci\xF3n","content-manager.emptyAttributes.description":"Agregue su primer campo a su Tipo de Colecci\xF3n","content-manager.emptyAttributes.title":"A\xFAn no hay campos","content-manager.error.attribute.key.taken":"Este valor ya existe","content-manager.error.attribute.sameKeyAndName":"No pueden ser iguales","content-manager.error.attribute.taken":"Este campo ya existe","content-manager.error.contentTypeName.taken":"Este nombre ya existe","content-manager.error.model.fetch":"Ocurri\xF3 un error durante la consulta de configuraci\xF3n de modelos.","content-manager.error.record.create":"Ocurri\xF3 un error durante la creaci\xF3n del registro.","content-manager.error.record.delete":"Ocurri\xF3 un error durante la eliminaci\xF3n del registro.","content-manager.error.record.fetch":"Ocurri\xF3 un error durante la consulta del registro.","content-manager.error.record.update":"Ocurri\xF3 un error durante la actualizaci\xF3n del registro.","content-manager.error.records.count":"Ocurri\xF3 un error durante la consulta del n\xFAmero de registros.","content-manager.error.records.fetch":"Ocurri\xF3 un error durante la consulta de registros.","content-manager.error.schema.generation":"Ocurri\xF3 un error durante la generaci\xF3n de esquema.","content-manager.error.validation.json":"Este no es un JSON","content-manager.error.validation.max":"El valor es muy alto.","content-manager.error.validation.maxLength":"El valor es muy largo.","content-manager.error.validation.min":"El valor es muy bajo.","content-manager.error.validation.minLength":"El valor es muy corto.","content-manager.error.validation.minSupMax":"No puede ser superior","content-manager.error.validation.regex":"El valor no cumple la expresi\xF3n regular.","content-manager.error.validation.required":"Este dato es requerido.","content-manager.form.Input.bulkActions":"Habilitar acciones en bloque","content-manager.form.Input.defaultSort":"Atributo para ordenar por defecto","content-manager.form.Input.description":"Descripci\xF3n","content-manager.form.Input.description.placeholder":"Mostrar nombre en el perf\xEDl","content-manager.form.Input.editable":"Campo editable","content-manager.form.Input.filters":"Habilitar filtros","content-manager.form.Input.label":"Etiqueta","content-manager.form.Input.label.inputDescription":"Este valor sobrescribe la etiqueta mostrada en la cabecera de la tabla","content-manager.form.Input.pageEntries":"Entradas por p\xE1gina","content-manager.form.Input.pageEntries.inputDescription":"Nota: Puede anular este valor en la p\xE1gina de configuraci\xF3n de Tipo de Colecci\xF3n.","content-manager.form.Input.placeholder":"Placeholder","content-manager.form.Input.placeholder.placeholder":"Mi valor maravilloso","content-manager.form.Input.search":"Habilitar la b\xFAsqueda","content-manager.form.Input.search.field":"Habilitar la b\xFAsqueda para este campo","content-manager.form.Input.sort.field":"Habilitar ordenado para este campo","content-manager.form.Input.sort.order":"Orden por defecto","content-manager.form.Input.wysiwyg":"Mostrar como WYSIWYG","content-manager.global.displayedFields":"Campos mostrados","content-manager.groups":"Grupos","content-manager.groups.numbered":"Grupos ({number})","content-manager.header.name":"Contenido","content-manager.link-to-ctb":"Edita el modelo","content-manager.models":"Tipos de Colecci\xF3n","content-manager.models.numbered":"Tipos de Colecci\xF3n ({number})","content-manager.notification.error.displayedFields":"Usted necesita al menos un campo mostrado","content-manager.notification.error.relationship.fetch":"Ocurri\xF3 un error durante la consulta de la relaci\xF3n.","content-manager.notification.info.SettingPage.disableSort":"Necesita tener un habilidato el ordenado en un atributo","content-manager.notification.info.minimumFields":"Debe tener al menos un campo mostrado","content-manager.notification.upload.error":"Se produjo un error al subir sus archivos","content-manager.pageNotFound":"P\xE1gina no encontrada","content-manager.pages.ListView.header-subtitle":"{number, plural, =0 {# entradas encontradas} one {# entrada encontrada} other {# entradas encontradas}}","content-manager.pages.NoContentType.button":"Crea tu primer tipo de contenido","content-manager.pages.NoContentType.text":"A\xFAn no tiene ning\xFAn contenido, le recomendamos que cree su primer tipo de contenido.","content-manager.permissions.not-allowed.create":"No se le permite crear un documento","content-manager.permissions.not-allowed.update":"No se le permite ver este documento","content-manager.plugin.description.long":"Ver, editar y eliminar informaci\xF3n de su base de datos de manera r\xE1pida.","content-manager.plugin.description.short":"Ver, editar y eliminar informaci\xF3n de su base de datos de manera r\xE1pida.","content-manager.popover.display-relations.label":"Display relations","content-manager.success.record.delete":"Eliminado","content-manager.success.record.publish":"Publicado","content-manager.success.record.save":"Guardado","content-manager.success.record.unpublish":"Sin publicar","content-manager.utils.data-loaded":"{number, plural, =1 {La entrada se ha cargado correctamente} other {Las entradas se han cargado correctamente}}","content-manager.popUpWarning.warning.publish-question":"\xBFA\xFAn quieres publicarlo?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Si, publicar","form.button.done":"Hecho","global.prompt.unsaved":"\xBFEst\xE1 seguro de que quiere salir de esta p\xE1gina? Todas sus modificaciones se perder\xE1n","notification.contentType.relations.conflict":"El Tipo de Contenido tiene relaciones conflictivas","notification.default.title":"Informaci\xF3n:","notification.error":"Se ha producido un error","notification.error.layout":"No se pudo recuperar el esquema","notification.form.error.fields":"El formulario contiene algunos errores","notification.form.success.fields":"Cambios guardados","notification.link-copied":"Enlace copiado en el portapapeles","notification.permission.not-allowed-read":"No tienes permiso para ver este documento","notification.success.delete":"El elemento ha sido eliminado","notification.success.saved":"Guardado","notification.success.title":"\xC9xito:","notification.version.update.message":"\xA1Hay una nueva versi\xF3n de Strapi disponible!","notification.warning.title":"Advertencia:",or:u,"request.error.model.unknown":"Este modelo no existe",skipToContent:g,submit:b,"Auth.form.active.label":"Activo","Auth.reset-password.title":"Resetear contrase\xF1a","Roles.RoleRow.user-count":"{number, plural, =0 {#  usuario} one {#  usuario} other {# usuarios}}","Settings.application.customization":"Personalizaci\xF3n","Settings.application.customization.carousel.title":"Logo","Settings.application.customization.carousel.change-action":"Cambiarlogo","Settings.application.customization.carousel.reset-action":"Resetear logo","Settings.application.customization.carousel-slide.label":"Logo slider","Settings.application.customization.carousel-hint":"Cambiar el logo del panel de administraci\xF3n (Dimensi\xF3n m\xE1xima: {dimension}x{dimension}, Tama\xF1o m\xE1ximo del archivo: {size}KB)","Settings.application.customization.modal.cancel":"Cancelar","Settings.application.customization.modal.upload":"Subir logo","Settings.application.customization.modal.tab.label":"\xBFC\xF3mo te gustar\xEDa subir tus archivos?","Settings.application.customization.modal.upload.from-computer":"Desde el ordenador","Settings.application.customization.modal.upload.file-validation":"Dimensi\xF3n m\xE1xima: {dimension}x{dimension}, Tama\xF1o m\xE1ximo: {size}KB","Settings.application.customization.modal.upload.error-format":"Se carg\xF3 un formato incorrecto (formatos aceptados: jpeg, jpg, png, svg).","Settings.application.customization.modal.upload.error-size":"El archivo es muy grande (Dimensi\xF3n m\xE1xima: {dimension}x{dimension},Tama\xF1o m\xE1ximo del archivo: {size}KB)","Settings.application.customization.modal.upload.error-network":"Error de red","Settings.application.customization.modal.upload.cta.browse":"Buscar archivos","Settings.application.customization.modal.upload.drag-drop":"Arrastrar aqu\xED o","Settings.application.customization.modal.upload.from-url":"Desde una url","Settings.application.customization.modal.upload.from-url.input-label":"URL","Settings.application.customization.modal.upload.next":"Siguiente","Settings.application.customization.modal.pending":"Logo pendiente","Settings.application.customization.modal.pending.choose-another":"Elegir otro logo","Settings.application.customization.modal.pending.title":"Logo ya subido","Settings.application.customization.modal.pending.subtitle":"Gestiona el logo elegido antes de subirlo","Settings.application.customization.modal.pending.upload":"Subir logo","Settings.application.customization.modal.pending.card-badge":"imagen","Settings.profile.form.section.experience.mode.label":"Modo de la interfaz","Settings.profile.form.section.experience.mode.hint":"Mostrar la interfaz en el modo seleccionado.","Settings.profile.form.section.experience.mode.option-label":"Mode {name} ",light:x,dark:f,"Usecase.back-end":"Desarrollador Back-end","Usecase.button.skip":"Skip this question","Usecase.content-creator":"Creador de Contenido","Usecase.front-end":"Desarrollador Front-end","Usecase.full-stack":"Desarrollador Full-stack","Usecase.input.work-type":"\xBFQu\xE9 tipo de trabajo realizas?","Usecase.notification.success.project-created":"El proyecto ha sido creado con \xE9xito","Usecase.other":"Otro","Usecase.title":"Dinos algo de ti","admin.pages.MarketPlacePage.offline.title":"Est\xE1s offline","admin.pages.MarketPlacePage.offline.subtitle":"Necesita estar conectado a Internet para acceder a Strapi Market.","admin.pages.MarketPlacePage.plugins":"Plugins","admin.pages.MarketPlacePage.plugin.copy":"Copiar comando de instalaci\xF3n","admin.pages.MarketPlacePage.plugin.copy.success":"Comando de instalaci\xF3n listo para ser pegado en su terminal","admin.pages.MarketPlacePage.plugin.info":"Aprender m\xE1s","admin.pages.MarketPlacePage.plugin.info.label":"Aprender m\xE1s about {pluginName}","admin.pages.MarketPlacePage.plugin.info.text":"Aprender m\xE1s","admin.pages.MarketPlacePage.plugin.installed":"Instalado","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"Hecho por Strapi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"Plugin verificado por Strapi","admin.pages.MarketPlacePage.providers":"Proovedores","admin.pages.MarketPlacePage.search.clear":"Limpiar la b\xFAsqueda","admin.pages.MarketPlacePage.search.empty":'No hay resultados para "{target}"',"admin.pages.MarketPlacePage.search.placeholder":"B\xFAscar","admin.pages.MarketPlacePage.submit.provider.link":"Enviar proveedor","admin.pages.MarketPlacePage.tab-group.label":"Plugins y proveedores para Strapi","admin.pages.MarketPlacePage.missingPlugin.title":"\xBFNecesitas un plugin?","admin.pages.MarketPlacePage.missingPlugin.description":"\xA1D\xEDganos qu\xE9 plugin est\xE1 buscando y le informaremos a los desarrolladores de plugin de nuestra comunidad en caso de que est\xE9n buscando inspiraci\xF3n!","app.components.GuidedTour.CM.create.content":"<p>Cree y administre todo el contenido aqu\xED en el Administrador de contenido.</p><p>Ej: tomando el ejemplo del sitio web del blog m\xE1s all\xE1, uno puede escribir un art\xEDculo, guardarlo y publicarlo como desee.</p>< p>\u{1F4A1} Consejo r\xE1pido: no olvides presionar publicar en el contenido que crees.</p>","app.components.GuidedTour.CM.create.title":"\u26A1\uFE0F Crear contenido","app.components.GuidedTour.CM.success.content":"<p>\xA1Asombroso, un paso m\xE1s!</p><b>\u{1F680}  Mirar el contenido en acci\xF3n</b>","app.components.GuidedTour.CM.success.cta.title":"Testear el API","app.components.GuidedTour.CM.success.title":"Paso 2: Completado \u2705","app.components.GuidedTour.CTB.create.content":"<p>Los tipos de colecci\xF3n lo ayudan a administrar varias entradas, los tipos \xFAnicos son adecuados para administrar solo una entrada.</p> <p>Ej: para un sitio web de blog, Los art\xEDculos ser\xEDan del tipo Colecci\xF3n, mientras que la P\xE1gina de inicio ser\xEDa del tipo \xDAnico.</p>","app.components.GuidedTour.CTB.create.cta.title":"Crear un tipo de colecci\xF3n","app.components.GuidedTour.CTB.create.title":"\u{1F9E0} Crear un primer tipo de colecci\xF3n","app.components.GuidedTour.CTB.success.content":"<p>\xA1Bien hecho!</p><b>\u26A1\uFE0F \xBFQu\xE9 te gustar\xEDa compartir con el mundo?</b>","app.components.GuidedTour.CTB.success.title":"Paso 1: Completado \u2705","app.components.GuidedTour.apiTokens.create.content":"<p>Genera un token de autenticaci\xF3n aqu\xED y recupera el contenido que acabas de crear.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"Generar un token de API","app.components.GuidedTour.apiTokens.create.title":"\u{1F680} Ver contenido en acci\xF3n","app.components.GuidedTour.apiTokens.success.content":"<p>Vea el contenido en acci\xF3n haciendo una solicitud HTTP:</p><ul><li><p>A esta URL: <light>https: //'<'TU_DOMINIO'>'/api/'<'TU_CT'>'</light></p></li><li><p>Con el encabezado: <light>Autorizaci\xF3n: portador '<' TU_API_TOKEN'>'</light></p></li></ul><p>Para obtener m\xE1s formas de interactuar con el contenido, consulte la <documentationLink>documentaci\xF3n</documentationLink>.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Volver a la p\xE1gina de inicio","app.components.GuidedTour.apiTokens.success.title":"Paso 3: Completado \u2705","app.components.GuidedTour.create-content":"Crear contenido","app.components.GuidedTour.home.CM.title":"\u26A1\uFE0F \xBFQu\xE9 te gustar\xEDa compartir con el mundo?","app.components.GuidedTour.home.CTB.cta.title":"Ir al Creador de tipo de contenido","app.components.GuidedTour.home.CTB.title":"\u{1F9E0} Construye la estructura del contenido","app.components.GuidedTour.home.apiTokens.cta.title":"Probar la API","app.components.GuidedTour.skip":"Omitir el recorrido","app.components.GuidedTour.title":"3 pasos para comenzar","app.components.LeftMenu.logo.alt":"Logotipo de la aplicaci\xF3n","app.components.LeftMenu.plugins":"Complementos","app.components.LeftMenu.navbrand.title":"Panel de control de Strapi","app.components.LeftMenu.navbrand.workplace":"Lugar de trabajo","app.page.not.found":"\xA1Vaya! Parece que no podemos encontrar la p\xE1gina que est\xE1s buscando...","components.Input.error.validation.lowercase":"El valor debe ser una cadena en min\xFAsculas","content-manager.ListViewTable.relation-loading":"Las relaciones se est\xE1n cargando","content-manager.ListViewTable.relation-more":"Esta relaci\xF3n contiene m\xE1s entidades de las que se muestran","content-manager.apiError.Este atributo debe ser \xFAnico":"{field} debe ser \xFAnico","form.button.continue":"Continue","global.search":"Buscar","global.actions":"Acciones","global.active":"Activo","global.inactive":"Inactivo","global.back":"Volver","global.cancel":"Cancelar","global.change-password":"Cambiar contrase\xF1a","global.content-manager":"Administrador de Contenido","global.continue":"Continuar","global.delete":"Borrar","global.delete-target":"Borrar {target}","global.description":"Descripci\xF3n","global.details":"Detalles","global.disabled":"Desactivado","global.documentation":"Documentaci\xF3n","global.enabled":"Habilitado","global.finish":"Terminar","global.marketplace":"Marketplace","global.name":"Nombre","global.none":"Ninguno","global.password":"Contrase\xF1a","global.plugins":"Plugins","global.plugins.content-manager":"Administrador de Contenido","global.plugins.content-manager.description":"Forma r\xE1pida de ver, editar y eliminar los datos en tu base de datos.","global.plugins.content-type-builder":"Generador de tipo de contenido","global.plugins.content-type-builder.description":"Modeliza la estructura de datos de tu API. Crea nuevos campos y relaciones en solo un minuto. Los archivos se crean y actualizan autom\xE1ticamente en tu proyecto.","global.plugins.email":"Correo electr\xF3nico","global.plugins.email.description":"Configura tu aplicaci\xF3n para enviar correos electr\xF3nicos.","global.plugins.upload":"Biblioteca Multimedia","global.plugins.upload.description":"Gesti\xF3n de archivos multimedia.","global.plugins.graphql":"GraphQL","global.plugins.graphql.description":"Agrega un punto final de GraphQL con m\xE9todos API predeterminados.","global.plugins.documentation":"Documentaci\xF3n","global.plugins.documentation.description":"Cree un documento OpenAPI y visualice su API con SWAGGER UI.","global.plugins.i18n":"Internacionalizaci\xF3n","global.plugins.i18n.description":"Este complemento permite crear, leer y actualizar contenido en diferentes idiomas, tanto desde el Panel de administraci\xF3n como desde la API.","global.plugins.sentry":"Sentry","global.plugins.sentry.description":"Enviar errores de Strapi a Sentry.","global.plugins.users-permissions":"Roles & Permisos","global.plugins.users-permissions.description":"Proteja su API con un proceso de autenticaci\xF3n completo basado en JWT. Este complemento tambi\xE9n viene con una estrategia ACL que le permite administrar los permisos entre los grupos de usuarios.","global.profile":"Perfil","global.reset-password":"Resetear contrase\xF1a","global.roles":"Roles","global.save":"Guardar","global.see-more":"Ver m\xE1s","global.select":"Seleccionar","global.select-all-entries":"Selecionar todas las entradas","global.settings":"Configuraciones","global.strapi-super-admin":"Super Admin","global.strapi-editor":"Editor","global.strapi-author":"Autor","global.table.header.email":"Email","global.table.header.firstname":"Nombre","global.table.header.isActive":"Estado del usuario","global.table.header.lastname":"Apellido","global.table.header.roles":"Roles","global.table.header.username":"Nombre de usuario","global.type":"Tipo","global.users":"Usuarios","notification.warning.404":"404 - No Encontrado"}}}]);
