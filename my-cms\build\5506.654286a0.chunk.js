"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[5506],{5506:(l,e,i)=>{i.r(e),i.d(e,{default:()=>t});const t={"Settings.email.plugin.button.test-email":"Enviar email de prueba","Settings.email.plugin.label.defaultFrom":"Email del remitente predeterminado","Settings.email.plugin.label.defaultReplyTo":"Email de respuesta predeterminado","Settings.email.plugin.label.provider":"Proveedor de email","Settings.email.plugin.label.testAddress":"Receptor de email","Settings.email.plugin.notification.config.error":"No se pudo recuperar la configuraci\xF3n del email","Settings.email.plugin.notification.data.loaded":"Se han cargado los datos de configuraci\xF3n de email","Settings.email.plugin.notification.test.error":"No se pudo enviar un correo de prueba a {to}","Settings.email.plugin.notification.test.success":"La prueba de correo electr\xF3nico se realiz\xF3 correctamente, verifique el buz\xF3n de {to}","Settings.email.plugin.placeholder.defaultFrom":"ej: Strapi No-Reply <<EMAIL>>","Settings.email.plugin.placeholder.defaultReplyTo":"ej: Strapi <<EMAIL>>","Settings.email.plugin.placeholder.testAddress":"ej: <EMAIL>","Settings.email.plugin.subTitle":"Pruebe la configuraci\xF3n del complemento de email","Settings.email.plugin.text.configuration":"El complemento se configura a trav\xE9s del archivo {file}, consulte este {link} para ver la documentaci\xF3n.","Settings.email.plugin.title":"Configuraci\xF3n","Settings.email.plugin.title.config":"Configuraci\xF3n","Settings.email.plugin.title.test":"Prueba el env\xEDo de email","SettingsNav.link.settings":"Ajustes","SettingsNav.section-label":"Plugin de email","components.Input.error.validation.email":"Este es un correo electr\xF3nico inv\xE1lido"}}}]);
