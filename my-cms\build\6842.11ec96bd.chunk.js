"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[6842],{26842:(w,P,s)=>{s.r(P),s.d(P,{InformationBoxEE:()=>J});var t=s(92132),a=s(55506),e=s(43543),y=s(76517),O=s(80782),A=s(7153),c=s(16918),g=s(10229),L=s(43064),I=s(83997),N=s(48323),G=s(30893),F=s(54894),j=s(85140),o=s(45534),V=s(21272),m=s(45084),T=s(25524),Y=s(35658),X=s(15126),$=s(63299),k=s(67014),b=s(59080),q=s(79275),ss=s(14718),ts=s(82437),as=s(61535),os=s(5790),ns=s(12083),es=s(35223),_s=s(5409),ls=s(74930),is=s(2600),Es=s(48940),rs=s(41286),ds=s(56336),Ms=s(13426),Ds=s(84624),Ps=s(77965),Os=s(54257),gs=s(71210),ms=s(51187),hs=s(39404),vs=s(58692),Cs=s(501),As=s(57646),cs=s(23120),Is=s(44414),Ws=s(25962),Ls=s(14664),Ts=s(42588),Rs=s(90325),Bs=s(62785),fs=s(87443),Us=s(41032),Ks=s(22957),us=s(93179),ys=s(73055),js=s(15747),xs=s(85306),Ss=s(26509),ps=s(32058),ws=s(81185),Ns=s(82261);const H=()=>{const{initialData:_,layout:l,isSingleType:h,onChange:x}=(0,a.Cu)(),i=(0,e.j)(n=>n.admin_app.permissions),{formatMessage:E}=(0,F.A)(),{_unstableFormatAPIError:S}=(0,a.wq)(),p=(0,a.hN)(),{allowedActions:{canRead:R},isLoading:W}=(0,a.ec)(i.settings?.users),{data:v,isLoading:B,isError:f}=(0,e.k)({},{skip:W||!R}),r=v?.users||[],d=_?.[o.A]??null,[M,{error:U,isLoading:K}]=(0,j.u)(),u=async n=>{const D=await M({slug:h?"single-types":"collection-types",model:l.uid,id:_.id,data:{id:n?parseInt(n,10):null}});"data"in D&&(x?.({target:{type:"",name:o.A,value:D.data[o.A]}},!0),p({type:"success",message:{id:"content-manager.reviewWorkflows.assignee.notification.saved",defaultMessage:"Assignee updated"}}))};return(0,t.jsx)(A.D,{name:o.A,id:o.A,children:(0,t.jsx)(I.s,{direction:"column",gap:2,alignItems:"stretch",children:(0,t.jsx)(y.G3,{clearLabel:E({id:"content-manager.reviewWorkflows.assignee.clear",defaultMessage:"Clear assignee"}),error:(f&&R&&E({id:"content-manager.reviewWorkflows.assignee.error",defaultMessage:"An error occurred while fetching users"})||U&&S(U))??void 0,disabled:!W&&!B&&r.length===0,name:o.A,id:o.A,value:d?d.id.toString():null,onChange:u,onClear:()=>u(null),placeholder:E({id:"content-manager.reviewWorkflows.assignee.placeholder",defaultMessage:"Select \u2026"}),label:E({id:"content-manager.reviewWorkflows.assignee.label",defaultMessage:"Assignee"}),loading:B||W||K,children:r.map(n=>(0,t.jsx)(O.j,{value:n.id.toString(),textValue:(0,e.l)(n,E),children:(0,e.l)(n,E)},n.id))})})})},Z=()=>{const{initialData:_,layout:l,isSingleType:h,onChange:x}=(0,a.Cu)(),{formatMessage:i}=(0,F.A)(),{_unstableFormatAPIError:E}=(0,a.wq)(),S=(0,a.hN)(),{data:p,isLoading:R}=(0,j.a)({slug:h?"single-types":"collection-types",model:l.uid,id:_.id},{skip:!_?.id||!l?.uid}),{meta:W,stages:v=[]}=p??{},{getFeature:B}=(0,e.m)(),[f,r]=V.useState(null),d=B("review-workflows")??{},M=_?.[o.S]??null,[U,{error:K}]=(0,j.b)(),u=async D=>{try{if(d?.[T.C]&&parseInt(d[T.C],10)<(W?.workflowCount??0))r("workflow");else if(d?.[T.a]&&parseInt(d[T.a],10)<v.length)r("stage");else if(_.id&&l){const C=await U({model:l.uid,id:_.id,slug:h?"single-types":"collection-types",data:{id:D}});"data"in C&&(x?.({target:{name:o.S,value:C.data[o.S],type:""}},!0),S({type:"success",message:{id:"content-manager.reviewWorkflows.stage.notification.saved",defaultMessage:"Review stage updated"}}))}}catch{}},{themeColorName:n}=(0,Y.g)(M?.color)??{};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.D,{hint:v.length===0&&i({id:"content-manager.reviewWorkflows.stages.no-transition",defaultMessage:"You don\u2019t have the permission to update this stage."}),name:o.S,id:o.S,children:(0,t.jsxs)(I.s,{direction:"column",gap:2,alignItems:"stretch",children:[(0,t.jsx)(N.Z,{disabled:v.length===0,error:K&&E(K)||void 0,name:o.S,id:o.S,value:M?.id,onChange:u,label:i({id:"content-manager.reviewWorkflows.stage.label",defaultMessage:"Review stage"}),startIcon:M&&(0,t.jsx)(I.s,{as:"span",height:2,background:M?.color,borderColor:n==="neutral0"?"neutral150":void 0,hasRadius:!0,shrink:0,width:2,marginRight:"-3px"}),customizeContent:()=>(0,t.jsxs)(I.s,{as:"span",justifyContent:"space-between",alignItems:"center",width:"100%",children:[(0,t.jsx)(G.o,{textColor:"neutral800",ellipsis:!0,children:M?.name??""}),R?(0,t.jsx)(L.a,{small:!0,style:{display:"flex"},"data-testid":"loader"}):null]}),children:v.map(({id:D,color:C,name:z})=>{const{themeColorName:Q}=(0,Y.g)(C)??{};return(0,t.jsx)(N.eY,{startIcon:(0,t.jsx)(I.s,{height:2,background:C,borderColor:Q==="neutral0"?"neutral150":void 0,hasRadius:!0,shrink:0,width:2}),value:D,textValue:z,children:z},D)})}),(0,t.jsx)(c.o,{}),(0,t.jsx)(g.b,{})]})}),(0,t.jsxs)(m.L.Root,{isOpen:f==="workflow",onClose:()=>r(null),children:[(0,t.jsx)(m.L.Title,{children:i({id:"content-manager.reviewWorkflows.workflows.limit.title",defaultMessage:"You\u2019ve reached the limit of workflows in your plan"})}),(0,t.jsx)(m.L.Body,{children:i({id:"content-manager.reviewWorkflows.workflows.limit.body",defaultMessage:"Delete a workflow or contact Sales to enable more workflows."})})]}),(0,t.jsxs)(m.L.Root,{isOpen:f==="stage",onClose:()=>r(null),children:[(0,t.jsx)(m.L.Title,{children:i({id:"content-manager.reviewWorkflows.stages.limit.title",defaultMessage:"You have reached the limit of stages for this workflow in your plan"})}),(0,t.jsx)(m.L.Body,{children:i({id:"content-manager.reviewWorkflows.stages.limit.body",defaultMessage:"Try deleting some stages or contact Sales to enable more stages."})})]})]})},J=()=>{const{isCreatingEntry:_,layout:l}=(0,a.Cu)(),h=l?.options?.reviewWorkflows??!1;return(0,t.jsxs)(e.I.Root,{children:[(0,t.jsx)(e.I.Title,{}),h&&!_&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Z,{}),(0,t.jsx)(H,{})]}),(0,t.jsx)(e.I.Body,{})]})}},35658:(w,P,s)=>{s.d(P,{a:()=>y,g:()=>e});var t=s(57438),a=s(25524);function e(O){if(!O)return null;const c=Object.entries(t._.colors).filter(([,g])=>g.toUpperCase()===O.toUpperCase()).reduce((g,[L])=>(a.S?.[L]&&(g=L),g),null);return c?{themeColorName:c,name:a.S[c]}:null}function y(){return Object.entries(a.S).map(([O,A])=>({hex:t._.colors[O].toUpperCase(),name:A}))}},45534:(w,P,s)=>{s.d(P,{A:()=>a,S:()=>t});const t="strapi_stage",a="strapi_assignee"}}]);
