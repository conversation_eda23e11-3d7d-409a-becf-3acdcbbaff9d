"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3265],{53265:(r,e,t)=>{t.r(e),t.d(e,{default:()=>o,from:()=>a});const a="de",o={"attribute.boolean":"Booleano","attribute.date":"Data","attribute.email":"Email","attribute.enumeration":"Enumera\xE7\xE3o","attribute.json":"JSON","attribute.media":"Media","attribute.password":"Palavra-passe","attribute.relation":"Rela\xE7\xE3o","attribute.text":"Texto","form.attribute.item.customColumnName":"Nomes de colunas personalizadas","form.attribute.item.customColumnName.description":"Isto \xE9 \xFAtil para renomear os nomes das colunas da base de dados num formato mais abrangente para as respostas da API","form.attribute.item.defineRelation.fieldName":"Nome do campo","form.attribute.item.enumeration.graphql":"Substitui\xE7\xE3o de nome para o GraphQL","form.attribute.item.enumeration.graphql.description":"Permite-lhe a substitui\xE7\xE3o do nome predefinido para o GraphQL","form.attribute.item.enumeration.placeholder":`Ex:
manh\xE3
tarde
noite`,"form.attribute.item.enumeration.rules":"Valores (um valor por linha)","form.attribute.item.maximum":"Valor m\xE1ximo","form.attribute.item.maximumLength":"Comprimento m\xE1ximo","form.attribute.item.minimum":"Valor m\xEDnimo","form.attribute.item.minimumLength":"Comprimento m\xEDnimo","form.attribute.item.number.type":"Formato num\xE9rico","form.attribute.item.number.type.decimal":"decimal (ex: 2.22)","form.attribute.item.number.type.float":"real (ex: 3.33333333)","form.attribute.item.number.type.integer":"inteiro (ex: 10)","form.attribute.item.requiredField":"Campo obrigat\xF3rio","form.attribute.item.requiredField.description":"N\xE3o ser\xE1 capaz de criar uma entrada se este campo estiver vazio","form.attribute.item.uniqueField":"Campo \xFAnico","form.attribute.item.uniqueField.description":"N\xE3o ser\xE1 capaz de criar uma entrada se houver uma entrada existente com conte\xFAdo id\xEAntico","form.attribute.settings.default":"Valor predefinido","form.button.cancel":"Cancelar",from:a,"modelPage.attribute.relationWith":"Rela\xE7\xE3o com","plugin.description.long":"Modele a estrutura de dados da sua API. Crie novos campos e rela\xE7\xF5es em apenas um minuto. Os ficheiros s\xE3o automaticamente criados e actualizados no seu projecto.","plugin.description.short":"Modele a estrutura de dados da sua API.","popUpForm.navContainer.advanced":"Defini\xE7\xF5es Avan\xE7adas","popUpForm.navContainer.base":"Defini\xE7\xF5es B\xE1sicas","popUpWarning.bodyMessage.contentType.delete":"Tem a certeza que pretende apagar este Tipo de Conte\xFAdo?","relation.attributeName.placeholder":"Ex: autor, categoria, tag","relation.manyToMany":"tem e pertence a v\xE1rios","relation.manyToOne":"tem v\xE1rios","relation.oneToMany":"pertence a v\xE1rios","relation.oneToOne":"tem e pertence a um","relation.oneWay":"tem um"}}}]);
