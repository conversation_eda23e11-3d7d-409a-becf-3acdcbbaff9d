"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[9328],{85071:(x,D,s)=>{s.d(D,{u:()=>h});var t=s(21272),g=s(55506),R=s(54894),i=s(43543);const h=(L={},m)=>{const{locale:A}=(0,R.A)(),O=(0,g.QM)(A,{sensitivity:"base"}),{data:d,error:C,isError:v,isLoading:c,refetch:I}=(0,i.z)(L,m);return{roles:t.useMemo(()=>[...d??[]].sort((W,B)=>O.compare(W.name,B.name)),[d,O]),error:C,isError:v,isLoading:c,refetch:I}}},89328:(x,D,s)=>{s.d(D,{ProtectedSSO:()=>F});var t=s(92132),g=s(85963),R=s(90151),i=s(68074),h=s(42455),L=s(4198),m=s(55356),A=s(38413),O=s(83997),d=s(56654),C=s(43739),v=s(95336),c=s(7441),I=s(30893),o=s(55506),W=s(54514),B=s(61535),y=s(54894),r=s(12083),M=s(43543),j=s(85071),q=s(15126),ss=s(63299),ts=s(67014),as=s(59080),os=s(79275),_s=s(14718),ns=s(21272),ls=s(82437),es=s(5790),Es=s(35223),is=s(5409),ds=s(74930),rs=s(2600),Ms=s(48940),Os=s(41286),Ps=s(56336),Ds=s(13426),gs=s(84624),Rs=s(77965),hs=s(54257),Ls=s(71210),ms=s(51187),As=s(39404),Cs=s(58692),vs=s(501),cs=s(57646),Is=s(23120),Ws=s(44414),Bs=s(25962),Ts=s(14664),Us=s(42588),Ks=s(90325),fs=s(62785),us=s(87443),xs=s(41032),ys=s(22957),js=s(93179),Ss=s(73055),ps=s(15747),Fs=s(85306),Ns=s(26509),zs=s(32058),Vs=s(81185),Qs=s(82261);const S=r.Ik().shape({autoRegister:r.lc().required(o.iW.required),defaultRole:r.gl().when("autoRegister",(a,_)=>a?_.required(o.iW.required):_.nullable()),ssoLockedRoles:r.YO().nullable().of(r.gl().when("ssoLockedRoles",(a,_)=>a?_.required(o.iW.required):_.nullable()))}),p=()=>{(0,o.L4)();const{formatMessage:a}=(0,y.A)(),_=(0,M.j)(e=>e.admin_app.permissions),{lockApp:N,unlockApp:z}=(0,o.MA)(),T=(0,o.hN)(),{_unstableFormatAPIError:V,_unstableFormatValidationErrors:Q}=(0,o.wq)(),{isLoading:G,data:X}=(0,M.W)(),[Y,{isLoading:$}]=(0,M.X)(),{isLoading:H,allowedActions:{canUpdate:U,canReadRoles:J}}=(0,o.ec)({..._.settings?.sso,readRoles:_.settings?.roles.read??[]}),{roles:f,isLoading:Z}=(0,j.u)(void 0,{skip:!J}),k=async(e,P)=>{N();try{const n=await Y(e);if("error"in n){(0,M.x)(n.error)&&n.error.name==="ValidationError"?P.setErrors(Q(n.error)):T({type:"warning",message:V(n.error)});return}T({type:"success",message:{id:"notification.success.saved"}})}catch{T({type:"warning",message:{id:"notification.error",defaultMessage:"An error occurred, please try again."}})}finally{z()}},u=Z||H||G;return(0,t.jsxs)(h.P,{children:[(0,t.jsx)(o.x7,{name:"SSO"}),(0,t.jsx)(A.g,{"aria-busy":$||u,tabIndex:-1,children:(0,t.jsx)(B.l1,{onSubmit:k,initialValues:X||{autoRegister:!1,defaultRole:null,ssoLockedRoles:null},validationSchema:S,validateOnChange:!1,enableReinitialize:!0,children:({handleChange:e,isSubmitting:P,values:n,setFieldValue:b,dirty:w,errors:E})=>(0,t.jsxs)(o.lV,{children:[(0,t.jsx)(m.Q,{primaryAction:(0,t.jsx)(g.$,{disabled:!w,loading:P,startIcon:(0,t.jsx)(W.A,{}),type:"submit",size:"L",children:a({id:"global.save",defaultMessage:"Save"})}),title:a({id:"Settings.sso.title",defaultMessage:"Single Sign-On"}),subtitle:a({id:"Settings.sso.description",defaultMessage:"Configure the settings for the Single Sign-On feature."})}),(0,t.jsx)(L.s,{children:P||u?(0,t.jsx)(o.Bl,{}):(0,t.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:4,background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:[(0,t.jsx)(I.o,{variant:"delta",as:"h2",children:a({id:"global.settings",defaultMessage:"Settings"})}),(0,t.jsxs)(R.x,{gap:4,children:[(0,t.jsx)(i.E,{col:6,s:12,children:(0,t.jsx)(c.l,{disabled:!U,checked:n.autoRegister,hint:a({id:"Settings.sso.form.registration.description",defaultMessage:"Create new user on SSO login if no account exists"}),label:a({id:"Settings.sso.form.registration.label",defaultMessage:"Auto-registration"}),name:"autoRegister",offLabel:a({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:a({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:e})}),(0,t.jsx)(i.E,{col:6,s:12,children:(0,t.jsx)(C.l,{disabled:!U,hint:a({id:"Settings.sso.form.defaultRole.description",defaultMessage:"It will attach the new authenticated user to the selected role"}),error:E.defaultRole?a({id:E.defaultRole,defaultMessage:E.defaultRole}):"",label:a({id:"Settings.sso.form.defaultRole.label",defaultMessage:"Default role"}),name:"defaultRole",onChange:l=>e({target:{name:"defaultRole",value:l}}),placeholder:a({id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}),value:n.defaultRole,children:f.map(({id:l,name:K})=>(0,t.jsx)(v.c,{value:l.toString(),children:K},l))})}),(0,t.jsx)(i.E,{col:6,s:12,children:(0,t.jsx)(d.KF,{disabled:!U,hint:a({id:"Settings.sso.form.localAuthenticationLock.description",defaultMessage:"Select the roles for which you want to disable the local authentication"}),error:E.ssoLockedRoles?a({id:E.ssoLockedRoles,defaultMessage:E.ssoLockedRoles}):"",label:a({id:"Settings.sso.form.localAuthenticationLock.label",defaultMessage:"Local authentication lock-out"}),name:"ssoLockedRoles",onChange:l=>e({target:{value:l,name:"ssoLockedRoles"}}),placeholder:a({id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}),onClear:()=>b("ssoLockedRoles",[]),value:n.ssoLockedRoles||[],withTags:!0,children:f.map(({id:l,name:K})=>(0,t.jsx)(d.fe,{value:l.toString(),children:K},l))})})]})]})})]})})})]})},F=()=>{const a=(0,M.j)(_=>_.admin_app.permissions.settings?.sso?.main);return(0,t.jsx)(o.kz,{permissions:a,children:(0,t.jsx)(p,{})})}}}]);
