"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[2585],{82585:(f,e,n)=>{n.r(e),n.d(e,{Analytics:()=>t,Documentation:()=>i,Email:()=>r,Password:()=>o,Provider:()=>s,ResetPasswordToken:()=>a,Role:()=>l,Username:()=>c,Users:()=>p,anErrorOccurred:()=>g,clearLabel:()=>m,default:()=>b,or:()=>u,skipToContent:()=>d,submit:()=>h});const t="Analytics",i="Dokumentation",r="E-Mail",o="Passwort",s="Methode",a="Passwort-Token zur\xFCcksetzen",l="Rolle",c="Benutzername",p="<PERSON>utzer",g="Ups! Ein unbekannter Fehler ist aufgetreten. Versuche es erneut.",m="Zur\xFCcksetzen",u="ODER",d="Zu Inhalt springen",h="Senden",b={Analytics:t,"Auth.components.Oops.text":"Dein Account wurde deaktiviert","Auth.components.Oops.text.admin":"Falls das ein Fehler war, kontaktiere bitte deinen Administrator.","Auth.components.Oops.title":"Ups...","Auth.form.button.forgot-password":"E-Mail versenden","Auth.form.button.go-home":"ZUR\xDCCK ZUR STARTSEITE","Auth.form.button.login":"Login","Auth.form.button.login.providers.error":"Durch den ausgew\xE4hlten Anbieter k\xF6nnen wir dich nicht verbinden","Auth.form.button.login.strapi":"Per Strapi einloggen","Auth.form.button.password-recovery":"Passwort zur\xFCcksetzen","Auth.form.button.register":"Los geht's","Auth.form.confirmPassword.label":"Passwort best\xE4tigen","Auth.form.currentPassword.label":"Aktuelles Passwort","Auth.form.email.label":"E-Mail","Auth.form.email.placeholder":"z.B. <EMAIL>","Auth.form.error.blocked":"Dein Account wurde vom Administrator blockiert.","Auth.form.error.code.provide":"Ung\xFCltiger Code.","Auth.form.error.confirmed":"Deine Account E-Mail-Adresse ist nicht best\xE4tigt.","Auth.form.error.email.invalid":"Diese E-Mail-Adresse ist ung\xFCltig.","Auth.form.error.email.provide":"Bitte nenne uns deinen Benutzernamen oder deine E-Mail-Adresse.","Auth.form.error.email.taken":"Diese E-Mail-Adresse wird bereits genutzt","Auth.form.error.invalid":"Ung\xFCltige Login-Daten.","Auth.form.error.params.provide":"Ung\xFCltige Parameter.","Auth.form.error.password.format":"Dein Passwort darf nicht mehr als dreimal das Symbol `$` enthalten.","Auth.form.error.password.local":"Dieser Benutzer hat kein lokales Passwort. Bitte logge dich mithilfe des Providers ein, der bei der Erstellung des Accounts genutzt wurde.","Auth.form.error.password.matching":"Passw\xF6rter sind nicht gleich.","Auth.form.error.password.provide":"Bitte gib dein Passwort ein.","Auth.form.error.ratelimit":"Zu viele Versuche, bitte versuche es in einer Minute erneut.","Auth.form.error.user.not-exist":"Diese E-Mail-Adresse ist nicht registriert.","Auth.form.error.username.taken":"Dieser Benutzername ist bereits vergeben","Auth.form.firstname.label":"Vorname","Auth.form.firstname.placeholder":"z.B. Max","Auth.form.forgot-password.email.label":"Gib deine E-Mail ein","Auth.form.forgot-password.email.label.success":"Eine E-Mail wurde erfolgreich verschickt an","Auth.form.lastname.label":"Nachname","Auth.form.lastname.placeholder":"z.B. Mustermann","Auth.form.password.hide-password":"Passwort ausblenden","Auth.form.password.hint":"Das Passwort muss mindestens 8 Zeichen, einen Gro\xDFbuchstaben, einen Kleinbuchstaben und eine Zahl enthalten","Auth.form.password.show-password":"Passwort einblenden","Auth.form.register.news.label":"Halte mich \xFCber die neuen Features und anstehenden Verbesserungen auf dem Laufenden (damit werden die {terms} und die {policy} akzeptiert).","Auth.form.register.subtitle":"Deine Zugangsdaten werden nur verwendet, um dich im Admin Panel einzuloggen. Alle Daten werden in der lokalen Datenbank gespeichert.","Auth.form.rememberMe.label":"Eingeloggt bleiben","Auth.form.username.label":"Benutzername","Auth.form.username.placeholder":"z.B. Max_Mustermann","Auth.form.welcome.subtitle":"Logge dich in deinen Strapi Account ein","Auth.form.welcome.title":"Willkommen!","Auth.link.forgot-password":"Passwort vergessen?","Auth.link.ready":"Bereit f\xFCr den Login?","Auth.link.signin":"Einloggen","Auth.link.signin.account":"Account bereits vorhanden?","Auth.login.sso.divider":"Oder einloggen mit","Auth.login.sso.loading":"Anbieter werden geladen...","Auth.login.sso.subtitle":"Per SSO einloggen","Auth.privacy-policy-agreement.policy":"Datenschutzerkl\xE4rung","Auth.privacy-policy-agreement.terms":"Nutzungsbedingungen","Auth.reset-password.title":"Passwort zur\xFCcksetzen","Content Manager":"Inhalts-Manager","Content Type Builder":"Inhaltstyp-Editor",Documentation:i,Email:r,"Files Upload":"Dateien hochladen","HomePage.helmet.title":"Startseite","HomePage.roadmap":"Siehe unsere Roadmap","HomePage.welcome.congrats":"Herzlichen Gl\xFCckwunsch!","HomePage.welcome.congrats.content":"Angemeldet als erster Administrator. Um die leistungsstarken Funktionen von Strapi zu entdecken,","HomePage.welcome.congrats.content.bold":"empfehlen wir Ihren ersten Inhaltstyp zu erstellen.","Media Library":"Medienbibliothek","New entry":"Neuer Eintrag",Password:o,Provider:s,ResetPasswordToken:a,Role:l,"Roles & Permissions":"Rollen & Berechtigungen","Roles.ListPage.notification.delete-all-not-allowed":"Manche Rollen konnten nicht gel\xF6scht werden, da sie mit Benutzern verkn\xFCpft sind","Roles.ListPage.notification.delete-not-allowed":"Eine Rolle, die mit einem Benutzer verkn\xFCpft ist, kann nicht gel\xF6scht werden","Roles.RoleRow.select-all":"W\xE4hle {name} f\xFCr Mehrfach-Aktionen","Roles.RoleRow.user-count":"Benutzer","Roles.components.List.empty.withSearch":"Es gibt keine Rolle die der Suche ({search}) entspricht...","Settings.PageTitle":"Einstellungen - {name}","Settings.apiTokens.addFirstToken":"Deinen ersten API-Token hinzuf\xFCgen","Settings.apiTokens.addNewToken":"Neuen API-Token hinzuf\xFCgen","Settings.tokens.copy.editMessage":"Aus Sicherheitsgr\xFCnden kannst du deinen Token nur einmal sehen.","Settings.tokens.copy.editTitle":"Auf diesen Token kann nicht mehr zugegriffen werden.","Settings.tokens.copy.lastWarning":"Stell sicher, dass dieser Token kopiert wurde, da du ihn nicht noch einmal sehen k\xF6nnen wirst!","Settings.apiTokens.create":"Neuen API-Token erstellen","Settings.apiTokens.description":"Liste der generierten Tokens mit Zugriff auf die API","Settings.apiTokens.emptyStateLayout":"Du hast noch keinen Inhalt...","Settings.tokens.notification.copied":"Token wurde in die Zwischenablage kopiert.","Settings.apiTokens.title":"API-Tokens","Settings.tokens.types.full-access":"Voller Zugriff","Settings.tokens.types.read-only":"Nur Lesezugriff","Settings.application.description":"Globale Informationen \xFCber die Administrationsoberfl\xE4che","Settings.application.edition-title":"Aktuelle Version","Settings.application.get-help":"Hilfe","Settings.application.link-pricing":"Alle Preisgestaltungen anzeigen","Settings.application.link-upgrade":"Deine Administrationsoberfl\xE4che aktualisieren","Settings.application.node-version":"node version","Settings.application.strapi-version":"strapi version","Settings.application.strapiVersion":"strapi version","Settings.application.title":"\xDCbersicht","Settings.error":"Fehler","Settings.global":"Globale Einstellungen","Settings.permissions":"Administrationsoberfl\xE4che","Settings.permissions.category":"Berechtigungseinstellungen f\xFCr die {category}","Settings.permissions.category.plugins":"Berechtigungseinstellungen f\xFCr das {category} Plugin","Settings.permissions.conditions.anytime":"Jederzeit","Settings.permissions.conditions.apply":"Anwenden","Settings.permissions.conditions.can":"Kann","Settings.permissions.conditions.conditions":"Bedingungen definieren","Settings.permissions.conditions.links":"Links","Settings.permissions.conditions.no-actions":"Keine Aktionen","Settings.permissions.conditions.none-selected":"Jederzeit","Settings.permissions.conditions.or":"ODER","Settings.permissions.conditions.when":"Wenn","Settings.permissions.select-all-by-permission":"W\xE4hle alle {label}-Berechtigungen","Settings.permissions.select-by-permission":"W\xE4hle {label}-Berechtigung","Settings.permissions.users.create":"Benutzer einladen","Settings.permissions.users.email":"E-Mail","Settings.permissions.users.firstname":"Vorname","Settings.permissions.users.lastname":"Nachname","Settings.permissions.users.form.sso":"Mit SSO einloggen","Settings.permissions.users.form.sso.description":"Nutzer k\xF6nnen sich per SSO einloggen wenn aktiviert (ON)","Settings.permissions.users.listview.header.subtitle":"Alle Nutzer mit Zugriff auf diese Administrationsoberfl\xE4che","Settings.permissions.users.tabs.label":"Tab Berechtigungen","Settings.profile.form.notify.data.loaded":"Deine Profildaten wurden geladen","Settings.profile.form.section.experience.clear.select":"Ausgew\xE4hlte Sprache der Oberfl\xE4che zur\xFCcksetzen","Settings.profile.form.section.experience.here":"hier","Settings.profile.form.section.experience.interfaceLanguage":"Sprache der Oberfl\xE4che","Settings.profile.form.section.experience.interfaceLanguage.hint":"Dies wird die Oberfl\xE4che f\xFCr dich in der ausgew\xE4hlten Sprache darstellen.","Settings.profile.form.section.experience.interfaceLanguageHelp":"Diese Einstellungen werden nur f\xFCr dich angewandt. Mehr Informationen dazu {here}.","Settings.profile.form.section.experience.mode.label":"Modus der Oberfl\xE4che","Settings.profile.form.section.experience.mode.hint":"Zeigt die Oberfl\xE4che im gew\xE4hlten Modus.","Settings.profile.form.section.experience.mode.option-label":"{name}-Modus","Settings.profile.form.section.experience.title":"Bedienung","Settings.profile.form.section.helmet.title":"Nutzerprofil","Settings.profile.form.section.profile.page.title":"Profil-Seite","Settings.roles.create.description":"Die Berechtigungen einer Rolle festlegen","Settings.roles.create.title":"Rolle erstellen","Settings.roles.created":"Rolle erstellt","Settings.roles.edit.title":"Rolle bearbeiten","Settings.roles.form.button.users-with-role":"Benutzer mit dieser Rolle","Settings.roles.form.created":"Erstellt","Settings.roles.form.description":"Name und Beschreibung der Rolle","Settings.roles.form.permission.property-label":"{label} Berechtigungen","Settings.roles.form.permissions.attributesPermissions":"Felderberechtigungen","Settings.roles.form.permissions.create":"Erstellen","Settings.roles.form.permissions.delete":"L\xF6schen","Settings.roles.form.permissions.publish":"Ver\xF6ffentlichen","Settings.roles.form.permissions.read":"Lesen","Settings.roles.form.permissions.update":"\xC4ndern","Settings.roles.list.button.add":"Neue Rolle hinzuf\xFCgen","Settings.roles.list.description":"Liste der Rollen","Settings.roles.title.singular":"Rolle","Settings.sso.description":"Einstellungen des Single Sign-On Features konfigurieren.","Settings.sso.form.defaultRole.description":"Die Standard-Rolle wird dem neu erstellten Nutzer zugewiesen","Settings.sso.form.defaultRole.description-not-allowed":"Du hast keine Lese-Berechtigung f\xFCr die Admin-Rollen","Settings.sso.form.defaultRole.label":"Standard-Rolle","Settings.sso.form.registration.description":"Neuen Nutzer beim Einloggen \xFCber SSO erstellen wenn noch kein Account existiert","Settings.sso.form.registration.label":"Auto-Registrierung","Settings.sso.title":"Single Sign-On","Settings.webhooks.create":"Webhook erstellen","Settings.webhooks.create.header":"Neuen Header erstellen","Settings.webhooks.created":"Webhook erstellt","Settings.webhooks.event.publish-tooltip":"Entwurf/Ver\xF6ffentlichen muss f\xFCr dieses Event aktiviert sein","Settings.webhooks.events.create":"Erstellen","Settings.webhooks.events.update":"Aktualisieren","Settings.webhooks.form.events":"Events","Settings.webhooks.form.headers":"Header","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"Header {number} entfernen","Settings.webhooks.key":"Key","Settings.webhooks.list.button.add":"Neuen Webhook hinzuf\xFCgen","Settings.webhooks.list.description":"POST-Benachrichtigungen bei \xC4nderungen empfangen.","Settings.webhooks.list.empty.description":"Ersten dieser Liste hinzuf\xFCgen.","Settings.webhooks.list.empty.link":"Siehe unsere Dokumentation","Settings.webhooks.list.empty.title":"Noch keine Webhooks","Settings.webhooks.list.th.actions":"Aktionen","Settings.webhooks.list.th.status":"Status","Settings.webhooks.singular":"Webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# Datei} other {# Dateien}} ausgew\xE4hlt","Settings.webhooks.trigger":"Trigger","Settings.webhooks.trigger.cancel":"Trigger abbrechen","Settings.webhooks.trigger.pending":"Ausstehend ...","Settings.webhooks.trigger.save":"Zur Ausf\xFChrung speichern","Settings.webhooks.trigger.success":"Erfolg!","Settings.webhooks.trigger.success.label":"Trigger war erfolgreich","Settings.webhooks.trigger.test":"Test-Trigger","Settings.webhooks.trigger.title":"Speichere vor Trigger","Settings.webhooks.value":"Wert","Usecase.back-end":"Backend-Entwickler","Usecase.button.skip":"Diese Frage \xFCberspringen","Usecase.content-creator":"Content Creator","Usecase.front-end":"Frontend-Entwickler","Usecase.full-stack":"Full-stack-Entwickler","Usecase.input.work-type":"Welche Rolle beschreibt deine Position am besten?","Usecase.notification.success.project-created":"Projekt wurde erfolgreich erstellt","Usecase.other":"Sonstige","Usecase.title":"Erz\xE4hle uns etwas mehr von dir",Username:c,Users:p,"Users & Permissions":"Benutzer & Berechtigungen","Users.components.List.empty":"Noch keine Nutzer\u2026","Users.components.List.empty.withFilters":"Es gibt keine Nutzer die den Filtern entsprechen...","Users.components.List.empty.withSearch":"Es gibt keine Nutzer die der Suche ({search}) entsprechen...","admin.pages.MarketPlacePage.helmet":"Marketplace - Plugins","admin.pages.MarketPlacePage.plugin.copy":"Installations-Befehl kopieren","admin.pages.MarketPlacePage.plugin.copy.success":"Installations-Befehl ist bereit, in deinem Terminal eingef\xFCgt zu werden","admin.pages.MarketPlacePage.plugin.info":"Mehr erfahren","admin.pages.MarketPlacePage.plugin.info.label":"Mehr \xFCber {pluginName} erfahren","admin.pages.MarketPlacePage.plugin.info.text":"Mehr erfahren","admin.pages.MarketPlacePage.plugin.installed":"Installiert","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"Erstellt von Strapi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"Plugin verifiziert von Strapi","admin.pages.MarketPlacePage.search.clear":"Plugin-Suche zur\xFCcksetzen","admin.pages.MarketPlacePage.search.empty":'Keine Ergebnisse f\xFCr "{target}"',"admin.pages.MarketPlacePage.search.placeholder":"Suche nach einem Plugin","admin.pages.MarketPlacePage.submit.plugin.link":"Eigenes Plugin einreichen","admin.pages.MarketPlacePage.subtitle":"Mache mehr mit Strapi",anErrorOccurred:g,"app.component.CopyToClipboard.label":"In Zwischenablage kopieren","app.component.search.label":"Suche nach {target}","app.component.table.duplicate":"Dupliziere {target}","app.component.table.edit":"Bearbeite {target}","app.component.table.select.one-entry":"Selektiere {target}","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Lies die neuesten Nachrichten \xFCber Strapi und das \xD6kosystem.","app.components.BlockLink.code":"Code-Beispiele","app.components.BlockLink.code.content":"Lerne duch das Testen von echten Projekten, die von der Community entwickelt wurden.","app.components.BlockLink.documentation.content":"Entdecke grundlegende Konzepte, Anleitungen und Anweisungen.","app.components.BlockLink.tutorial":"Tutorials","app.components.BlockLink.tutorial.content":"Folge Schritt-f\xFCr-Schritt Anleitungen die zeigen, wie du Strapi nutzen und anpassen kannst.","app.components.Button.cancel":"Abbrechen","app.components.Button.confirm":"Best\xE4tigen","app.components.Button.reset":"Zur\xFCcksetzen","app.components.ComingSoonPage.comingSoon":"Bald verf\xFCgbar","app.components.ConfirmDialog.title":"Best\xE4tigen","app.components.DownloadInfo.download":"Download wird ausgef\xFChrt...","app.components.DownloadInfo.text":"Dies k\xF6nnte kurz dauern. Danke f\xFCr deine Geduld.","app.components.EmptyAttributes.title":"Bisher gibt es noch keine Felder","app.components.EmptyStateLayout.content-document":"Kein Inhalt gefunden","app.components.EmptyStateLayout.content-permissions":"Du hast keine ausreichenden Berechtigungen, um auf diesen Inhalt zuzugreifen","app.components.GuidedTour.CM.create.content":'<p>Erstelle und verwalte all deinen Inhalt hier im Content Manager.</p><p>Beispiel: Im Beispiel der Blog-Website, kann man einen Artikel schreiben, speichern und ver\xF6ffentlichen.</p><p>\u{1F4A1} Kleiner Tipp: Vergiss nicht, beim Inhalt den du erstellst, "Ver\xF6ffentlichen" zu dr\xFCcken.</p>',"app.components.GuidedTour.CM.create.title":"\u26A1\uFE0F Erstelle Inhalt","app.components.GuidedTour.CM.success.content":"<p>Super, ein letzter Schritt noch!</p><b>\u{1F680}  Sehe deinen Inhalt in Aktion</b>","app.components.GuidedTour.CM.success.cta.title":"Teste die API","app.components.GuidedTour.CM.success.title":"Schritt 2: Abgeschlossen \u2705","app.components.GuidedTour.CTB.create.content":"<p>Sammlungen helfen dir, mehrere Eintr\xE4ge zu managen, w\xE4hrend Einzel-Eintr\xE4ge dazu da sind, nur einen Eintrag zu managen.</p> <p>Bsp: F\xFCr eine Blog-Website w\xE4ren die Artikel eine Sammlung, w\xE4hrend die Homepage ein Einzel-Eintrag w\xE4re.</p>","app.components.GuidedTour.CTB.create.cta.title":"Einen Inhaltstyp bauen","app.components.GuidedTour.CTB.create.title":"\u{1F9E0} Erstelle deinen ersten Inhaltstyp","app.components.GuidedTour.CTB.success.content":"<p>Sehr gut!</p><b>\u26A1\uFE0F Was willst du mit der Welt teilen?</b>","app.components.GuidedTour.CTB.success.title":"Schritt 1: Abgeschlossen \u2705","app.components.GuidedTour.apiTokens.create.content":"<p>Generiere einen Authentifizierungs-Token hier und greife auf den Inhalt, den du gerade erstellt hast, zu.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"Generiere einen API-Token","app.components.GuidedTour.apiTokens.create.title":"\u{1F680} Sehe Inhalt in Aktion","app.components.GuidedTour.apiTokens.success.content":"<p>Sehe Inhalt in Aktion indem du einen HTTP-Request machst:</p><ul><li><p>Zu dieser URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Mit dem Header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Mehr dazu, wie du mit deinem Inhalt interagierst, liest du in der <documentationLink>Dokumentation</documentationLink>.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Zur\xFCck zur Startseite","app.components.GuidedTour.apiTokens.success.title":"Schritt 3: Abgeschlossen \u2705","app.components.GuidedTour.create-content":"Erstelle Inhalt","app.components.GuidedTour.home.CM.title":"\u26A1\uFE0F Was willst du mit der Welt teilen?","app.components.GuidedTour.home.CTB.cta.title":"Gehe zum Content type Builder","app.components.GuidedTour.home.CTB.title":"\u{1F9E0} Baue die Struktur des Inhalts","app.components.GuidedTour.home.apiTokens.cta.title":"Teste die API","app.components.GuidedTour.skip":"Tour \xFCberspringen","app.components.GuidedTour.title":"3 Schritte zum Loslegen","app.components.HomePage.button.blog":"Mehr dazu im Blog","app.components.HomePage.community":"Finde die Community im Web","app.components.HomePage.community.content":"Diskutiere mit Teammitgliedern, Mitwirkenden und Entwicklern auf verschiedenen Kan\xE4len.","app.components.HomePage.create":"Ersten Content-Type erstellen","app.components.HomePage.roadmap":"Zu unserer Roadmap","app.components.HomePage.welcome":"Willkommen an Bord \u{1F44B}","app.components.HomePage.welcome.again":"Willkommen \u{1F44B}","app.components.HomePage.welcomeBlock.content":"Wir freuen uns, dich als Mitglied der Community zu haben. Wir sind offen f\xFCr Feedback, senden uns einfach eine direkt Nachricht in ","app.components.HomePage.welcomeBlock.content.again":"Wir hoffen, dass du Fortschritte bei deinem Projekt machst ... Lese das Neueste \xFCber Strapi. Wir geben unser Bestes, um das Produkt auf der Grundlage deines Feedbacks zu verbessern.","app.components.HomePage.welcomeBlock.content.issues":"Ticket.","app.components.HomePage.welcomeBlock.content.raise":" oder er\xF6ffne ","app.components.ImgPreview.hint":"Ziehe eine Datei hierher oder {browse} eine Datei zum hochladen aus","app.components.ImgPreview.hint.browse":"w\xE4hle","app.components.InputFile.newFile":"Neue Datei hinzuf\xFCgen","app.components.InputFileDetails.open":"In einem neuen Tab \xF6ffnen","app.components.InputFileDetails.originalName":"Original Name:","app.components.InputFileDetails.remove":"Entferne diese Datei","app.components.InputFileDetails.size":"Gr\xF6\xDFe:","app.components.InstallPluginPage.Download.description":"Es kann einige Sekunden dauern, bis das Plugin heruntergeladen und installiert ist.","app.components.InstallPluginPage.Download.title":"Herunterladen...","app.components.InstallPluginPage.description":"Erweitere deine App ganz einfach.","app.components.LeftMenu.collapse":"Navigationsleiste einklappen","app.components.LeftMenu.expand":"Navigationsleiste ausklappen","app.components.LeftMenu.logout":"Abmelden","app.components.LeftMenu.navbrand.title":"Strapi Dashboard","app.components.LeftMenu.navbrand.workplace":"Arbeitsplatz","app.components.LeftMenuFooter.help":"Hilfe","app.components.LeftMenuFooter.poweredBy":"Powered by ","app.components.LeftMenuLinkContainer.collectionTypes":"Sammlungen","app.components.LeftMenuLinkContainer.configuration":"Konfiguration","app.components.LeftMenuLinkContainer.general":"Allgemein","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Noch keine Plugins installiert","app.components.LeftMenuLinkContainer.plugins":"Plugins","app.components.LeftMenuLinkContainer.singleTypes":"Einzel-Eintr\xE4ge","app.components.ListPluginsPage.deletePlugin.description":"Das Deinstallieren des Plugins kann einen Augenblick dauern.","app.components.ListPluginsPage.deletePlugin.title":"Deinstalliere","app.components.ListPluginsPage.description":"Liste aller im Projekt installierten Plugins.","app.components.ListPluginsPage.helmet.title":"Plugins anzeigen","app.components.Logout.logout":"Ausloggen","app.components.Logout.profile":"Profil","app.components.MarketplaceBanner":"Entdecke von der Community entwickelte Plugins und noch viel mehr Dinge, um deinem Projekt zu helfen, auf Strapi Awesome.","app.components.MarketplaceBanner.image.alt":"ein Strapi-Raketen-Logo","app.components.MarketplaceBanner.link":"Jetzt entdecken","app.components.NotFoundPage.back":"Zur\xFCck zur Homepage","app.components.NotFoundPage.description":"Nicht gefunden","app.components.Official":"Offiziell","app.components.Onboarding.help.button":"Hilfe-Button","app.components.Onboarding.label.completed":"% abgeschlossen","app.components.Onboarding.title":"Videos zum Einstieg","app.components.PluginCard.Button.label.download":"Download","app.components.PluginCard.Button.label.install":"Bereits installiert","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"Die AutoReload-Funktion muss nicht aktiviert sein. Bitte die App mit `yarn develop` starten.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Ich verstehe, dass","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Aus Sicherheitsgr\xFCnden kann ein Plugin nur in einer Entwicklungsumgebung heruntergeladen werden.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Das Herunterladen ist nicht m\xF6glich.","app.components.PluginCard.compatible":"Mit dieser App kompatibel","app.components.PluginCard.compatibleCommunity":"Mit der Community kompatibel","app.components.PluginCard.more-details":"Mehr Details","app.components.ToggleCheckbox.off-label":"Nein","app.components.ToggleCheckbox.on-label":"Ja","app.components.Users.MagicLink.connect":"Diesen Link dem Benutzer zum Registrieren schicken.","app.components.Users.MagicLink.connect.sso":"Sende dem Benutzer diesen Link, das erste Login kann \xFCber einen SSO-Anbeiter gemacht werden","app.components.Users.ModalCreateBody.block-title.details":"Details","app.components.Users.ModalCreateBody.block-title.roles":"Rolle des Benutzers","app.components.Users.ModalCreateBody.block-title.roles.description":"Dein Benutzer kann eine oder mehrere Rollen haben","app.components.Users.SortPicker.button-label":"Sortieren nach","app.components.Users.SortPicker.sortby.email_asc":"E-Mail (A nach Z)","app.components.Users.SortPicker.sortby.email_desc":"E-Mail (Z nach A)","app.components.Users.SortPicker.sortby.firstname_asc":"Vorname (A nach Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Vorname (Z nach A)","app.components.Users.SortPicker.sortby.lastname_asc":"Nachname (A nach Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Nachname (Z nach A)","app.components.Users.SortPicker.sortby.username_asc":"Benutzername (A nach Z)","app.components.Users.SortPicker.sortby.username_desc":"Benutzername (Z nach A)","app.components.listPlugins.button":"Neues Plugin hinzuf\xFCgen","app.components.listPlugins.title.none":"Es ist kein Plugin installiert","app.components.listPluginsPage.deletePlugin.error":"Beim Entfernen des Plugins ist ein Fehler aufgetreten","app.containers.App.notification.error.init":"Beim Aufruf der API ist ein Fehler aufgetreten.","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Bitte den Administrator kontaktieren, sollte der Link nicht ankommen.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"Es kann ein paar Minuten dauern, bis der Wiederherstellungslink ankommt.","app.containers.AuthPage.ForgotPasswordSuccess.title":"E-Mail versendet","app.containers.Users.EditPage.form.active.label":"Aktiv","app.containers.Users.EditPage.header.label":"Bearbeite {name}","app.containers.Users.EditPage.header.label-loading":"Bearbeite Nutzer","app.containers.Users.EditPage.roles-bloc-title":"Zugewiesene Rollen","app.containers.Users.ModalForm.footer.button-success":"Nutzer erstellen","app.links.configure-view":"Anzeige konfigurieren","app.page.not.found":"Oh nein! Wir konnten die Seite, nach der du suchst, nicht finden...","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Filter hinzuf\xFCgen","app.utils.close-label":"Schlie\xDFen","app.utils.defaultMessage":" ","app.utils.duplicate":"Duplizieren","app.utils.edit":"Bearbeiten","app.utils.errors.file-too-big.message":"Datei ist zu gro\xDF","app.utils.filter-value":"Filter-Wert","app.utils.filters":"Filter","app.utils.notify.data-loaded":"{target} wurde geladen","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Ver\xF6ffentlichen","app.utils.select-all":"Alles ausw\xE4hlen","app.utils.select-field":"Feld ausw\xE4hlen","app.utils.select-filter":"Filter ausw\xE4hlen","app.utils.unpublish":"Nicht ver\xF6ffentlichen",clearLabel:m,"coming.soon":"Dieser Inhalt ist aktuell unter Bearbeitung und wird in ein paar Wochen zur\xFCck sein!","component.Input.error.validation.integer":"Der Wert muss eine Ganzzahl sein","components.AutoReloadBlocker.description":"Strapi mit einem der folgenden Befehle ausf\xFChren:","components.AutoReloadBlocker.header":"Dieses Plugin ben\xF6tigt das Neuladen-Feature.","components.ErrorBoundary.title":"Etwas ist falsch gelaufen...","components.FilterOptions.FILTER_TYPES.$contains":"enth\xE4lt","components.FilterOptions.FILTER_TYPES.$containsi":"enth\xE4lt (Gro\xDF- und Kleinschreibung wird nicht beachtet)","components.FilterOptions.FILTER_TYPES.$endsWith":"endet mit","components.FilterOptions.FILTER_TYPES.$endsWithi":"endet mit (Gro\xDF- und Kleinschreibung wird nicht beachtet)","components.FilterOptions.FILTER_TYPES.$eq":"ist","components.FilterOptions.FILTER_TYPES.$eqi":"ist (Gro\xDF- und Kleinschreibung wird nicht beachtet)","components.FilterOptions.FILTER_TYPES.$gt":"ist gr\xF6\xDFer als","components.FilterOptions.FILTER_TYPES.$gte":"is gr\xF6\xDFer als oder gleich","components.FilterOptions.FILTER_TYPES.$lt":"is kleiner als","components.FilterOptions.FILTER_TYPES.$lte":"is kleiner als oder gleich","components.FilterOptions.FILTER_TYPES.$ne":"ist nicht","components.FilterOptions.FILTER_TYPES.$nei":"ist nicht (Gro\xDF- und Kleinschreibung wird nicht beachtet)","components.FilterOptions.FILTER_TYPES.$notContains":"enth\xE4lt nicht","components.FilterOptions.FILTER_TYPES.$notContainsi":"enth\xE4lt nicht (Gro\xDF- und Kleinschreibung wird nicht beachtet)","components.FilterOptions.FILTER_TYPES.$notNull":"ist nicht null","components.FilterOptions.FILTER_TYPES.$null":"ist null","components.FilterOptions.FILTER_TYPES.$startsWith":"startet mit","components.FilterOptions.FILTER_TYPES.$startsWithi":"startet mit (Gro\xDF- und Kleinschreibung wird nicht beachtet)","components.Input.error.attribute.key.taken":"Dieser Wert existiert bereits","components.Input.error.attribute.sameKeyAndName":"Darf nicht gleich sein","components.Input.error.attribute.taken":"Dieser Feldname ist bereits vergeben","components.Input.error.contain.lowercase":"Das Passwort muss mindestens einen Kleinbuchstaben enthalten","components.Input.error.contain.number":"Das Passwort muss mindestens eine Zahl enthalten","components.Input.error.contain.uppercase":"Das Passwort muss mindestens einen Gro\xDFbuchstaben enthalten","components.Input.error.contentTypeName.taken":"Dieser Name existiert bereits","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"Passw\xF6rter stimmen nicht \xFCberein","components.Input.error.validation.email":"Das ist keine g\xFCltige E-Mail-Adresse","components.Input.error.validation.json":"Dies entspricht nicht dem JSON-Format.","components.Input.error.validation.lowercase":"Dieser Wert muss kleingeschreiben sein.","components.Input.error.validation.max":"Dieser Wert ist zu hoch {max}.","components.Input.error.validation.maxLength":"Dieser Wert ist zu lang {max}.","components.Input.error.validation.min":"Dieser Wert ist zu niedrig {min}.","components.Input.error.validation.minLength":"Dieser Wert ist zu kurz {min}.","components.Input.error.validation.minSupMax":"Darf nicht h\xF6her sein","components.Input.error.validation.regex":"Dieser Wert entspricht nicht dem RegEx.","components.Input.error.validation.required":"Die Eingabe dieses Wertes ist erforderlich.","components.Input.error.validation.unique":"Der Wert wird bereits genutzt.","components.InputSelect.option.placeholder":"Hier w\xE4hlen","components.ListRow.empty":"Es gibt keine Daten.","components.NotAllowedInput.text":"Keine Berechtigung dieses Feld zu sehen","components.OverlayBlocker.description":"Es wird ein Feature verwendet, das einen Neustart des Servers erfordert. Bitte warte bis der Server neu gestartet wurde.","components.OverlayBlocker.description.serverError":"Der Server sollte neu gestartet sein, bitte Logs im Terminal \xFCberpr\xFCfen.","components.OverlayBlocker.title":"Warten auf Neustart.....","components.OverlayBlocker.title.serverError":"Der Neustart dauert l\xE4nger als erwartet.","components.PageFooter.select":"Eintr\xE4ge pro Seite","components.ProductionBlocker.description":"Aus Sicherheitsgr\xFCnden m\xFCssen wir dieses Plugin in anderen Umgebungen deaktivieren.","components.ProductionBlocker.header":"Dieses Plugin ist nur in der Entwicklungsumgebung verf\xFCgbar.","components.Search.placeholder":"Suche...","components.TableHeader.sort":"Sortiere nach {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Markdown-Modus","components.Wysiwyg.ToggleMode.preview-mode":"Vorschau-Modus","components.Wysiwyg.collapse":"Verkleinern","components.Wysiwyg.selectOptions.H1":"\xDCberschrift H1","components.Wysiwyg.selectOptions.H2":"\xDCberschrift H2","components.Wysiwyg.selectOptions.H3":"\xDCberschrift H3","components.Wysiwyg.selectOptions.H4":"\xDCberschrift H4","components.Wysiwyg.selectOptions.H5":"\xDCberschrift H5","components.Wysiwyg.selectOptions.H6":"\xDCberschrift H6","components.Wysiwyg.selectOptions.title":"\xDCberschrift hinzuf\xFCgen","components.WysiwygBottomControls.charactersIndicators":"Zeichen","components.WysiwygBottomControls.fullscreen":"Vergr\xF6\xDFern","components.WysiwygBottomControls.uploadFiles":"Datei hierher ziehen, {browse} eine Datei zum hochladen ausw\xE4hlen oder aus der Zwischenablage einf\xFCgen.","components.WysiwygBottomControls.uploadFiles.browse":"w\xE4hle","components.pagination.go-to":"Gehe zu Seite {page}","components.pagination.go-to-next":"Gehe zur n\xE4chsten Seite","components.pagination.go-to-previous":"Gehe zur vorherigen Seite","components.pagination.remaining-links":"Und {number} weitere Links","components.popUpWarning.button.cancel":"Nein,abbrechen","components.popUpWarning.button.confirm":"Ja,best\xE4tigen","components.popUpWarning.message":"Wirklich l\xF6schen?","components.popUpWarning.title":"Bitte best\xE4tigen","content-manager.App.schemas.data-loaded":"Die Schemata wurden geladen","content-manager.ListViewTable.relation-loaded":"Beziehungen wurden geladen","content-manager.ListViewTable.relation-loading":"Beziehungen laden","content-manager.ListViewTable.relation-more":"Diese Beziehung enth\xE4lt mehr Eintr\xE4ge als angezeigt","content-manager.EditRelations.title":"Beziehungs-Daten","content-manager.HeaderLayout.button.label-add-entry":"Neuer Eintrag","content-manager.api.id":"API ID","content-manager.components.AddFilterCTA.add":"Filter","content-manager.components.AddFilterCTA.hide":"Filter","content-manager.components.DragHandle-label":"Ziehen","content-manager.components.DraggableAttr.edit":"Klicken zum Bearbeiten","content-manager.components.DraggableCard.delete.field":"L\xF6sche {item}","content-manager.components.DraggableCard.edit.field":"Bearbeite {item}","content-manager.components.DraggableCard.move.field":"Verschiebe {item}","content-manager.components.ListViewTable.row-line":"Eintrag Zeile {number}","content-manager.components.DynamicZone.ComponentPicker-label":"W\xE4hle eine Komponente","content-manager.components.DynamicZone.add-component":"F\xFCge {componentName} eine Komponente hinzu","content-manager.components.DynamicZone.delete-label":"L\xF6sche {name}","content-manager.components.DynamicZone.error-message":"Die Komponente enth\xE4lt einen oder mehrere Fehler","content-manager.components.DynamicZone.missing-components":"{number, plura, one {# Komponente} other {# Komponenten}} fehlen","content-manager.components.DynamicZone.move-down-label":"Verschiebe Komponente nach unten","content-manager.components.DynamicZone.move-up-label":"Verschiebe Komponente nach oben","content-manager.components.DynamicZone.pick-compo":"W\xE4hle eine Komponente","content-manager.components.DynamicZone.required":"Komponente wird ben\xF6tigt","content-manager.components.EmptyAttributesBlock.button":"Gehe zu den Einstellungen","content-manager.components.EmptyAttributesBlock.description":"Du kannst deine Einstellungen \xE4ndern","content-manager.components.FieldItem.linkToComponentLayout":"Layout der Komponente anpassen","content-manager.components.FieldSelect.label":"F\xFCge Feld hinzu","content-manager.components.FilterOptions.button.apply":"Anwenden","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Anwenden","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Alle l\xF6schen","content-manager.components.FiltersPickWrapper.PluginHeader.description":"Lege die Bedingungen fest, unter denen die Eintr\xE4ge gefiltert werden sollen","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Filter","content-manager.components.FiltersPickWrapper.hide":"Ausblenden","content-manager.components.LeftMenu.Search.label":"Suche nach einem Inhaltstyp","content-manager.components.LeftMenu.collection-types":"Sammlungen","content-manager.components.LeftMenu.single-types":"Einzel-Eintr\xE4ge","content-manager.components.LimitSelect.itemsPerPage":"Eintr\xE4ge pro Seite","content-manager.components.NotAllowedInput.text":"Keine Berechtigung dieses Feld anzusehen","content-manager.components.RepeatableComponent.error-message":"Die Komponente(n) enth\xE4lt einen/enthalten Fehler","content-manager.components.Search.placeholder":"Suche nach einem Eintrag....","content-manager.components.Select.draft-info-title":"Status: Entwurf","content-manager.components.Select.publish-info-title":"State: Ver\xF6ffentlicht","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Anpassen, wie die Bearbeitungsansicht aussieht.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Einstellungen der Listenansicht anpassen.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Ansicht anpassen - {name}","content-manager.components.TableDelete.delete":"Alle l\xF6schen","content-manager.components.TableDelete.deleteSelected":"Ausgew\xE4hlte l\xF6schen","content-manager.components.TableDelete.label":"{number, plural, one {# Eintrag} other {# Eintr\xE4ge}} ausgew\xE4hlt","content-manager.components.TableEmpty.withFilters":"Es gibt keine {contentType} mit den verwendeten Filtern...","content-manager.components.TableEmpty.withSearch":"Es gibt keine {contentType}, die der Suche ({search}) entsprechen...","content-manager.components.TableEmpty.withoutFilter":"Es gibt keine {contentType}...","content-manager.components.empty-repeatable":"Noch keine Eintr\xE4ge. Nutze den Button unten um einen hinzuzuf\xFCgen.","content-manager.components.notification.info.maximum-requirement":"Die maximale Anzahl an Feldern wurde erreicht","content-manager.components.notification.info.minimum-requirement":"Es wurde ein Feld hinzugef\xFCgt um die minimale Anzahl zu erf\xFCllen","content-manager.components.repeatable.reorder.error":"W\xE4hrend dem \xC4ndern der Reihenfolge der Komponenten ist ein Fehler aufgetreten, bitte versuche es erneut","content-manager.components.reset-entry":"Eintrag zur\xFCcksetzen","content-manager.components.uid.apply":"apply","content-manager.components.uid.available":"available","content-manager.components.uid.regenerate":"regenerate","content-manager.components.uid.suggested":"suggested","content-manager.components.uid.unavailable":"unavailable","content-manager.containers.Edit.Link.Layout":"Layout anpassen","content-manager.containers.Edit.Link.Model":"Sammlung bearbeiten","content-manager.containers.Edit.addAnItem":"F\xFCge ein Item hinzu...","content-manager.containers.Edit.clickToJump":"Klicke, um zu einem Eintrag zu springen","content-manager.containers.Edit.delete":"L\xF6schen","content-manager.containers.Edit.delete-entry":"Diesen Eintrag l\xF6schen","content-manager.containers.Edit.editing":"Bearbeite...","content-manager.containers.Edit.information":"Informationen","content-manager.containers.Edit.information.by":"Von","content-manager.containers.Edit.information.created":"Erstellt","content-manager.containers.Edit.information.draftVersion":"Entwurf","content-manager.containers.Edit.information.editing":"Bearbeite","content-manager.containers.Edit.information.lastUpdate":"Letzte \xC4nderung","content-manager.containers.Edit.information.publishedVersion":"ver\xF6ffentlichte Version","content-manager.containers.Edit.pluginHeader.title.new":"Eintrag erstellen","content-manager.containers.Edit.reset":"Zur\xFCcksetzen","content-manager.containers.Edit.returnList":"Zu Liste zur\xFCckkehren","content-manager.containers.Edit.seeDetails":"Details","content-manager.containers.Edit.submit":"Speichern","content-manager.containers.EditSettingsView.modal-form.edit-field":"Feld bearbeiten","content-manager.containers.EditView.add.new-entry":"Eintrag hinzuf\xFCgen","content-manager.containers.EditView.notification.errors":"Das Formular enth\xE4lt Fehler","content-manager.containers.Home.introduction":"Um deine Eintr\xE4ge zu verwalten, klicke auf den entsprechenden Link im Men\xFC links. Dieses Plugin ist noch in aktiver Entwicklung und seine Einstellungen k\xF6nnen nicht optimal angepasst werden.","content-manager.containers.Home.pluginHeaderDescription":"Verwalte deine Eintr\xE4ge mithilfe eines m\xE4chtigen und wundersch\xF6nen Interfaces.","content-manager.containers.Home.pluginHeaderTitle":"Inhalts-Manager","content-manager.containers.List.draft":"Entwurf","content-manager.containers.List.errorFetchRecords":"Fehler","content-manager.containers.List.published":"Ver\xF6ffentlicht","content-manager.containers.ListPage.displayedFields":"Dargestellte Felder","content-manager.containers.ListPage.items":"{number, plural, one {Eintrag} other {Eintr\xE4ge}}","content-manager.containers.ListPage.table-headers.publishedAt":"Status","content-manager.containers.ListSettingsView.modal-form.edit-label":"Beschriftung \xE4ndern","content-manager.containers.SettingPage.add.field":"Ein weiteres Feld hinzuf\xFCgen","content-manager.containers.SettingPage.attributes":"Attribut-Felder","content-manager.containers.SettingPage.attributes.description":"Reihenfolge der Attribute festlegen","content-manager.containers.SettingPage.editSettings.description":"Ziehe die Felder via Drag & Drop, um das Layout zu erstellen","content-manager.containers.SettingPage.editSettings.entry.title":"Anzeigefeld","content-manager.containers.SettingPage.editSettings.entry.title.description":"Anzeigefeld der Eintr\xE4ge w\xE4hlen","content-manager.containers.SettingPage.editSettings.relation-field.description":"Setze das dargestellte Feld sowohl in der Bearbeiten-, als auch der Listenansicht","content-manager.containers.SettingPage.editSettings.title":"Bearbeiten (einstellungen)","content-manager.containers.SettingPage.layout":"Layout","content-manager.containers.SettingPage.listSettings.description":"Konfiguriere die Einstellungen f\xFCr diesen Collection Type","content-manager.containers.SettingPage.listSettings.title":"Listenansicht (Einstellungen)","content-manager.containers.SettingPage.pluginHeaderDescription":"Konfiguriere die spezifische Ansicht f\xFCr diesen Collection Type","content-manager.containers.SettingPage.settings":"Einstellungen","content-manager.containers.SettingPage.view":"Ansicht","content-manager.containers.SettingViewModel.pluginHeader.title":"Inhalts-Manager - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Spezifische Einstellungen konfigurieren","content-manager.containers.SettingsPage.Block.contentType.title":"Sammlungen","content-manager.containers.SettingsPage.Block.generalSettings.description":"Standardoptionen f\xFCr Sammlungen konfigurieren","content-manager.containers.SettingsPage.Block.generalSettings.title":"Generell","content-manager.containers.SettingsPage.pluginHeaderDescription":"Einstellungen f\xFCr alle Sammlungen und Gruppen konfigurieren","content-manager.containers.SettingsView.list.subtitle":"Layout und Darstellung f\xFCr alle Sammlungen und Gruppen konfigurieren","content-manager.containers.SettingsView.list.title":"Darstellungsoptionen","content-manager.edit-settings-view.link-to-ctb.components":"Komponente bearbeiten","content-manager.edit-settings-view.link-to-ctb.content-types":"Inhalts-Typ bearbeiten","content-manager.emptyAttributes.button":"Zum Sammlungs-Editor","content-manager.emptyAttributes.description":"F\xFCge das erste Feld zur Sammlung hinzu","content-manager.emptyAttributes.title":"Es gibt noch keine Felder","content-manager.error.attribute.key.taken":"Dieser Wert existiert bereits","content-manager.error.attribute.sameKeyAndName":"Darf nicht gleich sein","content-manager.error.attribute.taken":"Dieser Feldname ist bereits vergeben","content-manager.error.contentTypeName.taken":"Dieser Name existiert bereits","content-manager.error.model.fetch":"Beim Abruf von model config fetch ist ein Fehler aufgetreten.","content-manager.error.record.create":"Beim Anlegen eines Dokuments ist ein Fehler aufgetreten.","content-manager.error.record.delete":"Beim L\xF6schen eines Dokuments ist ein Fehler aufgetreten.","content-manager.error.record.fetch":"Beim Abruf eines Dokuments ist ein Fehler aufgetreten.","content-manager.error.record.update":"Beim Aktualisieren eines Dokuments ist ein Fehler aufgetreten.","content-manager.error.records.count":"Beim Abrufen der Anzahl an Eintr\xE4gen ist ein Fehler aufgetreten.","content-manager.error.records.fetch":"Beim Abrufen von Dokumenten ist ein Fehler aufgetreten.","content-manager.error.schema.generation":"Bei der Generierung des Schemas ist ein Fehler aufgetreten.","content-manager.error.validation.json":"Dies ist kein JSON","content-manager.error.validation.max":"Dieser Wert ist zu hoch.","content-manager.error.validation.maxLength":"Dieser Wert ist zu lang.","content-manager.error.validation.min":"Dieser Wert ist zu niedrig.","content-manager.error.validation.minLength":"Dieser Wert ist zu kurz.","content-manager.error.validation.minSupMax":"Darf nicht h\xF6her sein","content-manager.error.validation.regex":"Dieser Wert entspricht nicht dem RegEx.","content-manager.error.validation.required":"Dieser Wert ist erforderlich.","content-manager.form.Input.bulkActions":"Bulk-Bearbeitung aktivieren","content-manager.form.Input.defaultSort":"Standard-Sortierattribut","content-manager.form.Input.description":"Beschreibung","content-manager.form.Input.description.placeholder":"Zeige den Namen im Profil","content-manager.form.Input.editable":"Editierbares Feld","content-manager.form.Input.filters":"Filter aktivieren","content-manager.form.Input.label":"Label","content-manager.form.Input.label.inputDescription":"Dieser Wert \xFCberschreibt das im Kopf der Tabelle angezeigte Label.","content-manager.form.Input.pageEntries":"Eintr\xE4ge pro Seite","content-manager.form.Input.pageEntries.inputDescription":"Hinweis: Dieser Wert l\xE4sst sich durch die Sammlungs-Einstellungen \xFCberschreiben.","content-manager.form.Input.placeholder":"Platzhalter","content-manager.form.Input.placeholder.placeholder":"Mein unglaublicher Wert","content-manager.form.Input.search":"Suche aktivieren","content-manager.form.Input.search.field":"Suche in diesem Feld aktivieren","content-manager.form.Input.sort.field":"Sortierung in diesem Feld aktivieren","content-manager.form.Input.sort.order":"Standard-Reihenfolge","content-manager.form.Input.wysiwyg":"Als visuellen Editor anzeigen","content-manager.global.displayedFields":"Angezeigte Felder","content-manager.groups":"Gruppen","content-manager.groups.numbered":"Gruppen ({number})","content-manager.header.name":"Inhalt","content-manager.link-to-ctb":"Modell bearbeiten","content-manager.models":"Sammlungen","content-manager.models.numbered":"Sammlungen ({number})","content-manager.notification.error.displayedFields":"Du ben\xF6tigst mindestens ein dargestelltes Feld","content-manager.notification.error.relationship.fetch":"Beim Abruf von Beziehungen ist ein Fehler aufgetreten.","content-manager.notification.info.SettingPage.disableSort":"Du musst ein Attribut mit aktivierter Sortierung haben.","content-manager.notification.info.minimumFields":"Du ben\xF6tigst mindestens ein dargestelltes Feld","content-manager.notification.upload.error":"Beim Hochladen deiner Dateien ist ein Fehler aufgetreten","content-manager.pageNotFound":"Seite nicht gefunden","content-manager.pages.ListView.header-subtitle":"{number, plural, one {# Eintrag} other {# Eintr\xE4ge}} gefunden","content-manager.pages.NoContentType.button":"Erstelle deinen ersten Inhalts-Typ","content-manager.pages.NoContentType.text":"Wenn du noch keinen Inhalt hast, empfehlen wir dir, zuerst einen Inhalts-Typ erstellen.","content-manager.permissions.not-allowed.create":"Du hast nicht die erforderlichen Berechtigungen, um ein Dokument zu erstellen","content-manager.permissions.not-allowed.update":"Du hast nicht die erforderlichen Berechtigungen, um dieses Dokument anzuschauen","content-manager.plugin.description.long":"Greife schnell auf alle Daten in der Datenbank zu und \xE4ndere sie.","content-manager.plugin.description.short":"Greife schnell auf alle Daten in der Datenbank zu und \xE4ndere sie.","content-manager.popover.display-relations.label":"Beziehungen darstellen","content-manager.success.record.delete":"Gel\xF6scht","content-manager.success.record.publish":"Ver\xF6ffentlicht","content-manager.success.record.save":"Gespeichert","content-manager.success.record.unpublish":"Ver\xF6ffentlichung zur\xFCckgenommen","content-manager.utils.data-loaded":"{number, plural, =1 {Der Eintrag wurde} other {Die Eintr\xE4ge wurden}} erfolgreich geladen","content-manager.popUpWarning.warning.publish-question":"Wollst du diesen Eintrag trotzdem ver\xF6ffentlichen?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Ja, ver\xF6ffentlichen","content-manager.popUpwarning.warning.has-draft-relations.message":"<b>{count, plural, =0 { von deinen Inhalts-Beziehungen sind} one { von deinen Inhalts-Beziehungen ist} other { von deinen Inhalts-Beziehungen sind}}</b> noch nicht ver\xF6ffentlicht.<br></br>Das kann zu kaputten Links und Fehlern in deinem Projekt f\xFChren.","form.button.done":"Fertig","global.actions":"Aktionen","global.back":"Zur\xFCck","global.change-password":"Passwort \xE4ndern","global.content-manager":"Inhalts-Manager","global.continue":"Weiter","global.delete":"L\xF6schen","global.delete-target":"L\xF6sche {target}","global.description":"Beschreibung","global.details":"Details","global.disabled":"Deaktiviert","global.documentation":"Dokumentation","global.enabled":"Aktiviert","global.finish":"Fertig","global.marketplace":"Marketplace","global.name":"Name","global.none":"Keine","global.password":"Passwort","global.plugins":"Plugins","global.profile":"Profil","global.prompt.unsaved":"Seite wirklich verlassen? Alle \xC4nderungen gehen hierdurch verloren.","global.reset-password":"Passwort zur\xFCcksetzen","global.roles":"Rollen","global.save":"Speichern","global.see-more":"Mehr anzeigen","global.select":"Ausw\xE4hlen","global.select-all-entries":"W\xE4hle alle Eintr\xE4ge aus","global.settings":"Einstellungen","global.type":"Typ","global.users":"Benutzer","notification.contentType.relations.conflict":"Content Type hat Konflikt in Beziehungen","notification.default.title":"Information:","notification.error":"Ein Fehler ist aufgetreten","notification.error.layout":"Das Layout konnte nicht abgerufen werden.","notification.form.error.fields":"Das Formular enth\xE4lt Fehler","notification.form.success.fields":"\xC4nderungen gespeichert","notification.link-copied":"Link in die Zwischenablage kopiert","notification.permission.not-allowed-read":"Keine Berechtigung dieses Dokument einzusehen","notification.success.delete":"Eintrag wurde gel\xF6scht","notification.success.saved":"Gespeichert","notification.success.title":"Erfolg:","notification.version.update.message":"Eine neue Strapi Version ist verf\xFCgbar","notification.warning.title":"Warnung:",or:u,"request.error.model.unknown":"Dieses Schema existiert nicht",skipToContent:d,submit:h}}}]);
