"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[9595],{69595:(K,s,_)=>{_.r(s),_.d(s,{HomePageEE:()=>W});var D=_(92132),a=_(86213),n=_(76616),C=_(21272),A=_(55506),R=_(14718),l=_(66248),v=_(55151),B=_(79077),U=_(43543),t=_(15126),i=_(63299),L=_(67014),I=_(59080),d=_(79275),O=_(82437),T=_(61535),M=_(5790),E=_(12083),o=_(35223),m=_(5409),P=_(74930),h=_(2600),r=_(48940),f=_(41286),g=_(56336),S=_(13426),y=_(84624),N=_(77965),j=_(54257),H=_(71210),V=_(51187),x=_(39404),F=_(58692),$=_(501),c=_(57646),G=_(23120),z=_(44414),e=_(25962),X=_(14664),Y=_(42588),J=_(90325),Q=_(62785),Z=_(87443),u=_(41032),p=_(22957),k=_(93179),w=_(73055),b=_(15747),q=_(85306),__=_(26509),E_=_(32058),t_=_(81185),s_=_(82261),O_=_(98898),M_=_(67031);const W=()=>((0,n.u)(),(0,D.jsx)(a.HomePageCE,{}))},76616:(K,s,_)=>{_.d(s,{u:()=>U});var D=_(21272),a=_(55506),n=_(67031),C=_(54894),A=_(17703),R=_(43543);const l="strapi-notification-seat-limit",v="https://cloud.strapi.io/profile/billing",B="https://strapi.io/billing/request-seats",U=()=>{const{formatMessage:t}=(0,C.A)(),{license:i,isError:L,isLoading:I}=(0,R.m)(),d=(0,a.hN)(),{pathname:O}=(0,A.zy)(),{enforcementUserCount:T,permittedSeats:M,licenseLimitStatus:E,isHostedOnStrapiCloud:o}=i??{};D.useEffect(()=>{if(L||I)return;const m=!n(M)&&!window.sessionStorage.getItem(`${l}-${O}`)&&(E==="AT_LIMIT"||E==="OVER_LIMIT");let P;E==="OVER_LIMIT"?P="warning":E==="AT_LIMIT"&&(P="softWarning"),m&&d({type:P,message:t({id:"notification.ee.warning.over-.message",defaultMessage:"Add seats to {licenseLimitStatus, select, OVER_LIMIT {invite} other {re-enable}} Users. If you already did it but it's not reflected in Strapi yet, make sure to restart your app."},{licenseLimitStatus:E}),title:t({id:"notification.ee.warning.at-seat-limit.title",defaultMessage:"{licenseLimitStatus, select, OVER_LIMIT {Over} other {At}} seat limit ({enforcementUserCount}/{permittedSeats})"},{licenseLimitStatus:E,enforcementUserCount:T,permittedSeats:M}),link:{url:o?v:B,label:t({id:"notification.ee.warning.seat-limit.link",defaultMessage:"{isHostedOnStrapiCloud, select, true {ADD SEATS} other {CONTACT SALES}}"},{isHostedOnStrapiCloud:o})},blockTransition:!0,onClose(){window.sessionStorage.setItem(`${l}-${O}`,"true")}})},[d,i,O,t,I,M,E,T,o,L])}}}]);
