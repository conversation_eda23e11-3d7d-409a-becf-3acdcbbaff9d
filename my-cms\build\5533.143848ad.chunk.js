"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[5533],{5533:(F,e,t)=>{t.r(e),t.d(e,{Analytics:()=>n,Documentation:()=>a,Email:()=>o,Password:()=>s,Provider:()=>l,ResetPasswordToken:()=>i,Role:()=>r,Username:()=>p,Users:()=>m,anErrorOccurred:()=>c,clearLabel:()=>g,dark:()=>E,default:()=>z,light:()=>x,or:()=>u,skipToContent:()=>k,submit:()=>d});const n="Analitika",a="Dokument\xE1ci\xF3",o="Email",s="Jelsz\xF3",l="Szolg\xE1ltat\xF3",i="Token vissza\xE1ll\xEDt\xE1sa",r="Szerepk\xF6r",x="Vil\xE1gos",E="S\xF6t\xE9t",p="Felhaszn\xE1l\xF3n\xE9v",m="Felhaszn\xE1l\xF3k",c="Hopp\xE1! Valami elromlott. K\xE9rlek pr\xF3b\xE1ld \xFAjra.",g="Ki\xFCr\xEDt",u="Vagy",k="Kihagy\xE1s",d="K\xFCld\xE9s",z={Analytics:n,"Auth.components.Oops.text":"A fi\xF3kodat felf\xFCggesztett\xFCk","Auth.components.Oops.text.admin":"Amennyiben ez hiba, k\xE9rj\xFCk vegye fel a kapcsolatot az adminisztr\xE1torokkal!","Auth.components.Oops.title":"Oops...","Auth.form.active.label":"Akt\xEDv","Auth.form.button.forgot-password":"Email k\xFCld\xE9se","Auth.form.button.go-home":"Vissza a kezd\u0151lapra","Auth.form.button.login":"Bejelentkez\xE9s","Auth.form.button.login.providers.error":"Nem siker\xFClt kapcsol\xF3dni a szolg\xE1ltat\xF3n kereszt\xFCl","Auth.form.button.login.strapi":"Bejelentkez\xE9s Strapi-val","Auth.form.button.password-recovery":"Jelsz\xF3 vissza\xE1ll\xEDt\xE1sa","Auth.form.button.register":"Kezdj\xFCk","Auth.form.confirmPassword.label":"Jelsz\xF3 meger\u0151s\xEDt\xE9se","Auth.form.currentPassword.label":"Jelenlegi jelsz\xF3","Auth.form.email.label":"Email","Auth.form.email.placeholder":"e.g. <EMAIL>","Auth.form.error.blocked":"A fi\xF3kodat az adminisztr\xE1tor blokkolta","Auth.form.error.code.provide":"Hib\xE1s a megadott k\xF3d","Auth.form.error.confirmed":"Az email c\xEDm nincs meger\u0151s\xEDtve","Auth.form.error.email.invalid":"Hib\xE1s email.","Auth.form.error.email.provide":"K\xE9rj\xFCk adja meg felhaszn\xE1l\xF3nev\xE9t \xE9s jelszav\xE1t.","Auth.form.error.email.taken":"Ez az email c\xEDm m\xE1r foglalt.","Auth.form.error.invalid":"Felhaszn\xE1l\xF3n\xE9v vagy jelsz\xF3 hib\xE1s.","Auth.form.error.params.provide":"Hib\xE1s a megadott adat.","Auth.form.error.password.format":"A jelsz\xF3 nem tartalmazhatja a `$` szimb\xF3lumot t\xF6bbsz\xF6r, mint h\xE1rom.","Auth.form.error.password.local":"Ez a felhaszn\xE1l\xF3 nem \xE1ll\xEDtott be jelsz\xF3t, k\xE9rj\xFCk jelentkezzen be szolg\xE1ltat\xF3n kereszt\xFCl.","Auth.form.error.password.matching":"A jelszavak nem egeyznek.","Auth.form.error.password.provide":"K\xE9rj\xFCk adja meg a jelszav\xE1t.","Auth.form.error.ratelimit":"T\xFAl sok pr\xF3b\xE1lkoz\xE1s, k\xE9rj\xFCk pr\xF3b\xE1lkozzon \xFAjra egy perc m\xFAlva.","Auth.form.error.user.not-exist":"Ez az email nem l\xE9tezik.","Auth.form.error.username.taken":"A felhaszn\xE1l\xF3n\xE9v foglalt.","Auth.form.firstname.label":"Keresztn\xE9v","Auth.form.firstname.placeholder":"pl. Elek","Auth.form.forgot-password.email.label":"Adja meg az email c\xEDm\xE9t","Auth.form.forgot-password.email.label.success":"Az email-t sikeresen kik\xFCldt\xFCk","Auth.form.lastname.label":"Vezet\xE9kn\xE9v","Auth.form.lastname.placeholder":"pl. Teszt","Auth.form.password.hide-password":"Jelsz\xF3 elrejt\xE9se","Auth.form.password.hint":"A jelsz\xF3nak legal\xE1bb 8 karaktert, 1 nagybet\u0171t, 1 kisbet\u0171t \xE9s 1 sz\xE1mot kell tartalmaznia.","Auth.form.password.show-password":"Jelsz\xF3 megjelen\xEDt\xE9se","Auth.form.register.news.label":"\xC9rtes\xEDt\xE9st k\xE9rek az \xFAj funkci\xF3kr\xF3l \xE9s jav\xEDt\xE1sokr\xF3l (ezzel elfogadja a {terms} \xE9s a {policy}).","Auth.form.register.subtitle":"Ez csak az admin oldalra val\xF3 bejelentkez\xE9sre ad lehet\u0151s\xE9get. Minden elmentett adat a saj\xE1t adatb\xE1zisaba ker\xFCl ment\xE9sre.","Auth.form.rememberMe.label":"Eml\xE9kezz r\xE1m","Auth.form.username.label":"Felhaszn\xE1l\xF3n\xE9v","Auth.form.username.placeholder":"e.g. Kai_Doe","Auth.form.welcome.subtitle":"Bejelentkez\xE9s a Strapi fi\xF3kj\xE1ba","Auth.form.welcome.title":"\xDCdv\xF6z\xF6lj\xFCk!","Auth.link.forgot-password":"Elfelejtette a jelszav\xE1t?","Auth.link.ready":"K\xE9szen \xE1ll a bejelentkez\xE9sre?","Auth.link.signin":"Bejelentkez\xE9s","Auth.link.signin.account":"M\xE1r van felhaszn\xE1l\xF3i fi\xF3kja?","Auth.login.sso.divider":"Vagy bejelentkez\xE9s ezzel:","Auth.login.sso.loading":"Szolg\xE1ltat\xF3k bet\xF6lt\xE9se...","Auth.login.sso.subtitle":"Bejelentkez\xE9s a fi\xF3kj\xE1ba SSO-val","Auth.privacy-policy-agreement.policy":"adatv\xE9delmi nyilatkozat","Auth.privacy-policy-agreement.terms":"felhaszn\xE1l\xE1si felt\xE9telek","Auth.reset-password.title":"Jelsz\xF3 vissza\xE1ll\xEDt\xE1sa","Content Manager":"Tartalom Menedzser","Content Type Builder":"Tartalomt\xEDpus \xE9p\xEDt\u0151",Documentation:a,Email:o,"Files Upload":"F\xE1jl felt\xF6lt\xE9s","HomePage.helmet.title":"Kezd\u0151lap","HomePage.roadmap":"N\xE9zze meg a terveinket","HomePage.welcome.congrats":"Gratul\xE1lunk!","HomePage.welcome.congrats.content":"Els\u0151 adminisztr\xE1tork\xE9nt jelentkezett be. Ahhoz, hogy felfedezhesse a Strapi funkci\xF3it,","HomePage.welcome.congrats.content.bold":"azt aj\xE1nljuk, hogy hozza l\xE9tre az els\u0151 tartalomt\xEDpust.","Media Library":"M\xE9dia K\xF6nyvt\xE1r","New entry":"\xDAj elem",Password:s,Provider:l,ResetPasswordToken:i,Role:r,"Roles & Permissions":"Szerepk\xF6r\xF6k & Enged\xE9lyek","Roles.ListPage.notification.delete-all-not-allowed":"Egyes szerepk\xF6r\xF6k nem t\xF6r\xF6lhet\u0151k, mivel felhaszn\xE1l\xF3khoz vannak t\xE1rs\xEDtva","Roles.ListPage.notification.delete-not-allowed":"A felhaszn\xE1l\xF3khoz t\xE1rs\xEDtott szerepk\xF6r nem t\xF6r\xF6lhet\u0151","Roles.RoleRow.select-all":"{name} kiv\xE1laszt\xE1sa t\xF6meges m\u0171veletekhez","Roles.RoleRow.user-count":"{number, plural, =0 {# felhaszn\xE1l\xF3} one {# felhaszn\xE1l\xF3} other {# felhaszn\xE1l\xF3k}}","Roles.components.List.empty.withSearch":"Nincs a keres\xE9snek megfelel\u0151 szerepk\xF6r ({search})...","Settings.PageTitle":"Be\xE1ll\xEDt\xE1sok - {name}","Settings.apiTokens.addFirstToken":"Els\u0151 API Token hozz\xE1ad\xE1sa","Settings.apiTokens.addNewToken":"\xDAj API Token hozz\xE1ad\xE1sa","Settings.tokens.copy.editMessage":"Biztons\xE1gi okokb\xF3l csak egyszer l\xE1thatja a tokent.","Settings.tokens.copy.editTitle":"Ez a token m\xE1r nem el\xE9rhet\u0151.","Settings.tokens.copy.lastWarning":"M\xE1solja le a tokent, mert k\xE9s\u0151bb m\xE1r nem lesz l\xE1that\xF3!","Settings.apiTokens.create":"\xDAj hozz\xE1ad\xE1sa","Settings.apiTokens.description":"Az API felhaszn\xE1l\xE1s\xE1hoz gener\xE1lt tokenek list\xE1ja","Settings.apiTokens.emptyStateLayout":"M\xE9g nincs tartalom hozz\xE1adva...","Settings.apiTokens.ListView.headers.name":"N\xE9v","Settings.apiTokens.ListView.headers.description":"Le\xEDr\xE1s","Settings.apiTokens.ListView.headers.type":"Token t\xEDpusa","Settings.apiTokens.ListView.headers.createdAt":"L\xE9trehozva","Settings.apiTokens.ListView.headers.lastUsedAt":"Utolj\xE1ra haszn\xE1lva","Settings.tokens.notification.copied":"Token a v\xE1g\xF3lapra m\xE1solva.","Settings.apiTokens.title":"API Token-ek","Settings.tokens.types.full-access":"Teljes hozz\xE1f\xE9r\xE9s","Settings.tokens.types.read-only":"Csak olvashat\xF3","Settings.tokens.duration.7-days":"7 nap","Settings.tokens.duration.30-days":"30 nap","Settings.tokens.duration.90-days":"90 nap","Settings.tokens.duration.unlimited":"Korl\xE1tlan","Settings.tokens.form.duration":"Token id\u0151tartama","Settings.tokens.form.type":"Token t\xEDpusa","Settings.tokens.duration.expiration-date":"Lej\xE1rati d\xE1tum","Settings.apiTokens.createPage.permissions.title":"Enged\xE9lyek","Settings.apiTokens.createPage.permissions.description":"Csak az \xFAtvonalakhoz k\xF6t\xF6tt m\u0171veletek szerepelnek az al\xE1bbiakban.","Settings.tokens.RegenerateDialog.title":"Token \xFAjragener\xE1l\xE1sa","Settings.tokens.popUpWarning.message":"Biztosan \xFAjragener\xE1lod ezt a token-t?","Settings.tokens.Button.cancel":"M\xE9gse","Settings.tokens.Button.regenerate":"\xDAjragener\xE1l\xE1s","Settings.application.description":"Az adminisztr\xE1ci\xF3s panel glob\xE1lis inform\xE1ci\xF3i","Settings.application.edition-title":"Aktu\xE1lis csomag","Settings.application.get-help":"K\xE9rje seg\xEDts\xE9g\xFCnket","Settings.application.link-pricing":"Tekintse meg az \xF6sszes csomagot","Settings.application.link-upgrade":"Friss\xEDtse az adminisztr\xE1ci\xF3s panelt","Settings.application.node-version":"node verzi\xF3","Settings.application.strapi-version":"strapi verzi\xF3","Settings.application.strapiVersion":"strapi verzi\xF3","Settings.application.title":"\xC1ttekint\xE9s","Settings.application.customization":"Testreszab\xE1s","Settings.application.customization.carousel.title":"Log\xF3","Settings.application.customization.carousel.change-action":"Log\xF3 m\xF3dos\xEDt\xE1sa","Settings.application.customization.carousel.reset-action":"Log\xF3 vissza\xE1ll\xEDt\xE1sa","Settings.application.customization.carousel-slide.label":"Log\xF3 diasor","Settings.application.customization.carousel-hint":"V\xE1ltoztasd meg az admin panel log\xF3j\xE1t (Max m\xE9ret: {dimension}x{dimension}, Max f\xE1jlm\xE9ret: {size}KB)","Settings.application.customization.modal.cancel":"M\xE9gse","Settings.application.customization.modal.upload":"Log\xF3 felt\xF6lt\xE9se","Settings.application.customization.modal.tab.label":"Hogyan szeretn\xE9d felt\xF6lteni az \xE1llom\xE1nyaidat?","Settings.application.customization.modal.upload.from-computer":"Sz\xE1m\xEDt\xF3g\xE9pr\u0151l","Settings.application.customization.modal.upload.file-validation":"Max m\xE9ret: {dimension}x{dimension}, Max m\xE9ret: {size}KB","Settings.application.customization.modal.upload.error-format":"Rossz form\xE1tumot t\xF6lt\xF6tt\xE9l fel (csak a k\xF6vetkez\u0151 form\xE1tumokat fogadja el: jpeg, jpg, png, svg).","Settings.application.customization.modal.upload.error-size":"A felt\xF6lt\xF6tt f\xE1jl t\xFAl nagy (max m\xE9ret: {dimension}x{dimension}, max f\xE1jlm\xE9ret: {size}KB)","Settings.application.customization.modal.upload.error-network":"H\xE1l\xF3zati hiba","Settings.application.customization.modal.upload.cta.browse":"F\xE1jlok tall\xF3z\xE1sa","Settings.application.customization.modal.upload.drag-drop":"H\xFAzz \xE9s ejtsd ide vagy","Settings.application.customization.modal.upload.from-url":"URL-r\u0151l","Settings.application.customization.modal.upload.from-url.input-label":"URL","Settings.application.customization.modal.upload.next":"K\xF6vetkez\u0151","Settings.application.customization.modal.pending":"F\xFCgg\u0151ben l\xE9v\u0151 log\xF3","Settings.application.customization.modal.pending.choose-another":"V\xE1lassz m\xE1sik log\xF3t","Settings.application.customization.modal.pending.title":"Log\xF3 k\xE9szen \xE1ll a felt\xF6lt\xE9sre","Settings.application.customization.modal.pending.subtitle":"Kezeljed a kiv\xE1lasztott log\xF3t a felt\xF6lt\xE9s el\u0151tt","Settings.application.customization.modal.pending.upload":"Log\xF3 felt\xF6lt\xE9se","Settings.application.customization.modal.pending.card-badge":"k\xE9p","Settings.error":"Hiba","Settings.global":"Glob\xE1lis Be\xE1ll\xEDt\xE1sok","Settings.permissions":"Adminisztr\xE1ci\xF3s panel","Settings.permissions.category":"{category} enged\xE9lyeinek be\xE1ll\xEDt\xE1sai","Settings.permissions.category.plugins":"{category} plugin enged\xE9lyeinek be\xE1ll\xEDt\xE1sai","Settings.permissions.conditions.anytime":"B\xE1rmikor","Settings.permissions.conditions.apply":"Alkalmaz","Settings.permissions.conditions.can":"Tudja","Settings.permissions.conditions.conditions":"Hat\xE1rozza meg a felt\xE9teleket","Settings.permissions.conditions.links":"Linkek","Settings.permissions.conditions.no-actions":"El\u0151sz\xF6r v\xE1lasztania kell egy m\u0171veletet (create, read, update, ...) miel\u0151tt megadja a felt\xE9teleket.","Settings.permissions.conditions.none-selected":"B\xE1rmikor","Settings.permissions.conditions.or":"VAGY","Settings.permissions.conditions.when":"Mikor","Settings.permissions.select-all-by-permission":"Minden {label} hozz\xE1f\xE9res kiv\xE1laszt\xE1sa","Settings.permissions.select-by-permission":"{label} hozz\xE1f\xE9res kiv\xE1laszt\xE1sa","Settings.permissions.users.create":"\xDAj felhaszn\xE1l\xF3 megh\xEDv\xE1sa","Settings.permissions.users.email":"Email","Settings.permissions.users.firstname":"Keresztn\xE9v","Settings.permissions.users.lastname":"Vezet\xE9kn\xE9v","Settings.permissions.users.user-status":"Felhaszn\xE1l\xF3i \xE1llapot","Settings.permissions.users.roles":"Szerepek","Settings.permissions.users.username":"Felhaszn\xE1l\xF3n\xE9v","Settings.permissions.users.active":"Akt\xEDv","Settings.permissions.users.inactive":"Inakt\xEDv","Settings.permissions.users.form.sso":"Csatlakozas SSO-val","Settings.permissions.users.form.sso.description":"Ha enged\xE9lyezve van (ON), a felhaszn\xE1l\xF3k bejelentkezhetnek SSO-n kereszt\xFCl","Settings.permissions.users.listview.header.subtitle":"Minden felhaszn\xE1l\xF3, aki hozz\xE1f\xE9r a Strapi adminisztr\xE1ci\xF3s panelhez","Settings.permissions.users.tabs.label":"Hozz\xE1f\xE9r\xE9sek Tab","Settings.permissions.users.strapi-super-admin":"Super Adminisztr\xE1tor","Settings.permissions.users.strapi-editor":"Szerkeszt\u0151","Settings.permissions.users.strapi-author":"Szerz\u0151","Settings.profile.form.notify.data.loaded":"Profiladatok bet\xF6ltve","Settings.profile.form.section.experience.clear.select":"A kiv\xE1lasztott fel\xFClet nyelv\xE9nek t\xF6rl\xE9se","Settings.profile.form.section.experience.here":"itt","Settings.profile.form.section.experience.documentation":"dokument\xE1ci\xF3","Settings.profile.form.section.experience.interfaceLanguage":"A fel\xFClet nyelve","Settings.profile.form.section.experience.interfaceLanguage.hint":"Ez csak a saj\xE1t fel\xFClet\xE9t jelen\xEDti meg a kiv\xE1lasztott nyelven.","Settings.profile.form.section.experience.interfaceLanguageHelp":"A kiv\xE1laszt\xE1s csak az \xD6n sz\xE1m\xE1ra m\xF3dos\xEDtja a fel\xFClet nyelv\xE9t. K\xE9rj\xFCk, olvassa el ezt a {document}, hogy m\xE1s nyelveket a csapata sz\xE1m\xE1ra is el\xE9rhet\u0151v\xE9 tehesse.","Settings.profile.form.section.experience.mode.label":"Fel\xFClet m\xF3d","Settings.profile.form.section.experience.mode.hint":"Megjelen\xEDti a felhaszn\xE1l\xF3i fel\xFCletedet a kiv\xE1lasztott m\xF3dban.","Settings.profile.form.section.experience.mode.option-label":"{name} m\xF3d",light:x,dark:E,"Settings.profile.form.section.experience.title":"Tapasztalat","Settings.profile.form.section.helmet.title":"Felhaszn\xE1l\xF3i profil","Settings.profile.form.section.profile.page.title":"Profil oldal","Settings.roles.create.description":"Hat\xE1rozza meg a szerephezk\xF6rh\xF6z biztos\xEDtott jogokat","Settings.roles.create.title":"Szerepk\xF6r l\xE9trehoz\xE1sa","Settings.roles.created":"A szerepk\xF6r l\xE9trej\xF6tt","Settings.roles.edit.title":"Szerepk\xF6r m\xF3dos\xEDt\xE1sa","Settings.roles.form.button.users-with-role":"{number, plural, =0 {# felhaszn\xE1l\xF3} one {# felhaszn\xE1l\xF3} other {# felhaszn\xE1l\xF3k}} ezzel a szereppel","Settings.roles.form.created":"L\xE9trehozva","Settings.roles.form.description":"A szerepk\xF6r neve \xE9s le\xEDr\xE1sa","Settings.roles.form.permission.property-label":"{label} hozz\xE1f\xE9re's","Settings.roles.form.permissions.attributesPermissions":"Mez\u0151k hozz\xE1f\xE9r\xE9sei","Settings.roles.form.permissions.create":"L\xE9trehoz","Settings.roles.form.permissions.delete":"T\xF6r\xF6l","Settings.roles.form.permissions.publish":"K\xF6zz\xE9tesz","Settings.roles.form.permissions.read":"Olvas\xE1s","Settings.roles.form.permissions.update":"Friss\xEDt\xE9s","Settings.roles.list.button.add":"\xDAj szerepk\xF6r hozz\xE1ad\xE1sa","Settings.roles.list.description":"Szerepk\xF6r\xF6k list\xE1ja","Settings.roles.title.singular":"Szerepk\xF6r","Settings.sso.description":"Konfigur\xE1lja az egyszeri bejelentkez\xE9s funkci\xF3 be\xE1ll\xEDt\xE1sait.","Settings.sso.form.defaultRole.description":"Az \xFAj hiteles\xEDtett felhaszn\xE1l\xF3t a kiv\xE1lasztott szerepk\xF6rh\xF6z csatolja","Settings.sso.form.defaultRole.description-not-allowed":"Nincs megfelel\u0151 enged\xE9lye az adminisztr\xE1tori szerepk\xF6r\xF6k olvas\xE1s\xE1hoz","Settings.sso.form.defaultRole.label":"Alap\xE9rtelmezett szerepk\xF6r","Settings.sso.form.registration.description":"Egyszeri bejelentkez\xE9skor, ha nincs fi\xF3k, hozzon l\xE9tre \xFAj felhaszn\xE1l\xF3t","Settings.sso.form.registration.label":"Automatikus regisztr\xE1ci\xF3","Settings.sso.title":"Egyszeri bejelentkez\xE9s","Settings.webhooks.create":"Webhook l\xE9trehoz\xE1sa","Settings.webhooks.create.header":"\xDAj fejl\xE9c l\xE9trehoz\xE1sa","Settings.webhooks.created":"Webhook l\xE9trehozva","Settings.webhooks.event.publish-tooltip":"Ez az esem\xE9ny csak olyan tartalmak eset\xE9ben l\xE9tezik, amelyekn\xE9l enged\xE9lyezve van a Piszkozat/K\xF6zz\xE9t\xE9tel rendszer","Settings.webhooks.events.create":"L\xE9trehoz","Settings.webhooks.events.update":"Friss\xEDt","Settings.webhooks.form.events":"Esemn\xE9nyek","Settings.webhooks.form.headers":"Fejl\xE9c","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"Fejl\xE9sor elt\xE1vol\xEDt\xE1sa {number}","Settings.webhooks.key":"Kulcs","Settings.webhooks.list.button.add":"\xDAj webhook l\xE9trehoz\xE1sa","Settings.webhooks.list.description":"\xC9rtes\xEDt\xE9sek a POST m\xF3dos\xEDt\xE1sair\xF3l","Settings.webhooks.list.empty.description":"Nem tal\xE1lhat\xF3 webhook","Settings.webhooks.list.empty.link":"Tekintse meg dokument\xE1ci\xF3nkat","Settings.webhooks.list.empty.title":"M\xE9g nincsenek webhookok","Settings.webhooks.list.th.actions":"M\u0171veletek","Settings.webhooks.list.th.status":"St\xE1tusz","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhook-ok","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# elem} other {# elemek}} kiv\xE1lasztva","Settings.webhooks.trigger":"Kapcsol\xF3","Settings.webhooks.trigger.cancel":"Kapcsol\xF3 t\xF6rl\xE9se","Settings.webhooks.trigger.pending":"Folyamatban\u2026","Settings.webhooks.trigger.save":"K\xE9rj\xFCk mentse","Settings.webhooks.trigger.success":"Siker\xFClt!","Settings.webhooks.trigger.success.label":"A kapcsol\xF3 siker\xFClt","Settings.webhooks.trigger.test":"Teszt-kapcsol\xF3","Settings.webhooks.trigger.title":"El\u0151sz\xF6r mentsen","Settings.webhooks.value":"\xC9rt\xE9k","Usecase.back-end":"Back-end fejleszt\u0151","Usecase.button.skip":"K\xE9rdez\xE9s kihagy\xE1sa","Usecase.content-creator":"Tartalomk\xE9sz\xEDt\u0151","Usecase.front-end":"Front-end fejleszt\u0151","Usecase.full-stack":"Teljesk\xF6r\u0171 fejleszt\u0151","Usecase.input.work-type":"Milyen t\xEDpus\xFA munk\xE1t v\xE9gzel?","Usecase.notification.success.project-created":"A projekt sikeresen l\xE9trehozva","Usecase.other":"Egy\xE9b","Usecase.title":"Mes\xE9lj egy kicsit magadr\xF3l",Username:p,Users:m,"Users & Permissions":"Felhaszn\xE1l\xF3k & Enged\xE9lyek","Users.components.List.empty":"Nincsenek felhaszn\xE1l\xF3k...","Users.components.List.empty.withFilters":"Nincs a be\xE1ll\xEDtott sz\u0171r\u0151knek megfelel\u0151 felhaszn\xE1l\xF3..","Users.components.List.empty.withSearch":"Nincs a keres\xE9nek megfelel\u0151 felhaszn\xE1l\xF3 ({search})...","admin.pages.MarketPlacePage.helmet":"Piact\xE9r - Plugin-ok","admin.pages.MarketPlacePage.offline.title":"\xD6n offline \xE1llapotban van","admin.pages.MarketPlacePage.offline.subtitle":"Csatlakoznia kell az internethez a Strapi Market el\xE9r\xE9s\xE9hez.","admin.pages.MarketPlacePage.plugins":"B\u0151v\xEDtm\xE9nyek","admin.pages.MarketPlacePage.plugin.copy":"Telep\xEDt\xE9si parancs m\xE1sol\xE1sa","admin.pages.MarketPlacePage.plugin.copy.success":"A telep\xEDt\xE9si parancs k\xE9szen \xE1ll a termin\xE1lba val\xF3 bem\xE1sol\xE1sra","admin.pages.MarketPlacePage.plugin.info":"Tov\xE1bbi inform\xE1ci\xF3k","admin.pages.MarketPlacePage.plugin.info.label":"{pluginName} b\u0151v\xEDtm\xE9ny tov\xE1bbi inform\xE1ci\xF3i","admin.pages.MarketPlacePage.plugin.info.text":"Tov\xE1bbi inform\xE1ci\xF3k","admin.pages.MarketPlacePage.plugin.installed":"Telep\xEDtve","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"K\xE9sz\xEDtette: Strapi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"B\u0151v\xEDtm\xE9ny hiteles\xEDtve a Strapi \xE1ltal","admin.pages.MarketPlacePage.plugin.version":'Friss\xEDtsd a Strapi verzi\xF3d: "{strapiAppVersion}" erre: "{versionRange}"',"admin.pages.MarketPlacePage.plugin.version.null":'Nem siker\xFClt ellen\u0151rizni a kompatibilit\xE1st a Strapi verzi\xF3ddal: "{strapiAppVersion}"',"admin.pages.MarketPlacePage.plugin.githubStars":"Ezt a plugint {starsCount} csillagra jel\xF6lt\xE9k a GitHub-on","admin.pages.MarketPlacePage.plugin.downloads":"Ezt a plugint hetente {downloadsCount} alkalommal t\xF6ltik le","admin.pages.MarketPlacePage.providers":"Szolg\xE1ltat\xF3k","admin.pages.MarketPlacePage.provider.githubStars":"Ezt a szolg\xE1ltat\xF3t {starsCount} csillagra jel\xF6lt\xE9k a GitHub-on","admin.pages.MarketPlacePage.provider.downloads":"Ezt a szolg\xE1ltat\xF3t hetente {downloadsCount} alkalommal t\xF6ltik le","admin.pages.MarketPlacePage.search.clear":"Keres\xE9s t\xF6rl\xE9se","admin.pages.MarketPlacePage.search.empty":'Nincs tal\xE1lat erre: "{target}"',"admin.pages.MarketPlacePage.search.placeholder":"Keres\xE9s","admin.pages.MarketPlacePage.submit.plugin.link":"Plugin k\xFCld\xE9se","admin.pages.MarketPlacePage.submit.provider.link":"Provider bek\xFCld\xE9se","admin.pages.MarketPlacePage.subtitle":"Hozzon ki t\xF6bbet a Strapi-b\xF3l","admin.pages.MarketPlacePage.tab-group.label":"Strapi pluginek \xE9s szolg\xE1ltat\xF3k","admin.pages.MarketPlacePage.missingPlugin.title":"Hi\xE1nyzik egy plugin?","admin.pages.MarketPlacePage.missingPlugin.description":"Mondd el, milyen pluginra van sz\xFCks\xE9ged, \xE9s t\xE1j\xE9koztatjuk a k\xF6z\xF6ss\xE9gi plugin fejleszt\u0151inket, hogy esetleg \xF6tletet mer\xEDthessenek bel\u0151le!","admin.pages.MarketPlacePage.sort.alphabetical":"Bet\u0171rendes rendez\xE9s","admin.pages.MarketPlacePage.sort.newest":"Leg\xFAjabb","admin.pages.MarketPlacePage.sort.alphabetical.selected":"Rendez\xE9s bet\u0171rend szerint","admin.pages.MarketPlacePage.sort.newest.selected":"Rendez\xE9s leg\xFAjabbak szerint","admin.pages.MarketPlacePage.sort.githubStars":"GitHub csillagok sz\xE1ma","admin.pages.MarketPlacePage.sort.githubStars.selected":"Rendez\xE9s GitHub csillagok szerint","admin.pages.MarketPlacePage.sort.npmDownloads":"Let\xF6lt\xE9sek sz\xE1ma","admin.pages.MarketPlacePage.sort.npmDownloads.selected":"Rendez\xE9s npm let\xF6lt\xE9sek szerint","admin.pages.MarketPlacePage.filters.collections":"Gy\u0171jtem\xE9nyek","admin.pages.MarketPlacePage.filters.collectionsSelected":"{count, plural, =0 {Nincsenek gy\u0171jtem\xE9nyek} one {# gy\u0171jtem\xE9ny} other {# gy\u0171jtem\xE9nyek}} kiv\xE1lasztva","admin.pages.MarketPlacePage.filters.categories":"Kateg\xF3ri\xE1k","admin.pages.MarketPlacePage.filters.categoriesSelected":"{count, plural, =0 {Nincsenek kateg\xF3ri\xE1k} one {# kateg\xF3ria} other {# kateg\xF3ri\xE1k}} kiv\xE1lasztva",anErrorOccurred:c,"app.component.CopyToClipboard.label":"M\xE1sol\xE1s a v\xE1g\xF3lapra","app.component.search.label":"{target} keres\xE9se","app.component.table.duplicate":"{target} duplik\xE1l\xE1sa","app.component.table.edit":"{target} szerkeszt\xE9se","app.component.table.select.one-entry":"{target} kiv\xE1laszt\xE1sa","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Olvassa el a legfrissebb h\xEDreket a Strapir\xF3l \xE9s az \xF6kosziszt\xE9m\xE1r\xF3l.","app.components.BlockLink.code":"K\xF3d p\xE9ld\xE1k","app.components.BlockLink.code.content":"Tanuljon a k\xF6z\xF6ss\xE9g \xE1ltal fejlesztett val\xF3s projektek seg\xEDts\xE9g\xE9vel.","app.components.BlockLink.documentation.content":"Fedezze fel az alapvet\u0151 fogalmakat, \xFAtmutat\xF3kat \xE9s utas\xEDt\xE1sokat.","app.components.BlockLink.tutorial":"Oktat\xF3anyagok","app.components.BlockLink.tutorial.content":"K\xF6vesse az utas\xEDt\xE1sokat a Strapi haszn\xE1lat\xE1hoz \xE9s testreszab\xE1s\xE1hoz.","app.components.Button.cancel":"M\xE9gsem","app.components.Button.confirm":"Meger\u0151s\xEDt\xE9s","app.components.Button.reset":"Vissza\xE1ll\xEDt\xE1s","app.components.ComingSoonPage.comingSoon":"Hamarosan","app.components.ConfirmDialog.title":"Meger\u0151s\xEDt\xE9s","app.components.DownloadInfo.download":"Let\xF6lt\xE9s folyamatban...","app.components.DownloadInfo.text":"Ez eltarthat egy percig. K\xF6sz\xF6nj\xFCk a t\xFCrelm\xE9t.","app.components.EmptyAttributes.title":"M\xE9g nincsenek mez\u0151k","app.components.EmptyStateLayout.content-document":"Nem tal\xE1lhat\xF3 tartalom","app.components.EmptyStateLayout.content-permissions":"Nincs megfelel\u0151 jogosults\xE1ga a tartalomhozhoz","app.components.GuidedTour.CM.create.content":"<p>Hozz l\xE9tre \xE9s kezelj minden tartalmat itt a Tartalomkezel\u0151ben.</p><p>P\xE9ld\xE1ul: A Blog weboldal p\xE9ld\xE1j\xE1t folytatva, \xEDrhatsz egy Cikket, mentheted \xE9s publik\xE1lhatod \xFAgy, ahogy szeretn\xE9d.</p><p>\u{1F4A1} Gyors tipp - Ne felejtsd el publik\xE1lni a l\xE9trehozott tartalmat.</p>","app.components.GuidedTour.CM.create.title":"\u26A1\uFE0F Tartalom l\xE9trehoz\xE1sa","app.components.GuidedTour.CM.success.content":"<p>Szuper, m\xE9g egy l\xE9p\xE9s van h\xE1tra!</p><b>\u{1F680} L\xE1sd a tartalmat m\u0171k\xF6d\xE9s k\xF6zben</b>","app.components.GuidedTour.CM.success.cta.title":"API tesztel\xE9se","app.components.GuidedTour.CM.success.title":"2. l\xE9p\xE9s: K\xE9sz \u2705","app.components.GuidedTour.CTB.create.content":"<p>A Gy\u0171jtem\xE9ny t\xEDpusok seg\xEDts\xE9g\xE9vel t\xF6bb bejegyz\xE9st tudsz kezelni, m\xEDg az Egy t\xEDpusok a csak egy bejegyz\xE9s kezel\xE9s\xE9re alkalmasak.</p> <p>P\xE9ld\xE1ul: Egy Blog weboldaln\xE1l a Cikkek lennek egy Gy\u0171jtem\xE9ny t\xEDpus, m\xEDg a Honlap lenne egy Egy t\xEDpus.</p>","app.components.GuidedTour.CTB.create.cta.title":"Hozz l\xE9tre egy Gy\u0171jtem\xE9ny t\xEDpust","app.components.GuidedTour.CTB.create.title":"\u{1F9E0} Hozz l\xE9tre els\u0151 Gy\u0171jtem\xE9ny t\xEDpust","app.components.GuidedTour.CTB.success.content":"<p>J\xF3l haladsz!</p><b>\u26A1\uFE0F Mit szeretn\xE9l megosztani a vil\xE1ggal?</b>","app.components.GuidedTour.CTB.success.title":"1. l\xE9p\xE9s: K\xE9sz \u2705","app.components.GuidedTour.apiTokens.create.content":"<p>Hozz l\xE9tre itt egy hiteles\xEDt\xE9si token-t, \xE9s t\xF6ltsd le az \xE1ltalad l\xE9trehozott tartalmat.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"API Token gener\xE1l\xE1sa","app.components.GuidedTour.apiTokens.create.title":"\u{1F680} L\xE1sd a tartalmat m\u0171k\xF6d\xE9s k\xF6zben","app.components.GuidedTour.apiTokens.success.content":"<p>L\xE1sd a tartalmat m\u0171k\xF6d\xE9s k\xF6zben az HTTP k\xE9r\xE9ssel:</p><ul><li><p>Erre a URL-re: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Ezzel a fejl\xE9ccel: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Tov\xE1bbi lehet\u0151s\xE9gek a tartalommal val\xF3 interakci\xF3hoz, l\xE1sd a <documentationLink>dokument\xE1ci\xF3t</documentationLink>.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Menj vissza a f\u0151oldalra","app.components.GuidedTour.apiTokens.success.title":"3. l\xE9p\xE9s: befejezve \u2705","app.components.GuidedTour.create-content":"Tartalom l\xE9trehoz\xE1sa","app.components.GuidedTour.home.CM.title":"\u26A1\uFE0F Mire szeretn\xE9d megosztani a vil\xE1ggal?","app.components.GuidedTour.home.CTB.cta.title":"Menj a Content type Builder-be","app.components.GuidedTour.home.CTB.title":"\u{1F9E0} \xC9p\xEDtsd fel a tartalom strukt\xFAr\xE1j\xE1t","app.components.GuidedTour.home.apiTokens.cta.title":"API tesztel\xE9se","app.components.GuidedTour.skip":"A t\xFAra \xE1tugr\xE1sa","app.components.GuidedTour.title":"3 l\xE9p\xE9s a kezd\xE9shez","app.components.HomePage.button.blog":"B\u0151vebben a blogon","app.components.HomePage.community":"Csatlakozz a k\xF6z\xF6ss\xE9ghez","app.components.HomePage.community.content":"Besz\xE9lgessen a csapattagokkal, a k\xF6zrem\u0171k\xF6d\u0151kkel \xE9s a fejleszt\u0151kkel k\xFCl\xF6nb\xF6z\u0151 csatorn\xE1kon.","app.components.HomePage.create":"Hozza l\xE9tre az els\u0151 tartalomt\xEDpust","app.components.HomePage.roadmap":"Tekintse meg terveinket","app.components.HomePage.welcome":"\xDCdv\xF6z\xF6lj\xFCk a fed\xE9lzeten \u{1F44B}","app.components.HomePage.welcome.again":"\xDCdv\xF6z\xF6lj\xFCk \u{1F44B}","app.components.HomePage.welcomeBlock.content":"Gratul\xE1lunk! Els\u0151 rendszergazdak\xE9nt jelentkezett be. A Strapi \xE1ltal ny\xFAjtott funkci\xF3k felfedez\xE9s\xE9hez javasoljuk, hogy hozza l\xE9tre els\u0151 tartalomt\xEDpus\xE1t!","app.components.HomePage.welcomeBlock.content.again":"Rem\xE9lj\xFCk, hogy j\xF3l halad a projektje! Olvassa el a Strapi legfrissebb h\xEDreit. Visszajelz\xE9sei alapj\xE1n mindent megtesz\xFCnk, hogy jav\xEDtsuk a term\xE9ket.","app.components.HomePage.welcomeBlock.content.issues":"probl\xE9m\xE1k.","app.components.HomePage.welcomeBlock.content.raise":" vagy \xEDrjon ","app.components.ImgPreview.hint":"H\xFAzza a f\xE1jlt erre a ter\xFCletre, vagy {browse} a felt\xF6ltend\u0151 f\xE1jl\xE9rt","app.components.ImgPreview.hint.browse":"tall\xF3z\xE1s","app.components.InputFile.newFile":"\xDAj f\xE1jl hozz\xE1ad\xE1sa","app.components.InputFileDetails.open":"Megnyit\xE1s \xFAj lapon","app.components.InputFileDetails.originalName":"Eredeti n\xE9v:","app.components.InputFileDetails.remove":"F\xE1jl elt\xE1vol\xEDt\xE1sa","app.components.InputFileDetails.size":"M\xE9ret:","app.components.InstallPluginPage.Download.description":"A b\u0151v\xEDtm\xE9ny let\xF6lt\xE9se \xE9s telep\xEDt\xE9se eltarthat n\xE9h\xE1ny m\xE1sodpercig.","app.components.InstallPluginPage.Download.title":"Let\xF6lt\xE9s...","app.components.InstallPluginPage.description":"B\u0151v\xEDtse alkalmaz\xE1s\xE1t er\u0151fesz\xEDt\xE9s n\xE9lk\xFCl.","app.components.LeftMenu.collapse":"A navig\xE1ci\xF3s s\xE1v \xF6sszecsuk\xE1sa","app.components.LeftMenu.expand":"A navig\xE1ci\xF3s s\xE1v kinyit\xE1sa","app.components.LeftMenu.general":"\xC1ltal\xE1nos","app.components.LeftMenu.logout":"Kijelentkez\xE9s","app.components.LeftMenu.logo.alt":"Alkalmaz\xE1s log\xF3","app.components.LeftMenu.plugins":"B\u0151v\xEDtm\xE9nyek","app.components.LeftMenu.navbrand.title":"Strapi M\u0171szerfal","app.components.LeftMenu.navbrand.workplace":"Munkater\xFClet","app.components.LeftMenuFooter.help":"Seg\xEDts\xE9g","app.components.LeftMenuFooter.poweredBy":"Powered by ","app.components.LeftMenuLinkContainer.collectionTypes":"Gy\u0171jtem\xE9ny t\xEDpusai","app.components.LeftMenuLinkContainer.configuration":"Be\xE1ll\xEDt\xE1sok","app.components.LeftMenuLinkContainer.general":"\xC1ltal\xE1nos","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Nincs b\u0151v\xEDtm\xE9ny telep\xEDtve","app.components.LeftMenuLinkContainer.plugins":"B\u0151v\xEDtm\xE9nyek","app.components.LeftMenuLinkContainer.singleTypes":"Egyed\xFCl\xE1ll\xF3 t\xEDpusok","app.components.ListPluginsPage.deletePlugin.description":"A b\u0151v\xEDtm\xE9ny elt\xE1vol\xEDt\xE1sa eltarthat n\xE9h\xE1ny m\xE1sodpercig.","app.components.ListPluginsPage.deletePlugin.title":"Elt\xE1vol\xEDt\xE1s","app.components.ListPluginsPage.description":"A telep\xEDtett b\u0151v\xEDtm\xE9nyek list\xE1ja.","app.components.ListPluginsPage.helmet.title":"A b\u0151v\xEDtm\xE9nyek list\xE1ja","app.components.Logout.logout":"Kijelentkez\xE9s","app.components.Logout.profile":"Profil","app.components.MarketplaceBanner":"Fedezze fel a k\xF6z\xF6ss\xE9g \xE1ltal \xE9p\xEDtett modulokat, \xE9s m\xE9g sok m\xE1s fantasztikus dolgot, amik seg\xEDtenek a projekt elind\xEDt\xE1s\xE1ban.","app.components.MarketplaceBanner.image.alt":"a strapi rocket logo","app.components.MarketplaceBanner.link":"N\xE9zze meg most","app.components.NotFoundPage.back":"Vissza a kezd\u0151oldalra","app.components.NotFoundPage.description":"Nem tal\xE1lhat\xF3","app.components.Official":"Hivatalos","app.components.Onboarding.help.button":"S\xFAg\xF3 gomb","app.components.Onboarding.label.completed":"% elk\xE9sz\xFClt","app.components.Onboarding.title":"Bemutat\xF3 vide\xF3k","app.components.PluginCard.Button.label.download":"Let\xF6lt\xE9s","app.components.PluginCard.Button.label.install":"M\xE1r telep\xEDtve van","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"Az automatikus \xFAjrat\xF6lt\xE9s funkci\xF3t enged\xE9lyezni kell. K\xE9rj\xFCk, ind\xEDtsa el az alkalmaz\xE1st ezzel a paranccsal: `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Meg\xE9rtettem!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Biztons\xE1gi okokb\xF3l egy plugin csak fejleszt\u0151i k\xF6rnyezetben t\xF6lthet\u0151 le.","app.components.PluginCard.PopUpWarning.install.impossible.title":"A let\xF6lt\xE9s nem lehets\xE9ges","app.components.PluginCard.compatible":"Kompatibilis az alkalmaz\xE1soddal","app.components.PluginCard.compatibleCommunity":"Kompatibilis a k\xF6z\xF6ss\xE9ggel","app.components.PluginCard.more-details":"Tov\xE1bbi r\xE9szletek","app.components.ToggleCheckbox.off-label":"Kikapcsol","app.components.ToggleCheckbox.on-label":"Bekapcsol","app.components.Users.MagicLink.connect":"M\xE1solja ki \xE9s ossza meg ezt a linket, hogy hozz\xE1f\xE9r\xE9st biztos\xEDtson ehhez a felhaszn\xE1l\xF3hoz","app.components.Users.MagicLink.connect.sso":"K\xFCldje el ezt a linket a felhaszn\xE1l\xF3nak. Az els\u0151 bejelentkez\xE9s t\xF6rt\xE9nhet SSO szolg\xE1ltat\xF3n kereszt\xFCl","app.components.Users.ModalCreateBody.block-title.details":"Felhaszn\xE1l\xF3i adatok","app.components.Users.ModalCreateBody.block-title.roles":"A felhaszn\xE1l\xF3 szerepk\xF6rei","app.components.Users.ModalCreateBody.block-title.roles.description":"Egy felhaszn\xE1l\xF3nak lehet egy, illetve t\xF6bb szerepk\xF6re is","app.components.Users.SortPicker.button-label":"Rendez\xE9s","app.components.Users.SortPicker.sortby.email_asc":"Email (A - Z)","app.components.Users.SortPicker.sortby.email_desc":"Email (Z - A)","app.components.Users.SortPicker.sortby.firstname_asc":"Keresztn\xE9v (A - Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Keresztn\xE9v (Z - A)","app.components.Users.SortPicker.sortby.lastname_asc":"Vezet\xE9kn\xE9v (A - Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Vezet\xE9kn\xE9v (Z - A)","app.components.Users.SortPicker.sortby.username_asc":"Felhaszn\xE1l\xF3n\xE9v (A - Z)","app.components.Users.SortPicker.sortby.username_desc":"Felhaszn\xE1l\xF3n\xE9v (Z - A)","app.components.listPlugins.button":"\xDAj b\u0151v\xEDtm\xE9ny hozz\xE1ad\xE1sa","app.components.listPlugins.title.none":"Nincs telep\xEDtve b\u0151v\xEDtm\xE9ny","app.components.listPluginsPage.deletePlugin.error":"Hiba t\xF6rt\xE9nt a b\u0151v\xEDtm\xE9ny elt\xE1vol\xEDt\xE1sa k\xF6zben","app.containers.App.notification.error.init":"Hiba t\xF6rt\xE9nt az API k\xE9r\xE9se k\xF6zben","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Ha nem kapja meg ezt a linket, forduljon az adminisztr\xE1torhoz.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"Eltarthat n\xE9h\xE1ny percig, am\xEDg megkapja a jelsz\xF3-helyre\xE1ll\xEDt\xE1si linket.","app.containers.AuthPage.ForgotPasswordSuccess.title":"Email elk\xFCdlve","app.containers.Users.EditPage.form.active.label":"Akt\xEDv","app.containers.Users.EditPage.header.label":"{name} m\xF3dos\xEDt\xE1sa","app.containers.Users.EditPage.header.label-loading":"Felhaszn\xE1l\xF3 szerkeszt\xE9se","app.containers.Users.EditPage.roles-bloc-title":"A hozz\xE1rendelt szerepk\xF6r\xF6k","app.containers.Users.ModalForm.footer.button-success":"Felhaszn\xE1l\xF3 megh\xEDv\xE1sa","app.links.configure-view":"A n\xE9zet testreszab\xE1sa","app.page.not.found":"Hopp\xE1! \xDAgy t\u0171nik, nem tal\xE1ljuk a keresett oldalt...","app.static.links.cheatsheet":"Puska","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Sz\u0171r\u0151 hozz\xE1ad\xE1sa","app.utils.close-label":"Bez\xE1r\xE1s","app.utils.defaultMessage":" ","app.utils.duplicate":"Duplik\xE1l\xE1s","app.utils.edit":"Szerkeszt\xE9s","app.utils.errors.file-too-big.message":"A f\xE1jl m\xE9rete t\xFAl nagy","app.utils.filter-value":"Sz\u0171r\xE9si \xE9rt\xE9k","app.utils.filters":"Sz\u0171r\u0151k","app.utils.notify.data-loaded":"A {target} bet\xF6lt\u0151d\xF6tt","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"K\xF6zz\xE9tesz","app.utils.select-all":"Minden kiv\xE1laszt\xE1sa","app.utils.select-field":"Mez\u0151 kiv\xE1laszt\xE1sa","app.utils.select-filter":"Sz\u0171r\u0151 kiv\xE1laszt\xE1sa","app.utils.unpublish":"K\xF6zz\xE9t\xE9tel visszavon\xE1sa",clearLabel:g,"coming.soon":"Ez a tartalom jelenleg fejleszt\xE9s alatt \xE1ll, \xE9s n\xE9h\xE1ny h\xE9ten bel\xFCl \xFAjra el\xE9rhet\u0151 lesz!","component.Input.error.validation.integer":"Az \xE9rt\xE9knek eg\xE9sz sz\xE1mnak kell lennie","components.AutoReloadBlocker.description":"Futtassa a Strapit a k\xF6vetkez\u0151 parancsok egyik\xE9vel:","components.AutoReloadBlocker.header":"Ehhez a b\u0151v\xEDtm\xE9nyhez t\xF6ltse be \xFAjra a funkci\xF3t.","components.ErrorBoundary.title":"Valami elromlott...","components.FilterOptions.FILTER_TYPES.$contains":"tartalmazza","components.FilterOptions.FILTER_TYPES.$containsi":"tartalmazza (nem nagybet\u0171 \xE9rz\xE9keny)","components.FilterOptions.FILTER_TYPES.$endsWith":"erre v\xE9gz\u0151dik","components.FilterOptions.FILTER_TYPES.$endsWithi":"erre v\xE9gz\u0151dik (nem nagybet\u0171 \xE9rz\xE9keny)","components.FilterOptions.FILTER_TYPES.$eq":"egyenl\u0151","components.FilterOptions.FILTER_TYPES.$eqi":"egyenl\u0151 (nem nagybet\u0171 \xE9rz\xE9keny)","components.FilterOptions.FILTER_TYPES.$gt":"nagyobb, mint","components.FilterOptions.FILTER_TYPES.$gte":"nagyobb, vagy egyenl\u0151, mint","components.FilterOptions.FILTER_TYPES.$lt":"kisebb, mint","components.FilterOptions.FILTER_TYPES.$lte":"kisebb, vagy egyenl\u0151, mint","components.FilterOptions.FILTER_TYPES.$ne":"nem egyenl\u0151","components.FilterOptions.FILTER_TYPES.$nei":"nem egyenl\u0151 (nem nagybet\u0171 \xE9rz\xE9keny)","components.FilterOptions.FILTER_TYPES.$notContains":"nem tartalmazza","components.FilterOptions.FILTER_TYPES.$notContainsi":"nem tartalmazza (nem nagybet\u0171 \xE9rz\xE9keny)","components.FilterOptions.FILTER_TYPES.$notNull":"nem null","components.FilterOptions.FILTER_TYPES.$null":"null","components.FilterOptions.FILTER_TYPES.$startsWith":"ezzel kezd\u0151dik","components.FilterOptions.FILTER_TYPES.$startsWithi":"ezzel kezd\u0151dik (nem nagybet\u0171 \xE9rz\xE9keny)","components.Input.error.attribute.key.taken":"Ez az \xE9rt\xE9k m\xE1r l\xE9tezik","components.Input.error.attribute.sameKeyAndName":"Nem lehet egyenl\u0151","components.Input.error.attribute.taken":"Ez a mez\u0151n\xE9v m\xE1r l\xE9tezik","components.Input.error.contain.lowercase":"A jelsz\xF3nak tartalmaznia kell legal\xE1bb egy kisbet\u0171t","components.Input.error.contain.number":"A jelsz\xF3nak tartalmaznia kell legal\xE1bb egy sz\xE1mot","components.Input.error.contain.uppercase":"A jelsz\xF3nak tartalmaznia kell legal\xE1bb egy nagybet\u0171t","components.Input.error.contentTypeName.taken":"Ez a n\xE9v m\xE1r l\xE9tezik","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"A jelszavak nem egyeznek","components.Input.error.validation.email":"\xC9rv\xE9nytelen e-mail","components.Input.error.validation.json":"Hib\xE1s JSON form\xE1tum","components.Input.error.validation.lowercase":"Az \xE9rt\xE9knek kisbet\u0171s karakterl\xE1ncnak kell lennie","components.Input.error.validation.max":"A megadott \xE9rt\xE9k t\xFAl nagy {max}.","components.Input.error.validation.maxLength":"A megadott \xE9rt\xE9k t\xFAl hossz\xFA {max}.","components.Input.error.validation.min":"A megadott \xE9rt\xE9k t\xFAl alacsony {min}.","components.Input.error.validation.minLength":"A megadott \xE9rt\xE9k t\xFAl r\xF6vid {min}.","components.Input.error.validation.minSupMax":"Nem lehet fels\u0151bbrend\u0171","components.Input.error.validation.regex":"A megadott \xE9rt\xE9k form\xE1tuma nem megfelel\u0151.","components.Input.error.validation.required":"Ez az \xE9rt\xE9k k\xF6telez\u0151.","components.Input.error.validation.unique":"Ez az \xE9rt\xE9k m\xE1r haszn\xE1latban van.","components.InputSelect.option.placeholder":"V\xE1lasszon itt","components.ListRow.empty":"Nincsenek megjelen\xEDtend\u0151 adatok.","components.NotAllowedInput.text":"Nincs jogosults\xE1ga a mez\u0151 megtekint\xE9s\xE9hez","components.OverlayBlocker.description":"Olyan funkci\xF3t haszn\xE1l, amelynek \xFAjra kell ind\xEDtania a szervert. K\xE9rj\xFCk, v\xE1rja meg, am\xEDg a szerver fel\xE1ll.","components.OverlayBlocker.description.serverError":"A szervernek \xFAjra kellett volna indulnia, k\xE9rj\xFCk, ellen\u0151rizze a logokat a termin\xE1lban.","components.OverlayBlocker.title":"\xDAjraind\xEDt\xE1sra v\xE1r...","components.OverlayBlocker.title.serverError":"Az \xFAjraind\xEDt\xE1s a v\xE1rtn\xE1l tov\xE1bb tart","components.PageFooter.select":"Bejegyz\xE9sek oldalank\xE9nt","components.ProductionBlocker.description":"Biztons\xE1gi okokb\xF3l le kell tiltanunk ezt a b\u0151v\xEDtm\xE9nyt m\xE1s k\xF6rnyezetekben.","components.ProductionBlocker.header":"Ez a b\u0151v\xEDtm\xE9ny csak fejleszt\u0151i k\xF6rnyezetben \xE9rhet\u0151 el.","components.Search.placeholder":"Keres\xE9s...","components.TableHeader.sort":"Rendez\xE9s {label} szerint","components.Wysiwyg.ToggleMode.markdown-mode":"Markdown m\xF3d","components.Wysiwyg.ToggleMode.preview-mode":"El\u0151n\xE9zet m\xF3d","components.Wysiwyg.collapse":"\xD6sszecsuk","components.Wysiwyg.selectOptions.H1":"C\xEDm H1","components.Wysiwyg.selectOptions.H2":"C\xEDm H2","components.Wysiwyg.selectOptions.H3":"C\xEDm H3","components.Wysiwyg.selectOptions.H4":"C\xEDm H4","components.Wysiwyg.selectOptions.H5":"C\xEDm H5","components.Wysiwyg.selectOptions.H6":"C\xEDm H6","components.Wysiwyg.selectOptions.title":"C\xEDm hozz\xE1d\xE1sa","components.WysiwygBottomControls.charactersIndicators":"karakterek","components.WysiwygBottomControls.fullscreen":"Kinyit","components.WysiwygBottomControls.uploadFiles":"F\xE1jlok beh\xFAz\xE1sa, beilleszt\xE9se a v\xE1g\xF3lapr\xF3l vagy {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"V\xE1lassza ki \u0151ket","components.pagination.go-to":"Ugr\xE1s a(z) {page} oldalra","components.pagination.go-to-next":"Ugr\xE1s a k\xF6vetkez\u0151 oldalra","components.pagination.go-to-previous":"Ugr\xE1s az el\u0151z\u0151 oldalra","components.pagination.remaining-links":"\xC9s {number} tov\xE1bbi link","components.popUpWarning.button.cancel":"M\xE9gsem","components.popUpWarning.button.confirm":"Meger\u0151s\xEDt\xE9s","components.popUpWarning.message":"Biztosan t\xF6r\xF6lni szeretn\xE9?","components.popUpWarning.title":"Er\u0151s\xEDtse meg","content-manager.App.schemas.data-loaded":"A s\xE9m\xE1k sikeresen bet\xF6ltve","content-manager.ListViewTable.relation-loaded":"A kapcsolatok bet\xF6lt\u0151dtek","content-manager.ListViewTable.relation-loading":"Relations are loading","content-manager.ListViewTable.relation-more":"This relation contains more entities than displayed","content-manager.EditRelations.title":"Rel\xE1ci\xF3s adatok","content-manager.HeaderLayout.button.label-add-entry":"\xDAj bejegyz\xE9s l\xE9trehoz\xE1sa","content-manager.api.id":"API ID","content-manager.components.AddFilterCTA.add":"Sz\u0171r\u0151k","content-manager.components.AddFilterCTA.hide":"Sz\u0171r\u0151k","content-manager.components.DragHandle-label":"H\xFAz","content-manager.components.DraggableAttr.edit":"Kattintson a szerkeszt\xE9shez","content-manager.components.DraggableCard.delete.field":"{item} t\xF6rl\xE9se","content-manager.components.DraggableCard.edit.field":"{item} szerkeszt\xE9se","content-manager.components.DraggableCard.move.field":"{item} mozgat\xE1sa","content-manager.components.ListViewTable.row-line":"{number}. sor","content-manager.components.DynamicZone.ComponentPicker-label":"V\xE1lasszon egy komponenst","content-manager.components.DynamicZone.add-component":"Komponens hozz\xE1ad\xE1sa a {componentName}-hoz","content-manager.components.DynamicZone.delete-label":"{name} t\xF6rl\xE9se","content-manager.components.DynamicZone.error-message":"Az \xF6sszetev\u0151 hib\xE1t (hib\xE1kat) tartalmaz","content-manager.components.DynamicZone.missing-components":"Hi\xE1nyzik {number} komponens","content-manager.components.DynamicZone.move-down-label":"Mozgassa a komponenst lefel\xE9","content-manager.components.DynamicZone.move-up-label":"Mozgassa a komponenst felfel\xE9","content-manager.components.DynamicZone.pick-compo":"V\xE1lasszon egy komponenst","content-manager.components.DynamicZone.required":"A komponens k\xF6telez\u0151","content-manager.components.EmptyAttributesBlock.button":"Ugr\xE1s a be\xE1ll\xEDt\xE1sok oldalra","content-manager.components.EmptyAttributesBlock.description":"M\xF3dos\xEDthatja a be\xE1ll\xEDt\xE1sait","content-manager.components.FieldItem.linkToComponentLayout":"\xC1ll\xEDtsa be az \xF6sszetev\u0151 elrendez\xE9s\xE9t","content-manager.components.FieldSelect.label":"Mez\u0151 hozz\xE1ad\xE1sa","content-manager.components.FilterOptions.button.apply":"Alkalmaz","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Alkalmaz","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Mindent kit\xF6r\xF6l","content-manager.components.FiltersPickWrapper.PluginHeader.description":"\xC1ll\xEDtsa be a bejegyz\xE9sek sz\u0171r\xE9s\xE9hez alkalmazand\xF3 felt\xE9teleket","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Sz\u0171r\u0151k","content-manager.components.FiltersPickWrapper.hide":"Elrejt","content-manager.components.LeftMenu.Search.label":"Tartalomt\xEDpus keres\xE9se","content-manager.components.LeftMenu.collection-types":"Gy\u0171jtem\xE9nyt\xEDpusok","content-manager.components.LeftMenu.single-types":"Egyed\xFCl\xE1ll\xF3 t\xEDpusok","content-manager.components.LimitSelect.itemsPerPage":"Oldalank\xE9nti elemek sz\xE1ma","content-manager.components.NotAllowedInput.text":"Nincs jogosults\xE1g a mez\u0151 megtekint\xE9s\xE9hez","content-manager.components.RepeatableComponent.error-message":"Az \xF6sszetev\u0151(k) hib\xE1t tartalmaz(nak)","content-manager.components.Search.placeholder":"Bejegyz\xE9s keres\xE9se...","content-manager.components.Select.draft-info-title":"\xC1llapot: Piszkozat","content-manager.components.Select.publish-info-title":"\xC1llapot: K\xF6zz\xE9tett","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"A szerkeszt\xE9si n\xE9zet megjelen\xE9s\xE9nek testreszab\xE1sa.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"A listan\xE9zet be\xE1ll\xEDt\xE1sainak megad\xE1sa.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"A n\xE9zet testreszab\xE1sa - {name}","content-manager.components.TableDelete.delete":"Mindegyik t\xF6rl\xE9se","content-manager.components.TableDelete.deleteSelected":"A kiv\xE1lasztott elem t\xF6rl\xE9se","content-manager.components.TableDelete.label":"{number} elem kiv\xE1lasztva","content-manager.components.TableEmpty.withFilters":"Nincsenek {contentType} az alkalmazott sz\u0171r\u0151kkel...","content-manager.components.TableEmpty.withSearch":"A megadott keres\xE9ssel ({search}) nincs {contentType}...","content-manager.components.TableEmpty.withoutFilter":"Nincs {contentType}...","content-manager.components.empty-repeatable":"M\xE9g nincs elem hozz\xE1adva. Kattintson az al\xE1bbi gombra a hozz\xE1ad\xE1s\xE1hoz.","content-manager.components.notification.info.maximum-requirement":"M\xE1r el\xE9rte a mez\u0151k maxim\xE1lis sz\xE1m\xE1t","content-manager.components.notification.info.minimum-requirement":"A minim\xE1lis k\xF6vetelm\xE9nynek megfelel\u0151 mez\u0151t hozz\xE1adtuk","content-manager.components.repeatable.reorder.error":"Hiba t\xF6rt\xE9nt az mez\u0151k \xE1trendez\xE9se k\xF6zben. K\xE9rj\xFCk, pr\xF3b\xE1lja \xFAjra","content-manager.components.reset-entry":"Bejegyz\xE9s vissza\xE1ll\xEDt\xE1sa","content-manager.components.uid.apply":"alkalmaz","content-manager.components.uid.available":"El\xE9rhet\u0151","content-manager.components.uid.regenerate":"Meg\xFAj\xEDt","content-manager.components.uid.suggested":"javasolt","content-manager.components.uid.unavailable":"Nem \xE9rhet\u0151 el","content-manager.containers.Edit.Link.Layout":"Az elrendez\xE9s testreszab\xE1sa","content-manager.containers.Edit.Link.Model":"A gy\u0171jtem\xE9nyt\xEDpus szerkeszt\xE9se","content-manager.containers.Edit.addAnItem":"Elem hozz\xE1ad\xE1sa...","content-manager.containers.Edit.clickToJump":"Kattintson a bejegyz\xE9sre ugr\xE1shoz","content-manager.containers.Edit.delete":"T\xF6rl\xE9s","content-manager.containers.Edit.delete-entry":"Bejegyz\xE9s t\xF6rl\xE9se","content-manager.containers.Edit.editing":"Szerkeszt\xE9s...","content-manager.containers.Edit.information":"Inform\xE1ci\xF3","content-manager.containers.Edit.information.by":"\xE1ltal","content-manager.containers.Edit.information.created":"L\xE9trehozva","content-manager.containers.Edit.information.draftVersion":"piszkozat","content-manager.containers.Edit.information.editing":"Szerkeszt\xE9s","content-manager.containers.Edit.information.lastUpdate":"Utols\xF3 friss\xEDt\xE9s","content-manager.containers.Edit.information.publishedVersion":"k\xF6zz\xE9tett v\xE1ltozat","content-manager.containers.Edit.pluginHeader.title.new":"Bejegyz\xE9s l\xE9trehoz\xE1sa","content-manager.containers.Edit.reset":"Vissza\xE1ll\xEDt\xE1s","content-manager.containers.Edit.returnList":"Vissza a list\xE1hoz","content-manager.containers.Edit.seeDetails":"R\xE9szletek","content-manager.containers.Edit.submit":"Ment\xE9s","content-manager.containers.EditSettingsView.modal-form.edit-field":"Mez\u0151 szerkeszt\xE9se","content-manager.containers.EditView.add.new-entry":"Bejegyz\xE9s hozz\xE1ad\xE1sa","content-manager.containers.EditView.notification.errors":"Az \u0171rlap hib\xE1s","content-manager.containers.Home.introduction":"A bejegyz\xE9sek szerkeszt\xE9s\xE9hez l\xE9pjen a bal oldali men\xFCben tal\xE1lhat\xF3 hivatkoz\xE1sra. Ennek a b\u0151v\xEDtm\xE9nynek nem m\xF3dos\xEDthat\xF3ak a be\xE1ll\xEDt\xE1sai, \xE9s m\xE9g mindig akt\xEDv fejleszt\xE9s alatt \xE1ll.","content-manager.containers.Home.pluginHeaderDescription":"Kezelje bejegyz\xE9seit egy sokoldal\xFA \xE9s sz\xE9p fel\xFCleten kereszt\xFCl.","content-manager.containers.Home.pluginHeaderTitle":"Tartalomkezel\u0151","content-manager.containers.List.draft":"Piszkozat","content-manager.containers.List.errorFetchRecords":"Hiba","content-manager.containers.List.published":"K\xF6zz\xE9tett","content-manager.containers.ListPage.displayedFields":"Megjelen\xEDtett mez\u0151k","content-manager.containers.ListPage.items":"{number} elem","content-manager.containers.ListPage.table-headers.publishedAt":"\xC1llapot","content-manager.containers.ListSettingsView.modal-form.edit-label":"{fieldName} szerkeszt\xE9se","content-manager.containers.SettingPage.add.field":"M\xE1sik mez\u0151 besz\xFAr\xE1sa","content-manager.containers.SettingPage.attributes":"Attrib\xFAtummez\u0151k","content-manager.containers.SettingPage.attributes.description":"Attrib\xFAtumok sorrendj\xE9nek meghat\xE1roz\xE1sa","content-manager.containers.SettingPage.editSettings.description":"H\xFAzza a megfelel\u0151 helyre a mez\u0151ket a v\xE9gleges megjelen\xEDt\xE9s kialak\xEDt\xE1s\xE1hoz","content-manager.containers.SettingPage.editSettings.entry.title":"Bejegyz\xE9s c\xEDme","content-manager.containers.SettingPage.editSettings.entry.title.description":"A bejegyz\xE9s megjelen\xEDtett mez\u0151j\xE9nek be\xE1ll\xEDt\xE1sa","content-manager.containers.SettingPage.editSettings.relation-field.description":"A megjelen\xEDtett mez\u0151 be\xE1ll\xEDt\xE1sa szerkeszt\xE9si \xE9s listan\xE9zetben","content-manager.containers.SettingPage.editSettings.title":"N\xE9zet szerkeszt\xE9se (be\xE1ll\xEDt\xE1sok)","content-manager.containers.SettingPage.layout":"Elrendez\xE9s","content-manager.containers.SettingPage.listSettings.description":"Konfigur\xE1lja a be\xE1ll\xEDt\xE1sokat ehhez a gy\u0171jtem\xE9nyt\xEDpushoz","content-manager.containers.SettingPage.listSettings.title":"Lista n\xE9zet (be\xE1ll\xEDt\xE1sok)","content-manager.containers.SettingPage.pluginHeaderDescription":"Gy\u0171jtem\xE9nyt\xEDpus speci\xE1lis be\xE1ll\xEDt\xE1sainak megad\xE1sa","content-manager.containers.SettingPage.settings":"Be\xE1ll\xEDt\xE1sok","content-manager.containers.SettingPage.view":"N\xE9zet","content-manager.containers.SettingViewModel.pluginHeader.title":"Tartalom kezel\u0151 - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"A be\xE1ll\xEDt\xE1sok testreszab\xE1sa","content-manager.containers.SettingsPage.Block.contentType.title":"Gy\u0171jtem\xE9nyt\xEDpusok","content-manager.containers.SettingsPage.Block.generalSettings.description":"A gy\u0171jtem\xE9nyt\xEDpusok alap\xE9rtelmezett be\xE1ll\xEDt\xE1sainak testreszab\xE1sa","content-manager.containers.SettingsPage.Block.generalSettings.title":"\xC1ltal\xE1nos","content-manager.containers.SettingsPage.pluginHeaderDescription":"Az \xF6sszes gy\u0171jtem\xE9nyt\xEDpus \xE9s csoport be\xE1ll\xEDt\xE1sainak testreszab\xE1sa","content-manager.containers.SettingsView.list.subtitle":"A gy\u0171jtem\xE9nyt\xEDpusok \xE9s csoportok elrendez\xE9s\xE9nek \xE9s megjelen\xEDt\xE9s\xE9nek testreszab\xE1sa","content-manager.containers.SettingsView.list.title":"Megjelen\xEDt\xE9s be\xE1ll\xEDt\xE1sai","content-manager.edit-settings-view.link-to-ctb.components":"Komponensek szerkeszt\xE9se","content-manager.edit-settings-view.link-to-ctb.content-types":"Tartalomt\xEDpusok szerkeszt\xE9se","content-manager.emptyAttributes.button":"Ugr\xE1s a gy\u0171jtem\xE9nyt\xEDpus-k\xE9sz\xEDt\u0151h\xF6z","content-manager.emptyAttributes.description":"Adja hozz\xE1 az els\u0151 mez\u0151t a gy\u0171jtem\xE9ny t\xEDpus\xE1hoz","content-manager.emptyAttributes.title":"M\xE9g nincsenek mez\u0151k","content-manager.error.attribute.key.taken":"Ez az \xE9rt\xE9k m\xE1r l\xE9tezik","content-manager.error.attribute.sameKeyAndName":"Nem lehetnek egyenl\u0151k","content-manager.error.attribute.taken":"Ez a mez\u0151n\xE9v m\xE1r l\xE9tezik","content-manager.error.contentTypeName.taken":"Ez a n\xE9v m\xE1r l\xE9tezik","content-manager.error.model.fetch":"Hiba t\xF6rt\xE9nt a modellek konfigur\xE1ci\xF3j\xE1nak lek\xE9r\xE9se sor\xE1n.","content-manager.error.record.create":"Hiba t\xF6rt\xE9nt a rekord l\xE9trehoz\xE1sa k\xF6zben.","content-manager.error.record.delete":"Hiba t\xF6rt\xE9nt a rekord t\xF6rl\xE9se k\xF6zben.","content-manager.error.record.fetch":"Hiba t\xF6rt\xE9nt a rekord lek\xE9r\xE9se sor\xE1n.","content-manager.error.record.update":"Hiba t\xF6rt\xE9nt a rekord friss\xEDt\xE9se k\xF6zben.","content-manager.error.records.count":"Hiba t\xF6rt\xE9nt a rekordok sz\xE1m\xE1nak lek\xE9r\xE9se k\xF6zben.","content-manager.error.records.fetch":"Hiba t\xF6rt\xE9nt a rekordok lek\xE9r\xE9se k\xF6zben.","content-manager.error.schema.generation":"Hiba t\xF6rt\xE9nt a s\xE9ma l\xE9trehoz\xE1sa sor\xE1n.","content-manager.error.validation.json":"Nem megfelel\u0151 JSON form\xE1tum","content-manager.error.validation.max":"A megadott \xE9rt\xE9k t\xFAl magas.","content-manager.error.validation.maxLength":"A megadott \xE9rt\xE9k t\xFAl hossz\xFA.","content-manager.error.validation.min":"A megadott \xE9rt\xE9k t\xFAl alacsony.","content-manager.error.validation.minLength":"A megadott \xE9rt\xE9k t\xFAl r\xF6vid.","content-manager.error.validation.minSupMax":"Nem lehet magasabb","content-manager.error.validation.regex":"A megadott \xE9rt\xE9k nem megfelel\u0151.","content-manager.error.validation.required":"Az \xE9rt\xE9k megad\xE1sa k\xF6telez\u0151.","content-manager.form.Input.bulkActions":"T\xF6meges m\u0171velet enged\xE9lyez\xE9se","content-manager.form.Input.defaultSort":"Alap\xE9rtelmezett rendez\xE9si attrib\xFAtum","content-manager.form.Input.description":"Le\xEDr\xE1s","content-manager.form.Input.description.placeholder":"Megjelen\xEDt\xE9si n\xE9v a profilban","content-manager.form.Input.editable":"Szerkeszthet\u0151 mez\u0151","content-manager.form.Input.filters":"Sz\u0171r\u0151k enged\xE9lyez\xE9se","content-manager.form.Input.label":"C\xEDmke","content-manager.form.Input.label.inputDescription":"Ez az \xE9rt\xE9k fel\xFCl\xEDrja a t\xE1bl\xE1zat fejl\xE9c\xE9ben megjelen\u0151 c\xEDmk\xE9t","content-manager.form.Input.pageEntries":"Bejegyz\xE9sek oldalank\xE9nt","content-manager.form.Input.pageEntries.inputDescription":"Megjegyz\xE9s: Ezt az \xE9rt\xE9ket fel\xFCl\xEDrhatja a Gy\u0171jtem\xE9nyt\xEDpus be\xE1ll\xEDt\xE1sainak oldal\xE1n.","content-manager.form.Input.placeholder":"Helykit\xF6lt\u0151","content-manager.form.Input.placeholder.placeholder":"Alap\xE9rt\xE9k","content-manager.form.Input.search":"Keres\xE9s enged\xE9lyez\xE9se","content-manager.form.Input.search.field":"Keres\xE9s enged\xE9lyez\xE9se ezen a mez\u0151n","content-manager.form.Input.sort.field":"Rendez\xE9s enged\xE9lyez\xE9se ezen a mez\u0151n","content-manager.form.Input.sort.order":"Alap\xE9rtelmezett rendez\xE9si sorrend","content-manager.form.Input.wysiwyg":"Megjelen\xEDt\xE9s, mint WYSIWYG","content-manager.global.displayedFields":"Megjelen\xEDtett mez\u0151k","content-manager.groups":"Csoportok","content-manager.groups.numbered":"Csoportok ({number})","content-manager.header.name":"Tartalom","content-manager.link-to-ctb":"Modell szerkeszt\xE9se","content-manager.models":"Gy\u0171jtem\xE9nyt\xEDpusok","content-manager.models.numbered":"Gy\u0171jtem\xE9nyt\xEDpusok ({number})","content-manager.notification.error.displayedFields":"Legal\xE1bb egy megjelen\xEDtett mez\u0151re sz\xFCks\xE9g van","content-manager.notification.error.relationship.fetch":"Hiba t\xF6rt\xE9nt a kapcsolat lek\xE9r\xE9se sor\xE1n.","content-manager.notification.info.SettingPage.disableSort":"Legal\xE1bb egy attrib\xFAtumnak rendezhet\u0151nek kell lennie","content-manager.notification.info.minimumFields":"Legal\xE1bb egy mez\u0151t meg kell jelen\xEDteni","content-manager.notification.upload.error":"Hiba t\xF6rt\xE9nt a f\xE1jlok felt\xF6lt\xE9se k\xF6zben","content-manager.pageNotFound":"Az oldal nem tal\xE1lhat\xF3","content-manager.pages.ListView.header-subtitle":"{number, plural, =0 {# bejegyz\xE9s} one {# bejegyz\xE9s} other {# bejegyz\xE9s}} tal\xE1lhat\xF3","content-manager.pages.NoContentType.button":"Tartalomt\xEDpus l\xE9trehoz\xE1sa","content-manager.pages.NoContentType.text":"M\xE9g nincs tartalom, javasoljuk, hogy hozza l\xE9tre az els\u0151 tartalomt\xEDpust.","content-manager.permissions.not-allowed.create":"Nem hozhat l\xE9tre dokumentumot","content-manager.permissions.not-allowed.update":"Ezt a dokumentumot nem tekintheti meg","content-manager.plugin.description.long":"Gyors m\xF3d az adatb\xE1zisban l\xE9v\u0151 adatok megtekint\xE9s\xE9hez, szerkeszt\xE9s\xE9hez \xE9s t\xF6rl\xE9s\xE9hez.","content-manager.plugin.description.short":"Gyors m\xF3d az adatb\xE1zisban l\xE9v\u0151 adatok megtekint\xE9s\xE9hez, szerkeszt\xE9s\xE9hez \xE9s t\xF6rl\xE9s\xE9hez.","content-manager.popover.display-relations.label":"Kapcsolatok megjelen\xEDt\xE9se","content-manager.select.currently.selected":"jelenleg {count} kiv\xE1lasztva","content-manager.success.record.delete":"T\xF6r\xF6lve","content-manager.success.record.publish":"K\xF6zz\xE9t\xE9ve","content-manager.success.record.save":"Mentett","content-manager.success.record.unpublish":"Nem k\xF6zz\xE9tett","content-manager.utils.data-loaded":"{number} elem sikeresen bet\xF6lt\u0151d\xF6tt","content-manager.apiError.This attribute must be unique":"{field} \xE9rt\xE9k\xE9nek egyedinek kell lennie","content-manager.popUpWarning.warning.has-draft-relations.title":"Meger\u0151s\xEDt\xE9s","content-manager.popUpWarning.warning.publish-question":"Biztosan k\xF6zz\xE9 akarja tenni?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Igen, k\xF6zz\xE9t\xE9tel","content-manager.popUpwarning.warning.has-draft-relations.message":"<b>{count, plural, one { rel\xE1ci\xF3 m\xE9g nincs } other { rel\xE1ci\xF3 m\xE9g nincs } }</b> publik\xE1lva, \xE9s nem v\xE1rt viselked\xE9st okozhat.","form.button.continue":"Folytat\xE1s","form.button.done":"K\xE9sz","global.search":"Keres\xE9s","global.actions":"M\u0171veletek","global.back":"Vissza","global.cancel":"M\xE9gsem","global.change-password":"Jelsz\xF3 megv\xE1ltoztat\xE1sa","global.content-manager":"Tartalomkezel\u0151","global.continue":"Folytat\xE1s","global.delete":"T\xF6rl\xE9s","global.delete-target":"{target} t\xF6rl\xE9se","global.description":"Le\xEDr\xE1s","global.details":"R\xE9szletek","global.disabled":"Letiltva","global.documentation":"Dokument\xE1ci\xF3","global.enabled":"Enged\xE9lyezve","global.finish":"Befejez\xE9s","global.marketplace":"Piact\xE9r","global.name":"N\xE9v","global.none":"Nincs","global.password":"Jelsz\xF3","global.plugins":"B\u0151v\xEDtm\xE9nyek","global.plugins.content-manager":"Tartalomkezel\u0151","global.plugins.content-manager.description":"Gyors m\xF3dja annak, hogy megtekintse, szerkesztse \xE9s t\xF6r\xF6lje az adatokat az adatb\xE1zis\xE1ban.","global.plugins.content-type-builder":"Tartalomt\xEDpus-\xE9p\xEDt\u0151","global.plugins.content-type-builder.description":"Modellezze az API adatszerkezet\xE9t. Hozzon l\xE9tre \xFAj mez\u0151ket \xE9s relationokat csak egy perc alatt. A f\xE1jlok automatikusan l\xE9trehoz\xF3dnak \xE9s friss\xFClnek a projektj\xE9ben.","global.plugins.email":"E-mail","global.plugins.email.description":"\xC1ll\xEDtsa be az alkalmaz\xE1st, hogy e-maileket k\xFCldj\xF6n.","global.plugins.upload":"M\xE9diat\xE1r","global.plugins.upload.description":"M\xE9diaf\xE1jlok kezel\xE9se.","global.plugins.graphql":"GraphQL","global.plugins.graphql.description":"GraphQL v\xE9gpont hozz\xE1ad\xE1sa alap\xE9rtelmezett API met\xF3dusokkal.","global.plugins.documentation":"Dokument\xE1ci\xF3","global.plugins.documentation.description":"OpenAPI Dokumentum l\xE9trehoz\xE1sa \xE9s API megjelen\xEDt\xE9se SWAGGER UI-val.","global.plugins.i18n":"Nemzetk\xF6zi\xEDt\xE9s","global.plugins.i18n.description":"Ez a plugin lehet\u0151v\xE9 teszi k\xFCl\xF6nb\xF6z\u0151 nyelveken t\xF6rt\xE9n\u0151 tartalom l\xE9trehoz\xE1s\xE1t, olvas\xE1s\xE1t \xE9s friss\xEDt\xE9s\xE9t, tanto az Admin Panelb\xF3l, mint az API-b\xF3l.","global.plugins.sentry":"Sentry","global.plugins.sentry.description":"Strapi hibaesem\xE9nyek k\xFCld\xE9se a Sentry-be.","global.plugins.users-permissions":"Szerepek & Enged\xE9lyek","global.plugins.users-permissions.description":"API v\xE9delme teljes hiteles\xEDt\xE9si folyamattal JWT alapj\xE1n. Ez a plugin egy\xFAttal olyan ACL strat\xE9gi\xE1t is tartalmaz, amely lehet\u0151v\xE9 teszi a felhaszn\xE1l\xF3i csoportok k\xF6z\xF6tti enged\xE9lyek kezel\xE9s\xE9t.","global.profile":"Profil","global.prompt.unsaved":"Biztos, hogy elhagyja ezt az oldalt? Az \xF6sszes m\xF3dos\xEDt\xE1sa elveszik","global.reset-password":"Jelsz\xF3 vissza\xE1ll\xEDt\xE1sa","global.roles":"Szerepek","global.save":"Ment\xE9s","global.see-more":"Tov\xE1bbiak megtekint\xE9se","global.select":"Kiv\xE1laszt\xE1s","global.select-all-entries":"Az \xF6sszes bejegyz\xE9s kiv\xE1laszt\xE1sa","global.settings":"Be\xE1ll\xEDt\xE1sok","global.type":"T\xEDpus","global.users":"Felhaszn\xE1l\xF3k","notification.contentType.relations.conflict":"A tartalomt\xEDpusnak ellenkez\u0151 kapcsolatai vannak","notification.default.title":"Inform\xE1ci\xF3:","notification.error":"Hiba l\xE9pett fel","notification.error.layout":"Nem siker\xFClt lek\xE9rni az elrendez\xE9st","notification.form.error.fields":"Az \u0171rlap kit\xF6lt\xE9se hib\xE1s","notification.form.success.fields":"V\xE1ltoztat\xE1sok elmentve","notification.link-copied":"A link a v\xE1g\xF3lapra m\xE1solva","notification.permission.not-allowed-read":"Ezt a dokumentumot nem tekintheti meg","notification.success.delete":"Az elemet t\xF6r\xF6lt\xE9k","notification.success.saved":"Mentve","notification.success.title":"Sikeres:","notification.success.apitokencreated":"API Token sikeresen l\xE9trehozva","notification.success.apitokenedited":"API Token sikeresen szerkesztve","notification.error.tokennamenotunique":"N\xE9v m\xE1r hozz\xE1rendelve egy m\xE1sik tokenhez","notification.version.update.message":"Megjelent a Strapi \xFAj verzi\xF3ja!","notification.warning.title":"Figyelmeztet\xE9s:","notification.warning.404":"404 - Nem tal\xE1lhat\xF3",or:u,"request.error.model.unknown":"Ez a modell nem l\xE9tezik",skipToContent:k,submit:d}}}]);
