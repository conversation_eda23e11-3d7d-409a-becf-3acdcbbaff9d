"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[5480],{5480:(i,e,a)=>{a.r(e),a.d(e,{default:()=>o});const o={"BoundRoute.title":"Wywo\u0142ywanie","EditForm.inputSelect.description.role":"Po\u0142\u0105czy nowego uwierzytelnionego u\u017Cytkownika z wybran\u0105 rol\u0105.","EditForm.inputSelect.label.role":"Domy\u015Blna rola dla uwierzytelnionych u\u017Cytkownik\xF3w","EditForm.inputToggle.description.email":"Nie zezwalaj u\u017Cytkownikowi na tworzenie wielu kont za pomoc\u0105 tego samego adresu e-mail u r\xF3\u017Cnych dostawc\xF3w uwierzytelniania.","EditForm.inputToggle.description.email-confirmation":"G<PERSON> w\u0142\u0105czone (ON), nowo zarejestrowani uzytkownicy otrzymaj\u0105 wiadomo\u015B\u0107 potwierdzaj\u0105c\u0105.","EditForm.inputToggle.description.email-confirmation-redirection":"Po potwierdzeniu adresu email, wybierz gdzie zostaniesz przekierowany.","EditForm.inputToggle.description.email-reset-password":"Adres URL strony resetowania has\u0142a aplikacji","EditForm.inputToggle.description.sign-up":"Po wy\u0142\u0105czeniu (OFF) proces rejestracji jest zabroniony. Nikt nie mo\u017Ce ju\u017C do\u0142\u0105czy\u0107 bez wzgl\u0119du na u\u017Cywanego dostawc\u0119.","EditForm.inputToggle.label.email":"Jedno konto na adres email","EditForm.inputToggle.label.email-confirmation":"Zezw\xF3l na potwierdzenie adresu email","EditForm.inputToggle.label.email-confirmation-redirection":"Url przekierowania","EditForm.inputToggle.label.email-reset-password":"Strona resetowania has\u0142a","EditForm.inputToggle.label.sign-up":"W\u0142\u0105cz mo\u017Cliwo\u015B\u0107 rejestracji","EditForm.inputToggle.placeholder.email-confirmation-redirection":"ex: https://yourfrontend.com/confirmation-redirection","EditForm.inputToggle.placeholder.email-reset-password":"ex: https://yourfrontend.com/reset-password","EditPage.form.roles":"Szczeg\xF3\u0142y roli","Email.template.data.loaded":"Szablon email zosta\u0142 za\u0142adowany","Email.template.email_confirmation":"Potwierdzenie adresu email","Email.template.form.edit.label":"Edytuj szablon","Email.template.table.action.label":"akcja","Email.template.table.icon.label":"ikonka","Email.template.table.name.label":"nazwa","Form.advancedSettings.data.loaded":"Ustawienia zaawansowane zosta\u0142y za\u0142adowane","HeaderNav.link.advancedSettings":"Zaawansowane","HeaderNav.link.emailTemplates":"Szablony e-mail","HeaderNav.link.providers":"Dostawcy","Plugin.permissions.plugins.description":"Okre\u015Bl dozwolone dzia\u0142ania dla pluginu {name}.","Plugins.header.description":"Jedynie akcje zwi\u0105zane z wywo\u0142ywaniami s\u0105 wymienione poni\u017Cej.","Plugins.header.title":"Uprawnienia","Policies.header.hint":"Wybierz dzia\u0142ania aplikacji lub dzia\u0142ania pluginu i kliknij ikon\u0119 ko\u0142a z\u0119batego, aby wy\u015Bwietli\u0107 wywo\u0142ywania","Policies.header.title":"Zaawansowane","PopUpForm.Email.email_templates.inputDescription":"Nie wiesz jak skonfigurowa\u0107 zmienne? {link}","PopUpForm.Email.link.documentation":"sprawd\u017A dokumentacj\u0119.","PopUpForm.Email.options.from.email.label":"Email nadawcy","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Nazwa nadawcy","PopUpForm.Email.options.from.name.placeholder":"Jan Nowak","PopUpForm.Email.options.message.label":"Wiadomo\u015B\u0107","PopUpForm.Email.options.object.label":"Temat","PopUpForm.Email.options.object.placeholder":"Prosz\u0119 potwierd\u017A adres email dla %APP_NAME%","PopUpForm.Email.options.response_email.label":"Email zwrotny","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"W przypadku wy\u0142\u0105czenia, u\u017Cytkownicy nie b\u0119d\u0105 mogli skorzysta\u0107 z tego dostawcy.","PopUpForm.Providers.enabled.label":"W\u0142\u0105czony","PopUpForm.Providers.key.label":"ID klienta","PopUpForm.Providers.key.placeholder":"TEKST","PopUpForm.Providers.redirectURL.front-end.label":"Adres przekierowania do w\u0142asnej aplikacji","PopUpForm.Providers.redirectURL.label":"Adres przekierowania do dodania w twoich ustawieniach aplikacji: {provider}","PopUpForm.Providers.secret.label":"Klucz sekretny klienta","PopUpForm.Providers.secret.placeholder":"TEKST","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Zmie\u0144 szablony e-mail","PopUpForm.header.edit.providers":"Edytuj dostawc\u0119","Providers.data.loaded":"Dostawcy zostali za\u0142adowani","Providers.status":"Status","Roles.empty":"Nie masz jeszcze \u017Cadnych r\xF3l.","Roles.empty.search":"\u017Badne role nie pasuj\u0105 do wyszukiwania.","Settings.roles.deleted":"Rola usuni\u0119ta","Settings.roles.edited":"Rola edytowana","Settings.section-label":"U\u017Cytkownicy i Uprawnienia","components.Input.error.validation.email":"Ten email jest niepoprawny","components.Input.error.validation.json":"Nie pasuje do formatu JSON","components.Input.error.validation.max":"Warto\u015B\u0107 zbyt du\u017Ca.","components.Input.error.validation.maxLength":"Warto\u015B\u0107 zbyt d\u0142uga.","components.Input.error.validation.min":"Warto\u015B\u0107 zbyt ma\u0142a.","components.Input.error.validation.minLength":"Warto\u015B\u0107 zbyt kr\xF3tka.","components.Input.error.validation.minSupMax":"Nie mo\u017Ce by\u0107 wy\u017Cszy","components.Input.error.validation.regex":"Warto\u015B\u0107 nie pasuje do regexa.","components.Input.error.validation.required":"Warto\u015B\u0107 wymagana.","components.Input.error.validation.unique":"Warto\u015B\u0107 ju\u017C u\u017Cywana.","notification.success.submit":"Ustawienia zosta\u0142y zaktualizowane","page.title":"Ustawienia - Role","plugin.description.long":"Chro\u0144 API za pomoc\u0105 procesu pe\u0142nego uwierzytelniania opartego na JWT. Ten plugin zawiera r\xF3wnie\u017C strategi\u0119 ACL, kt\xF3ra pozwala zarz\u0105dza\u0107 uprawnieniami mi\u0119dzy grupami u\u017Cytkownik\xF3w.","plugin.description.short":"Chro\u0144 API za pomoc\u0105 procesu pe\u0142nego uwierzytelniania opartego na JWT","plugin.name":"Role i Uprawnienia","popUpWarning.button.cancel":"Anuluj","popUpWarning.button.confirm":"Potwierd\u017A","popUpWarning.title":"Prosz\u0119 potwierd\u017A","popUpWarning.warning.cancel":"Czy jeste\u015B pewny, \u017Ce chcesz anulowa\u0107 zmiany?"}}}]);
