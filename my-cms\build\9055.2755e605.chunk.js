"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[9055],{69055:(i,t,e)=>{e.r(t),e.d(t,{configurations:()=>o,default:()=>a,from:()=>n});const o="nastaven\xED",n="od",a={"attribute.boolean":"Boolean","attribute.boolean.description":"Yes/no, 1/0, true/false","attribute.component":"Komponent","attribute.component.description":"S<PERSON>pina pol\xED, kter\xE9 je mo\u017En\xE9 opakovan\u011B pou\u017E\xEDvat","attribute.date":"Datum a \u010Das","attribute.date.description":"Dialog pro v\xFDb\u011Br datumu a \u010Dasu","attribute.datetime":"Datum a \u010Das","attribute.dynamiczone":"Dynamick\xE1 z\xF3na","attribute.dynamiczone.description":"Umo\u017E\u0148uje dynamicky zvolit komponenty p\u0159i \xFAprav\xE1ch obsahu","attribute.email":"E-mailov\xE1 adresa","attribute.email.description":"Pole s automatickou validac\xED form\xE1tu e-mailov\xE9 adresy","attribute.enumeration":"V\xFD\u010Det","attribute.enumeration.description":"Seznam hodnot s v\xFDb\u011Brem jedn\xE9 mo\u017Enosti","attribute.json":"JSON","attribute.json.description":"Data vo form\xE1tu JSON","attribute.media":"Soubory","attribute.media.description":"Nap\u0159. obr\xE1zky, videa, ...","attribute.null":" ","attribute.number":"\u010C\xEDslo","attribute.number.description":"\u010C\xEDsla (cel\xE9, desetinn\xE9)","attribute.password":"Heslo","attribute.password.description":"Pol\xED\u010Dko pro zad\xE1n\xED hesla","attribute.relation":"Relace","attribute.relation.description":"Ur\u010Duje vztah k jin\xE9mu Typu obsahu","attribute.richtext":"Textov\xFD editor","attribute.richtext.description":"Textov\xE9 pole s mo\u017Enostmi form\xE1tov\xE1n\xED","attribute.text":"Text","attribute.text.description":"Kr\xE1tk\xFD nebo del\u0161\xED text","attribute.time":"\u010Cas","attribute.uid":"UID","attribute.uid.description":"Unik\xE1tn\xED identifik\xE1tor","button.attributes.add.another":"P\u0159idat dal\u0161\xED pole","button.component.add":"P\u0159idat komponent","button.component.create":"Vytvorit nov\xFD komponent","button.model.create":"Vytvo\u0159it nov\xFD Typ obsahu","component.repeatable":"(opakuj\xEDc\xED)","components.componentSelect.no-component-available":"U\u017E jste p\u0159idali v\u0161echny komponenty","components.componentSelect.no-component-available.with-search":"Nena\u0161el se \u017E\xE1dn\xFD komponent spl\u0148uj\xEDc\xED v\xFDraz","components.componentSelect.value-component":"Ozna\u010Den\xE9 komponenty: {number} (zadejte hledan\xFD text)","components.componentSelect.value-components":"Ozna\u010Den\xE9 komponenty: {number}",configurations:o,"contentType.collectionName.label":"Intern\xED n\xE1zev","contentType.displayName.label":"N\xE1zev","error.contentTypeName.reserved-name":"Tento n\xE1zev je vyhrazen\xFD a nem\u016F\u017Ee b\xFDt pou\u017Eit","error.validation.minSupMax":"Nem\u016F\u017Ee b\xFDt nad\u0159azen\xFD","form.attribute.component.option.add":"P\u0159idat komponent","form.attribute.component.option.create":"Vytvo\u0159it nov\xFD komponent","form.attribute.component.option.create.description":"Komponent je dostupn\xFD mezi v\u0161emi typy a komponenty.","form.attribute.component.option.repeatable":"Znovu pou\u017Eiteln\xFD komponent","form.attribute.component.option.repeatable.description":"Nejlep\u0161\xED pro n\u011Bkolikan\xE1sobn\xE9 instance (pole) hodnot, meta tagy, apod...","form.attribute.component.option.reuse-existing":"Pou\u017E\xEDt existuj\xEDc\xED komponent","form.attribute.component.option.reuse-existing.description":"Pou\u017E\xEDvejte u\u017E vytvo\u0159en\xE9 komponenty pro uchov\xE1n\xED konzistentn\xEDch dat mezi Typy obsahu.","form.attribute.component.option.single":"Jednoduch\xFD komponent","form.attribute.component.option.single.description":"Vhodn\xE9 pro seskupen\xED pol\xED\u010Dek, nap\u0159. cel\xE1 adresa","form.attribute.item.customColumnName":"Vlastn\xE9 n\xE1zvy st\u013Apcov","form.attribute.item.customColumnName.description":"Umo\u017E\u0148uje p\u0159ejmenovat datab\xE1zov\xFD sloupec pro pot\u0159eby API","form.attribute.item.defineRelation.fieldName":"N\xE1zev pole","form.attribute.item.enumeration.graphql":"N\xE1zev pole pro GraphQL","form.attribute.item.enumeration.graphql.description":"Umo\u017E\u0148uje p\u0159epsat p\u0159ednastaven\xE9 n\xE1zvy n\xE1zvy pro GraphQL","form.attribute.item.enumeration.placeholder":`Nap\u0159.:
r\xE1no
den
ve\u010Der`,"form.attribute.item.enumeration.rules":"Hodnoty (jedna na \u0159\xE1dek)","form.attribute.item.maximum":"Maxim\xE1ln\xED hodnota","form.attribute.item.maximumLength":"Maxim\xE1ln\xED d\xE9lka","form.attribute.item.minimum":"Minim\xE1ln\xED hodnota","form.attribute.item.minimumLength":"Minim\xE1ln\xED d\xE9lka","form.attribute.item.number.type":"\u010C\xEDseln\xFD form\xE1t","form.attribute.item.number.type.biginteger":"velk\xE9 \u010D\xEDslo (nap\u0159.: 123456789)","form.attribute.item.number.type.decimal":"desetinn\xE9 \u010D\xEDslo (nap\u0159.: 2.22)","form.attribute.item.number.type.float":"desetinn\xE9 \u010D\xEDslo (nap\u0159.: 3.33333333)","form.attribute.item.number.type.integer":"cel\xE9 \u010D\xEDslo (nap\u0159.: 10)","form.attribute.item.privateField":"Skryt\xE9 pole","form.attribute.item.privateField.description":"Toto pole se nebude zobrazovat v API","form.attribute.item.requiredField":"Povinn\xE9 pole","form.attribute.item.requiredField.description":"Nedovol\xED vytvo\u0159it z\xE1znam kdy\u017E toto pole z\u016Fstane pr\xE1zdne","form.attribute.item.uniqueField":"Unik\xE1tn\xED pole","form.attribute.item.uniqueField.description":"Nedovol\xED vytvo\u0159it z\xE1znam kdy\u017E u\u017E existuje jin\xFD z\xE1znam se stejnou hodnotou","form.attribute.media.option.multiple":"V\xEDce soubor\u016F","form.attribute.media.option.multiple.description":"Vhodn\xE9 pro galerii, seznam soubor\u016F na st\xE1hnut\xED","form.attribute.media.option.single":"Jeden soubor","form.attribute.media.option.single.description":"Vhodn\xE9 pro profilovou fotku nebo hlavn\xED obr\xE1zek","form.attribute.settings.default":"P\u0159edvolen\xE1 hodnota","form.attribute.text.option.long-text":"Dlouh\xFD text","form.attribute.text.option.long-text.description":"Vhodn\xE9 pro del\u0161\xED popisy. P\u0159esn\xE9 vyhled\xE1v\xE1n\xED je vypnut\xE9.","form.attribute.text.option.short-text":"Kr\xE1tky text","form.attribute.text.option.short-text.description":"Vhodn\xE9 pro nadpisy, n\xE1zvy, URL adresy. P\u0159esn\xE9 vyhled\xE1v\xE1n\xED je zapnut\xE9.","form.button.add-components-to-dynamiczone":"P\u0159idat komponenty do z\xF3ny","form.button.add-field":"P\u0159idat dal\u0161\xED pole","form.button.add-first-field-to-created-component":"P\u0159idat prvn\xED pole do komponentu","form.button.add.field.to.component":"P\u0159idat dal\u0161\xED pole do komponentu","form.button.cancel":"Zru\u0161it","form.button.configure-component":"Nastavit komponent","form.button.configure-view":"Upravit vzhled","form.button.select-component":"Vybrat komponent",from:n,"modalForm.attribute.form.base.name.description":"Mezery v n\xE1zvu pole nejsou povoleny","modalForm.attributes.select-component":"Vyberte komponent","modalForm.attributes.select-components":"Vyberte komponenty","modalForm.component.header-create":"Vytvorte komponent","modalForm.components.create-component.category.label":"Vyberte kategorii nebo zadejte n\xE1zev pro vytvo\u0159en\xED nov\xE9","modalForm.components.icon.label":"Ikona","modalForm.editCategory.base.name.description":"Mezery v n\xE1zvu kategorie nejsou povoleny","modalForm.header-edit":"Upravit {name}","modalForm.header.categories":"Kategorie","modalForm.header.back":"Zadn\xED","modalForm.sub-header.addComponentToDynamicZone":"P\u0159idat nov\xFD komponent do dynamick\xE9 z\xF3ny","modalForm.sub-header.attribute.create":"P\u0159idat nov\xE9 pole {type}","modalForm.sub-header.attribute.create.step":"P\u0159idat nov\xFD komponent ({step}/2)","modalForm.sub-header.attribute.edit":"Upravit {name}","modalForm.sub-header.chooseAttribute.collectionType":"Vyberte typ pole pro Typ obsahu","modalForm.sub-header.chooseAttribute.component":"Vyberte typ pole pro komponent","modelPage.attribute.relationWith":"Propojen\xED s","notification.info.creating.notSaved":"Ulo\u017Ete zm\u011Bny p\u0159ed vytvo\u0159en\xEDm nov\xE9ho Typu obsahu nebo komponentu","plugin.description.long":"Navrhn\u011Bte strukturu webu jednodu\u0161e. Vytvo\u0159te nov\xE1 pole a propojen\xED b\u011Bhem p\xE1r sekund. Soubory se automaticky vytvo\u0159\xED a uprav\xED v r\xE1mci projektu.","plugin.description.short":"Navrhn\u011Bte strukturu webu jednodu\u0161e.","popUpForm.navContainer.advanced":"Pokro\u010Dil\xE1 nastaven\xED","popUpForm.navContainer.base":"Z\xE1kladn\xED nastaven\xED","popUpWarning.bodyMessage.cancel-modifications":"Jste si jisti, \u017Ee chcete zru\u0161it \xFApravy?","popUpWarning.bodyMessage.cancel-modifications.with-components":"Jste si jisti, \u017Ee chcete zru\u0161it \xFApravy? N\u011Bkter\xE9 komponenty byly vytvo\u0159eny nebo upraveny...","popUpWarning.bodyMessage.category.delete":"Jste si jisti, \u017Ee chcete odstranit tuto kategorii? V\u0161echny komponentu budou rovn\u011B\u017E odstran\u011Bny.","popUpWarning.bodyMessage.component.delete":"Jste si jisti, \u017Ee chcete odstranit tento komponent?","popUpWarning.bodyMessage.contentType.delete":"Jste si jisti, \u017Ee chcete odstranit tento Typ obsahu?","prompt.unsaved":"Jste si jisti, \u017Ee chcete odej\xEDt? V\u0161echny \xFApravy budou ztraceny.","relation.attributeName.placeholder":"Nap\u0159: autor, kategorie, tag","relation.manyToMany":"m\xE1 v\xEDc a pat\u0159\xED v\u0161em","relation.manyToOne":"m\xE1 v\xEDc","relation.manyWay":"m\xE1 v\xEDc","relation.oneToMany":"pat\u0159\xED v\xEDce","relation.oneToOne":"m\xE1 jeden a pat\u0159\xED jednomu","relation.oneWay":"m\xE1 jeden"}}}]);
