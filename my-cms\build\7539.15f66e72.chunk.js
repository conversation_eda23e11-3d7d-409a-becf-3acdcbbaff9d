"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[7539],{17539:(r,e,i)=>{i.r(e),i.d(e,{default:()=>o});const o={"BoundRoute.title":"Route associ\xE9e \xE0","EditForm.inputSelect.description.role":"Choisissez le r\xF4le qui sera li\xE9 aux utilisateurs lors de leur enregistrement.","EditForm.inputSelect.label.role":"R\xF4le par defaut pour les nouveaux utilisateurs","EditForm.inputToggle.description.email":"Interdire l'utilisateur de cr\xE9er de multiple comptes avec la m\xEAme adresse e-mail avec des providers diff\xE9rents","EditForm.inputToggle.description.email-confirmation":"Quand cette option est activ\xE9e (ON), les nouveaux utilisateurs enregistr\xE9s re\xE7oivent un e-mail de confirmation.","EditForm.inputToggle.description.email-confirmation-redirection":"Apr\xE8s confirmation de votre e-mail, choisissez o\xF9 vous allez \xEAtre redirig\xE9.","EditForm.inputToggle.description.email-reset-password":"URL de la page de r\xE9initialisation de mot de passe.","EditForm.inputToggle.description.sign-up":"Quand l'inscription est d\xE9sactiv\xE9e (OFF), aucun utilisateur ne peut s'inscrire qu'importe le provider","EditForm.inputToggle.label.email":"Un compte par adresse e-mail","EditForm.inputToggle.label.email-confirmation":"Activer l'e-mail de confirmation","EditForm.inputToggle.label.email-confirmation-redirection":"Redirection de l'URL","EditForm.inputToggle.label.email-reset-password":"Page de r\xE9initialisation de mot de passe","EditForm.inputToggle.label.sign-up":"Activer l'inscription","HeaderNav.link.advancedSettings":"Param\xE8tres avanc\xE9s","HeaderNav.link.emailTemplates":"Templates d'e-mail","HeaderNav.link.providers":"Fournisseurs","Plugin.permissions.plugins.description":"D\xE9finissez les actions autoris\xE9es dans le {name} plugin.","Plugins.header.description":"Sont list\xE9s uniquement les actions associ\xE9es \xE0 une route.","Plugins.header.title":"Permissions","Policies.header.hint":"S\xE9lectionnez les actions de l'application ou d'un plugin et cliquer sur l'icon de param\xE8tres pour voir les routes associ\xE9es \xE0 cette action","Policies.header.title":"Param\xE8tres avanc\xE9s","PopUpForm.Email.email_templates.inputDescription":"Regardez la documentation des variables, {link}","PopUpForm.Email.options.from.email.label":"E-mail de l'envoyeur","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Nom de l'envoyeur","PopUpForm.Email.options.from.name.placeholder":"Arthur Dupont","PopUpForm.Email.options.message.label":"Message","PopUpForm.Email.options.object.label":"Objet","PopUpForm.Email.options.response_email.label":"E-mail de r\xE9ponse","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"S'il est d\xE9sactiv\xE9 les utilisateurs ne pourront pas utiliser ce provider.","PopUpForm.Providers.enabled.label":"Activer","PopUpForm.Providers.key.label":"Client ID","PopUpForm.Providers.key.placeholder":"TEXT","PopUpForm.Providers.redirectURL.front-end.label":"L'URL de redirection de votre app front-end","PopUpForm.Providers.secret.label":"Client Secret","PopUpForm.Providers.secret.placeholder":"TEXT","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Editer E-mail Templates","notification.success.submit":"Les configurations ont bien \xE9t\xE9 sauvegard\xE9s","plugin.description.long":"Prot\xE9gez votre API avec un syst\xE8me d'authentification complet bas\xE9 sur JWT (JSON Web Token). Ce plugin ajoute aussi une strat\xE9gie ACL (Access Control Layer) qui vous permet de g\xE9rer les permissions entre les groupes d'utilisateurs.","plugin.description.short":"Prot\xE9gez votre API avec un syst\xE8me d'authentification complet bas\xE9 sur JWT.","plugin.name":"R\xF4les et autorisations"}}}]);
