"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4972],{54972:(a,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i={"BoundRoute.title":"Ligar rota a","EditForm.inputSelect.description.role":"Vai atribuir o grupo selecionado ao novo utilizador autenticado.","EditForm.inputSelect.label.role":"Grupo por defeito para utilizadores autenticados","EditForm.inputToggle.description.email":"Proibir a cria\xE7\xE3o de m\xFAltiplas contas com o mesmo email por servi\xE7os de autentica\xE7\xE3o diferentes.","EditForm.inputToggle.description.email-confirmation":"Quando ativado (ON), os novos utilizadores recebem um email de confirma\xE7\xE3o.","EditForm.inputToggle.description.email-confirmation-redirection":"Ap\xF3s confirmar o seu email, escolha para onde vai ser redirecionado.","EditForm.inputToggle.description.sign-up":"Quando desativado (OFF), o processo de registo est\xE1 proibido. Ningu\xE9m se consegue registar mais, independentemente do servi\xE7o de authentica\xE7\xE3o.","EditForm.inputToggle.label.email":"Uma conta por endere\xE7o de email","EditForm.inputToggle.label.email-confirmation":"Ativar email de confirma\xE7\xE3o","EditForm.inputToggle.label.email-confirmation-redirection":"Endere\xE7o de redirecionamento (URL)","EditForm.inputToggle.label.sign-up":"Ativar registos","HeaderNav.link.advancedSettings":"Configura\xE7\xF5es avan\xE7adas","HeaderNav.link.emailTemplates":"Modelos de email","HeaderNav.link.providers":"Servi\xE7os de autentica\xE7\xE3o","Plugin.permissions.plugins.description":"Defina todas as a\xE7\xF5es permitidas para o plugin {name}.","Plugins.header.description":"Todas as a\xE7\xF5es associadas a uma rota est\xE3o listadas abaixo.","Plugins.header.title":"Permiss\xF5es","Policies.header.hint":"Selecione as a\xE7\xF5es da aplica\xE7\xE3o ou dos plugins e clique no \xEDcone para mostrar as rotas associadas","Policies.header.title":"Configura\xE7\xF5es avan\xE7adas","PopUpForm.Email.email_templates.inputDescription":"Se n\xE3o tem a certeza de como usar as vari\xE1veis, {link}","PopUpForm.Email.options.from.email.label":"Shipper email","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Shipper name","PopUpForm.Email.options.from.name.placeholder":"Kai Doe","PopUpForm.Email.options.message.label":"Mensagem","PopUpForm.Email.options.object.label":"Assunto","PopUpForm.Email.options.response_email.label":"Email de resposta","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"Se desativado, os utilizadores n\xE3o conseguir\xE3o utilizar este servi\xE7o de autentica\xE7\xE3o.","PopUpForm.Providers.enabled.label":"Ativar","PopUpForm.Providers.key.label":"ID de Client","PopUpForm.Providers.key.placeholder":"TEXTO","PopUpForm.Providers.redirectURL.front-end.label":"Endere\xE7o de redirecionamento para a sua aplica\xE7\xE3o de front-end","PopUpForm.Providers.secret.label":"Segredo de cliente","PopUpForm.Providers.secret.placeholder":"TEXTO","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Editar Modelos de Email","notification.success.submit":"As configura\xE7\xF5es foram atualizadas","plugin.description.long":"Proteja a sua API com um processo completo de autentica\xE7\xE3o baseado em JWT. Este plugin tamb\xE9m vem com estrat\xE9gia de ACL que permite gerir permiss\xF5es entre grupos de utilizadores.","plugin.description.short":"Proteja a sua API com um processo completo de autentica\xE7\xE3o baseado em JWT","plugin.name":"Grupos & Permiss\xF5es"}}}]);
