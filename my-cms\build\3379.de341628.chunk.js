"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3379],{83379:(i,e,o)=>{o.r(e),o.d(e,{default:()=>a});const a={"BoundRoute.title":"Rota definida para","EditForm.inputSelect.description.role":"Ele anexar\xE1 o novo usu\xE1rio autenticado ao n\xEDvel selecionado.","EditForm.inputSelect.label.role":"N\xEDvel padr\xE3o para usu\xE1rios autenticados","EditForm.inputToggle.description.email":"N\xE3o permitir que o usu\xE1rio crie v\xE1rias contas usando o mesmo endere\xE7o de e-mail com diferentes provedores de autentica\xE7\xE3o.","EditForm.inputToggle.description.sign-up":"Quando desativado (OFF), o processo de registro \xE9 proibido. Nenhum novo usu\xE1rio poder\xE1 se registrar, n\xE3o importa o provedor usado.","EditForm.inputToggle.label.email":"Limitar 1 conta por endere\xE7o de email","EditForm.inputToggle.label.sign-up":"Ativar registro de usu\xE1rios","HeaderNav.link.advancedSettings":"Configura\xE7\xF5es avan\xE7adas","HeaderNav.link.emailTemplates":"Modelos de email","HeaderNav.link.providers":"Provedores","Plugin.permissions.plugins.description":"Defina todas as a\xE7\xF5es permitidas para o plugin {name}.","Plugins.header.description":"Somente a\xE7\xF5es vinculadas por uma rota est\xE3o listadas abaixo.","Plugins.header.title":"Permiss\xF5es","Policies.header.hint":"Selecione as a\xE7\xF5es do aplicativo ou as a\xE7\xF5es do plugin e clique no \xEDcone do cog para exibir a rota","Policies.header.title":"Configura\xE7\xF5es avan\xE7adas","PopUpForm.Email.email_templates.inputDescription":"Se n\xE3o tiver certeza de como usar vari\xE1veis, {link}","PopUpForm.Email.options.from.email.label":"Email do remetente","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Nome do remetente","PopUpForm.Email.options.from.name.placeholder":"Kai Doe","PopUpForm.Email.options.message.label":"Mensagem","PopUpForm.Email.options.object.label":"Assunto","PopUpForm.Email.options.response_email.label":"Email de resposta","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"Se desativado, os usu\xE1rios n\xE3o poder\xE3o usar este provedor","PopUpForm.Providers.enabled.label":"Ativar","PopUpForm.Providers.key.label":"ID do cliente","PopUpForm.Providers.key.placeholder":"TEXT","PopUpForm.Providers.redirectURL.front-end.label":"O URL de redirecionamento para seu aplicativo front-end","PopUpForm.Providers.secret.label":"Segredo do Cliente","PopUpForm.Providers.secret.placeholder":"TEXT","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Editar modelos de email","notification.success.submit":"As configura\xE7\xF5es foram atualizadas","plugin.description.long":"Proteja sua API com um processo de autentica\xE7\xE3o completo baseado no JWT. Esse plugin tamb\xE9m vem com uma estrat\xE9gia de ACL que permite gerenciar as permiss\xF5es entre os grupos de usu\xE1rios.","plugin.description.short":"Proteja sua API com um processo de autentica\xE7\xE3o completo baseado no JWT","plugin.name":"Pap\xE9is e permiss\xF5es"}}}]);
