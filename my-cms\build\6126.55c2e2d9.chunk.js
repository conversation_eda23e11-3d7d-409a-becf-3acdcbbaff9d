"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[6126],{26126:(r,e,o)=>{o.r(e),o.d(e,{default:()=>t});const t={"Plugin.name":"D\xE9ploiement","Homepage.title":"H\xE9bergement cloud enti\xE8rement g\xE9r\xE9 pour votre projet Strapi","Homepage.subTitle":"Suivez ce processus en 2 \xE9tapes pour obtenir tout ce dont vous avez besoin pour ex\xE9cuter Strapi en production.","Homepage.githubBox.title.versioned":"Projet upload\xE9 sur GitHub","Homepage.githubBox.title.not-versioned":"Upload ton projet sur GitHub","Homepage.githubBox.subTitle.versioned":"Bien jou\xE9 ! Il ne manque plus qu'une \xE9tape pour d\xE9ployer ton projet sur Strapi Cloud","Homepage.githubBox.subTitle.not-versioned":"Ton projet doit \xEAtre versionn\xE9 sur GitHub avant d'\xEAtre d\xE9ploy\xE9 sur Strapi Cloud.","Homepage.githubBox.buttonText":"Upload sur GitHub","Homepage.cloudBox.title":"Deploie sur Strapi Cloud","Homepage.cloudBox.subTitle":"Profitez d'une stack opptimis\xE9 pour Strapi comprenant une base de donn\xE9es, un email provider et un CDN.","Homepage.cloudBox.buttonText":"Deploie sur Strapi Cloud","Homepage.textBox.label.versioned":"Essaies Strapi Cloud gratuitement !","Homepage.textBox.label.not-versioned":"Pourquoi uploader mon projet sur GitHub ?","Homepage.textBox.text.versioned":"Strapi Cloud propose un essai gratuit de 14 jours pour te permettre d'exp\xE9rimenter ton projet sur le cloud !","Homepage.textBox.text.not-versioned":"Strapi Cloud r\xE9cup\xE9rera et d\xE9ploiera ton projet \xE0 partir de ton repo GitHub. C\u2019est la meilleure fa\xE7on de versionner, g\xE9rer et d\xE9ployer votre projet. Suivez les \xE9tapes sur GitHub pour l'uploader avec succ\xE8s"}}}]);
