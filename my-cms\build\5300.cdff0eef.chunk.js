(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[5300],{4191:(U,H,a)=>{var t=a(87864),f=a(86386),h=a(45353),k=a(29884),D=a(74565),R=a(52689),x=a(48126),T=a(82388),W=a(82261);function J(_,ae,Ee){ae.length?ae=t(ae,function(te){return W(te)?function(se){return f(se,te.length===1?te[0]:te)}:te}):ae=[T];var le=-1;ae=t(ae,R(h));var ie=k(_,function(te,se,ne){var Fe=t(ae,function(Ne){return Ne(te)});return{criteria:Fe,index:++le,value:te}});return D(ie,function(te,se){return x(te,se,Ee)})}U.exports=J},7233:(U,H,a)=>{var t=a(97449);function f(h,k,D,R){return t(h,function(x,T,W){k(R,x,D(x),W)}),R}U.exports=f},14311:(U,H,a)=>{var t=a(32628),f=a(50633),h=a(91522),k=a(34827),D=a(49605),R="[object Map]",x="[object Set]";function T(W){if(W==null)return 0;if(h(W))return k(W)?D(W):W.length;var J=f(W);return J==R||J==x?W.size:t(W).length}U.exports=T},22171:(U,H,a)=>{var t=a(41225),f=a(52689),h=a(54765),k=h&&h.isRegExp,D=k?f(k):t;U.exports=D},29884:(U,H,a)=>{var t=a(97449),f=a(91522);function h(k,D){var R=-1,x=f(k)?Array(k.length):[];return t(k,function(T,W,J){x[++R]=D(T,W,J)}),x}U.exports=h},33999:(U,H,a)=>{var t=a(32193),f=t("length");U.exports=f},34827:(U,H,a)=>{var t=a(81204),f=a(82261),h=a(51646),k="[object String]";function D(R){return typeof R=="string"||!f(R)&&h(R)&&t(R)==k}U.exports=D},35300:(U,H,a)=>{"use strict";a.r(H),a.d(H,{A:()=>qe,C:()=>ht,a:()=>Ge,g:()=>d,i:()=>lr,u:()=>Re});var t=a(92132),f=a(21272),h=a(94061),k=a(18629),D=a(58805),R=a(76079),x=a(83997),T=a(30893),W=a(94293),J=a(90151),_=a(68074),ae=a(25815),Ee=a(6239),le=a(12081),ie=a(12408),te=a(48653),se=a(24093),ne=a(43739),Fe=a(95336),Ne=a(78732),pe=a(4181),xe=a(7537),at=a(73989),ee=a(85963),Je=a(99248),st=a(7153),Rn=a(8361),kn=a(98765),Pn=a(74447),Bn=a(7297),Dt=a(88353),Un=a(79739),Wn=a(76517),zn=a(80782),Zn=a(5287),Vn=a(61485),Yn=a(67030),Gn=a(42455),F=a(55506),Hn=a(14718),w=a(54894),Rt=a(71389),Te=a(17703),wn=a(41156),Kn=a(32091),Qn=a(29404),Jn=a(87419),Xn=a(9005),Me=a(63891),qn=a(84175);const _n=(0,Me.Ay)(h.a)`
  svg {
    height: ${4/16}rem;
    path {
      fill: ${({theme:e})=>e.colors.neutral700};
    }
  }
`,ea=Me.Ay.button`
  border: none;
  padding: 0;
  background: transparent;
  display: flex;
  align-items: center;
`,ta=Me.Ay.div`
  display: flex;
  align-items: center;
  width: ${12/16}rem;
  transform: rotateX(${({rotated:e})=>e?"0deg":"180deg"});
`,na=({label:e,children:n,id:s})=>{const[i,o]=(0,f.useState)(!0),l=(0,qn.B)(s),r=()=>{o(c=>!c)};return(0,t.jsxs)(h.a,{children:[(0,t.jsx)(_n,{paddingLeft:7,paddingTop:2,paddingBottom:2,paddingRight:4,children:(0,t.jsx)(x.s,{justifyContent:"space-between",children:(0,t.jsxs)(ea,{onClick:r,"aria-expanded":i,"aria-controls":l,children:[(0,t.jsx)(ta,{rotated:i,children:(0,t.jsx)(Xn.A,{"aria-hidden":!0})}),(0,t.jsx)(h.a,{paddingLeft:2,children:(0,t.jsx)(T.o,{as:"span",fontWeight:"semiBold",textColor:"neutral800",children:e})})]})})}),i&&(0,t.jsx)("ul",{id:l,children:f.Children.map(n,(c,u)=>(0,t.jsx)("li",{children:c},u))})]})};var kt=a(63996),Pt=a(21610),aa=a(37679),sa=a(60043),ot=a(56936),oa=a(58215),ia=a(66322),ra=a(9032),la=a(51505),Bt=a(46270),da=a(72417),ca=a(42798),ua=a(22405),ma=a(26865),pa=a(74091),ga=a(54429),fa=a(69542),ha=a(12602),ya=a(60426),va=a(96584),xa=a(34280),ba=a(63679),Ca=a(38864),Aa=a(12430),Ta=a(10908),Ma=a(54514),ja=a(14448),Sa=a(64169),Fa=a(54245),Na=a(20415),La=a(60623),Ia=a(26665),$a=a(89315),Oa=a(18536),Ea=a(81355),Da=a(27421),Ra=a(69646),ka=a(31084),Pa=a(99334),Ba=a(37901),Ua=a(69512),Wa=a(19443),za=a(59598),Za=a(27249),Va=a(23646),Ya=a(34313),Ga=a(28442),Ha=a(86986),wa=a(83925),Ka=a(64199),Qa=a(4992),Ja=a(85914),Xa=a(25614),qa=a(28604),_a=a(17470),es=a(83060),ts=a(32816),ns=a(49389),as=a(60362),ss=a(82151),os=a(51346),is=a(87868),rs=a(38934),ls=a(39238),ds=a(52512),cs=a(87578),us=a(2919),ms=a(64213),ps=a(77701),gs=a(43896),fs=a(93659),hs=a(28826),ys=a(41231),vs=a(3851),Ut=a(57549),Wt=a(47684),zt=a(39783),xs=a(1423),bs=a(39527),Cs=a(82813),As=a(71656),Ts=a(65841),Ms=a(23483),Zt=a(78580),Vt=a(82431),Yt=a(30899),js=a(47926),Ss=a(54678),Fs=a(64486),Ns=a(41909),Ls=a(77902),Is=a(95628),$s=a(20055),Os=a(4487),Es=a(89292),Ds=a(98052),Be=a(5194),Rs=a(13955),ks=a(38910),Ps=a(57574),Bs=a(97358),Us=a(84447),Ws=a(84823),zs=a(25490),Zs=a(19143),Vs=a(63931),Gt=a(60678),Ys=a(46469),Gs=a(15363),Hs=a(61377),ws=a(90682),Ks=a(21396),Qs=a(44862),Js=a(84114),Xs=a(42386),qs=a(50929),_s=a(58971),eo=a(24408),to=a(18762),no=a(25262),ao=a(53485),so=a(87348),oo=a(5503),io=a(22353),ro=a(20126),lo=a(11171),co=a(6499),uo=a(94959),mo=a(76721),po=a(80906),go=a(29047),Xe=a(57009);const fo=e=>(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 34 25",...e,children:[(0,t.jsx)("rect",{width:33,height:23,x:.5,y:1,fill:"#EAF5FF",stroke:"#B8E1FF",rx:2.5}),(0,t.jsx)("path",{fill:"#0C75AF",d:"M18.901 9.828a1.043 1.043 0 1 0 0-2.086 1.043 1.043 0 0 0 0 2.086Z"}),(0,t.jsx)("path",{fill:"#0C75AF",d:"M19.703 8.785a.81.81 0 0 1-.512.748.814.814 0 0 1-.91-.239.804.804 0 0 1 .753-1.301.814.814 0 0 1 .669.792c.005.311.487.311.483 0a1.308 1.308 0 0 0-.867-1.215 1.288 1.288 0 0 0-1.4.39 1.296 1.296 0 0 0-.119 1.489c.283.468.83.697 1.364.596.597-.113 1.012-.664 1.021-1.258.005-.314-.477-.314-.482-.002ZM18.901 13.488a1.043 1.043 0 1 0 0-2.086 1.043 1.043 0 0 0 0 2.086Z"}),(0,t.jsx)("path",{fill:"#0C75AF",d:"M19.703 12.445a.81.81 0 0 1-.512.748.814.814 0 0 1-.91-.239.804.804 0 0 1 .753-1.301.812.812 0 0 1 .669.792c.005.311.487.311.483 0a1.307 1.307 0 0 0-.867-1.215 1.288 1.288 0 0 0-1.4.39 1.296 1.296 0 0 0-.119 1.489c.283.468.83.697 1.364.596.597-.113 1.012-.664 1.021-1.258.005-.314-.477-.314-.482-.002ZM18.901 17.247a1.043 1.043 0 1 0 0-2.086 1.043 1.043 0 0 0 0 2.086Z"}),(0,t.jsx)("path",{fill:"#0C75AF",d:"M19.703 16.204a.81.81 0 0 1-.512.748.814.814 0 0 1-.91-.239.804.804 0 0 1 .753-1.301.812.812 0 0 1 .669.792c.005.311.487.311.483 0a1.308 1.308 0 0 0-.867-1.215 1.288 1.288 0 0 0-1.4.39 1.296 1.296 0 0 0-.119 1.489c.283.468.83.698 1.364.596.597-.113 1.012-.664 1.021-1.258.005-.313-.477-.313-.482-.002ZM15.075 9.842a1.043 1.043 0 1 0 0-2.086 1.043 1.043 0 0 0 0 2.086Z"}),(0,t.jsx)("path",{fill:"#0C75AF",d:"M15.876 8.8a.81.81 0 0 1-.512.748.814.814 0 0 1-.91-.24.804.804 0 0 1 .753-1.301.81.81 0 0 1 .669.792c.005.312.488.312.483 0a1.308 1.308 0 0 0-.867-1.214 1.288 1.288 0 0 0-1.4.389 1.296 1.296 0 0 0-.119 1.49c.283.468.831.697 1.365.596.596-.114 1.011-.664 1.02-1.258.006-.314-.477-.314-.482-.003ZM15.075 13.503a1.043 1.043 0 1 0 0-2.086 1.043 1.043 0 0 0 0 2.086Z"}),(0,t.jsx)("path",{fill:"#0C75AF",d:"M15.876 12.46a.81.81 0 0 1-.512.748.814.814 0 0 1-.91-.24.804.804 0 0 1 .753-1.301.81.81 0 0 1 .669.792c.005.312.488.312.483 0a1.308 1.308 0 0 0-.867-1.214 1.288 1.288 0 0 0-1.4.389 1.296 1.296 0 0 0-.119 1.49c.283.468.831.697 1.365.596.596-.114 1.011-.664 1.02-1.258.006-.314-.477-.314-.482-.003ZM15.075 17.261a1.043 1.043 0 1 0 0-2.086 1.043 1.043 0 0 0 0 2.086Z"}),(0,t.jsx)("path",{fill:"#0C75AF",d:"M15.876 16.218a.81.81 0 0 1-.512.749.814.814 0 0 1-.91-.24.804.804 0 0 1 .753-1.301.808.808 0 0 1 .669.792c.005.312.488.312.483 0a1.308 1.308 0 0 0-.867-1.214 1.288 1.288 0 0 0-1.4.389 1.293 1.293 0 0 0-.119 1.487c.283.468.831.698 1.365.596.596-.113 1.011-.664 1.02-1.258.006-.311-.477-.311-.482 0Z"})]}),ho=fo;var yo=a(71982);const vo=e=>(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 32 24",...e,children:[(0,t.jsx)("rect",{width:31,height:23,x:.5,y:.5,fill:"#4945FF",stroke:"#4945FF",rx:2.5}),(0,t.jsx)("path",{fill:"#fff",d:"M15.328 10.54h1.723c.012-.089.012-.165.012-.253 0-1.676-1.471-2.959-3.41-2.959-2.696 0-4.647 2.22-4.647 5.344 0 2.15 1.383 3.545 3.504 3.545 2.045 0 3.597-1.154 3.967-2.936h-1.752c-.276.826-1.102 1.371-2.063 1.371-1.137 0-1.846-.802-1.846-2.103 0-2.08 1.19-3.65 2.725-3.65 1.037 0 1.746.62 1.787 1.558v.082ZM21.053 16l1.488-6.943h2.531l.31-1.512H18.54l-.31 1.512h2.53L19.272 16h1.782Z"})]}),Ht=vo;var xo=a(21639),it=a(52078),bo=a(98659),Co=a(19170),wt=a(49167),ut=a(5046),Kt=a(52076);const Ao=e=>(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 32 24",...e,children:[(0,t.jsx)("path",{fill:"#FDF4DC",stroke:"#FAE7B9",d:"M.5 3A2.5 2.5 0 0 1 3 .5h26A2.5 2.5 0 0 1 31.5 3v18a2.5 2.5 0 0 1-2.5 2.5H3A2.5 2.5 0 0 1 .5 21V3Z"}),(0,t.jsx)("path",{fill:"#D9822F",d:"M20.158 11.995c0-.591-.463-1.073-1.045-1.11H13.53V9.245a2.05 2.05 0 0 1 2.046-2.049c1.13 0 2.048.784 2.049 1.913 0 .24.194.433.433.433h.33a.433.433 0 0 0 .433-.433C18.82 7.32 17.365 5.999 15.577 6a3.246 3.246 0 0 0-3.241 3.244v1.642h-.223c-.615 0-1.113.499-1.113 1.114v4.887c.001.615.5 1.113 1.115 1.113l6.93-.003c.616 0 1.114-.5 1.114-1.115l-.001-4.887Z"})]}),To=Ao;var Mo=a(91086);const jo=e=>(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 32 24",...e,children:[(0,t.jsx)("rect",{width:31,height:23,x:.5,y:.5,fill:"#EAF5FF",stroke:"#B8E1FF",rx:2.5}),(0,t.jsx)("path",{fill:"#0C75AF",fillRule:"evenodd",d:"M19.286 9.286v-.857a.397.397 0 0 0-.138-.302A.465.465 0 0 0 18.82 8h-8.357a.465.465 0 0 0-.326.127.397.397 0 0 0-.138.302v.857c0 .116.046.216.138.301.092.085.2.127.326.127h8.357a.465.465 0 0 0 .327-.127.397.397 0 0 0 .138-.301Zm2.785 2.713v.857a.397.397 0 0 1-.137.301.465.465 0 0 1-.327.128H10.464a.465.465 0 0 1-.326-.128.397.397 0 0 1-.138-.301v-.857c0-.116.046-.217.138-.302a.465.465 0 0 1 .326-.127h11.143c.126 0 .235.043.327.127a.397.397 0 0 1 .137.302Zm-1.857 3.574v.857a.397.397 0 0 1-.137.302.465.465 0 0 1-.327.127h-9.286a.465.465 0 0 1-.326-.127.397.397 0 0 1-.138-.302v-.857c0-.116.046-.216.138-.301a.465.465 0 0 1 .326-.127h9.286c.126 0 .235.042.326.127a.397.397 0 0 1 .138.301Z",clipRule:"evenodd"})]}),So=jo,Fo=e=>(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 32 24",...e,children:[(0,t.jsx)("rect",{width:31,height:23,x:.5,y:.5,fill:"#0C75AF",stroke:"#0C75AF",rx:2.5}),(0,t.jsx)("path",{fill:"#fff",d:"M8.523 13.586c.106 1.64 1.418 2.63 3.34 2.63 2.098 0 3.516-1.113 3.516-2.788 0-1.143-.65-1.846-2.086-2.297l-.867-.27c-.797-.252-1.137-.597-1.137-1.066 0-.598.633-1.031 1.459-1.031.873 0 1.512.474 1.617 1.183h1.67c-.053-1.54-1.36-2.619-3.217-2.619-1.91 0-3.328 1.131-3.328 2.678 0 1.09.715 1.922 1.963 2.309l.879.275c.914.287 1.266.592 1.266 1.084 0 .662-.657 1.107-1.606 1.107-.914 0-1.635-.469-1.758-1.195h-1.71ZM20.107 16l1.489-6.943h2.531l.31-1.512h-6.843l-.31 1.512h2.53L18.326 16h1.781Z"})]}),No=Fo;var Qt=a(45559),Lo=a(75652);const Io=e=>(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 24 24",...e,children:[(0,t.jsx)("path",{fill:"#181826",d:"m10.614 17.796.878-2.01a7.742 7.742 0 0 1 3.94-3.992l2.416-1.072c.768-.341.768-1.458 0-1.8l-2.34-1.038a7.747 7.747 0 0 1-3.997-4.125l-.89-2.142a.946.946 0 0 0-1.758 0l-.889 2.142a7.747 7.747 0 0 1-3.997 4.125l-2.34 1.039c-.768.34-.768 1.458 0 1.799l2.415 1.072a7.742 7.742 0 0 1 3.94 3.991l.878 2.01a.946.946 0 0 0 1.744 0Zm8.787 4.894.247-.566a4.365 4.365 0 0 1 2.221-2.25l.76-.339a.53.53 0 0 0 0-.963l-.717-.319a4.368 4.368 0 0 1-2.253-2.326l-.254-.611a.507.507 0 0 0-.942 0l-.254.61a4.368 4.368 0 0 1-2.253 2.327l-.718.32a.53.53 0 0 0 0 .962l.76.338a4.365 4.365 0 0 1 2.222 2.251l.247.566c.18.414.754.414.934 0Z"}),(0,t.jsx)("path",{fill:"#181826",d:"m10.614 17.796.878-2.01a7.742 7.742 0 0 1 3.94-3.992l2.416-1.072c.768-.341.768-1.458 0-1.8l-2.34-1.038a7.747 7.747 0 0 1-3.997-4.125l-.89-2.142a.946.946 0 0 0-1.758 0l-.889 2.142a7.747 7.747 0 0 1-3.997 4.125l-2.34 1.039c-.768.34-.768 1.458 0 1.799l2.415 1.072a7.742 7.742 0 0 1 3.94 3.991l.878 2.01a.946.946 0 0 0 1.744 0Zm8.787 4.894.247-.566a4.365 4.365 0 0 1 2.221-2.25l.76-.339a.53.53 0 0 0 0-.963l-.717-.319a4.368 4.368 0 0 1-2.253-2.326l-.254-.611a.507.507 0 0 0-.942 0l-.254.61a4.368 4.368 0 0 1-2.253 2.327l-.718.32a.53.53 0 0 0 0 .962l.76.338a4.365 4.365 0 0 1 2.222 2.251l.247.566c.18.414.754.414.934 0Z"})]}),$o=Io;var Oo=a(36481),Eo=a(50612),De=a(39404),b=a(40754),mt=a(56336),E=a(2600),Do=a(94710),Jt=a(48940),Ro=a(14311),Ye=a(82437),ko=a(412),Xt=a(89102),Po=a(5409),rt=a(21835),qt=a(35336),Bo=a(71547),C=a(12083),Uo=a(17024),Y=a(58692),pt=a(71210),_t=a(70653),Wo=a(5790),en=a(35223),zo=a(45635);const d=e=>`${b.p}.${e}`,tn=(0,f.createContext)(),Re=()=>(0,f.useContext)(tn),nn=f.createContext(),Ge=()=>(0,f.useContext)(nn),Zo=()=>{const{components:e,componentsGroupedByCategory:n,contentTypes:s,isInDevelopmentMode:i,sortedContentTypesList:o,modifiedData:l,initialData:r}=Re(),c=(0,F.hN)(),{trackUsage:u}=(0,F.z1)(),[p,g]=(0,f.useState)(""),{onOpenModalCreateSchema:m,onOpenModalEditCategory:O}=Ge(),{locale:S}=(0,w.A)(),{startsWith:A}=(0,F.U2)(S,{sensitivity:"base"}),j=(0,F.QM)(S,{sensitivity:"base"}),z=!Object.keys(s).some(Z=>s[Z].isTemporary===!0)&&!Object.keys(e).some(Z=>e[Z].isTemporary===!0)&&mt(l,r),M=()=>{z?(u("willCreateContentType"),m({modalType:"contentType",kind:"collectionType",actionType:"create",forTarget:"contentType"})):B()},I=()=>{z?(u("willCreateSingleType"),m({modalType:"contentType",kind:"singleType",actionType:"create",forTarget:"contentType"})):B()},P=()=>{z?(u("willCreateComponent"),m({modalType:"component",kind:null,actionType:"create",forTarget:"component"})):B()},B=()=>{c({type:"info",message:{id:d("notification.info.creating.notSaved"),defaultMessage:"Please save your work before creating a new collection type or component"}})},X=Object.entries(n).map(([Z,ve])=>({name:Z,title:Z,isEditable:i,onClickEdit(q,Ce){q.stopPropagation(),z?O(Ce.name):B()},links:ve.map(q=>({name:q.uid,to:`/plugins/${b.p}/component-categories/${Z}/${q.uid}`,title:q.schema.displayName})).sort((q,Ce)=>j.compare(q.title,Ce.title))})).sort((Z,ve)=>j.compare(Z.title,ve.title)),ue=o.filter(Z=>Z.visible);return{menu:[{name:"models",title:{id:`${d("menu.section.models.name")}`,defaultMessage:"Collection Types"},customLink:i&&{id:`${d("button.model.create")}`,defaultMessage:"Create new collection type",onClick:M},links:ue.filter(Z=>Z.kind==="collectionType")},{name:"singleTypes",title:{id:`${d("menu.section.single-types.name")}`,defaultMessage:"Single Types"},customLink:i&&{id:`${d("button.single-types.create")}`,defaultMessage:"Create new single type",onClick:I},links:ue.filter(Z=>Z.kind==="singleType")},{name:"components",title:{id:`${d("menu.section.components.name")}`,defaultMessage:"Components"},customLink:i&&{id:`${d("button.component.create")}`,defaultMessage:"Create a new component",onClick:P},links:X}].map(Z=>Z.links.some(q=>Array.isArray(q.links))?{...Z,links:Z.links.map(q=>{const Ce=q.links.filter(ge=>A(ge.title,p));return Ce.length===0?null:{...q,links:Ce.sort((ge,oe)=>j.compare(ge.title,oe.title))}}).filter(Boolean)}:{...Z,links:Z.links.filter(q=>A(q.title,p)).sort((q,Ce)=>j.compare(q.title,Ce.title))}),searchValue:p,onSearchChange:g}},Vo=()=>{const{menu:e,searchValue:n,onSearchChange:s}=Zo(),{formatMessage:i}=(0,w.A)();return(0,t.jsxs)(wn.C,{ariaLabel:i({id:`${d("plugin.name")}`,defaultMessage:"Content-Types Builder"}),children:[(0,t.jsx)(Kn.X,{searchable:!0,value:n,onClear:()=>s(""),onChange:o=>s(o.target.value),label:i({id:`${d("plugin.name")}`,defaultMessage:"Content-Types Builder"}),searchLabel:i({id:"global.search",defaultMessage:"Search"})}),(0,t.jsx)(Qn.w,{children:e.map(o=>(0,t.jsxs)(f.Fragment,{children:[(0,t.jsx)(Jn.L,{label:i({id:o.title.id,defaultMessage:o.title.defaultMessage}),collapsable:!0,badgeLabel:o.links.length.toString(),children:o.links.map(l=>l.links?(0,t.jsx)(na,{label:De(l.title),children:l.links.map(r=>(0,t.jsx)(kt.u,{as:Rt.k2,to:r.to,active:r.active,isSubSectionChild:!0,children:De(i({id:r.name,defaultMessage:r.title}))},r.name))},l.name):(0,t.jsx)(kt.u,{as:Rt.k2,to:l.to,active:l.active,children:De(i({id:l.name,defaultMessage:l.title}))},l.name))}),o.customLink&&(0,t.jsx)(h.a,{paddingLeft:7,children:(0,t.jsx)(k.Q,{onClick:o.customLink.onClick,startIcon:(0,t.jsx)(D.I,{as:Be.A,width:(0,F.a8)(8),height:(0,F.a8)(8)}),marginTop:2,children:i({id:o.customLink.id,defaultMessage:o.customLink.defaultMessage})})})]},o.name))})]})},an=e=>e.kind==="collectionType"&&(e.restrictRelationsTo===null||Array.isArray(e.restrictRelationsTo)&&e.restrictRelationsTo.length>0),gt=(e,n)=>e.find(({name:s})=>s===n),Yo=[{label:"All",children:[{label:"images (JPEG, PNG, GIF, SVG, TIFF, ICO, DVU)",value:"images"},{label:"videos (MPEG, MP4, Quicktime, WMV, AVI, FLV)",value:"videos"},{label:"audios (MP3, WAV, OGG)",value:"audios"},{label:"files (CSV, ZIP, PDF, Excel, JSON, ...)",value:"files"}]}],Go=({intlLabel:e,name:n,onChange:s,value:i=null})=>{const{formatMessage:o}=(0,w.A)(),l=i===null||i?.length===0?o({id:"global.none",defaultMessage:"None"}):[...i].sort().map(c=>De(c)).join(", "),r=e.id?o({id:e.id,defaultMessage:e.defaultMessage}):n;return(0,t.jsx)(R.B,{id:"select1",label:r,customizeContent:()=>l,onChange:c=>{c.length>0?s({target:{name:n,value:c,type:"allowed-types-select"}}):s({target:{name:n,value:null,type:"allowed-types-select"}})},options:Yo,value:i||[]})},sn={biginteger:Xe.A,blocks:ho,boolean:yo.A,collectionType:Ht,component:xo.A,contentType:Ht,date:it.A,datetime:it.A,decimal:Xe.A,dynamiczone:bo.A,email:Co.A,enum:wt.A,enumeration:wt.A,file:ut.A,files:ut.A,float:Xe.A,integer:Xe.A,json:Kt.A,JSON:Kt.A,media:ut.A,number:Xe.A,password:To,relation:Mo.A,richtext:So,singleType:No,string:Qt.A,text:Qt.A,time:it.A,timestamp:it.A,uid:Lo.A},Ho=(0,Me.Ay)(h.a)`
  svg {
    height: 100%;
    width: 100%;
  }
`,qe=({type:e,customField:n=null,...s})=>{const i=(0,F.AC)();let o=sn[e];if(n){const r=i.get(n)?.icon;r&&(o=r)}return sn[e]?(0,t.jsx)(Ho,{height:(0,F.a8)(24),width:(0,F.a8)(32),shrink:0,...s,"aria-hidden":!0,children:(0,t.jsx)(h.a,{as:o})}):null},on=(0,Me.Ay)(h.a)`
  width: 100%;
  height: 100%;
  border: 1px solid ${({theme:e})=>e.colors.neutral200};
  text-align: left;
  &:hover {
    background: ${({theme:e})=>e.colors.primary100};
    border: 1px solid ${({theme:e})=>e.colors.primary200};
  }
`,wo=[],Ko=()=>(0,t.jsx)(x.s,{grow:1,justifyContent:"flex-end",children:(0,t.jsxs)(x.s,{gap:1,hasRadius:!0,background:"alternative100",padding:`${2/16}rem ${4/16}rem`,children:[(0,t.jsx)(D.I,{width:`${10/16}rem`,height:`${10/16}rem`,as:$o,color:"alternative600"}),(0,t.jsx)(T.o,{textColor:"alternative600",variant:"sigma",children:"New"})]})}),Qo=({type:e="text"})=>{const{formatMessage:n}=(0,w.A)(),{onClickSelectField:s}=Ge(),i=()=>{s({attributeType:e,step:e==="component"?"1":null})};return(0,t.jsx)(on,{padding:4,as:"button",hasRadius:!0,type:"button",onClick:i,children:(0,t.jsxs)(x.s,{children:[(0,t.jsx)(qe,{type:e}),(0,t.jsxs)(h.a,{paddingLeft:4,width:"100%",children:[(0,t.jsxs)(x.s,{justifyContent:"space-between",children:[(0,t.jsx)(T.o,{fontWeight:"bold",children:n({id:d(`attribute.${e}`),defaultMessage:e})}),wo.includes(e)&&(0,t.jsx)(Ko,{})]}),(0,t.jsx)(x.s,{children:(0,t.jsx)(T.o,{variant:"pi",textColor:"neutral600",children:n({id:d(`attribute.${e}.description`),defaultMessage:"A type for modeling data"})})})]})]})})},Jo=({attributes:e})=>(0,t.jsx)(W.r,{tagName:"button",children:(0,t.jsx)(x.s,{direction:"column",alignItems:"stretch",gap:8,children:e.map((n,s)=>(0,t.jsx)(J.x,{gap:3,children:n.map(i=>(0,t.jsx)(_.E,{col:6,children:(0,t.jsx)(Qo,{type:i})},i))},s))})}),Xo=({customFieldUid:e,customField:n})=>{const{type:s,intlLabel:i,intlDescription:o}=n,{formatMessage:l}=(0,w.A)(),{onClickSelectCustomField:r}=Ge(),c=()=>{r({attributeType:s,customFieldUid:e})};return(0,t.jsx)(on,{padding:4,as:"button",hasRadius:!0,type:"button",onClick:c,children:(0,t.jsxs)(x.s,{children:[(0,t.jsx)(qe,{type:s,customField:e}),(0,t.jsxs)(h.a,{paddingLeft:4,children:[(0,t.jsx)(x.s,{children:(0,t.jsx)(T.o,{fontWeight:"bold",children:l(i)})}),(0,t.jsx)(x.s,{children:(0,t.jsx)(T.o,{variant:"pi",textColor:"neutral600",children:l(o)})})]})]})})},qo=(0,Me.Ay)(h.a)`
  background: ${({theme:e})=>`linear-gradient(180deg, rgba(234, 234, 239, 0) 0%, ${e.colors.neutral150} 100%)`};
  opacity: 0.33;
`,_o=()=>(0,t.jsx)(x.s,{wrap:"wrap",gap:4,children:[...Array(4)].map((e,n)=>(0,t.jsx)(qo,{height:"138px",width:"375px",hasRadius:!0},`empty-card-${n}`))}),ei=()=>{const{formatMessage:e}=(0,w.A)();return(0,t.jsxs)(h.a,{position:"relative",children:[(0,t.jsx)(_o,{}),(0,t.jsx)(h.a,{position:"absolute",top:6,width:"100%",children:(0,t.jsxs)(x.s,{alignItems:"center",justifyContent:"center",direction:"column",children:[(0,t.jsx)(D.I,{as:Oo.A,color:"",width:"160px",height:"88px"}),(0,t.jsx)(h.a,{paddingTop:6,paddingBottom:4,children:(0,t.jsxs)(h.a,{textAlign:"center",children:[(0,t.jsx)(T.o,{variant:"delta",as:"p",textColor:"neutral600",children:e({id:d("modalForm.empty.heading"),defaultMessage:"Nothing in here yet."})}),(0,t.jsx)(h.a,{paddingTop:4,children:(0,t.jsx)(T.o,{variant:"delta",as:"p",textColor:"neutral600",children:e({id:d("modalForm.empty.sub-heading"),defaultMessage:"Find what you are looking for through a wide range of extensions."})})})]})}),(0,t.jsx)(ae.z,{to:`/marketplace?${Po.stringify({categories:["Custom fields"]})}`,variant:"secondary",startIcon:(0,t.jsx)(Be.A,{}),children:e({id:d("modalForm.empty.button"),defaultMessage:"Add custom fields"})})]})})]})},ti=()=>{const{formatMessage:e}=(0,w.A)(),n=(0,F.AC)(),s=Object.entries(n.getAll());if(!s.length)return(0,t.jsx)(ei,{});const i=s.sort((o,l)=>o[1].name>l[1].name?1:-1);return(0,t.jsx)(W.r,{tagName:"button",children:(0,t.jsxs)(x.s,{direction:"column",alignItems:"stretch",gap:3,children:[(0,t.jsx)(J.x,{gap:3,children:i.map(([o,l])=>(0,t.jsx)(_.E,{col:6,children:(0,t.jsx)(Xo,{customFieldUid:o,customField:l},o)},o))}),(0,t.jsx)(Pt.N,{href:"https://docs.strapi.io/developer-docs/latest/development/custom-fields.html",isExternal:!0,children:e({id:d("modalForm.tabs.custom.howToLink"),defaultMessage:"How to add custom fields"})})]})})},ni=({attributes:e,forTarget:n,kind:s})=>{const{formatMessage:i}=(0,w.A)(),o=d("modalForm.tabs.default"),l=d("modalForm.tabs.custom"),r=n.includes("component")?"component":s,c=d(`modalForm.sub-header.chooseAttribute.${r}`);return(0,t.jsx)(Ee.c,{padding:7,children:(0,t.jsxs)(le.f,{label:i({id:d("modalForm.tabs.label"),defaultMessage:"Default and Custom types tabs"}),id:"attribute-type-tabs",variant:"simple",children:[(0,t.jsxs)(x.s,{justifyContent:"space-between",children:[(0,t.jsx)(T.o,{variant:"beta",as:"h2",children:i({id:c,defaultMessage:"Select a field"})}),(0,t.jsxs)(ie.t,{children:[(0,t.jsx)(ie.o,{children:i({id:o,defaultMessage:"Default"})}),(0,t.jsx)(ie.o,{children:i({id:l,defaultMessage:"Custom"})})]})]}),(0,t.jsx)(h.a,{paddingBottom:6,children:(0,t.jsx)(te.c,{})}),(0,t.jsxs)(se.T,{children:[(0,t.jsx)(se.K,{children:(0,t.jsx)(Jo,{attributes:e})}),(0,t.jsx)(se.K,{children:(0,t.jsx)(ti,{})})]})]})})},ai=({intlLabel:e,name:n,options:s,onChange:i,value:o=null})=>{const{formatMessage:l}=(0,w.A)(),r=e.id?l({id:e.id,defaultMessage:e.defaultMessage},{...e.values}):n,c=u=>{let p="";u==="true"&&(p=!0),u==="false"&&(p=!1),i({target:{name:n,value:p,type:"select-default-boolean"}})};return(0,t.jsx)(ne.l,{label:r,id:n,name:n,onChange:c,value:(o===null?"":o).toString(),children:s.map(({metadatas:{intlLabel:u,disabled:p,hidden:g},key:m,value:O})=>(0,t.jsx)(Fe.c,{value:O,disabled:p,hidden:g,children:u.defaultMessage},m))})},si=(0,Me.Ay)(x.s)`
  position: relative;
  align-items: stretch;

  label {
    border-radius: 4px;
    max-width: 50%;
    cursor: pointer;
    user-select: none;
    flex: 1;
    ${(0,Ne.id)()}
  }

  input {
    position: absolute;
    opacity: 0;
  }

  .option {
    height: 100%;
    border-radius: 4px;
    border: 1px solid ${({theme:e})=>e.colors.neutral200};
    will-change: transform, opacity;
    background: ${({theme:e})=>e.colors.neutral0};

    .checkmark {
      position: relative;
      display: block;
      will-change: transform;
      background: ${({theme:e})=>e.colors.neutral0};
      width: ${({theme:e})=>e.spaces[5]};
      height: ${({theme:e})=>e.spaces[5]};
      border: solid 1px ${({theme:e})=>e.colors.neutral300};
      border-radius: 50%;

      &:before,
      &:after {
        content: '';
        display: block;
        border-radius: 50%;
        width: ${({theme:e})=>e.spaces[3]};
        height: ${({theme:e})=>e.spaces[3]};
        position: absolute;
        top: 3px;
        left: 3px;
      }

      &:after {
        transform: scale(0);
        transition: inherit;
        will-change: transform;
      }
    }
  }

  .container input:checked ~ div {
    background: ${({theme:e})=>e.colors.primary100};
    ${T.o} {
      color: ${({theme:e})=>e.colors.primary600};
    }
    border: 1px solid ${({theme:e})=>e.colors.primary200};
    .checkmark {
      border: solid 1px ${({theme:e})=>e.colors.primary600};
      &::after {
        background: ${({theme:e})=>e.colors.primary600};
        transform: scale(1);
      }
    }
  }
`,ft=({intlLabel:e,name:n,onChange:s,radios:i=[],value:o})=>{const{formatMessage:l}=(0,w.A)();return(0,t.jsxs)(x.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,t.jsx)(T.o,{variant:"pi",fontWeight:"bold",textColor:"neutral800",htmlFor:n,as:"label",children:l(e)}),(0,t.jsx)(si,{gap:4,alignItems:"stretch",children:i.map(r=>(0,t.jsxs)("label",{htmlFor:r.value.toString(),className:"container",children:[(0,t.jsx)("input",{id:r.value.toString(),name:n,className:"option-input",checked:r.value===o,value:r.value,onChange:s,type:"radio"},r.value),(0,t.jsx)(h.a,{className:"option",padding:4,children:(0,t.jsxs)(x.s,{children:[(0,t.jsx)(h.a,{paddingRight:4,children:(0,t.jsx)("span",{className:"checkmark"})}),(0,t.jsxs)(x.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,t.jsx)(T.o,{fontWeight:"bold",children:l(r.title)}),(0,t.jsx)(T.o,{variant:"pi",textColor:"neutral600",children:l(r.description)})]})]})})]},r.value))})]})},oi=({onChange:e,name:n,intlLabel:s,...i})=>{const o=l=>{const r=l.target.value!=="false";e({target:{name:n,value:r,type:"boolean-radio-group"}})};return(0,t.jsx)(ft,{...i,name:n,onChange:o,intlLabel:s})},ii=({error:e,intlLabel:n,modifiedData:s,name:i,onChange:o,value:l=null})=>{const{formatMessage:r}=(0,w.A)(),[c,u]=(0,f.useState)(!!l||l===0),p=n.id?r({id:n.id,defaultMessage:n.defaultMessage},{...n.values}):i,g=s.type==="biginteger"?"text":"number",m=!s.type,O=e?r({id:e,defaultMessage:e}):"";return(0,t.jsxs)(x.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,t.jsx)(pe.S,{id:i,name:i,onValueChange:S=>{o({target:{name:i,value:S?g==="text"?"0":0:null}}),u(z=>!z)},value:c,children:p}),c&&(0,t.jsx)(h.a,{paddingLeft:6,style:{maxWidth:"200px"},children:g==="text"?(0,t.jsx)(xe.k,{label:"","aria-label":p,disabled:m,error:O,id:i,name:i,onChange:o,value:l===null?"":l}):(0,t.jsx)(at.Q,{"aria-label":p,disabled:m,error:O,id:i,name:i,onValueChange:S=>{o({target:{name:i,value:S,type:g}})},value:l||0})})]})},ri=({onChange:e,...n})=>{const s=(0,F.hN)(),i=o=>{s({type:"info",message:{id:d("contentType.kind.change.warning"),defaultMessage:"You just changed the kind of a content type: API will be reset (routes, controllers, and services will be overwritten)."}}),e(o)};return(0,t.jsx)(ft,{...n,onChange:i})},li=({description:e,disabled:n=!1,intlLabel:s,isCreating:i,name:o,onChange:l,value:r=!1})=>{const{formatMessage:c}=(0,w.A)(),[u,p]=(0,f.useState)(!1),g=s.id?c({id:s.id,defaultMessage:s.defaultMessage},{...s.values}):o,m=e?c({id:e.id,defaultMessage:e.defaultMessage},{...e.values}):"",O=()=>p(j=>!j),S=()=>{l({target:{name:o,value:!1}}),O()},A=({target:{checked:j}})=>{if(!j&&!i){O();return}l({target:{name:o,value:j}})};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(pe.S,{checked:r,disabled:n,hint:m,name:o,onChange:A,children:g}),(0,t.jsx)(F.TM,{isOpen:u,onToggleDialog:O,onConfirm:S,bodyText:{id:d("popUpWarning.draft-publish.message"),defaultMessage:"If you disable the draft & publish, your drafts will be deleted."},leftButtonText:{id:"components.popUpWarning.button.cancel",defaultMessage:"No, cancel"},rightButtonText:{id:d("popUpWarning.draft-publish.button.confirm"),defaultMessage:"Yes, disable"}})]})},di=({categoryName:e,deleteCategory:n,deleteComponent:s,deleteContentType:i,isAttributeModal:o,isCustomFieldModal:l,isComponentAttribute:r,isComponentToDzModal:c,isContentTypeModal:u,isCreatingComponent:p,isCreatingComponentAttribute:g,isCreatingComponentInDz:m,isCreatingComponentWhileAddingAField:O,isCreatingContentType:S,isCreatingDz:A,isComponentModal:j,isDzAttribute:z,isEditingAttribute:M,isEditingCategory:I,isInFirstComponentStep:P,onSubmitAddComponentAttribute:B,onSubmitAddComponentToDz:X,onSubmitCreateContentType:ue,onSubmitCreateComponent:be,onSubmitCreateDz:Z,onSubmitEditAttribute:ve,onSubmitEditCategory:q,onSubmitEditComponent:Ce,onSubmitEditContentType:ge,onSubmitEditCustomFieldAttribute:oe,onSubmitEditDz:ke,onClickFinish:me})=>{const{formatMessage:Q}=(0,w.A)();return c?m?(0,t.jsx)(ee.$,{variant:"secondary",type:"submit",onClick:N=>{N.preventDefault(),X(N,!0)},startIcon:(0,t.jsx)(Be.A,{}),children:Q({id:d("form.button.add-first-field-to-created-component"),defaultMessage:"Add first field to the component"})}):(0,t.jsx)(ee.$,{variant:"default",type:"submit",onClick:N=>{N.preventDefault(),X(N,!1)},children:Q({id:"global.finish",defaultMessage:"Finish"})}):o&&z&&!A?(0,t.jsx)(ee.$,{variant:"default",type:"submit",onClick:N=>{N.preventDefault(),me(),ke(N,!1)},children:Q({id:"global.finish",defaultMessage:"Finish"})}):o&&z&&A?(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(ee.$,{variant:"secondary",type:"submit",onClick:N=>{N.preventDefault(),Z(N,!0)},startIcon:(0,t.jsx)(Be.A,{}),children:Q({id:d("form.button.add-components-to-dynamiczone"),defaultMessage:"Add components to the zone"})})}):o&&r?P?(0,t.jsx)(ee.$,{variant:"secondary",type:"submit",onClick:N=>{N.preventDefault(),B(N,!0)},children:Q(g?{id:d("form.button.configure-component"),defaultMessage:"Configure the component"}:{id:d("form.button.select-component"),defaultMessage:"Configure the component"})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ee.$,{variant:"secondary",type:"submit",onClick:N=>{N.preventDefault(),B(N,!0)},startIcon:(0,t.jsx)(Be.A,{}),children:Q(O?{id:d("form.button.add-first-field-to-created-component"),defaultMessage:"Add first field to the component"}:{id:d("form.button.add-field"),defaultMessage:"Add another field"})}),(0,t.jsx)(ee.$,{variant:"default",type:"button",onClick:N=>{N.preventDefault(),me(),B(N,!1)},children:Q({id:"global.finish",defaultMessage:"Finish"})})]}):o&&!r&&!z?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ee.$,{type:M?"button":"submit",variant:"secondary",onClick:N=>{N.preventDefault(),ve(N,!0)},startIcon:(0,t.jsx)(Be.A,{}),children:Q({id:d("form.button.add-field"),defaultMessage:"Add another field"})}),(0,t.jsx)(ee.$,{type:M?"submit":"button",variant:"default",onClick:N=>{N.preventDefault(),me(),ve(N,!1)},children:Q({id:"global.finish",defaultMessage:"Finish"})})]}):u?(0,t.jsxs)(t.Fragment,{children:[!S&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ee.$,{type:"button",variant:"danger",onClick:N=>{N.preventDefault(),i()},children:Q({id:"global.delete",defaultMessage:"Delete"})}),(0,t.jsx)(ee.$,{type:"submit",variant:"default",onClick:N=>{N.preventDefault(),ge(N,!1)},children:Q({id:"global.finish",defaultMessage:"Finish"})})]}),S&&(0,t.jsx)(ee.$,{type:"submit",variant:"secondary",onClick:N=>{N.preventDefault(),ue(N,!0)},children:Q({id:"global.continue",defaultMessage:"Continue"})})]}):j?(0,t.jsxs)(t.Fragment,{children:[!p&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ee.$,{type:"button",variant:"danger",onClick:N=>{N.preventDefault(),s()},children:Q({id:"global.delete",defaultMessage:"Delete"})}),(0,t.jsx)(ee.$,{type:"submit",variant:"default",onClick:N=>{N.preventDefault(),Ce(N,!1)},children:Q({id:"global.finish",defaultMessage:"Finish"})})]}),p&&(0,t.jsx)(ee.$,{type:"submit",variant:"secondary",onClick:N=>{N.preventDefault(),be(N,!0)},children:Q({id:"global.continue",defaultMessage:"Continue"})})]}):I?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ee.$,{type:"button",variant:"danger",onClick:N=>{N.preventDefault(),e&&n(e)},children:Q({id:"global.delete",defaultMessage:"Delete"})}),(0,t.jsx)(ee.$,{type:"submit",variant:"default",onClick:N=>{N.preventDefault(),q(N)},children:Q({id:"global.finish",defaultMessage:"finish"})})]}):l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ee.$,{type:M?"button":"submit",variant:"secondary",onClick:N=>{N.preventDefault(),oe(N,!0)},startIcon:(0,t.jsx)(Be.A,{}),children:Q({id:d("form.button.add-field"),defaultMessage:"Add another field"})}),(0,t.jsx)(ee.$,{type:M?"submit":"button",variant:"default",onClick:N=>{N.preventDefault(),me(),oe(N,!1)},children:Q({id:"global.finish",defaultMessage:"Finish"})})]}):null},ci=({actionType:e=null,attributeName:n,attributeType:s,categoryName:i,contentTypeKind:o,dynamicZoneTarget:l,forTarget:r,modalType:c=null,targetUid:u,customFieldUid:p=null,showBackLink:g=!1})=>{const{formatMessage:m}=(0,w.A)(),{modifiedData:O}=Re(),{onOpenModalAddField:S}=Ge();let A="component",j=[];const z=O?.[r]?.[u]||O?.[r]||null,M=z?.schema.displayName;if(c==="contentType"&&(A=o),["component","editCategory"].includes(c||"")&&(A="component"),["component","contentType"].includes(c||"")){let P=d(`modalForm.component.header-${e}`);return c==="contentType"&&(P=d(`modalForm.${o}.header-create`)),e==="edit"&&(P=d("modalForm.header-edit")),(0,t.jsx)(Je.r,{children:(0,t.jsxs)(x.s,{children:[(0,t.jsx)(h.a,{children:(0,t.jsx)(qe,{type:A})}),(0,t.jsx)(h.a,{paddingLeft:3,children:(0,t.jsx)(T.o,{fontWeight:"bold",textColor:"neutral800",as:"h2",id:"title",children:m({id:P},{name:M})})})]})})}return j=[{label:M,info:{category:z?.category||null,name:z?.schema.displayName}}],c==="chooseAttribute"&&(A=["component","components"].includes(r)?"component":z.schema.kind),c==="addComponentToDynamicZone"&&(A="dynamiczone",j.push({label:l})),(c==="attribute"||c==="customField")&&(A=s,j.push({label:n})),c==="editCategory"&&(j=[{label:m({id:d("modalForm.header.categories"),defaultMessage:"Categories"})},{label:i}]),(0,t.jsx)(Je.r,{children:(0,t.jsxs)(x.s,{gap:3,children:[g&&(0,t.jsx)(Pt.N,{"aria-label":m({id:d("modalForm.header.back"),defaultMessage:"Back"}),startIcon:(0,t.jsx)(Bt.A,{}),onClick:()=>S({forTarget:r,targetUid:u}),href:"#back",isExternal:!1}),(0,t.jsx)(qe,{type:A,customField:p}),(0,t.jsx)(aa.B,{label:j.map(({label:P})=>P).join(","),children:j.map(({label:P,info:B},X,ue)=>{if(P=De(P),!P)return null;const be=`${P}.${X}`;return B?.category&&(P=`${P} (${De(B.category)} - ${De(B.name)})`),(0,t.jsx)(sa.m,{isCurrent:X===ue.length-1,children:P},be)})})]})})},ui=({modalType:e,forTarget:n,kind:s,actionType:i,step:o})=>{switch(e){case"chooseAttribute":return d(`modalForm.sub-header.chooseAttribute.${n?.includes("component")?"component":s||"collectionType"}`);case"attribute":return d(`modalForm.sub-header.attribute.${i}${o!=="null"&&o!==null&&i!=="edit"?".step":""}`);case"customField":return d(`modalForm.sub-header.attribute.${i}`);case"addComponentToDynamicZone":return d("modalForm.sub-header.addComponentToDynamicZone");default:return d("configurations")}},mi=({actionType:e,modalType:n,forTarget:s,kind:i,step:o,attributeType:l,attributeName:r,customField:c})=>{const{formatMessage:u}=(0,w.A)(),p=n==="customField"?c?.intlLabel:{id:d(`attribute.${l}`)};return(0,t.jsxs)(x.s,{direction:"column",alignItems:"flex-start",paddingBottom:2,gap:1,children:[(0,t.jsx)(T.o,{as:"h2",variant:"beta",children:u({id:ui({actionType:e,forTarget:s,kind:i,step:o,modalType:n}),defaultMessage:"Add new field"},{type:p?De(u(p)):"",name:De(r),step:o})}),(0,t.jsx)(T.o,{variant:"pi",textColor:"neutral600",children:u({id:d(`attribute.${l}.description`),defaultMessage:"A type for modeling data"})})]})},ht={alien:oa.A,apps:ia.A,archive:ra.A,arrowDown:la.A,arrowLeft:Bt.A,arrowRight:da.A,arrowUp:ca.A,attachment:ua.A,bell:ma.A,bold:pa.A,book:ga.A,briefcase:fa.A,brush:ha.A,bulletList:ya.A,calendar:va.A,car:xa.A,cast:ba.A,chartBubble:Ca.A,chartCircle:Aa.A,chartPie:Ta.A,check:Ma.A,clock:ja.A,cloud:Sa.A,code:Fa.A,cog:Na.A,collapse:La.A,command:Ia.A,connector:$a.A,crop:Oa.A,crown:Ea.A,cube:Da.A,cup:Ra.A,cursor:ka.A,dashboard:Pa.A,database:Ba.A,discuss:Ua.A,doctor:Wa.A,earth:za.A,emotionHappy:Za.A,emotionUnhappy:Va.A,envelop:Ya.A,exit:Ga.A,expand:Ha.A,eye:wa.A,feather:Ka.A,file:Qa.A,fileError:Ja.A,filePdf:Xa.A,filter:qa.A,folder:_a.A,gate:es.A,gift:ts.A,globe:ns.A,grid:as.A,handHeart:ss.A,hashtag:os.A,headphone:is.A,heart:rs.A,house:ls.A,information:ds.A,italic:cs.A,key:us.A,landscape:ms.A,layer:ps.A,layout:gs.A,lightbulb:fs.A,link:hs.A,lock:ys.A,magic:vs.A,manyToMany:Ut.A,manyToOne:Wt.A,manyWays:zt.A,medium:xs.A,message:bs.A,microphone:Cs.A,monitor:As.A,moon:Ts.A,music:Ms.A,oneToMany:Zt.A,oneToOne:Vt.A,oneWay:Yt.A,paint:js.A,paintBrush:Ss.A,paperPlane:Fs.A,pencil:Ns.A,phone:Ls.A,picture:Is.A,pin:$s.A,pinMap:Os.A,plane:Es.A,play:Ds.A,plus:Be.A,priceTag:Rs.A,puzzle:ks.A,question:Ps.A,quote:Bs.A,repeat:Us.A,restaurant:Ws.A,rocket:zs.A,rotate:Zs.A,scissors:Vs.A,search:Gt.A,seed:Ys.A,server:Gs.A,shield:Hs.A,shirt:ws.A,shoppingCart:Ks.A,slideshow:Qs.A,stack:Js.A,star:Xs.A,store:qs.A,strikeThrough:_s.A,sun:eo.A,television:to.A,thumbDown:no.A,thumbUp:ao.A,train:so.A,twitter:oo.A,typhoon:io.A,underline:ro.A,user:lo.A,volumeMute:co.A,volumeUp:uo.A,walk:mo.A,wheelchair:po.A,write:go.A},pi=(0,Me.Ay)(x.s)`
  label {
    ${(0,Ne.id)()}
    border-radius: ${({theme:e})=>e.borderRadius};
    border: 1px solid ${({theme:e})=>e.colors.neutral100};
  }
`,gi=({iconKey:e,name:n,onChange:s,isSelected:i,ariaLabel:o})=>(0,t.jsx)(st.D,{name:n,required:!1,children:(0,t.jsxs)(Rn.d,{htmlFor:e,id:`${e}-label`,children:[(0,t.jsxs)(kn.s,{children:[(0,t.jsx)(Pn.T,{type:"radio",id:e,name:n,checked:i,onChange:s,value:e,"aria-checked":i,"aria-labelledby":`${e}-label`}),o]}),(0,t.jsx)(x.s,{padding:2,cursor:"pointer",hasRadius:!0,background:i?"primary200":void 0,children:(0,t.jsx)(D.I,{as:ht[e],color:i?"primary600":"neutral300"})})]})}),fi=({intlLabel:e,name:n,onChange:s,value:i=""})=>{const{formatMessage:o}=(0,w.A)(),[l,r]=(0,f.useState)(!1),[c,u]=(0,f.useState)(""),p=Object.keys(ht),[g,m]=(0,f.useState)(p),O=(0,f.useRef)(null),S=(0,f.useRef)(null),A=()=>{r(!l)},j=({target:{value:I}})=>{u(I),m(()=>p.filter(P=>P.toLowerCase().includes(I.toLowerCase())))},z=()=>{A(),u(""),m(p)},M=()=>{s({target:{name:n,value:""}})};return(0,f.useEffect)(()=>{l&&S.current?.focus()},[l]),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(x.s,{justifyContent:"space-between",paddingBottom:2,children:[(0,t.jsx)(T.o,{variant:"pi",fontWeight:"bold",textColor:"neutral800",as:"label",children:o(e)}),(0,t.jsxs)(x.s,{gap:1,children:[l?(0,t.jsx)(Bn.S,{ref:S,name:"searchbar",size:"S",placeholder:o({id:d("ComponentIconPicker.search.placeholder"),defaultMessage:"Search for an icon"}),onBlur:()=>{c||A()},onChange:j,value:c,onClear:z,clearLabel:o({id:d("IconPicker.search.clear.label"),defaultMessage:"Clear the icon search"}),children:o({id:d("IconPicker.search.placeholder.label"),defaultMessage:"Search for an icon"})}):(0,t.jsx)(Dt.K,{ref:O,onClick:A,"aria-label":o({id:d("IconPicker.search.button.label"),defaultMessage:"Search icon button"}),icon:(0,t.jsx)(Gt.A,{}),noBorder:!0}),i&&(0,t.jsx)(Un.m,{description:o({id:d("IconPicker.remove.tooltip"),defaultMessage:"Remove the selected icon"}),children:(0,t.jsx)(Dt.K,{onClick:M,"aria-label":o({id:d("IconPicker.remove.button"),defaultMessage:"Remove the selected icon button"}),icon:(0,t.jsx)(Eo.A,{}),noBorder:!0})})]})]}),(0,t.jsx)(pi,{position:"relative",padding:1,background:"neutral100",hasRadius:!0,wrap:"wrap",gap:2,maxHeight:"126px",overflow:"auto",textAlign:"center",children:g.length>0?g.map(I=>(0,t.jsx)(gi,{iconKey:I,name:n,onChange:s,isSelected:I===i,ariaLabel:o({id:d("IconPicker.icon.label"),defaultMessage:"Select {icon} icon"},{icon:I})},I)):(0,t.jsx)(h.a,{padding:4,grow:2,children:(0,t.jsx)(T.o,{variant:"delta",textColor:"neutral600",textAlign:"center",children:o({id:d("IconPicker.emptyState.label"),defaultMessage:"No icon found"})})})})]})},hi=({description:e,error:n,intlLabel:s,modifiedData:i,name:o,onChange:l,value:r})=>{const{formatMessage:c}=(0,w.A)(),u=(0,f.useRef)(l),p=i?.displayName||"";(0,f.useEffect)(()=>{if(p){const S=(0,b.n)(p);try{const A=rt(S,2);u.current({target:{name:o,value:A}})}catch{u.current({target:{name:o,value:S}})}}else u.current({target:{name:o,value:""}})},[p,o]);const g=n?c({id:n,defaultMessage:n}):"",m=e?c({id:e.id,defaultMessage:e.defaultMessage},{...e.values}):"",O=c(s);return(0,t.jsx)(xe.k,{error:g,label:O,id:o,hint:m,name:o,onChange:l,value:r||""})},yi=({oneThatIsCreatingARelationWithAnother:e,target:n})=>{const{contentTypes:s,sortedContentTypesList:i}=Re(),o=(0,Ye.wA)(),l=i.filter(an),{plugin:r=null,schema:{displayName:c}={displayName:"error"}}=s?.[n]??{},u=({uid:p,plugin:g,title:m,restrictRelationsTo:O})=>()=>{const S=g?`${g}_${m}`:m;o({type:b.O,target:{value:p,oneThatIsCreatingARelationWithAnother:e,selectedContentTypeFriendlyName:S,targetContentTypeAllowedRelations:O}})};return(0,t.jsxs)(ot.bL,{children:[(0,t.jsx)(vi,{children:`${c} ${r?`(from: ${r})`:""}`}),(0,t.jsx)(ot.UC,{zIndex:5,children:l.map(({uid:p,title:g,restrictRelationsTo:m,plugin:O})=>(0,t.jsxs)(ot.q7,{onSelect:u({uid:p,plugin:O,title:g,restrictRelationsTo:m}),children:[g,"\xA0",O&&(0,t.jsxs)(t.Fragment,{children:["(from: ",O,")"]})]},p))})]})},vi=(0,Me.Ay)(ot.l9)`
  svg {
    width: ${6/16}rem;
    height: ${4/16}rem;
  }
`,rn=({disabled:e=!1,error:n,header:s,isMain:i=!1,name:o,onChange:l,oneThatIsCreatingARelationWithAnother:r="",target:c="",value:u=""})=>(0,t.jsxs)(h.a,{background:"neutral100",hasRadius:!0,borderColor:"neutral200",children:[(0,t.jsx)(x.s,{paddingTop:i?4:1,paddingBottom:i?3:1,justifyContent:"center",children:i?(0,t.jsx)(T.o,{variant:"pi",fontWeight:"bold",textColor:"neutral800",children:s}):(0,t.jsx)(yi,{target:c,oneThatIsCreatingARelationWithAnother:r})}),(0,t.jsx)(te.c,{background:"neutral200"}),(0,t.jsx)(h.a,{padding:4,children:(0,t.jsx)(F.ah,{disabled:e,error:n?.id||null,intlLabel:{id:d("form.attribute.item.defineRelation.fieldName"),defaultMessage:"Field name"},name:o,onChange:l,type:"text",value:u})})]}),xi=(0,Me.Ay)(h.a)`
  position: relative;
  width: 100%;
  &::before {
    content: '';
    position: absolute;
    top: calc(50% - 0px);
    height: 2px;
    width: 100%;
    background-color: ${({theme:e})=>e.colors.primary600};
    z-index: 0;
  }
`,bi=(0,Me.Ay)(h.a)`
  background: ${({theme:e,isSelected:n})=>e.colors[n?"primary100":"neutral0"]};
  border: 1px solid
    ${({theme:e,isSelected:n})=>e.colors[n?"primary700":"neutral200"]};
  border-radius: ${({theme:e})=>e.borderRadius};
  z-index: 1;
  svg {
    width: 1.5rem;
    height: 100%;
    path {
      fill: ${({theme:e,isSelected:n})=>e.colors[n?"primary700":"neutral500"]};
    }
  }
  &:disabled {
    cursor: not-allowed;
  }
`,Ci=(0,Me.Ay)(x.s)`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
`,Ai={oneWay:Yt.A,oneToOne:Vt.A,oneToMany:Zt.A,manyToOne:Wt.A,manyToMany:Ut.A,manyWay:zt.A},Ti=({naturePickerType:e,oneThatIsCreatingARelationWithAnother:n,relationType:s,target:i})=>{const o=(0,Ye.wA)(),{formatMessage:l}=(0,w.A)(),{contentTypes:r,modifiedData:c}=Re(),u=["oneWay","oneToOne","oneToMany","manyToOne","manyToMany","manyWay"],p=["oneWay","manyWay"],m=(e==="contentType"?E(c,[e,"schema","kind"],""):e)==="collectionType"?u:p,O=s==="manyToOne",S=E(r,[i,"schema","displayName"],"unknown"),A=O?S:n,j=O?n:S,z=rt(A,s==="manyToMany"?2:1),M=E(r,[i,"schema","restrictRelationsTo"],null),I=rt(j,["manyToMany","oneToMany","manyToOne","manyWay"].includes(s)?2:1);return s?(0,t.jsxs)(x.s,{style:{flex:1},children:[(0,t.jsx)(xi,{children:(0,t.jsx)(x.s,{paddingLeft:9,paddingRight:9,paddingTop:1,justifyContent:"center",children:(0,t.jsx)(W.r,{tagName:"button",children:(0,t.jsx)(x.s,{gap:3,children:m.map(P=>{const B=Ai[P],X=M===null||M.includes(P);return(0,t.jsx)(bi,{as:"button",isSelected:s===P,disabled:!X,onClick:()=>{X&&o({type:b.a,target:{oneThatIsCreatingARelationWithAnother:n,targetContentType:i,value:P}})},padding:2,type:"button",children:(0,t.jsx)(B,{},P)},P)})})})})}),(0,t.jsxs)(Ci,{justifyContent:"center",children:[(0,t.jsxs)(T.o,{children:[qt(z,{length:24}),"\xA0"]}),(0,t.jsxs)(T.o,{textColor:"primary600",children:[l({id:d(`relation.${s}`)}),"\xA0"]}),(0,t.jsx)(T.o,{children:qt(I,{length:24})})]})]}):null},Mi=({formErrors:e,mainBoxHeader:n,modifiedData:s,naturePickerType:i,onChange:o})=>{const l=(0,b.g)(s.relation,s.targetAttribute);return(0,t.jsxs)(x.s,{style:{position:"relative"},children:[(0,t.jsx)(rn,{isMain:!0,header:n,error:e?.name||null,name:"name",onChange:o,value:s?.name||""}),(0,t.jsx)(Ti,{naturePickerType:i,oneThatIsCreatingARelationWithAnother:n,relationType:l,target:s.target}),(0,t.jsx)(rn,{disabled:["oneWay","manyWay"].includes(l),error:e?.targetAttribute||null,name:"targetAttribute",onChange:o,oneThatIsCreatingARelationWithAnother:n,target:s.target,value:s?.targetAttribute||""})]})},ji=({error:e=null,intlLabel:n,name:s,onChange:i,value:o=void 0})=>{const{formatMessage:l}=(0,w.A)(),{allComponentsCategories:r}=Re(),[c,u]=(0,f.useState)(r),p=e?l({id:e,defaultMessage:e}):"",g=l(n),m=S=>{i({target:{name:s,value:S,type:"select-category"}})},O=S=>{u(A=>[...A,S]),m(S)};return(0,t.jsx)(Wn.nP,{error:p,id:s,label:g,name:s,onChange:m,onCreateOption:O,value:o,children:c.map(S=>(0,t.jsx)(zn.j,{value:S,children:S},S))})},Si=({error:e=null,intlLabel:n,isAddingAComponentToAnotherComponent:s,isCreating:i,isCreatingComponentWhileAddingAField:o,componentToCreate:l,name:r,onChange:c,targetUid:u,forTarget:p,value:g})=>{const{formatMessage:m}=(0,w.A)(),O=e?m({id:e,defaultMessage:e}):"",S=m(n),{componentsGroupedByCategory:A,componentsThatHaveOtherComponentInTheirAttributes:j}=Re(),z=["component","components"].includes(p);let M=Object.entries(A).reduce((I,P)=>{const[B,X]=P,ue=X.map(be=>({uid:be.uid,label:be.schema.displayName,categoryName:B}));return[...I,...ue]},[]);return s&&(M=M.filter(I=>!j.includes(I.uid))),z&&(M=M.filter(I=>I.uid!==u)),o&&(M=[{uid:g,label:l?.displayName,categoryName:l?.category}]),(0,t.jsx)(ne.l,{disabled:o||!i,error:O,label:S,id:r,name:r,onChange:I=>{c({target:{name:r,value:I,type:"select-category"}})},value:g||"",children:M.map(I=>(0,t.jsx)(Fe.c,{value:I.uid,children:`${I.categoryName} - ${I.label}`},I.uid))})},Fi=({dynamicZoneTarget:e,intlLabel:n,name:s,onChange:i,value:o})=>{const{formatMessage:l}=(0,w.A)(),{componentsGroupedByCategory:r,modifiedData:c}=Re(),p=gt(c.contentType.schema.attributes,e)?.components||[],g=Object.keys(r).reduce((S,A)=>{const j=r[A].filter(({uid:z})=>!p.includes(z));return j.length>0&&(S[A]=j),S},{}),m=Object.entries(g).reduce((S,A)=>{const[j,z]=A,M={label:j,children:z.map(({uid:I,schema:{displayName:P}})=>({label:P,value:I}))};return S.push(M),S},[]),O=l({id:d("components.SelectComponents.displayed-value"),defaultMessage:"{number, plural, =0 {# components} one {# component} other {# components}} selected"},{number:o?.length??0});return(0,t.jsx)(R.B,{id:"select1",label:l(n),customizeContent:()=>O,name:s,onChange:S=>{i({target:{name:s,value:S,type:"select-components"}})},options:m,value:o||[]})},Ni=({intlLabel:e,error:n=void 0,modifiedData:s,name:i,onChange:o,options:l,value:r=""})=>{const{formatMessage:c}=(0,w.A)(),u=c(e),p=n?c({id:n,defaultMessage:n}):"",g=m=>{o({target:{name:i,value:m,type:"select"}}),r&&s.default!==void 0&&s.default!==null&&o({target:{name:"default",value:null}})};return(0,t.jsx)(ne.l,{error:p,label:u,id:i,name:i,onChange:g,value:r||"",children:l.map(({metadatas:{intlLabel:m,disabled:O,hidden:S},key:A,value:j})=>(0,t.jsx)(Fe.c,{value:j,disabled:O,hidden:S,children:c({id:m.id,defaultMessage:m.defaultMessage},m.values)},A))})},ln=({intlLabel:e,error:n=void 0,modifiedData:s,name:i,onChange:o,options:l,value:r=""})=>{const{formatMessage:c}=(0,w.A)(),u=c(e),p=n?c({id:n,defaultMessage:n}):"",g=m=>{o({target:{name:i,value:m,type:"select"}}),r&&(m==="biginteger"&&r!=="biginteger"&&(s.default!==void 0&&s.default!==null&&o({target:{name:"default",value:null}}),s.max!==void 0&&s.max!==null&&o({target:{name:"max",value:null}}),s.min!==void 0&&s.min!==null&&o({target:{name:"min",value:null}})),typeof m=="string"&&["decimal","float","integer"].includes(m)&&r==="biginteger"&&(s.default!==void 0&&s.default!==null&&o({target:{name:"default",value:null}}),s.max!==void 0&&s.max!==null&&o({target:{name:"max",value:null}}),s.min!==void 0&&s.min!==null&&o({target:{name:"min",value:null}})))};return(0,t.jsx)(ne.l,{error:p,label:u,id:i,name:i,onChange:g,value:r||"",children:l.map(({metadatas:{intlLabel:m,disabled:O,hidden:S},key:A,value:j})=>(0,t.jsx)(Fe.c,{value:j,disabled:O,hidden:S,children:c(m)},A))})};ln.defaultProps={error:void 0,value:""};const Li=({description:e=null,error:n=null,intlLabel:s,modifiedData:i,name:o,onChange:l,value:r=null})=>{const{formatMessage:c}=(0,w.A)(),u=(0,f.useRef)(l),p=i?.displayName||"";(0,f.useEffect)(()=>{p?u.current({target:{name:o,value:(0,b.n)(p)}}):u.current({target:{name:o,value:""}})},[p,o]);const g=n?c({id:n,defaultMessage:n}):"",m=e?c({id:e.id,defaultMessage:e.defaultMessage},{...e.values}):"",O=c(s);return(0,t.jsx)(xe.k,{error:g,label:O,id:o,hint:m,name:o,onChange:l,value:r||""})},dn=({form:e,formErrors:n,genericInputProps:s,modifiedData:i,onChange:o})=>{const{formatMessage:l}=(0,w.A)();return(0,t.jsx)(t.Fragment,{children:e.map((r,c)=>r.items.length===0?null:(0,t.jsxs)(h.a,{children:[r.sectionTitle&&(0,t.jsx)(h.a,{paddingBottom:4,children:(0,t.jsx)(T.o,{variant:"delta",as:"h3",children:l(r.sectionTitle)})}),(0,t.jsx)(J.x,{gap:4,children:r.items.map((u,p)=>{const g=`${c}.${p}`,m=E(i,u.name,void 0),O=Object.keys(n).find(A=>A===u.name),S=O?n[O].id:E(n,[...u.name.split(".").filter(A=>A!=="componentToCreate"),"id"],null);return u.type==="pushRight"?(0,t.jsx)(_.E,{col:u.size||6,children:(0,t.jsx)("div",{})},u.name||g):(0,t.jsx)(_.E,{col:u.size||6,children:(0,t.jsx)(F.ah,{...u,...s,error:S,onChange:o,value:m})},u.name||g)})})]},c))})},Ii=({description:e=null,disabled:n=!1,error:s="",intlLabel:i,labelAction:o,name:l,onChange:r,placeholder:c=null,value:u=""})=>{const{formatMessage:p}=(0,w.A)(),g=s?p({id:s,defaultMessage:s}):"",m=e?p({id:e.id,defaultMessage:e.defaultMessage},{...e.values}):"",O=p(i),S=c?p({id:c.id,defaultMessage:c.defaultMessage},{...c.values}):"",A=Array.isArray(u)?u.join(`
`):"",j=z=>{const M=z.target.value.split(`
`);r({target:{name:l,value:M}})};return(0,t.jsx)(Zn.T,{disabled:n,error:g,label:O,labelAction:o,id:l,hint:m,name:l,onChange:j,placeholder:S,value:A,children:A})},Oe={name:"name",type:"text",intlLabel:{id:"global.name",defaultMessage:"Name"},description:{id:d("modalForm.attribute.form.base.name.description"),defaultMessage:"No space is allowed for the name of the attribute"}},$i={sections:[{sectionTitle:null,items:[Oe]}]},He={base(e=""){return[{sectionTitle:null,items:[{name:`${e}displayName`,type:"text",intlLabel:{id:d("contentType.displayName.label"),defaultMessage:"Display Name"}},{name:`${e}category`,type:"select-category",intlLabel:{id:d("modalForm.components.create-component.category.label"),defaultMessage:"Select a category or enter a name to create a new one"}}]},{sectionTitle:null,items:[{name:`${e}icon`,type:"icon-picker",size:12,intlLabel:{id:d("modalForm.components.icon.label"),defaultMessage:"Icon"}}]}]},advanced(){return[]}},L={default:{name:"default",type:"text",intlLabel:{id:d("form.attribute.settings.default"),defaultMessage:"Default value"}},max:{name:"max",type:"checkbox-with-number-field",intlLabel:{id:d("form.attribute.item.maximum"),defaultMessage:"Maximum value"}},maxLength:{name:"maxLength",type:"checkbox-with-number-field",intlLabel:{id:d("form.attribute.item.maximumLength"),defaultMessage:"Maximum length"}},min:{name:"min",type:"checkbox-with-number-field",intlLabel:{id:d("form.attribute.item.minimum"),defaultMessage:"Minimum value"}},minLength:{name:"minLength",type:"checkbox-with-number-field",intlLabel:{id:d("form.attribute.item.minimumLength"),defaultMessage:"Minimum length"}},private:{name:"private",type:"checkbox",intlLabel:{id:d("form.attribute.item.privateField"),defaultMessage:"Private field"},description:{id:d("form.attribute.item.privateField.description"),defaultMessage:"This field will not show up in the API response"}},regex:{intlLabel:{id:d("form.attribute.item.text.regex"),defaultMessage:"RegExp pattern"},name:"regex",type:"text",description:{id:d("form.attribute.item.text.regex.description"),defaultMessage:"The text of the regular expression"}},required:{name:"required",type:"checkbox",intlLabel:{id:d("form.attribute.item.requiredField"),defaultMessage:"Required field"},description:{id:d("form.attribute.item.requiredField.description"),defaultMessage:"You won't be able to create an entry if this field is empty"}},unique:{name:"unique",type:"checkbox",intlLabel:{id:d("form.attribute.item.uniqueField"),defaultMessage:"Unique field"},description:{id:d("form.attribute.item.uniqueField.description"),defaultMessage:"You won't be able to create an entry if there is an existing entry with identical content"}}},Oi={blocks(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.private]}]}},boolean(){return{sections:[{sectionTitle:null,items:[{autoFocus:!0,type:"select-default-boolean",intlLabel:{id:d("form.attribute.settings.default"),defaultMessage:"Default value"},name:"default",options:[{value:"true",key:"true",metadatas:{intlLabel:{id:"true",defaultMessage:"true"}}},{value:"",key:"null",metadatas:{intlLabel:{id:"null",defaultMessage:"null"}}},{value:"false",key:"false",metadatas:{intlLabel:{id:"false",defaultMessage:"false"}}}]}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.private]}]}},component({repeatable:e},n){return n==="1"?{sections:He.advanced()}:e?{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.private,L.max,L.min]}]}:{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.private]}]}},date({type:e}){return{sections:[{sectionTitle:null,items:[{...L.default,type:e||"date",value:null,withDefaultValue:!1,disabled:!e,autoFocus:!1}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.unique,L.private]}]}},dynamiczone(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.max,L.min]}]}},email(){return{sections:[{sectionTitle:null,items:[{...L.default,type:"email"}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.unique,L.maxLength,L.minLength,L.private]}]}},enumeration(e){return{sections:[{sectionTitle:null,items:[{name:"default",type:"select",intlLabel:{id:d("form.attribute.settings.default"),defaultMessage:"Default value"},validations:{},options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}}},...(e.enum||[]).filter((n,s)=>e.enum.indexOf(n)===s&&n).map(n=>({key:n,value:n,metadatas:{intlLabel:{id:`${n}.no-override`,defaultMessage:n}}}))]},{intlLabel:{id:d("form.attribute.item.enumeration.graphql"),defaultMessage:"Name override for GraphQL"},name:"enumName",type:"text",validations:{},description:{id:d("form.attribute.item.enumeration.graphql.description"),defaultMessage:"Allows you to override the default generated name for GraphQL"}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.private]}]}},json(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.private]}]}},media(){return{sections:[{sectionTitle:null,items:[{intlLabel:{id:d("form.attribute.media.allowed-types"),defaultMessage:"Select allowed types of media"},name:"allowedTypes",type:"allowed-types-select",size:7,value:"",validations:{}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.private]}]}},number(e){const n=e.type==="decimal"||e.type==="float"?"any":1;return{sections:[{sectionTitle:null,items:[{autoFocus:!0,name:"default",type:e.type==="biginteger"?"text":"number",step:n,intlLabel:{id:d("form.attribute.settings.default"),defaultMessage:"Default value"},validations:{}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.unique,L.max,L.min,L.private]}]}},password(){return{sections:[{sectionTitle:null,items:[L.default]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.maxLength,L.minLength,L.private]}]}},relation(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.private]}]}},richtext(){return{sections:[{sectionTitle:null,items:[L.default]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.maxLength,L.minLength,L.private]}]}},text(){return{sections:[{sectionTitle:null,items:[L.default,L.regex]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.unique,L.maxLength,L.minLength,L.private]}]}},uid(e){return{sections:[{sectionTitle:null,items:[{...L.default,disabled:Boolean(e.targetField),type:"text"}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[L.required,L.maxLength,L.minLength,L.private]}]}}},yt={intlLabel:{id:"global.type",defaultMessage:"Type"},name:"createComponent",type:"boolean-radio-group",size:12,radios:[{title:{id:d("form.attribute.component.option.create"),defaultMessage:"Create a new component"},description:{id:d("form.attribute.component.option.create.description"),defaultMessage:"A component is shared across types and components, it will be available and accessible everywhere."},value:!0},{title:{id:d("form.attribute.component.option.reuse-existing"),defaultMessage:"Use an existing component"},description:{id:d("form.attribute.component.option.reuse-existing.description"),defaultMessage:"Reuse a component already created to keep your data consistent across content-types."},value:!1}]},cn={advanced:Oi,base:{component(e,n){if(n==="1"){const s=e.createComponent===!0?He.base("componentToCreate."):[];return{sections:[{sectionTitle:null,items:[yt]},...s]}}return{sections:[{sectionTitle:null,items:[Oe,{name:"component",type:"select-component",intlLabel:{id:d("modalForm.attributes.select-component"),defaultMessage:"Select a component"},isMultiple:!1}]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"repeatable",type:"boolean-radio-group",size:12,radios:[{title:{id:d("form.attribute.component.option.repeatable"),defaultMessage:"Repeatable component"},description:{id:d("form.attribute.component.option.repeatable.description"),defaultMessage:"Best for multiple instances (array) of ingredients, meta tags, etc.."},value:!0},{title:{id:d("form.attribute.component.option.single"),defaultMessage:"Single component"},description:{id:d("form.attribute.component.option.single.description"),defaultMessage:"Best for grouping fields like full address, main information, etc..."},value:!1}]}]}]}},date(){return{sections:[{sectionTitle:null,items:[Oe,{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",type:"select-date",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"},hidden:!0}},{key:"date",value:"date",metadatas:{intlLabel:{id:d("form.attribute.item.date.type.date"),defaultMessage:"date (ex: 01/01/{currentYear})",values:{currentYear:new Date().getFullYear()}}}},{key:"datetime",value:"datetime",metadatas:{intlLabel:{id:d("form.attribute.item.date.type.datetime"),defaultMessage:"datetime (ex: 01/01/{currentYear} 00:00 AM)",values:{currentYear:new Date().getFullYear()}}}},{key:"time",value:"time",metadatas:{intlLabel:{id:d("form.attribute.item.date.type.time"),defaultMessage:"time (ex: 00:00 AM)"}}}]}]}]}},enumeration(){return{sections:[{sectionTitle:null,items:[Oe]},{sectionTitle:null,items:[{name:"enum",type:"textarea-enum",size:6,intlLabel:{id:d("form.attribute.item.enumeration.rules"),defaultMessage:"Values (one line per value)"},placeholder:{id:d("form.attribute.item.enumeration.placeholder"),defaultMessage:`Ex:
morning
noon
evening`},validations:{required:!0}}]}]}},media(){return{sections:[{sectionTitle:null,items:[Oe]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"multiple",size:12,type:"boolean-radio-group",radios:[{title:{id:d("form.attribute.media.option.multiple"),defaultMessage:"Multiple media"},description:{id:d("form.attribute.media.option.multiple.description"),defaultMessage:"Best for sliders, carousels or multiple files download"},value:!0},{title:{id:d("form.attribute.media.option.single"),defaultMessage:"Single media"},description:{id:d("form.attribute.media.option.single.description"),defaultMessage:"Best for avatar, profile picture or cover"},value:!1}]}]}]}},number(){return{sections:[{sectionTitle:null,items:[Oe,{intlLabel:{id:d("form.attribute.item.number.type"),defaultMessage:"Number format"},name:"type",type:"select-number",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"},hidden:!0}},{key:"integer",value:"integer",metadatas:{intlLabel:{id:d("form.attribute.item.number.type.integer"),defaultMessage:"integer (ex: 10)"}}},{key:"biginteger",value:"biginteger",metadatas:{intlLabel:{id:d("form.attribute.item.number.type.biginteger"),defaultMessage:"biginteger (ex: 123456789)"}}},{key:"decimal",value:"decimal",metadatas:{intlLabel:{id:d("form.attribute.item.number.type.decimal"),defaultMessage:"decimal (ex: 2.22)"}}},{key:"float",value:"float",metadatas:{intlLabel:{id:d("form.attribute.item.number.type.float"),defaultMessage:"decimal (ex: 3.3333333)"}}}]}]}]}},relation(){return{sections:[{sectionTitle:null,items:[{intlLabel:{id:"FIXME",defaultMessage:"FIXME"},name:"relation",size:12,type:"relation"}]}]}},string(){return{sections:[{sectionTitle:null,items:[Oe]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",size:12,type:"radio-group",radios:[{title:{id:d("form.attribute.text.option.short-text"),defaultMessage:"Sort text"},description:{id:d("form.attribute.text.option.short-text.description"),defaultMessage:"Best for titles, names, links (URL). It also enables exact search on the field."},value:"string"},{title:{id:d("form.attribute.text.option.long-text"),defaultMessage:"Long text"},description:{id:d("form.attribute.text.option.long-text.description"),defaultMessage:"Best for descriptions, biography. Exact search is disabled."},value:"text"}]}]}]}},text(){return{sections:[{sectionTitle:null,items:[Oe]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",size:12,type:"radio-group",radios:[{title:{id:d("form.attribute.text.option.short-text"),defaultMessage:"Sort text"},description:{id:d("form.attribute.text.option.short-text.description"),defaultMessage:"Best for titles, names, links (URL). It also enables exact search on the field."},value:"string"},{title:{id:d("form.attribute.text.option.long-text"),defaultMessage:"Long text"},description:{id:d("form.attribute.text.option.long-text.description"),defaultMessage:"Best for descriptions, biography. Exact search is disabled."},value:"text"}]}]}]}},uid(e,n,s){const i=s.filter(({type:o})=>["string","text"].includes(o)).map(({name:o})=>({key:o,value:o,metadatas:{intlLabel:{id:`${o}.no-override`,defaultMessage:o}}}));return{sections:[{sectionTitle:null,items:[{...Oe,placeholder:{id:d("modalForm.attribute.form.base.name.placeholder"),defaultMessage:"e.g. slug, seoUrl, canonicalUrl"}},{intlLabel:{id:d("modalForm.attribute.target-field"),defaultMessage:"Attached field"},name:"targetField",type:"select",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"global.none",defaultMessage:"None"}}},...i]}]}]}}}},vt=e=>e?Uo(e,{decamelize:!1,lowercase:!1,separator:"_"}):"",un=/^[A-Za-z][_0-9A-Za-z]*$/,mn=e=>({name:"attributeNameAlreadyUsed",message:F.iW.unique,test(n){if(!n)return!1;const s=(0,Y.snakeCase)(n);return!e.some(i=>i===n?!1:(0,Y.snakeCase)(i)===s)}}),xt=e=>({name:"forbiddenAttributeName",message:d("error.attributeName.reserved-name"),test(n){if(!n)return!1;const s=(0,Y.snakeCase)(n);return!e.some(i=>(0,Y.snakeCase)(i)===s)}}),v={default:()=>C.Yj().nullable(),max:()=>C.ai().integer().nullable(),min:()=>C.ai().integer().when("max",(e,n)=>e?n.max(e,d("error.validation.minSupMax")):n).nullable(),maxLength:()=>C.ai().integer().positive(d("error.validation.positive")).nullable(),minLength:()=>C.ai().integer().min(0).when("maxLength",(e,n)=>e?n.max(e,d("error.validation.minSupMax")):n).nullable(),name(e,n){return C.Yj().test(mn(e)).test(xt(n)).matches(un,F.iW.regex).required(F.iW.required)},required:()=>C.zM(),type:()=>C.Yj().required(F.iW.required),unique:()=>C.zM().nullable()},bt=(e,n)=>({name:v.name(e,n),type:v.type(),default:v.default(),unique:v.unique(),required:v.required(),maxLength:v.maxLength(),minLength:v.minLength(),regex:C.Yj().test({name:"isValidRegExpPattern",message:d("error.validation.regex"),test(i){return new RegExp(i||"")!==null}}).nullable()}),Ct=()=>({name:"isMinSuperiorThanMax",message:d("error.validation.minSupMax"),test(e){if(!e)return!0;const{max:n}=this.parent;return!n||Number.isNaN(pt(e))?!0:pt(n)>=pt(e)}}),_e={date(e,n){const s={name:v.name(e,n),type:v.type()};return C.Ik(s)},datetime(e,n){const s={name:v.name(e,n),type:v.type()};return C.Ik(s)},time(e,n){const s={name:v.name(e,n),type:v.type()};return C.Ik(s)},default(e,n){const s={name:v.name(e,n),type:v.type()};return C.Ik(s)},biginteger(e,n){const s={name:v.name(e,n),type:v.type(),default:C.Yj().nullable().matches(/^-?\d*$/),unique:v.unique(),required:v.required(),max:C.Yj().nullable().matches(/^-?\d*$/,F.iW.regex),min:C.Yj().nullable().test(Ct()).matches(/^-?\d*$/,F.iW.regex)};return C.Ik(s)},boolean(e,n){const s={name:v.name(e,n),default:C.zM().nullable(),required:v.required(),unique:v.unique()};return C.Ik(s)},component(e,n){const s={name:v.name(e,n),type:v.type(),required:v.required(),max:v.max(),min:v.min(),component:C.Yj().required(F.iW.required)};return C.Ik(s)},decimal(e,n){const s={name:v.name(e,n),type:v.type(),default:C.ai(),required:v.required(),max:C.ai(),min:C.ai().test(Ct())};return C.Ik(s)},dynamiczone(e,n){const s={name:v.name(e,n),type:v.type(),required:v.required(),max:v.max(),min:v.min()};return C.Ik(s)},email(e,n){const s={name:v.name(e,n),type:v.type(),default:C.Yj().email().nullable(),unique:v.unique(),required:v.required(),maxLength:v.maxLength(),minLength:v.minLength()};return C.Ik(s)},enumeration(e,n){const s=/^[_A-Za-z][_0-9A-Za-z]*$/,i={name:C.Yj().test(mn(e)).test(xt(n)).matches(s,F.iW.regex).required(F.iW.required),type:v.type(),default:v.default(),unique:v.unique(),required:v.required(),enum:C.YO().of(C.Yj()).min(1,F.iW.min).test({name:"areEnumValuesUnique",message:d("error.validation.enum-duplicate"),test(o){return o?!Bo(o.map(vt).filter((r,c,u)=>u.indexOf(r)!==c)).length:!1}}).test({name:"doesNotHaveEmptyValues",message:d("error.validation.enum-empty-string"),test:o=>o?!o.map(vt).some(l=>l===""):!1}).test({name:"doesMatchRegex",message:d("error.validation.enum-regex"),test:o=>o?o.map(vt).every(l=>s.test(l)):!1}),enumName:C.Yj().nullable()};return C.Ik(i)},float(e,n){const s={name:v.name(e,n),type:v.type(),required:v.required(),default:C.ai(),max:C.ai(),min:C.ai().test(Ct())};return C.Ik(s)},integer(e,n){const s={name:v.name(e,n),type:v.type(),default:C.ai().integer(),unique:v.unique(),required:v.required(),max:v.max(),min:v.min()};return C.Ik(s)},json(e,n){const s={name:v.name(e,n),type:v.type(),required:v.required(),unique:v.unique()};return C.Ik(s)},media(e,n){const s={name:v.name(e,n),type:v.type(),multiple:C.zM(),required:v.required(),allowedTypes:C.YO().of(C.Yj().oneOf(["images","videos","files","audios"])).min(1).nullable()};return C.Ik(s)},password(e,n){const s={name:v.name(e,n),type:v.type(),default:v.default(),unique:v.unique(),required:v.required(),maxLength:v.maxLength(),minLength:v.minLength()};return C.Ik(s)},relation(e,n,s,{initialData:i,modifiedData:o}){const l={name:v.name(e,n),target:C.Yj().required(F.iW.required),relation:C.Yj().required(),type:C.Yj().required(),targetAttribute:C.RZ(()=>{const r=(0,b.g)(o.relation,o.targetAttribute);if(r==="oneWay"||r==="manyWay")return C.Yj().nullable();const c=C.Yj().test(xt(n)),p=[...s.map(({name:g})=>g),o.name].filter(g=>g!==i.targetAttribute);return c.matches(un,F.iW.regex).test({name:"forbiddenTargetAttributeName",message:d("error.validation.relation.targetAttribute-taken"),test(g){return g?!p.includes(g):!1}}).required(F.iW.required)})};return C.Ik(l)},richtext(e,n){const s={name:v.name(e,n),type:v.type(),default:v.default(),unique:v.unique(),required:v.required(),maxLength:v.maxLength(),minLength:v.minLength()};return C.Ik(s)},blocks(e,n){const s={name:v.name(e,n),type:v.type(),default:v.default(),unique:v.unique(),required:v.required(),maxLength:v.maxLength(),minLength:v.minLength()};return C.Ik(s)},string(e,n){const s=bt(e,n);return C.Ik(s)},text(e,n){const s=bt(e,n);return C.Ik(s)},uid(e,n){const s=bt(e,n);return C.Ik(s)}},pn=/^[A-Za-z][-_0-9A-Za-z]*$/,Ei=e=>{const n={name:C.Yj().matches(pn,F.iW.regex).test({name:"nameNotAllowed",message:F.iW.unique,test(s){return s?!e.includes(s?.toLowerCase()):!1}}).required(F.iW.required)};return C.Ik(n)},Di={base:{sections:[{sectionTitle:null,items:[{autoFocus:!0,name:"name",type:"text",intlLabel:{id:"global.name",defaultMessage:"Name"},description:{id:d("modalForm.editCategory.base.name.description"),defaultMessage:"No space is allowed for the name of the category"}}]}]}},Ri=(e,n,s,i,o)=>{const l={displayName:C.Yj().test({name:"nameAlreadyUsed",message:F.iW.unique,test(r){if(!r)return!1;const c=(0,b.c)(r,s),u=(0,Y.snakeCase)(c),p=(0,Y.snakeCase)(o);return e.every(g=>(0,Y.snakeCase)(g)!==u)&&i.every(g=>(0,Y.snakeCase)(g)!==p)}}).test({name:"nameNotAllowed",message:d("error.contentTypeName.reserved-name"),test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return n.every(u=>(0,Y.snakeCase)(u)!==c)}}).required(F.iW.required),category:C.Yj().matches(pn,F.iW.regex).required(F.iW.required),icon:C.Yj()};return C.Ik(l)},gn={name:"displayName",type:"text",intlLabel:{id:d("contentType.displayName.label"),defaultMessage:"Display name"}},At={advanced:{default(){return{sections:[{items:[{intlLabel:{id:d("contentType.draftAndPublish.label"),defaultMessage:"Draft & publish"},description:{id:d("contentType.draftAndPublish.description"),defaultMessage:"Allows writing a draft version of an entry, before it is published"},name:"draftAndPublish",type:"toggle-draft-publish",validations:{}}]}]}}},base:{create(){return{sections:[{sectionTitle:null,items:[gn,{description:{id:d("contentType.apiId-singular.description"),defaultMessage:"Used to generate the API routes and databases tables/collections"},intlLabel:{id:d("contentType.apiId-singular.label"),defaultMessage:"API ID (Singular)"},name:"singularName",type:"text-singular"},{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{description:{id:d("contentType.apiId-plural.description"),defaultMessage:"Pluralized API ID"},intlLabel:{id:d("contentType.apiId-plural.label"),defaultMessage:"API ID (Plural)"},name:"pluralName",type:"text-plural"}]}]}},edit(){return{sections:[{sectionTitle:null,items:[gn,{disabled:!0,description:{id:d("contentType.apiId-singular.description"),defaultMessage:"Used to generate the API routes and databases tables/collections"},intlLabel:{id:d("contentType.apiId-singular.label"),defaultMessage:"API ID (Singular)"},name:"singularName",type:"text"},{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{disabled:!0,description:{id:d("contentType.apiId-plural.description"),defaultMessage:"Pluralized API ID"},intlLabel:{id:d("contentType.apiId-plural.label"),defaultMessage:"API ID (Plural)"},name:"pluralName",type:"text"},{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"kind",type:"content-type-radio-group",size:12,radios:[{title:{id:d("form.button.collection-type.name"),defaultMessage:"Collection Type"},description:{id:d("form.button.collection-type.description"),defaultMessage:"Best for multiple instances like articles, products, comments, etc."},value:"collectionType"},{title:{id:d("form.button.single-type.name"),defaultMessage:"Single Type"},description:{id:d("form.button.single-type.description"),defaultMessage:"Best for single instance like about us, homepage, etc."},value:"singleType"}]}]}]}}}},ki=({usedContentTypeNames:e=[],reservedModels:n=[],singularNames:s=[],pluralNames:i=[],collectionNames:o=[]})=>{const l={displayName:C.Yj().test({name:"nameAlreadyUsed",message:F.iW.unique,test(r){if(!r)return!1;const c=(0,b.b)(r),u=(0,Y.snakeCase)(c);return!e.some(p=>(0,Y.snakeCase)(p)===u)}}).test({name:"nameNotAllowed",message:d("error.contentTypeName.reserved-name"),test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return!n.some(u=>(0,Y.snakeCase)(u)===c)}}).required(F.iW.required),pluralName:C.Yj().test({name:"pluralNameAlreadyUsed",message:F.iW.unique,test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return!i.some(u=>(0,Y.snakeCase)(u)===c)}}).test({name:"pluralNameAlreadyUsedAsSingular",message:d("error.contentType.pluralName-equals-singularName"),test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return!s.some(u=>(0,Y.snakeCase)(u)===c)}}).test({name:"pluralAndSingularAreUnique",message:d("error.contentType.pluralName-used"),test(r,c){return r?(0,Y.snakeCase)(c.parent.singularName)!==(0,Y.snakeCase)(r):!1}}).test({name:"pluralNameNotAllowed",message:d("error.contentTypeName.reserved-name"),test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return!n.some(u=>(0,Y.snakeCase)(u)===c)}}).test({name:"pluralNameNotAlreadyUsedInCollectionName",message:d("error.contentType.pluralName-equals-collectionName"),test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return!o.some(u=>(0,Y.snakeCase)(u)===c)}}).required(F.iW.required),singularName:C.Yj().test({name:"singularNameAlreadyUsed",message:F.iW.unique,test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return!s.some(u=>(0,Y.snakeCase)(u)===c)}}).test({name:"singularNameAlreadyUsedAsPlural",message:d("error.contentType.singularName-equals-pluralName"),test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return!i.some(u=>(0,Y.snakeCase)(u)===c)}}).test({name:"pluralAndSingularAreUnique",message:d("error.contentType.singularName-used"),test(r,c){return r?(0,Y.snakeCase)(c.parent.pluralName)!==(0,Y.snakeCase)(r):!1}}).test({name:"singularNameNotAllowed",message:d("error.contentTypeName.reserved-name"),test(r){if(!r)return!1;const c=(0,Y.snakeCase)(r);return!n.some(u=>(0,Y.snakeCase)(u)===c)}}).required(F.iW.required),draftAndPublish:C.zM(),kind:C.Yj().oneOf(["singleType","collectionType"]),reviewWorkflows:C.zM()};return C.Ik(l)},Tt={advanced:{default(){return{sections:He.advanced()}}},base:{createComponent(){return{sections:[{sectionTitle:null,items:[yt]},...He.base("componentToCreate.")]}},default(){return{sections:[{sectionTitle:null,items:[yt]},{sectionTitle:null,items:[{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{name:"components",type:"select-components",intlLabel:{id:d("modalForm.attributes.select-components"),defaultMessage:"Select the components"},isMultiple:!0}]}]}}}},fn=(e,n)=>{e.forEach(s=>{if(!("sectionTitle"in s)){n[0].items?.push(s);return}n.push(s)})},Pi=(e,n)=>`components_${(0,Y.snakeCase)(n)}_${rt((0,Y.snakeCase)(e))}`,hn=(e,n)=>e.filter(({name:s})=>s!==n.initialData.name).map(({name:s})=>s),Ue={customField:{schema({schemaAttributes:e,attributeType:n,customFieldValidator:s,reservedNames:i,schemaData:o,ctbFormsAPI:l}){const r=hn(e,o);_e[n];let c;return n==="relation"?c=_e[n](r,i.attributes,[],{initialData:{},modifiedData:{}}):c=_e[n](r,i.attributes),l.makeCustomFieldValidator(c,s,r,i.attributes,o)},form:{base({customField:e}){const n=[{sectionTitle:null,items:[Oe]}];return e.options?.base&&fn(e.options.base,n),{sections:n}},advanced({customField:e,data:n,step:s,extensions:i,...o}){const l=[{sectionTitle:null,items:[]}],r=i.getAdvancedForm(["attribute",e.type],{data:n,type:e.type,step:s,...o});if(e.options?.advanced&&fn(e.options.advanced,l),r){const c={sectionTitle:{id:d("modalForm.custom-fields.advanced.settings.extended"),defaultMessage:"Extended settings"},items:r};l.push(c)}return{sections:l}}}},attribute:{schema(e,n,s,i,o,l){const r=e?.schema?.attributes??[],c=hn(r,o);try{const u=_e[n](c,s.attributes,i,o);return l.makeValidator(["attribute",n],u,c,s.attributes,i,o)}catch(u){return console.error("Error yup build schema",u),_e.default(c,s.attributes)}},form:{advanced({data:e,type:n,step:s,extensions:i,...o}){try{const l=cn.advanced[n](e,s).sections,r=i.getAdvancedForm(["attribute",n],{data:e,type:n,step:s,...o});return{sections:l.reduce((u,p)=>(p.sectionTitle===null?u.push(p):u.push({...p,items:[...p.items,...r]}),u),[])}}catch(l){return console.error(l),{sections:[]}}},base({data:e,type:n,step:s,attributes:i}){try{return cn.base[n](e,s,i)}catch{return $i}}}},contentType:{schema(e,n,s,i,o,l){const r=Object.values(l).map(A=>A.schema.singularName),c=Object.values(l).map(A=>A?.schema?.pluralName??""),u=n?e.filter(A=>A!==s):e,p=n?r.filter(A=>{const{schema:j}=l[s];return j.singularName!==A}):r,g=n?c.filter(A=>{const{schema:j}=l[s];return j.pluralName!==A}):c,m=Object.values(l).map(A=>A?.schema?.collectionName??""),O=n?m.filter(A=>{const{schema:j}=l[s],z=j.collectionName;return A!==z}):m,S=ki({usedContentTypeNames:u,reservedModels:i.models,singularNames:p,pluralNames:g,collectionNames:O});return o.makeValidator(["contentType"],S,u,i.models,p,g)},form:{base({actionType:e}){return e==="create"?At.base.create():At.base.edit()},advanced({extensions:e}){const n=At.advanced.default().sections.map(i=>i.items).flat(),s=e.getAdvancedForm(["contentType"]);return{sections:[{items:[...n,...s]}]}}}},component:{schema(e,n,s,i=!1,o,l,r=null){const c=i?e.filter(m=>m!==r):e,u=Object.values(o).map(m=>m?.schema?.collectionName),p=Pi(l,n),g=i?u.filter(m=>m!==p):u;return Ri(c,s.models,n,g,p)},form:{advanced(){return{sections:He.advanced()}},base(){return{sections:He.base()}}}},addComponentToDynamicZone:{form:{advanced(){return Tt.advanced.default()},base({data:e}){return e?.createComponent??!1?Tt.base.createComponent():Tt.base.default()}}},editCategory:{schema(e,n){const s=e.filter(i=>i!==n.name).map(i=>i.toLowerCase());return Ei(s)},form:{advanced:()=>({sections:[]}),base(){return Di.base}}}},Bi=()=>e=>e[`${b.p}_formModal`]||b.i,Ui=()=>(0,_t.Mz)(Bi(),e=>e),Wi=(e,n)=>{const s=E(e,["contentType","schema","kind"],"");return s==="singleType"||s===n.kind?!0:E(e,["contentType","schema","attributes"],[]).filter(({relation:l,type:r,targetAttribute:c})=>{const u=(0,b.g)(l,c);return r==="relation"&&!["oneWay","manyWay"].includes(u||"")}).length===0},zi=(e="",n,s)=>{const i=["text","boolean","blocks","json","number","email","date","password","media","enumeration","relation","richtext"],o=e==="contentType",l=s.includes(n),r=!o&&!l;return o?[[...i.slice(0,-1),"uid",...i.slice(-1)],["component","dynamiczone"]]:r?[i,["component"]]:[i]},yn=e=>e.reduce((n,s)=>{const i=s.items.reduce((o,l)=>(l.name&&o.push(l.name),o),[]);return[...n,...i]},[]),Zi=()=>{const{onCloseModal:e,onNavigateToChooseAttributeModal:n,onNavigateToAddCompoToDZModal:s,onNavigateToCreateComponentStep2:i,actionType:o,attributeName:l,attributeType:r,customFieldUid:c,categoryName:u,dynamicZoneTarget:p,forTarget:g,modalType:m,isOpen:O,kind:S,step:A,targetUid:j,showBackLink:z}=Ge(),M=(0,F.AC)().get(c),I=(0,f.useRef)(),P=(0,f.useMemo)(Ui,[]),B=(0,Ye.wA)(),X=(0,F.hN)(),ue=(0,Ye.d4)(G=>P(G),Ye.bN),{push:be}=(0,Te.W6)(),{trackUsage:Z}=(0,F.z1)(),{formatMessage:ve}=(0,w.A)(),{getPlugin:q}=(0,F.vD)(),ge=q(b.p)?.apis.forms,oe=ge.components.inputs,{addAttribute:ke,addCustomFieldAttribute:me,addCreatedComponentToDynamicZone:Q,allComponentsCategories:N,changeDynamicZoneComponents:Mt,contentTypes:we,components:We,createSchema:Ke,deleteCategory:jt,deleteData:dt,editCategory:St,editCustomFieldAttribute:Ft,submitData:Nt,modifiedData:fe,nestedComponents:Lt,setModifiedData:It,sortedContentTypesList:$t,updateSchema:ct,reservedNames:ze}=Re(),{componentToCreate:tt,formErrors:Pe,initialData:de,isCreatingComponentWhileAddingAField:nt,modifiedData:y}=ue,$=g==="contentType"||g==="component"?[g]:[g,j];(0,f.useEffect)(()=>{if(O){const G=$t.filter(an);m==="editCategory"&&It(),o==="edit"&&m==="attribute"&&g==="contentType"&&Z("willEditFieldOfContentType");const Ae=[...$,"schema","attributes"],$e=gt(E(fe,Ae,[]),p)||null;if(m==="editCategory"&&o==="edit"&&B({type:b.S,modalType:m,actionType:o,data:{name:u}}),m==="contentType"&&o==="create"&&B({type:b.S,modalType:m,actionType:o,data:{draftAndPublish:!0},pluginOptions:{}}),m==="contentType"&&o==="edit"){const{displayName:K,draftAndPublish:ce,kind:Se,pluginOptions:ye,pluralName:Tr,reviewWorkflows:Mr,singularName:jr}=E(fe,[...$,"schema"],{displayName:null,pluginOptions:{},singularName:null,pluralName:null});B({type:b.S,actionType:o,modalType:m,data:{displayName:K,draftAndPublish:ce,kind:Se,pluginOptions:ye,pluralName:Tr,reviewWorkflows:Mr??!1,singularName:jr}})}if(m==="component"&&o==="edit"){const K=E(fe,$,{});B({type:b.S,actionType:o,modalType:m,data:{displayName:K.schema.displayName,category:K.category,icon:K.schema.icon}})}if(m==="addComponentToDynamicZone"&&o==="edit"){const K={...$e,components:[],name:p,createComponent:!1,componentToCreate:{type:"component"}};B({type:b.d,attributeToEdit:K})}if(r){const ce={...gt(E(fe,Ae,[]),l),name:l};r==="component"&&o==="edit"&&(ce.repeatable||Jt(ce,"repeatable",!1)),B(m==="customField"?{type:b.e,customField:M,isEditing:o==="edit",modifiedDataToSetForEditing:ce,forTarget:g}:{type:b.f,attributeType:r,nameToSetForRelation:E(G,["0","title"],"error"),targetUid:E(G,["0","uid"],"error"),isEditing:o==="edit",modifiedDataToSetForEditing:ce,step:A,forTarget:g})}}else B({type:b.R})},[o,l,r,u,p,g,O,m]);const V=m==="contentType",re=m==="component",he=m==="attribute",Le=m==="customField",Ie=r==="component"&&he,Ze=o==="create",Ve=E(y,"createComponent",!1)||nt,Qe=A==="1",Fn=m==="editCategory",Nn=m==="chooseAttribute",Ot=(0,b.b)(y.displayName||""),Et=E(fe,[...$,"schema","attributes"],null),dr=async()=>{let G;const Ae=Ve&&A==="1"?E(y,"componentToCreate",{}):y;if(V)G=Ue.contentType.schema(Object.keys(we),o==="edit",E(fe,[...$,"uid"],null),ze,ge,we);else if(re)G=Ue.component.schema(Object.keys(We),y.category||"",ze,o==="edit",We,y.displayName||"",E(fe,[...$,"uid"],null));else if(Le)G=Ue.customField.schema({schemaAttributes:E(fe,[...$,"schema","attributes"],[]),attributeType:M.type,reservedNames:ze,schemaData:{modifiedData:y,initialData:de},ctbFormsAPI:ge,customFieldValidator:M.options?.validator});else if(Ie&&Ve&&Qe)G=Ue.component.schema(Object.keys(We),E(y,"componentToCreate.category",""),ze,o==="edit",We,y.componentToCreate.displayName||"");else if(he&&!Qe){const $e=r==="relation"?"relation":y.type;let K=[];if($e==="relation"){const ce=E(y,["target"],null);K=E(we,[ce,"schema","attributes"],[]).filter(({name:ye})=>o!=="edit"?!0:ye!==de.targetAttribute)}G=Ue.attribute.schema(E(fe,$,{}),$e,ze,K,{modifiedData:y,initialData:de},ge)}else if(Fn)G=Ue.editCategory.schema(N,de);else if(Qe&&Ve)G=Ue.component.schema(Object.keys(We),E(y,"componentToCreate.category",""),ze,o==="edit",We,y.componentToCreate.displayName||"");else return;await G.validate(Ae,{abortEarly:!1})},Ln=(0,f.useCallback)(({target:{name:G,value:Ae,type:$e,...K}})=>{const ce=["enumName","max","min","maxLength","minLength","regex","default"];let Se;ce.includes(G)&&Ae===""?Se=null:Se=Ae;const ye=Object.assign({},Pe);G==="max"&&delete ye.min,G==="maxLength"&&delete ye.minLength,delete ye[G],B({type:b.h,errors:ye}),B({type:b.j,keys:G.split("."),value:Se,...K})},[B,Pe]),je=async(G,Ae=Ze)=>{G.preventDefault();try{await dr(),mr(Ae);const $e=g==="components"?j:Ot;if(V)if(Ze)Ke({...y,kind:S},m,Ot),be({pathname:`/plugins/${b.p}/content-types/${Ot}`}),n({forTarget:g,targetUid:$e});else{Wi(fe,y)?(e(),Nt(y)):X({type:"warning",message:{id:"notification.contentType.relations.conflict"}});return}else if(m==="component")if(Ze){const K=(0,b.c)(y.displayName,y.category),{category:ce,...Se}=y;Ke(Se,"component",K,ce),be({pathname:`/plugins/${b.p}/component-categories/${ce}/${K}`}),n({forTarget:g,targetUid:K})}else{ct(y,m,j),e();return}else if(Fn){if(Xt(de.name)===Xt(y.name)){e();return}St(de.name,y);return}else if(Le){const K={attributeToSet:{...y,customField:c},forTarget:g,targetUid:j,initialAttribute:de};o==="edit"?Ft(K):me(K),Ae?n({forTarget:g,targetUid:$e}):e();return}else if(he&&!Ve){if(r==="dynamiczone"){ke(y,g,j,o==="edit",de),Ze?(B({type:b.k}),I.current!==void 0&&I.current._handlers.setSelectedTabIndex(0),s({dynamicZoneTarget:y.name})):e();return}if(!Ie){ke(y,g,j,o==="edit",de),Ae?n({forTarget:g,targetUid:$e}):e();return}if(Qe){i(),B({type:b.l,forTarget:g});return}ke(y,g,j,o==="edit",de,!0),Ae?n({forTarget:g,targetUid:j}):e()}else if(he&&Ve){if(Qe){Z("willCreateComponentFromAttributesModal"),B({type:b.m,forTarget:g}),i();return}const{category:K,type:ce,...Se}=tt,ye=(0,b.c)(tt.displayName,K);Ke(Se,ce,ye,K,Ve),ke(y,g,j,!1),B({type:b.R}),Ae?n({forTarget:"components",targetUid:ye}):e();return}else{if(Qe)if(Ve){const{category:K,type:ce,...Se}=y.componentToCreate,ye=(0,b.c)(y.componentToCreate.displayName,K);Ke(Se,ce,ye,K,Ve),Q(p,[ye]),n({forTarget:"components",targetUid:ye})}else Mt(p,y.components),e();else console.error("This case is not handled");return}B({type:b.R})}catch($e){const K=(0,F.ed)($e);B({type:b.h,errors:K})}},cr=()=>{window.confirm(ve({id:"window.confirm.close-modal.file",defaultMessage:"Are you sure? Your changes will be lost."}))&&(e(),B({type:b.R}))},In=()=>{mt(y,de)?(e(),B({type:b.R})):cr()},ur=G=>{if(G==="advanced"){if(V){Z("didSelectContentTypeSettings");return}g==="contentType"&&Z("didSelectContentTypeFieldSettings")}},mr=G=>{m==="attribute"&&g==="contentType"&&r!=="dynamiczone"&&G&&Z("willAddMoreFieldToContentType")},pr=()=>!!(m==="editCategory"||m==="component"||ko(y,"createComponent")),gr=zi(g,j,Lt);if(!O||!m)return null;const $n=E(Ue,[m,"form"],{advanced:()=>({sections:[]}),base:()=>({sections:[]})}),fr=g==="components"||g==="component",On={customInputs:{"allowed-types-select":Go,"boolean-radio-group":oi,"checkbox-with-number-field":ii,"icon-picker":fi,"content-type-radio-group":ri,"radio-group":ft,relation:Mi,"select-category":ji,"select-component":Si,"select-components":Fi,"select-default-boolean":ai,"select-number":ln,"select-date":Ni,"toggle-draft-publish":li,"text-plural":hi,"text-singular":Li,"textarea-enum":Ii,...oe},componentToCreate:tt,dynamicZoneTarget:p,formErrors:Pe,isAddingAComponentToAnotherComponent:fr,isCreatingComponentWhileAddingAField:nt,mainBoxHeader:E(fe,[...$,"schema","displayName"],""),modifiedData:y,naturePickerType:g,isCreating:Ze,targetUid:j,forTarget:g},En=$n.advanced({data:y,type:r,step:A,actionType:o,attributes:Et,extensions:ge,forTarget:g,contentTypeSchema:fe.contentType||{},customField:M}).sections,Dn=$n.base({data:y,type:r,step:A,actionType:o,attributes:Et,extensions:ge,forTarget:g,contentTypeSchema:fe.contentType||{},customField:M}).sections,hr=yn(Dn),yr=yn(En),vr=Object.keys(Pe).some(G=>hr.includes(G)),xr=Object.keys(Pe).some(G=>yr.includes(G)),br=E(we,[j,"schema","kind"]),Cr=()=>o==="edit"&&Et.every(({name:G})=>G!==y?.name),Ar=()=>{Cr()&&Z("didEditFieldNameOnContentType")};return(0,t.jsxs)(Vn.k,{onClose:In,labelledBy:"title",children:[(0,t.jsx)(ci,{actionType:o,attributeName:l,categoryName:u,contentTypeKind:S,dynamicZoneTarget:p,modalType:m,forTarget:g,targetUid:j,attributeType:r,customFieldUid:c,showBackLink:z}),Nn&&(0,t.jsx)(ni,{attributes:gr,forTarget:g,kind:br||"collectionType"}),!Nn&&(0,t.jsxs)("form",{onSubmit:je,children:[(0,t.jsx)(Ee.c,{children:(0,t.jsxs)(le.f,{label:"todo",id:"tabs",variant:"simple",ref:I,onTabChange:G=>{G===1&&ur("advanced")},children:[(0,t.jsxs)(x.s,{justifyContent:"space-between",children:[(0,t.jsx)(mi,{actionType:o,forTarget:g,kind:S,step:A,modalType:m,attributeType:r,attributeName:l,customField:M}),(0,t.jsxs)(ie.t,{children:[(0,t.jsx)(ie.o,{hasError:vr,children:ve({id:d("popUpForm.navContainer.base"),defaultMessage:"Basic settings"})}),(0,t.jsx)(ie.o,{hasError:xr,disabled:pr(),children:ve({id:d("popUpForm.navContainer.advanced"),defaultMessage:"Advanced settings"})})]})]}),(0,t.jsx)(te.c,{}),(0,t.jsx)(h.a,{paddingTop:6,children:(0,t.jsxs)(se.T,{children:[(0,t.jsx)(se.K,{children:(0,t.jsx)(x.s,{direction:"column",alignItems:"stretch",gap:6,children:(0,t.jsx)(dn,{form:Dn,formErrors:Pe,genericInputProps:On,modifiedData:y,onChange:Ln})})}),(0,t.jsx)(se.K,{children:(0,t.jsx)(x.s,{direction:"column",alignItems:"stretch",gap:6,children:(0,t.jsx)(dn,{form:En,formErrors:Pe,genericInputProps:On,modifiedData:y,onChange:Ln})})})]})})]})}),(0,t.jsx)(Yn.j,{endActions:(0,t.jsx)(di,{deleteCategory:jt,deleteContentType:dt,deleteComponent:dt,categoryName:de.name,isAttributeModal:m==="attribute",isCustomFieldModal:m==="customField",isComponentToDzModal:m==="addComponentToDynamicZone",isComponentAttribute:r==="component",isComponentModal:m==="component",isContentTypeModal:m==="contentType",isCreatingComponent:o==="create",isCreatingDz:o==="create",isCreatingComponentAttribute:y.createComponent||!1,isCreatingComponentInDz:y.createComponent||!1,isCreatingComponentWhileAddingAField:nt,isCreatingContentType:o==="create",isEditingAttribute:o==="edit",isDzAttribute:r==="dynamiczone",isEditingCategory:m==="editCategory",isInFirstComponentStep:A==="1",onSubmitAddComponentAttribute:je,onSubmitAddComponentToDz:je,onSubmitCreateComponent:je,onSubmitCreateContentType:je,onSubmitCreateDz:je,onSubmitEditAttribute:je,onSubmitEditCategory:je,onSubmitEditComponent:je,onSubmitEditContentType:je,onSubmitEditCustomFieldAttribute:je,onSubmitEditDz:je,onClickFinish:Ar}),startActions:(0,t.jsx)(ee.$,{variant:"tertiary",onClick:In,children:ve({id:"app.components.Button.cancel",defaultMessage:"Cancel"})})})]})]})},Vi=()=>e=>e[`${b.p}_dataManagerProvider`]||b.o,Yi=()=>(0,_t.Mz)(Vi(),e=>e),Gi=(e,n)=>{const s=Object.keys(e).filter(i=>{const o=E(e,i,{}),l=E(n,i,{}),r=E(o,["isTemporary"],!1),c=!mt(o,l);return r||c});return(0,b.q)(s)},Hi=(e,n)=>{const s=xn(E(e,"schema.attributes",[]),n),i=E(e,"isTemporary",!1)?{tmpUID:e.uid}:{uid:e.uid};return Object.assign({},i,{category:e.category},en(e.schema,"attributes"),{attributes:s})},vn=(e,n=!1)=>{const s=E(e,"uid",null),i=xn(E(e,"schema.attributes",[]),s),o=n?{category:E(e,"category","")}:{},l=Object.assign(o,en(e.schema,"attributes"),{attributes:i});return delete l.uid,delete l.isTemporary,delete l.visible,delete l.restrictRelationsTo,l},xn=(e,n)=>e.reduce((s,{name:i,...o})=>{const l=o,r=l.target===n,c=l.type==="relation",u=E(l,"targetAttribute",null);if(!r)if(c){const p=Object.assign({},l,{targetAttribute:bn(u)});s[i]=lt(p)}else s[i]=lt(l);if(r){const p=l.target,g=Object.assign({},l,{target:p,targetAttribute:bn(u)});s[i]=lt(g)}if(l.customField){const p={...l,type:"customField"};s[i]=lt(p)}return s},{}),bn=e=>e==="-"?null:e,lt=e=>Object.keys(e).reduce((n,s)=>(e[s]!==null&&s!=="plugin"&&(n[s]=e[s]),n),{}),wi=(e,n,s)=>Gi(e,n).map(l=>{const r=E(e,l,{});return Hi(r,s)}),Ki=e=>zo(Object.keys(e).map(n=>({visible:e[n].schema.visible,name:n,title:e[n].schema.displayName,plugin:e[n].plugin||null,uid:n,to:`/plugins/${b.p}/content-types/${n}`,kind:e[n].schema.kind,restrictRelationsTo:e[n].schema.restrictRelationsTo})).filter(n=>n!==null),n=>Wo(n.title)),Cn=e=>e.reduce((n,s)=>(n[s.uid]=s,n),{}),Qi=(e,n,s,i)=>{const o=n.reduce((c,u)=>{const p=E(s,u,{});return c[u]=p,c},{});return{[i?"contentType":"component"]:e,components:o}},An=e=>Object.keys(e).reduce((n,s)=>{const i=e[s].schema;return n[s]={...e[s],schema:{...i,attributes:Ji(i.attributes)}},n},{}),Ji=e=>Object.keys(e).reduce((n,s)=>(n.push({...e[s],name:s}),n),[]),Xi=e=>{const n=Object.keys(e).reduce((s,i)=>{const o=E(e,[i]),l=o.uid;return qi(o)&&s.push(l),s},[]);return(0,b.q)(n)},qi=e=>E(e,["schema","attributes"],[]).some(s=>{const{type:i}=s;return i==="component"}),Tn=e=>{const n=Object.keys(e).reduce((s,i)=>{const o=e?.[i]?.schema?.attributes??[],l=_i(o);return[...s,...l]},[]);return(0,b.q)(n)},_i=e=>e.reduce((n,s)=>{const{type:i,component:o}=s;return i==="component"&&n.push(o),n},[]),er=(e,n)=>{const s=Object.keys(e).map(i=>E(e,[i,...n],""));return(0,b.q)(s)},Mn="did-not-kill-server",tr="server is down";function et(e,n){return new Promise(s=>{fetch(`${window.strapi.backendURL}/_health`,{method:"HEAD",mode:"no-cors",headers:{"Content-Type":"application/json","Keep-Alive":"false"}}).then(i=>{if(i.status>=400)throw new Error(tr);if(!n)throw new Error(Mn);s(e)}).catch(i=>{setTimeout(()=>et(e,i.message!==Mn).then(s),100)})})}const nr=e=>Object.values(e.attributes).filter(s=>s.type==="dynamiczone").every(s=>Array.isArray(s.components)&&s.components.length>0),ar=({children:e})=>{const n=(0,Ye.wA)(),{components:s,contentTypes:i,isLoading:o,isLoadingForDataToBeSet:l,initialData:r,modifiedData:c,reservedNames:u}=(0,Ye.d4)(Yi()),p=(0,F.hN)(),{lockAppWithAutoreload:g,unlockAppWithAutoreload:m}=(0,F.Ip)(),{setCurrentStep:O}=(0,F.Cx)(),{getPlugin:S}=(0,F.vD)(),A=S(b.p),{autoReload:j}=(0,F.Xe)(),{formatMessage:z}=(0,w.A)(),{trackUsage:M}=(0,F.z1)(),{refetchPermissions:I}=(0,F.r5)(),{pathname:P}=(0,Te.zy)(),{onCloseModal:B}=Ge(),X=(0,Te.W5)(`/plugins/${b.p}/content-types/:uid`),ue=(0,Te.W5)(`/plugins/${b.p}/component-categories/:categoryUid/:componentUid`),be=(0,F.ry)(),{put:Z,post:ve,del:q}=be,Ce=(0,f.useRef)();Ce.current=z;const ge=j,oe=X!==null,ke=oe?"contentType":"component",me=oe?E(X,"params.uid",null):E(ue,"params.componentUid",null),Q=(0,f.useRef)(),N=oe?"content-types":"components";Q.current=async()=>{try{const[{data:{data:y}},{data:{data:$}},{data:V}]=await Promise.all(["components","content-types","reserved-names"].map(Ze=>be.get(`/${b.p}/${Ze}`))),re=Cn(y),he=An(re),Le=Cn($),Ie=An(Le);n({type:b.G,components:he,contentTypes:Ie,reservedNames:V})}catch(y){console.error({err:y}),p({type:"warning",message:{id:"notification.error"}})}},(0,f.useEffect)(()=>(Q.current(),()=>{n({type:b.r})}),[]),(0,f.useEffect)(()=>{!o&&me&&ct()},[o,P,me]),(0,f.useEffect)(()=>{j||p({type:"info",message:{id:d("notification.info.autoreaload-disable")}})},[j,p]);const Mt=(y,$,V,re=!1,he,Le=!1)=>{const Ie=re?b.x:b.y;n({type:Ie,attributeToSet:y,forTarget:$,targetUid:V,initialAttribute:he,shouldAddComponentToData:Le})},we=({attributeToSet:y,forTarget:$,targetUid:V,initialAttribute:re})=>{n({type:b.A,attributeToSet:y,forTarget:$,targetUid:V,initialAttribute:re})},We=({attributeToSet:y,forTarget:$,targetUid:V,initialAttribute:re})=>{n({type:b.E,attributeToSet:y,forTarget:$,targetUid:V,initialAttribute:re})},Ke=(y,$)=>{n({type:b.u,dynamicZoneTarget:y,componentsToAdd:$})},jt=(y,$,V,re,he=!1)=>{const Le=$==="contentType"?b.z:b.B;n({type:Le,data:y,componentCategory:re,schemaType:$,uid:V,shouldAddComponentToData:he})},dt=(y,$)=>{n({type:b.C,dynamicZoneTarget:y,newComponents:$})},St=(y,$,V="")=>{const re=y==="components"?b.F:b.H;y==="contentType"&&M("willDeleteFieldOfContentType"),n({type:re,mainDataKey:y,attributeToRemoveName:$,componentUid:V})},Ft=async y=>{try{const $=`/${b.p}/component-categories/${y}`,V=window.confirm(z({id:d("popUpWarning.bodyMessage.category.delete")}));B(),V&&(g?.(),await q($),await et(!0),m?.(),await de())}catch($){console.error({err:$}),p({type:"warning",message:{id:"notification.error"}})}finally{m?.()}},Nt=async()=>{try{const y=`/${b.p}/${N}/${me}`,$=E(c,[ke,"isTemporary"],!1),V=window.confirm(z({id:d(`popUpWarning.bodyMessage.${oe?"contentType":"component"}.delete`)}));if(B(),V){if($){n({type:b.D});return}g?.(),await q(y),await et(!0),await m?.(),await de()}}catch(y){console.error({err:y}),p({type:"warning",message:{id:"notification.error"}})}finally{m?.()}},fe=async(y,$)=>{try{const V=`/${b.p}/component-categories/${y}`;B(),g?.(),await Z(V,$),await et(!0),await m?.(),await de()}catch(V){console.error({err:V}),p({type:"warning",message:{id:"notification.error"}})}finally{m?.()}},Lt=()=>{const y=Object.assign({},s,c.components);if(!oe){const V=E(c,"component",{});Jt(y,E(V,["uid"],""),V)}const $=Xi(y);return(0,b.q)($)},It=()=>{const y=Tn(s),$=Tn(c.components||{});return(0,b.q)([...$,...y])},$t=(y,$)=>{n({type:b.v,dzName:y,componentToRemoveIndex:$})},ct=()=>{const $=E(oe?i:s,me??"",{schema:{attributes:[]}}),V=(0,b.s)($.schema.attributes,s),re=Qi($,V,s,oe),he=E($,"isTemporary",!1)&&Ro(E($,"schema.attributes",[]))===0;n({type:b.t,schemaToSet:re,hasJustCreatedSchema:he})},ze=(0,f.useMemo)(()=>{const y=oe?i:s;return me==="create-content-type"?!1:!Object.keys(y).includes(me||"")&&!o},[s,i,me,oe,o]),tt=(0,f.useMemo)(()=>{const y=Object.keys(i).filter($=>E(i,[$,"schema","visible"],!0)).sort();return E(y,"0","create-content-type")},[i]);if(ze)return(0,t.jsx)(Te.rd,{to:`/plugins/${b.p}/content-types/${tt}`});const Pe=async y=>{try{const $=E(c,[ke,"isTemporary"],!1),V={components:wi(c.components,s,me)};if(oe){const Ie=(A?.apis?.forms).mutateContentTypeSchema({...vn(c.contentType),...y},r.contentType);if(!nr(Ie)){p({type:"warning",message:{id:d("notification.error.dynamiczone-min.validation"),defaultMessage:"At least one component is required in a dynamic zone to be able to save a content type"}});return}V.contentType=Ie,M("willSaveContentType")}else V.component=vn(c.component,!0),M("willSaveComponent");g?.();const re=`/${b.p}/${N}`,he=$?re:`${re}/${me}`;if($?await ve(he,V):await Z(he,V),await et(!0),m?.(),$&&(r.contentType?.schema.kind==="collectionType"||r.contentType?.schema.kind==="singleType")&&O("contentTypeBuilder.success"),oe){M("didSaveContentType");const Le=E(V,["contentType","schema","name"],""),Ie=E(r,["contentType","schema","name"],"");!$&&Le!==Ie&&M("didEditNameOfContentType")}else M("didSaveComponent");await Q.current(),n({type:b.U}),await de()}catch($){oe||M("didNotSaveComponent"),console.error({err:$.response}),p({type:"warning",message:{id:"notification.error"}})}finally{m?.()}},de=async()=>{await I()},nt=(y,$,V)=>{n({type:b.w,data:y,schemaType:$,uid:V})};return(0,t.jsx)(tn.Provider,{value:{addAttribute:Mt,addCustomFieldAttribute:we,addCreatedComponentToDynamicZone:Ke,allComponentsCategories:er(s,["category"]),changeDynamicZoneComponents:dt,components:s,componentsGroupedByCategory:Do(s,"category"),componentsThatHaveOtherComponentInTheirAttributes:Lt(),contentTypes:i,createSchema:jt,deleteCategory:Ft,deleteData:Nt,editCategory:fe,editCustomFieldAttribute:We,isInDevelopmentMode:ge,initialData:r,isInContentTypeView:oe,modifiedData:c,nestedComponents:It(),removeAttribute:St,removeComponentFromDynamicZone:$t,reservedNames:u,setModifiedData:ct,sortedContentTypesList:Ki(i),submitData:Pe,updateSchema:nt},children:l?(0,t.jsx)(F.Bl,{}):(0,t.jsxs)(t.Fragment,{children:[e,ge&&(0,t.jsx)(Zi,{})]})})},sr=(0,f.memo)(ar),jn={actionType:null,attributeName:null,attributeType:null,categoryName:null,dynamicZoneTarget:null,forTarget:null,modalType:null,isOpen:!1,showBackLink:!1,kind:null,step:null,targetUid:null,customFieldUid:null},or=({children:e})=>{const[n,s]=f.useState(jn),{trackUsage:i}=(0,F.z1)(),o=({attributeType:M,customFieldUid:I})=>{s(P=>({...P,actionType:"create",modalType:"customField",attributeType:M,customFieldUid:I}))},l=({attributeType:M,step:I})=>{n.forTarget==="contentType"&&i("didSelectContentTypeFieldType",{type:M}),s(P=>({...P,actionType:"create",modalType:"attribute",step:I,attributeType:M,showBackLink:!0}))},r=({dynamicZoneTarget:M,targetUid:I})=>{s(P=>({...P,dynamicZoneTarget:M,targetUid:I,modalType:"addComponentToDynamicZone",forTarget:"contentType",step:"1",actionType:"edit",isOpen:!0}))},c=({forTarget:M,targetUid:I})=>{s(P=>({...P,actionType:"create",forTarget:M,targetUid:I,modalType:"chooseAttribute",isOpen:!0,showBackLink:!1}))},u=M=>{s(I=>({...I,...M,isOpen:!0}))},p=M=>{s(I=>({...I,categoryName:M,actionType:"edit",modalType:"editCategory",isOpen:!0}))},g=({forTarget:M,targetUid:I,attributeName:P,attributeType:B,customFieldUid:X})=>{s(ue=>({...ue,modalType:"customField",customFieldUid:X,actionType:"edit",forTarget:M,targetUid:I,attributeName:P,attributeType:B,isOpen:!0}))},m=({forTarget:M,targetUid:I,attributeName:P,attributeType:B,step:X})=>{s(ue=>({...ue,modalType:"attribute",actionType:"edit",forTarget:M,targetUid:I,attributeName:P,attributeType:B,step:X,isOpen:!0}))},O=({modalType:M,forTarget:I,targetUid:P,kind:B})=>{s(X=>({...X,modalType:M,actionType:"edit",forTarget:I,targetUid:P,kind:B,isOpen:!0}))},S=()=>{s(jn)},A=({forTarget:M,targetUid:I})=>{s(P=>({...P,forTarget:M,targetUid:I,modalType:"chooseAttribute"}))},j=()=>{s(M=>({...M,attributeType:"component",modalType:"attribute",step:"2"}))},z=({dynamicZoneTarget:M})=>{s(I=>({...I,dynamicZoneTarget:M,modalType:"addComponentToDynamicZone",actionType:"create",step:"1",attributeType:null,attributeName:null}))};return(0,t.jsx)(nn.Provider,{value:{...n,onClickSelectField:l,onClickSelectCustomField:o,onCloseModal:S,onNavigateToChooseAttributeModal:A,onNavigateToAddCompoToDZModal:z,onOpenModalAddComponentsToDZ:r,onNavigateToCreateComponentStep2:j,onOpenModalAddField:c,onOpenModalCreateSchema:u,onOpenModalEditCategory:p,onOpenModalEditField:m,onOpenModalEditCustomField:g,onOpenModalEditSchema:O,setFormModalNavigationState:s},children:e})},ir=(0,f.lazy)(()=>a.e(5388).then(a.bind(a,15388))),rr=()=>{const{url:e}=(0,Te.W5)();return(0,t.jsx)(f.Suspense,{fallback:(0,t.jsx)(F.Bl,{}),children:(0,t.jsx)(Te.dO,{children:(0,t.jsx)(Te.qh,{path:`${e}/:componentUid`,children:(0,t.jsx)(ir,{})})})})},Sn=(0,f.lazy)(()=>a.e(5388).then(a.bind(a,15388))),lr=Object.freeze(Object.defineProperty({__proto__:null,default:()=>{const{formatMessage:e}=(0,w.A)(),n=e({id:`${b.p}.plugin.name`,defaultMessage:"Content Types Builder"}),{startSection:s}=(0,F.Cx)(),i=(0,f.useRef)(s);return(0,f.useEffect)(()=>{i.current&&i.current("contentTypeBuilder")},[]),(0,t.jsxs)(F.kz,{permissions:b.P.main,children:[(0,t.jsx)(Hn.m,{title:n}),(0,t.jsx)(or,{children:(0,t.jsx)(sr,{children:(0,t.jsx)(Gn.P,{sideNav:(0,t.jsx)(Vo,{}),children:(0,t.jsx)(f.Suspense,{fallback:(0,t.jsx)(F.Bl,{}),children:(0,t.jsxs)(Te.dO,{children:[(0,t.jsx)(Te.qh,{path:`/plugins/${b.p}/content-types/create-content-type`,component:Sn}),(0,t.jsx)(Te.qh,{path:`/plugins/${b.p}/content-types/:uid`,component:Sn}),(0,t.jsx)(Te.qh,{path:`/plugins/${b.p}/component-categories/:categoryUid`,component:rr})]})})})})})]})}},Symbol.toStringTag,{value:"Module"}))},35336:(U,H,a)=>{var t=a(8928),f=a(88974),h=a(20598),k=a(57505),D=a(22171),R=a(49605),x=a(30660),T=a(88765),W=a(85306),J=30,_="...",ae=/\w*$/;function Ee(le,ie){var te=J,se=_;if(k(ie)){var ne="separator"in ie?ie.separator:ne;te="length"in ie?T(ie.length):te,se="omission"in ie?t(ie.omission):se}le=W(le);var Fe=le.length;if(h(le)){var Ne=x(le);Fe=Ne.length}if(te>=Fe)return le;var pe=te-R(se);if(pe<1)return se;var xe=Ne?f(Ne,0,pe).join(""):le.slice(0,pe);if(ne===void 0)return xe+se;if(Ne&&(pe+=xe.length-pe),D(ne)){if(le.slice(pe).search(ne)){var at,ee=xe;for(ne.global||(ne=RegExp(ne.source,W(ae.exec(ne))+"g")),ne.lastIndex=0;at=ne.exec(ee);)var Je=at.index;xe=xe.slice(0,Je===void 0?pe:Je)}}else if(le.indexOf(t(ne),pe)!=pe){var st=xe.lastIndexOf(ne);st>-1&&(xe=xe.slice(0,st))}return xe+se}U.exports=Ee},41225:(U,H,a)=>{var t=a(81204),f=a(51646),h="[object RegExp]";function k(D){return f(D)&&t(D)==h}U.exports=k},45635:(U,H,a)=>{var t=a(87212),f=a(4191),h=a(39226),k=a(3956),D=h(function(R,x){if(R==null)return[];var T=x.length;return T>1&&k(R,x[0],x[1])?x=[]:T>2&&k(x[0],x[1],x[2])&&(x=[x[0]]),f(R,t(x,1),[])});U.exports=D},48126:(U,H,a)=>{var t=a(64958);function f(h,k,D){for(var R=-1,x=h.criteria,T=k.criteria,W=x.length,J=D.length;++R<W;){var _=t(x[R],T[R]);if(_){if(R>=J)return _;var ae=D[R];return _*(ae=="desc"?-1:1)}}return h.index-k.index}U.exports=f},49605:(U,H,a)=>{var t=a(33999),f=a(20598),h=a(71387);function k(D){return f(D)?h(D):t(D)}U.exports=k},64958:(U,H,a)=>{var t=a(91662);function f(h,k){if(h!==k){var D=h!==void 0,R=h===null,x=h===h,T=t(h),W=k!==void 0,J=k===null,_=k===k,ae=t(k);if(!J&&!ae&&!T&&h>k||T&&W&&_&&!J&&!ae||R&&W&&_||!D&&_||!x)return 1;if(!R&&!T&&!ae&&h<k||ae&&D&&x&&!R&&!T||J&&D&&x||!W&&x||!_)return-1}return 0}U.exports=f},71387:U=>{var H="\\ud800-\\udfff",a="\\u0300-\\u036f",t="\\ufe20-\\ufe2f",f="\\u20d0-\\u20ff",h=a+t+f,k="\\ufe0e\\ufe0f",D="["+H+"]",R="["+h+"]",x="\\ud83c[\\udffb-\\udfff]",T="(?:"+R+"|"+x+")",W="[^"+H+"]",J="(?:\\ud83c[\\udde6-\\uddff]){2}",_="[\\ud800-\\udbff][\\udc00-\\udfff]",ae="\\u200d",Ee=T+"?",le="["+k+"]?",ie="(?:"+ae+"(?:"+[W,J,_].join("|")+")"+le+Ee+")*",te=le+Ee+ie,se="(?:"+[W+R+"?",R,J,_,D].join("|")+")",ne=RegExp(x+"(?="+x+")|"+se+te,"g");function Fe(Ne){for(var pe=ne.lastIndex=0;ne.test(Ne);)++pe;return pe}U.exports=Fe},71547:(U,H,a)=>{var t=a(19913);function f(h){return h&&h.length?t(h):[]}U.exports=f},74565:U=>{function H(a,t){var f=a.length;for(a.sort(t);f--;)a[f]=a[f].value;return a}U.exports=H},75821:(U,H,a)=>{var t=a(91522);function f(h,k){return function(D,R){if(D==null)return D;if(!t(D))return h(D,R);for(var x=D.length,T=k?x:-1,W=Object(D);(k?T--:++T<x)&&R(W[T],T,W)!==!1;);return D}}U.exports=f},76079:(U,H,a)=>{"use strict";a.d(H,{B:()=>k});var t=a(92132),f=a(63891),h=a(56654);const k=({options:R,...x})=>(0,t.jsx)(h.KF,{...x,children:R.map(T=>"children"in T?(0,t.jsx)(h.np,{label:T.label,values:T.children.map(W=>W.value.toString()),children:T.children.map(W=>(0,t.jsx)(D,{value:W.value,children:W.label},W.value))},T.label):(0,t.jsx)(h.fe,{value:T.value,children:T.label},T.value))}),D=(0,f.Ay)(h.fe)`
  padding-left: ${({theme:R})=>R.spaces[7]};
`},88532:(U,H,a)=>{var t=a(94445),f=a(7233),h=a(45353),k=a(82261);function D(R,x){return function(T,W){var J=k(T)?t:f,_=x?x():{};return J(T,R,h(W,2),_)}}U.exports=D},89102:(U,H,a)=>{var t=a(85306);function f(h){return t(h).toLowerCase()}U.exports=f},94445:U=>{function H(a,t,f,h){for(var k=-1,D=a==null?0:a.length;++k<D;){var R=a[k];t(h,R,f(R),a)}return h}U.exports=H},94710:(U,H,a)=>{var t=a(95292),f=a(88532),h=Object.prototype,k=h.hasOwnProperty,D=f(function(R,x,T){k.call(R,T)?R[T].push(x):t(R,T,[x])});U.exports=D},97449:(U,H,a)=>{var t=a(85373),f=a(75821),h=f(t);U.exports=h}}]);
