"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[8441],{58441:(i,e,a)=>{a.r(e),a.d(e,{default:()=>l});const l={"CMEditViewCopyLocale.copy-failure":"Yerel ayarlar kopyalanamad\u0131","CMEditViewCopyLocale.copy-success":"Yerel ayarlar kopyaland\u0131","CMEditViewCopyLocale.copy-text":"Ba\u015Fka bir yerel ayardan doldur","CMEditViewCopyLocale.submit-text":"Evet, doldur","CMListView.popover.display-locales.label":"Terc\xFCme edilmi\u015F yerel ayarlar\u0131 g\xF6ster","CheckboxConfirmation.Modal.body":"Devred\u0131\u015F\u0131 b\u0131rakmak istiyor musun?","CheckboxConfirmation.Modal.button-confirm":"<PERSON><PERSON>, devred\u0131\u015F\u0131 b\u0131rak","CheckboxConfirmation.Modal.content":"Yerelle\u015Ftirmeyi devred\u0131\u015F\u0131 b\u0131rakmak varsay\u0131lan yerel ayar\u0131n\u0131z ile ili\u015Fkili olmayan t\xFCm di\u011Fer i\xE7eriklerin silinmesine neden olur.","Field.localized":"Bu de\u011Fer se\xE7ilmi\u015F olan yerel ayara \xF6zg\xFCd\xFCr","Field.not-localized":"Bu de\u011Fer t\xFCm yerel ayarlarda ortakt\u0131r","Settings.list.actions.add":"Yeni bir yerel ayar ekle","Settings.list.actions.delete":"Bir yerel ayar\u0131 sil","Settings.list.actions.deleteAdditionalInfos":"Bu aktif yerel ayar versiyonlar\u0131n\u0131 silecektir <em>(Uluslararas\u0131la\u015Ft\u0131rma'dan)</em>","Settings.list.actions.edit":"Bir yerel ayar\u0131 d\xFCzenle","Settings.list.description":"Uluslararas\u0131la\u015Ft\u0131rma eklentisinin ayarlar\u0131n\u0131 d\xFCzenle","Settings.list.empty.description":"Bu beklendik bir davran\u0131\u015F de\u011Fil. Veritaban\u0131na elle m\xFCdahale oldu\u011Fu anlam\u0131na geliyor. Strapinin d\xFCzg\xFCn \xE7al\u0131\u015Fabilmesi i\xE7in veritaban\u0131n\u0131zda en az bir adet yerel ayar\u0131n oldu\u011Fundan emin olun.","Settings.list.empty.title":"Hi\xE7bir yerel ayar yok.","Settings.locales.default":"Varsay\u0131lan","Settings.locales.list.sort.default":"Varsay\u0131lan yerel ayara g\xF6re diz","Settings.locales.list.sort.displayName":"G\xF6r\xFCnt\xFClenme ad\u0131na g\xF6re diz","Settings.locales.list.sort.id":"Kimlik numaras\u0131na g\xF6re diz","Settings.locales.modal.advanced":"Geli\u015Fmi\u015F ayarlar","Settings.locales.modal.advanced.setAsDefault":"Varsay\u0131lan yerel ayar olarak belirle","Settings.locales.modal.advanced.setAsDefault.hint":"Bir varsay\u0131lan yerel ayar gereklidir. Farkl\u0131 bir yerel ayar se\xE7erek de\u011Fi\u015Ftirin","Settings.locales.modal.advanced.settings":"Ayarlar","Settings.locales.modal.base":"Temel Ayarlar","Settings.locales.modal.create.alreadyExist":"Bu yerel ayar zaten mevcut","Settings.locales.modal.create.defaultLocales.loading":"Mevcut yerel ayarlar y\xFCkleniyor...","Settings.locales.modal.create.success":"Yerel ayar ba\u015Far\u0131yla eklendi","Settings.locales.modal.create.tab.label":"Temel uluslararas\u0131la\u015Ft\u0131rma ayarlar\u0131 ile geli\u015Fmi\u015F ayarlar aras\u0131nda ge\xE7i\u015F yap\u0131l\u0131yor","Settings.locales.modal.delete.confirm":"Evet, sil","Settings.locales.modal.delete.message":"Bu yerel ayar\u0131 silmek ili\u015Fkili t\xFCm i\xE7eri\u011Fi de siler. E\u011Fer i\xE7erikleri korumak istiyorsan\u0131z, \xF6ncelikle farkl\u0131 bir yerel ayar ile ili\u015Fkilendirin.","Settings.locales.modal.delete.secondMessage":"Bu yerel ayar\u0131 silmek istiyor musun?","Settings.locales.modal.delete.success":"Yerel ayar ba\u015Far\u0131yla silindi","Settings.locales.modal.edit.confirmation":"Tamamla","Settings.locales.modal.edit.locales.label":"Yerel Ayarlar","Settings.locales.modal.edit.success":"Yerel ayar ba\u015Far\u0131yla d\xFCzenlendi","Settings.locales.modal.edit.tab.label":"Temel uluslararas\u0131la\u015Ft\u0131rma ayarlar\u0131 ile geli\u015Fmi\u015F ayarlar aras\u0131nda ge\xE7i\u015F yap\u0131l\u0131yor","Settings.locales.modal.locales.displayName":"Yerel ayar g\xF6r\xFCnt\xFClenme ad\u0131","Settings.locales.modal.locales.displayName.description":"Yerel ayar y\xF6netim panelinde bu isimde g\xF6r\xFCnt\xFClenecek","Settings.locales.modal.locales.displayName.error":"Yerel ayar g\xF6r\xFCnt\xFClenme ad\u0131 50 karakterden k\xFC\xE7\xFCk olmal\u0131d\u0131r","Settings.locales.modal.locales.label":"Yerel ayarlar","Settings.locales.modal.locales.loaded":"Yerel ayarlar ba\u015Far\u0131yla y\xFCklendi.","Settings.locales.modal.title":"Kurulumlar","Settings.locales.row.default-locale":"Varsay\u0131lan yerel ayar","Settings.locales.row.displayName":"G\xF6r\xFCnt\xFClenme ad\u0131","Settings.locales.row.id":"Kimlik Numaras\u0131","Settings.permissions.loading":"\u0130zinler y\xFCkleniyor","Settings.permissions.read.denied.description":"Bunu okuyabilmek i\xE7in sistem y\xF6neticinizle ileti\u015Fime ge\xE7in.","Settings.permissions.read.denied.title":"Bu i\xE7eriye ula\u015Fmak i\xE7in yetkiniz bulunmuyor.","actions.select-locale":"Bir yerel ayar se\xE7in","components.Select.locales.not-available":"\u0130\xE7erik mevcut de\u011Fil","plugin.description.long":"Bu eklenti, hem Y\xF6netim paneli hem de API \xFCzerinden, farkl\u0131 dillerdeki i\xE7eri\u011Fi olu\u015Fturma, okuma ve g\xFCncelleme imkan\u0131 sa\u011Flar.","plugin.description.short":"Bu eklenti, hem Y\xF6netim paneli hem de API \xFCzerinden, farkl\u0131 dillerdeki i\xE7eri\u011Fi olu\u015Fturma, okuma ve g\xFCncelleme imkan\u0131 sa\u011Flar.","plugin.name":"Uluslararas\u0131la\u015Ft\u0131rma","plugin.schema.i18n.ensure-unique-localization":"Benzersiz alanlar yerelle\u015Ftirilmelidir","plugin.schema.i18n.localized.description-content-type":"\u0130\xE7erikleri yerelle\u015Ftirebilmenize imkan tan\u0131r","plugin.schema.i18n.localized.description-field":"Bu alan farkl\u0131 yerel ayarlarda farkl\u0131 de\u011Fer alabilir","plugin.schema.i18n.localized.label-content-type":"Bu \u0130\xE7erik-Tipi i\xE7in yerelle\u015Ftirmeyi etkinle\u015Ftir","plugin.schema.i18n.localized.label-field":"Bu Alan i\xE7in yerelle\u015Ftirmeyi etkinle\u015Ftir"}}}]);
