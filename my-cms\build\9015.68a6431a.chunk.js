"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[9015],{9015:(l,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a={"BoundRoute.title":"Ba\u011Fl\u0131 rota","EditForm.inputSelect.description.role":"Ye<PERSON> kimli\u011Fi do\u011Frulanm\u0131\u015F kullan\u0131c\u0131y\u0131 se\xE7ilen rol\xFC ekler.","EditForm.inputSelect.label.role":"Kim<PERSON>\u011Fi do\u011Frulanm\u0131\u015F kullan\u0131c\u0131lar i\xE7in varsay\u0131lan rol","EditForm.inputToggle.description.email":"Kullan\u0131c\u0131y\u0131, farkl\u0131 kimlik do\u011Frulama sa\u011Flay\u0131c\u0131lar\u0131yla ayn\u0131 e-posta adresini kullanarak birden fazla hesap olu\u015Fturmas\u0131na izin vermeyin.","EditForm.inputToggle.description.email-confirmation":"Etkinle\u015Ftirildi\u011Finde yeni kay\u0131tl\u0131 kullan\u0131c\u0131lar bir onay e-postas\u0131 al\u0131r.","EditForm.inputToggle.description.email-confirmation-redirection":"E-postan\u0131z\u0131 onaylad\u0131ktan sonra, y\xF6nlendirilece\u011Finiz yeri se\xE7in.","EditForm.inputToggle.description.sign-up":"Devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131\u011F\u0131nda (KAPALI), kay\u0131t i\u015Flemi yasakt\u0131r. Art\u0131k kullan\u0131lan sa\u011Flay\u0131c\u0131 ne olursa olsun hi\xE7 kimse abone olamaz.","EditForm.inputToggle.label.email":"E-posta adresi ba\u015F\u0131na bir hesap","EditForm.inputToggle.label.email-confirmation":"E-posta onay\u0131n\u0131 etkinle\u015Ftir","EditForm.inputToggle.label.email-confirmation-redirection":"Y\xF6nlendirme URL'si","EditForm.inputToggle.label.sign-up":"Kay\u0131tlar\u0131 etkinle\u015Ftir","EditForm.inputToggle.placeholder.email-confirmation-redirection":"\xF6r: https://yourfrontend.com/email-confirmation-redirection","EditForm.inputToggle.placeholder.email-reset-password":"\xF6r: https://yourfrontend.com/reset-password","EditPage.form.roles":"Rol detaylar\u0131","Email.template.data.loaded":"E-posta \u015Fablonlar\u0131 y\xFCklendi","Email.template.email_confirmation":"E-posta adresi do\u011Frulamas\u0131","Email.template.form.edit.label":"\u015Eablonu d\xFCzenle","Email.template.table.action.label":"eylem","Email.template.table.icon.label":"ikon","Email.template.table.name.label":"ad","Form.advancedSettings.data.loaded":"Geli\u015Fmi\u015F ayarlar verisi y\xFCklendi","HeaderNav.link.advancedSettings":"Geli\u015Fmi\u015F Ayarlar","HeaderNav.link.emailTemplates":"E-posta \u015Eablonlar\u0131","HeaderNav.link.providers":"Sa\u011Flay\u0131c\u0131lar\u0131","Plugin.permissions.plugins.description":"{name} eklentisi i\xE7in izin verilen t\xFCm eylemleri tan\u0131mlay\u0131n.","Plugins.header.description":"Yaln\u0131zca bir g\xFCzergahla s\u0131n\u0131rland\u0131r\u0131lan i\u015Flemler a\u015Fa\u011F\u0131da listelenmi\u015Ftir.","Plugins.header.title":"\u0130zinler","Policies.header.hint":"Uygulaman\u0131n eylemlerini veya eklentinin eylemlerini se\xE7in ve ba\u011Fl\u0131 rotay\u0131 g\xF6r\xFCnt\xFClemek i\xE7in di\u015Fli \xE7ark simgesini t\u0131klay\u0131n","Policies.header.title":"Geli\u015Fmi\u015F Ayarlar","PopUpForm.Email.email_templates.inputDescription":"De\u011Fi\u015Fkenleri nas\u0131l kullanaca\u011F\u0131n\u0131zdan emin de\u011Filseniz, {link}","PopUpForm.Email.link.documentation":"dok\xFCmantasyonu kontrol et.","PopUpForm.Email.options.from.email.label":"G\xF6nderenin E-posta","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"G\xF6nderenin ad\u0131","PopUpForm.Email.options.from.name.placeholder":"Kai Doe","PopUpForm.Email.options.message.label":"Mesaj","PopUpForm.Email.options.object.label":"Konu","PopUpForm.Email.options.object.placeholder":"%APP_NAME% i\xE7in e-posta adresini do\u011Frula","PopUpForm.Email.options.response_email.label":"Yan\u0131t e-postas\u0131","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"Devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131ysa kullan\u0131c\u0131lar bu sa\u011Flay\u0131c\u0131y\u0131 kullanamaz.","PopUpForm.Providers.enabled.label":"Etkinle\u015Ftirme","PopUpForm.Providers.key.label":"Web istemcisi ID","PopUpForm.Providers.key.placeholder":"MET\u0130N","PopUpForm.Providers.redirectURL.front-end.label":"Aray\xFCz uygulaman\u0131z\u0131n y\xF6nlendirme URL'si","PopUpForm.Providers.redirectURL.label":"{provider} uygulama ayarlar\u0131na ekleyece\u011Fin y\xF6nlendirme URLi","PopUpForm.Providers.secret.label":"Web istemcisi Secret","PopUpForm.Providers.secret.placeholder":"MET\u0130N","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"E-posta \u015Eablonlar\u0131n\u0131 D\xFCzenle","PopUpForm.header.edit.providers":"Sa\u011Flay\u0131c\u0131y\u0131 D\xFCzenle","Providers.data.loaded":"Sa\u011Flay\u0131c\u0131lar y\xFCklendi","Providers.image":"G\xF6rsel","Providers.status":"Durum","Roles.empty":"Hen\xFCz hi\xE7 rol\xFCn yok.","Roles.empty.search":"Aramaya uygun rol bulunmad\u0131.","Settings.roles.deleted":"Rol silindi","Settings.roles.edited":"Rol d\xFCzenlendi","Settings.section-label":"Kullan\u0131c\u0131lar ve \u0130zinler eklentisi","components.Input.error.validation.email":"Bu ge\xE7ersiz bir e-posta","components.Input.error.validation.json":"Bu JSON bi\xE7imine uymuyor","components.Input.error.validation.max":"De\u011Fer \xE7ok y\xFCksek.","components.Input.error.validation.maxLength":"De\u011Fer \xE7ok uzun.","components.Input.error.validation.min":"De\u011Fer \xE7ok d\xFC\u015F\xFCk.","components.Input.error.validation.minLength":"De\u011Fer \xE7ok k\u0131sa.","components.Input.error.validation.minSupMax":"\xDCst olamaz","components.Input.error.validation.regex":"De\u011Fer RegExp'e uymuyor.","components.Input.error.validation.required":"De\u011Fer gerekli.","components.Input.error.validation.unique":"De\u011Fer zaten kullan\u0131l\u0131yor.","notification.success.submit":"Ayarlar g\xFCncellendi","page.title":"Ayarlar - Roller","plugin.description.long":"Servisinizi JWT'ye dayal\u0131 tam bir kimlik do\u011Frulama i\u015Flemi ile koruyun. Bu eklenti, kullan\u0131c\u0131 gruplar\u0131 aras\u0131ndaki izinleri y\xF6netmenize izin veren bir ACL stratejisiyle de gelir.","plugin.description.short":"Servisinizi JWT'ye dayal\u0131 tam bir kimlik do\u011Frulama i\u015Flemi ile koruyun","plugin.name":"Roller ve \u0130zinler","popUpWarning.button.cancel":"\u0130ptal Et","popUpWarning.button.confirm":"Onayla","popUpWarning.title":"L\xFCtfen onayla","popUpWarning.warning.cancel":"De\u011Fi\u015Fiklikleri iptal etmek istedi\u011Finden emin misin?"}}}]);
