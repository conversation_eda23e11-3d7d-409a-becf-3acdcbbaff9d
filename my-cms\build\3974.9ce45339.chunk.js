"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3974],{23974:(z,e,o)=>{o.r(e),o.d(e,{Analytics:()=>t,Documentation:()=>a,Email:()=>n,Password:()=>i,Provider:()=>r,ResetPasswordToken:()=>s,Role:()=>p,Username:()=>u,Users:()=>l,anErrorOccurred:()=>m,clearLabel:()=>c,dark:()=>d,default:()=>S,light:()=>g,or:()=>j,selectButtonTitle:()=>k,skipToContent:()=>v,submit:()=>b});const t="Analitika",a="Dokumentacija",n="E-po\u0161ta",i="Lozinka",r="Dobavlja\u010D",s="<PERSON>ni\u0161ti token lozinke",p="<PERSON>log<PERSON>",u="<PERSON><PERSON><PERSON>\u010Dko ime",l="Korisnici",m="Upa! Ne\u0161to je po\u0161lo krivo. Molimo, poku\u0161ajte ponovno.",c="O\u010Disti",d="tamno",g="Svjetlo",j="ILI",k="Odaberi",v="Presko\u010Di na sadr\u017Eaj",b="Po\u0161alji",S={Analytics:t,"Auth.components.Oops.text":"Va\u0161 ra\u010Dun je isklju\u010Den.","Auth.components.Oops.text.admin":"Ako je ovo pogre\u0161ka, obratite se svom administratoru.","Auth.components.Oops.title":"Ups...","Auth.form.active.label":"Aktivno","Auth.form.button.forgot-password":"Po\u0161alji e-po\u0161tu","Auth.form.button.go-home":"VRATI SE NA PO\u010CETAK","Auth.form.button.login":"Prijava","Auth.form.button.login.providers.error":"Ne mo\u017Eemo vas spojiti putem odabranog pru\u017Eatelja usluga.","Auth.form.button.login.strapi":"Prijavite se putem Strapi","Auth.form.button.password-recovery":"Oporavak lozinke","Auth.form.button.register":"Po\u010Dnimo","Auth.form.confirmPassword.label":"Potvrdi lozinku","Auth.form.currentPassword.label":"Trenutna lozinka","Auth.form.email.label":"E-po\u0161ta","Auth.form.email.placeholder":"npr. <EMAIL>","Auth.form.error.blocked":"Va\u0161 ra\u010Dun je blokirao administrator.","Auth.form.error.code.provide":"Naveden je pogre\u0161an kod.","Auth.form.error.confirmed":"E-po\u0161ta va\u0161eg ra\u010Duna nije potvr\u0111ena.","Auth.form.error.email.invalid":"Ova e-po\u0161ta nije va\u017Ee\u0107a.","Auth.form.error.email.provide":"Unesite svoje korisni\u010Dko ime ili svoju e-po\u0161tu.","Auth.form.error.email.taken":"E-po\u0161ta je ve\u0107 zauzeta.","Auth.form.error.invalid":"Identifikator ili lozinka nisu valjani.","Auth.form.error.params.provide":"Navedeni neto\u010Dni parametri.","Auth.form.error.password.format":"Va\u0161a lozinka ne mo\u017Ee sadr\u017Eavati simbol `$` vi\u0161e od tri puta.","Auth.form.error.password.local":"Ovaj korisnik nikada nije postavio lokalnu lozinku, molimo prijavite se preko pru\u017Eatelja usluga kori\u0161tenog tijekom kreiranja ra\u010Duna.","Auth.form.error.password.matching":"Lozinke se ne podudaraju.","Auth.form.error.password.provide":"Molimo dajte svoju lozinku.","Auth.form.error.ratelimit":"Previ\u0161e poku\u0161aja, poku\u0161ajte ponovno za minutu.","Auth.form.error.user.not-exist":"Ova e-po\u0161ta ne postoji.","Auth.form.error.username.taken":"Korisni\u010Dko ime je ve\u0107 zauzeto.","Auth.form.firstname.label":"Ime","Auth.form.firstname.placeholder":"npr. Darijo","Auth.form.forgot-password.email.label":"Unesite svoju e-po\u0161tu","Auth.form.forgot-password.email.label.success":"E-po\u0161ta je uspje\u0161no poslana","Auth.form.lastname.label":"Prezime","Auth.form.lastname.placeholder":"npr. Srna","Auth.form.password.hide-password":"Sakrij lozinku","Auth.form.password.hint":"Mora sadr\u017Eavati najmanje 8 znakova, 1 veliko slovo, 1 malo slovo i 1 broj","Auth.form.password.show-password":"Prika\u017Ei lozinku","Auth.form.register.news.label":"Obavje\u0161tavajte me o novim zna\u010Dajkama i nadolaze\u0107im pobolj\u0161anjima (time prihva\u0107ate {terms} i {policy}).","Auth.form.register.subtitle":"Ovo se koristi samo za autentifikaciju u Strapi. Svi spremljeni podaci bit \u0107e pohranjeni u va\u0161oj bazi podataka.","Auth.form.rememberMe.label":"Zapamti me","Auth.form.username.label":"Korisni\u010Dko ime","Auth.form.username.placeholder":"npr. Kai_Doe","Auth.form.welcome.subtitle":"Prijavite se na svoj Strapi ra\u010Dun","Auth.form.welcome.title":"Dobro do\u0161li u Strapi!","Auth.link.forgot-password":"Zaboravili ste lozinku?","Auth.link.ready":"Spremni za prijavu?","Auth.link.signin":"Prijavite se","Auth.link.signin.account":"Ve\u0107 imate ra\u010Dun?","Auth.login.sso.divider":"Ili se prijavite s","Auth.login.sso.loading":"U\u010Ditavanje pru\u017Eatelja...","Auth.login.sso.subtitle":"Prijavite se na svoj ra\u010Dun putem SSO-a","Auth.privacy-policy-agreement.policy":"pravila privatnosti","Auth.privacy-policy-agreement.terms":"uvjeti","Auth.reset-password.title":"Poni\u0161ti lozinku","Content Manager":"Upravitelj sadr\u017Eaja","Content Type Builder":"Izrada vrsta sadr\u017Eaja",Documentation:a,Email:n,"Files Upload":"U\u010Ditavanje datoteka","HomePage.helmet.title":"Po\u010Detna stranica","HomePage.roadmap":"Pogledajte na\u0161 plan razvoja","HomePage.welcome.congrats":"\u010Cestitamo!","HomePage.welcome.congrats.content":"Prijavljeni ste kao prvi administrator. Da biste otkrili mo\u0107ne zna\u010Dajke koje nudi Strapi,","HomePage.welcome.congrats.content.bold":"preporu\u010Dujemo da stvorite svoju prvu vrstu zbirke.","Media Library":"Medijska biblioteka","New entry":"Novi unos",Password:i,Provider:r,ResetPasswordToken:s,Role:p,"Roles & Permissions":"Uloge i dopu\u0161tenja","Roles.ListPage.notification.delete-all-not-allowed":"Neke uloge nije bilo mogu\u0107e izbrisati jer su povezane s korisnicima","Roles.ListPage.notification.delete-not-allowed":"Uloga se ne mo\u017Ee izbrisati ako je povezana s korisnicima","Roles.RoleRow.select-all":"Odaberite {name} za skupne akcije","Roles.RoleRow.user-count":"{number, plural, =0 {# user} one {# user} other {# users}}","Roles.components.List.empty.withSearch":"Ne postoji uloga koja odgovara pretrazi ({search})...","Settings.PageTitle":"Postavke \u2014 {name}","Settings.apiTokens.ListView.headers.createdAt":"Stvoreno u","Settings.apiTokens.ListView.headers.description":"Opis","Settings.apiTokens.ListView.headers.lastUsedAt":"Posljednji put kori\u0161teno","Settings.apiTokens.ListView.headers.name":"Ime","Settings.apiTokens.ListView.headers.type":"Vrsta tokena","Settings.apiTokens.regenerate":"Ponovo generiraj","Settings.apiTokens.createPage.title":"Stvori API token","Settings.transferTokens.createPage.title":"Stvori token prijenosa","Settings.tokens.RegenerateDialog.title":"Ponovo generiraj token","Settings.apiTokens.addFirstToken":"Dodajte svoj prvi API token","Settings.apiTokens.addNewToken":"Dodaj novi API token","Settings.tokens.copy.editMessage":"Iz sigurnosnih razloga, svoj token mo\u017Eete vidjeti samo jednom.","Settings.tokens.copy.editTitle":"Ovaj token vi\u0161e nije dostupan.","Settings.tokens.copy.lastWarning":"Obavezno kopirajte ovaj token, ne\u0107ete ga vi\u0161e mo\u0107i vidjeti!","Settings.apiTokens.create":"Stvori novi API token","Settings.apiTokens.createPage.permissions.description":"Dolje su navedene samo radnje vezane rutom.","Settings.apiTokens.createPage.permissions.title":"Dozvole","Settings.apiTokens.description":"Popis generiranih tokena za kori\u0161tenje API-ja","Settings.apiTokens.createPage.BoundRoute.title":"Povezana ruta za","Settings.apiTokens.createPage.permissions.header.title":"Napredne postavke","Settings.apiTokens.createPage.permissions.header.hint":"Odaberite radnje aplikacije ili radnje dodatka i kliknite na ikonu zup\u010Danika za prikaz vezane rute","Settings.tokens.duration.30-days":"30 dana","Settings.tokens.duration.7-days":"7 dana","Settings.tokens.duration.90-days":"90 dana","Settings.tokens.duration.expiration-date":"Datum isteka","Settings.tokens.duration.unlimited":"Neograni\u010Deno","Settings.apiTokens.emptyStateLayout":"Jo\u0161 nemate nikakav sadr\u017Eaj...","Settings.tokens.form.duration":"Trajanje tokena","Settings.tokens.form.type":"Vrsta tokena","Settings.tokens.form.name":"Ime","Settings.tokens.form.description":"Opis","Settings.tokens.notification.copied":"Token kopiran u me\u0111uspremnik.","Settings.tokens.popUpWarning.message":"Jeste li sigurni da \u017Eelite ponovno generirati ovaj token?","Settings.tokens.Button.cancel":"Odustani","Settings.tokens.Button.regenerate":"Ponovo generiraj","Settings.tokens.types.full-access":"Puni pristup","Settings.tokens.types.read-only":"Samo za \u010Ditanje","Settings.tokens.types.custom":"Prilago\u0111eno","Settings.tokens.regenerate":"Ponovo generiraj","Settings.transferTokens.title":"Tokeni za prijenos","Settings.transferTokens.description":"Popis generiranih prijenosnih tokena","Settings.transferTokens.create":"Stvori novi token za prijenos","Settings.transferTokens.addFirstToken":"Dodajte svoj prvi prijenosni token","Settings.transferTokens.addNewToken":"Dodaj novi token prijenosa","Settings.transferTokens.emptyStateLayout":"Jo\u0161 nemate nikakav sadr\u017Eaj...","Settings.tokens.ListView.headers.name":"Ime","Settings.tokens.ListView.headers.description":"Opis","Settings.transferTokens.ListView.headers.type":"Vrsta tokena","Settings.tokens.ListView.headers.createdAt":"Stvoreno u","Settings.tokens.ListView.headers.lastUsedAt":"Posljednji put kori\u0161teno","Settings.application.ee.admin-seats.count":"<text>{enforcementUserCount}</text>/{permittedSeats}","Settings.application.ee.admin-seats.at-limit-tooltip":"Na ograni\u010Denju: dodajte mjesta da pozovete vi\u0161e korisnika","Settings.application.ee.admin-seats.add-seats":"{isHostedOnStrapiCloud, select, true {Add seats} other {Contact sales}}","Settings.application.customization":"Prilagodba","Settings.application.customization.auth-logo.carousel-hint":"Zamijeni logotip na stranicama za provjeru autenti\u010Dnosti","Settings.application.customization.carousel-hint":"Promijenite logotip administratorske plo\u010De (maksimalna dimenzija: {dimension}x{dimension}, maksimalna veli\u010Dina datoteke: {size}KB)","Settings.application.customization.carousel-slide.label":"Slajd s logotipom","Settings.application.customization.carousel.auth-logo.title":"Auth logo","Settings.application.customization.carousel.change-action":"Promijeni logotip","Settings.application.customization.carousel.menu-logo.title":"Logotip izbornika","Settings.application.customization.carousel.reset-action":"Poni\u0161ti logotip","Settings.application.customization.carousel.title":"Logotip","Settings.application.customization.menu-logo.carousel-hint":"Zamijeni logotip u glavnoj navigaciji","Settings.application.customization.modal.cancel":"Odustani","Settings.application.customization.modal.pending":"Logotip na \u010Dekanju","Settings.application.customization.modal.pending.card-badge":"slika","Settings.application.customization.modal.pending.choose-another":"Odaberi drugi logo","Settings.application.customization.modal.pending.subtitle":"Upravljajte odabranim logotipom prije u\u010Ditavanja","Settings.application.customization.modal.pending.title":"Logotip spreman za prijenos","Settings.application.customization.modal.pending.upload":"U\u010Ditaj logotip","Settings.application.customization.modal.tab.label":"Kako \u017Eelite prenijeti svoja sredstva?","Settings.application.customization.modal.upload":"U\u010Ditaj logotip","Settings.application.customization.modal.upload.cta.browse":"Pregledaj datoteke","Settings.application.customization.modal.upload.drag-drop":"Povucite i ispustite ovdje ili","Settings.application.customization.modal.upload.error-format":"U\u010Ditan je pogre\u0161an format (samo prihva\u0107eni formati: jpeg, jpg, png, svg).","Settings.application.customization.modal.upload.error-network":"Mre\u017Ena pogre\u0161ka","Settings.application.customization.modal.upload.error-size":"U\u010Ditana datoteka je prevelika (maksimalna dimenzija: {dimension}x{dimension}, maksimalna veli\u010Dina datoteke: {size}KB)","Settings.application.customization.modal.upload.file-validation":"Maksimalna dimenzija: {dimension}x{dimension}, maksimalna veli\u010Dina: {size}KB","Settings.application.customization.modal.upload.from-computer":"S ra\u010Dunala","Settings.application.customization.modal.upload.from-url":"S URL-a","Settings.application.customization.modal.upload.from-url.input-label":"URL","Settings.application.customization.modal.upload.next":"Dalje","Settings.application.customization.size-details":"Maksimalna dimenzija: {dimension}\xD7{dimension}, maksimalna veli\u010Dina datoteke: {size}KB","Settings.application.description":"Globalne informacije administrativne plo\u010De","Settings.application.edition-title":"trenutni plan","Settings.application.ee-or-ce":"{communityEdition, select, true {Community Edition} other {Enterprise Edition}}","Settings.application.get-help":"Potra\u017Eite pomo\u0107","Settings.application.link-pricing":"Pogledajte sve planove cijena","Settings.application.link-upgrade":"Nadogradite svoju administrativnu plo\u010Du","Settings.application.node-version":"Verzija nodejs","Settings.application.strapi-version":"Verzija Strapi","Settings.application.strapiVersion":"Verzija Strapi","Settings.application.title":"Pregled","Settings.error":"Pogre\u0161ka","Settings.global":"Globalne postavke","Settings.permissions":"Administrativna plo\u010Da","Settings.permissions.auditLogs.action":"Akcija","Settings.permissions.auditLogs.admin.auth.success":"Administratorska prijava","Settings.permissions.auditLogs.admin.logout":"Odjava administratora","Settings.permissions.auditLogs.component.create":"Stvori komponentu","Settings.permissions.auditLogs.component.delete":"Izbri\u0161i komponentu","Settings.permissions.auditLogs.component.update":"A\u017Euriraj komponentu","Settings.permissions.auditLogs.content-type.create":"Stvori vrstu sadr\u017Eaja","Settings.permissions.auditLogs.content-type.delete":"Izbri\u0161i vrstu sadr\u017Eaja","Settings.permissions.auditLogs.content-type.update":"A\u017Euriraj vrstu sadr\u017Eaja","Settings.permissions.auditLogs.date":"Datum","Settings.permissions.auditLogs.details":"Detalji dnevnika","Settings.permissions.auditLogs.entry.create":"Stvori unos {model, select, undefined {} ostalo { ({model})}}","Settings.permissions.auditLogs.entry.delete":"Izbri\u0161i unos {model, select, undefined {} ostalo { ({model})}}","Settings.permissions.auditLogs.entry.publish":"Objavi unos {model, select, undefined {} ostalo {({model})}}","Settings.permissions.auditLogs.entry.unpublish":"Poni\u0161ti objavu unosa {model, select, undefined {} ostalo { ({model})}}","Settings.permissions.auditLogs.entry.update":"A\u017Euriraj unos {model, select, undefined {} ostalo { ({model})}}","Settings.permissions.auditLogs.filters.combobox.aria-label":"Pretra\u017Eite i odaberite opciju za filtriranje","Settings.permissions.auditLogs.listview.header.subtitle":"Dnevnici svih aktivnosti koje su se dogodile u va\u0161em okru\u017Eenju","Settings.permissions.auditLogs.media.create":"Stvori medij","Settings.permissions.auditLogs.media.delete":"Izbri\u0161i medij","Settings.permissions.auditLogs.media.update":"A\u017Euriraj medije","Settings.permissions.auditLogs.payload":"Korisni teret","Settings.permissions.auditLogs.permission.create":"Stvori dozvolu","Settings.permissions.auditLogs.permission.delete":"Brisanje dopu\u0161tenja","Settings.permissions.auditLogs.permission.update":"Dozvola za a\u017Euriranje","Settings.permissions.auditLogs.role.create":"Stvori ulogu","Settings.permissions.auditLogs.role.delete":"Izbri\u0161i ulogu","Settings.permissions.auditLogs.role.update":"A\u017Euriraj ulogu","Settings.permissions.auditLogs.user":"Korisnik","Settings.permissions.auditLogs.user.create":"Stvori korisnika","Settings.permissions.auditLogs.user.delete":"Izbri\u0161i korisnika","Settings.permissions.auditLogs.user.fullname":"{firstname} {lastname}","Settings.permissions.auditLogs.user.update":"A\u017Euriraj korisnika","Settings.permissions.auditLogs.userId":"ID korisnika","Settings.permissions.category":"Postavke dopu\u0161tenja za {category}","Settings.permissions.category.plugins":"Postavke dopu\u0161tenja za {category} dodatak","Settings.permissions.conditions.anytime":"Bilo kad","Settings.permissions.conditions.apply":"Primijeni","Settings.permissions.conditions.can":"Mo\u017Ee","Settings.permissions.conditions.conditions":"Uvjeti","Settings.permissions.conditions.define-conditions":"Definirajte uvjete","Settings.permissions.conditions.links":"Veze","Settings.permissions.conditions.no-actions":"Prvo morate odabrati radnje (kreiraj, \u010Ditaj, a\u017Euriraj, ...) prije definiranja uvjeta za njih.","Settings.permissions.conditions.none-selected":"Bilo kad","Settings.permissions.conditions.or":"ILI","Settings.permissions.conditions.when":"Kada","Settings.permissions.select-all-by-permission":"Odaberi sva {label} dopu\u0161tenja","Settings.permissions.select-by-permission":"Odaberi dopu\u0161tenje {label}","Settings.permissions.users.active":"Aktivno","Settings.permissions.users.create":"Pozovi novog korisnika","Settings.permissions.users.email":"E-po\u0161ta","Settings.permissions.users.firstname":"Ime","Settings.permissions.users.form.sso":"Pove\u017Ei se s SSO-om","Settings.permissions.users.form.sso.description":"Kada je omogu\u0107eno (ON), korisnici se mogu prijaviti putem SSO-a","Settings.permissions.users.inactive":"Neaktivan","Settings.permissions.users.lastname":"Prezime","Settings.permissions.users.listview.header.subtitle":"Svi korisnici koji imaju pristup Strapi administratorskoj plo\u010Di","Settings.permissions.users.roles":"Uloge","Settings.permissions.users.strapi-author":"Autor","Settings.permissions.users.strapi-editor":"Ure\u0111iva\u010D","Settings.permissions.users.strapi-super-admin":"Super-administrator","Settings.permissions.users.tabs.label":"Dozvole za kartice","Settings.permissions.users.user-status":"Status korisnika","Settings.permissions.users.username":"Korisni\u010Dko ime","Settings.profile.form.notify.data.loaded":"Podaci va\u0161eg profila su u\u010Ditani","Settings.profile.form.section.experience.clear.select":"O\u010Disti odabrani jezik su\u010Delja","Settings.profile.form.section.experience.here":"ovdje","Settings.profile.form.section.experience.interfaceLanguage":"Jezik su\u010Delja","Settings.profile.form.section.experience.interfaceLanguage.hint":"Ovo \u0107e prikazati samo va\u0161e vlastito su\u010Delje na odabranom jeziku.","Settings.profile.form.section.experience.interfaceLanguageHelp":"Promjene postavki primjenjivat \u0107e se samo na vas. Vi\u0161e informacija dostupno je {here}.","Settings.profile.form.section.experience.mode.hint":"Prikazuje va\u0161e su\u010Delje u odabranom na\u010Dinu rada.","Settings.profile.form.section.experience.mode.label":"Na\u010Din su\u010Delja","Settings.profile.form.section.experience.mode.option-label":"na\u010Din {name}","Settings.profile.form.section.experience.title":"Iskustvo","Settings.profile.form.section.helmet.title":"Korisni\u010Dki profil","Settings.profile.form.section.profile.page.title":"Stranica profila","Settings.roles.create.description":"Definirajte prava dana ulozi","Settings.roles.create.title":"Stvori ulogu","Settings.roles.created":"Uloga stvorena","Settings.roles.edit.title":"Uredi ulogu","Settings.roles.form.button.users-with-role":"{number, plural, =0 {# korisnika} jedan {# korisnik} drugih {# korisnika}} s ovom ulogom","Settings.roles.form.created":"Stvoreno","Settings.roles.form.description":"Ime i opis uloge","Settings.roles.form.permission.property-label":"{label} dopu\u0161tenja","Settings.roles.form.permissions.attributesPermissions":"Dozvole za polja","Settings.roles.form.permissions.create":"Stvori","Settings.roles.form.permissions.delete":"Izbri\u0161i","Settings.roles.form.permissions.publish":"Objavi","Settings.roles.form.permissions.read":"Pro\u010Ditaj","Settings.roles.form.permissions.update":"A\u017Euriraj","Settings.roles.list.button.add":"Dodaj novu ulogu","Settings.roles.list.description":"Popis uloga","Settings.roles.title.singular":"uloga","Settings.sso.description":"Konfigurirajte postavke za zna\u010Dajku jedinstvene prijave.","Settings.sso.form.defaultRole.description":"Pripojit \u0107e novog autentificiranog korisnika odabranoj ulozi","Settings.sso.form.defaultRole.description-not-allowed":"Morate imati dopu\u0161tenje za \u010Ditanje administratorskih uloga","Settings.sso.form.defaultRole.label":"Zadana uloga","Settings.sso.form.registration.description":"Stvori novog korisnika na SSO prijavi ako ne postoji ra\u010Dun","Settings.sso.form.registration.label":"Automatska registracija","Settings.sso.title":"Jedna prijava","Settings.webhooks.create":"Stvori webhook","Settings.webhooks.create.header":"Stvori novo zaglavlje","Settings.webhooks.created":"Webhook stvoren","Settings.webhooks.event.publish-tooltip":"Ovaj doga\u0111aj postoji samo za sadr\u017Eaje s omogu\u0107enim sustavom Draft/Publish","Settings.webhooks.events.create":"Stvori","Settings.webhooks.events.update":"A\u017Euriraj","Settings.webhooks.form.events":"Doga\u0111aji","Settings.webhooks.form.headers":"Zaglavlja","Settings.webhooks.form.url":"URL","Settings.webhooks.headers.remove":"Ukloni redak zaglavlja {number}","Settings.webhooks.key":"Klju\u010D","Settings.webhooks.list.button.add":"Stvori novi webhook","Settings.webhooks.list.description":"Primajte obavijesti o POST promjenama","Settings.webhooks.list.empty.description":"Nije prona\u0111en nijedan webhooks","Settings.webhooks.list.empty.link":"Pogledajte na\u0161u dokumentaciju","Settings.webhooks.list.empty.title":"Jo\u0161 nema webhookova","Settings.webhooks.list.th.actions":"radnje","Settings.webhooks.list.th.status":"status","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, jedan {# zapis} drugi {# zapisa}} odabran","Settings.webhooks.trigger":"Okida\u010D","Settings.webhooks.trigger.cancel":"Otka\u017Ei okida\u010D","Settings.webhooks.trigger.pending":"Na \u010Dekanju...","Settings.webhooks.trigger.save":"Molimo spremite za okida\u010D","Settings.webhooks.trigger.success":"Uspjeh!","Settings.webhooks.trigger.success.label":"Okida\u010D je uspio","Settings.webhooks.trigger.test":"Testni okida\u010D","Settings.webhooks.trigger.title":"Spremi prije okidanja","Settings.webhooks.value":"Vrijednost","Usecase.back-end":"Pozadinski programer","Usecase.button.skip":"Presko\u010Di ovo pitanje","Usecase.content-creator":"Kreator sadr\u017Eaja","Usecase.front-end":"Prednji programer","Usecase.full-stack":"Programer punog stoga","Usecase.input.work-type":"Koju vrstu posla radite?","Usecase.notification.success.project-created":"Projekt je uspje\u0161no kreiran","Usecase.other":"Ostalo","Usecase.title":"Recite nam ne\u0161to vi\u0161e o sebi",Username:u,"Users & Permissions":"Korisnici i dozvole",Users:l,"Users.components.List.empty":"Nema korisnika...","Users.components.List.empty.withFilters":"Nema korisnika s primijenjenim filtrima...","Users.components.List.empty.withSearch":"Nema korisnika koji odgovaraju pretrazi ({search})...","admin.pages.MarketPlacePage.sort.label":"Razvrstaj po","admin.pages.MarketPlacePage.filters.categories":"Kategorije","admin.pages.MarketPlacePage.filters.categoriesSelected":"{count, plural, =0 {Nema kategorija} jedna {# kategorija} druga {# kategorija}} odabrano","admin.pages.MarketPlacePage.filters.collections":"Zbirke","admin.pages.MarketPlacePage.filters.collectionsSelected":"{count, plural, =0 {Nema zbirki} one {# zbirka} other {# zbirke}} selected","admin.pages.MarketPlacePage.helmet":"Tr\u017Ei\u0161te \u2014 dodaci","admin.pages.MarketPlacePage.missingPlugin.description":"Recite nam koji dodatak tra\u017Eite i obavijestit \u0107emo programere dodataka na\u0161e zajednice u slu\u010Daju da su u potrazi za inspiracijom!","admin.pages.MarketPlacePage.missingPlugin.title":"Nedostaje vam dodatak?","admin.pages.MarketPlacePage.offline.subtitle":"Morate biti spojeni na Internet za pristup Strapi Marketu.","admin.pages.MarketPlacePage.offline.title":"Izvan mre\u017Ee ste","admin.pages.MarketPlacePage.plugin.copy":"Kopiraj instalacijsku naredbu","admin.pages.MarketPlacePage.plugin.copy.success":"Naredba za instalaciju spremna za lijepljenje u va\u0161 terminal","admin.pages.MarketPlacePage.plugin.downloads":"Ovaj dodatak ima {downloadsCount} tjednih preuzimanja","admin.pages.MarketPlacePage.plugin.githubStars":"Ovaj je dodatak ozna\u010Den zvjezdicom {starsCount} na GitHubu","admin.pages.MarketPlacePage.plugin.info":"Saznajte vi\u0161e","admin.pages.MarketPlacePage.plugin.info.label":"Saznajte vi\u0161e o {pluginName}","admin.pages.MarketPlacePage.plugin.info.text":"Vi\u0161e","admin.pages.MarketPlacePage.plugin.installed":"Instalirano","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"Izradio Strapi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"Strapi je potvrdio dodatak","admin.pages.MarketPlacePage.plugin.version":'A\u017Eurirajte svoju Strapi verziju: "{strapiAppVersion}" na: "{versionRange}"',"admin.pages.MarketPlacePage.plugin.version.null":'Nije mogu\u0107e provjeriti kompatibilnost s va\u0161om Strapi verzijom: "{strapiAppVersion}"',"admin.pages.MarketPlacePage.plugins":"Dodaci","admin.pages.MarketPlacePage.provider.downloads":"Ovaj pru\u017Eatelj ima {downloadsCount} tjednih preuzimanja","admin.pages.MarketPlacePage.provider.githubStars":"Ovaj pru\u017Eatelj je ozna\u010Den zvjezdicom {starsCount} na GitHubu","admin.pages.MarketPlacePage.providers":"Ponu\u0111a\u010Di","admin.pages.MarketPlacePage.search.clear":"Obri\u0161i pretra\u017Eivanje","admin.pages.MarketPlacePage.search.empty":'Nema rezultata za "{target}"',"admin.pages.MarketPlacePage.search.placeholder":"Tra\u017Ei","admin.pages.MarketPlacePage.sort.alphabetical":"Abecedni red","admin.pages.MarketPlacePage.sort.alphabetical.selected":"Razvrstaj po abecednom redu","admin.pages.MarketPlacePage.sort.githubStars":"Broj GitHub zvjezdica","admin.pages.MarketPlacePage.sort.githubStars.selected":"Razvrstaj po GitHub zvjezdicama","admin.pages.MarketPlacePage.sort.newest":"Najnovije","admin.pages.MarketPlacePage.sort.newest.selected":"Razvrstaj po najnovijem","admin.pages.MarketPlacePage.sort.npmDownloads":"Broj preuzimanja","admin.pages.MarketPlacePage.sort.npmDownloads.selected":"Razvrstaj po npm preuzimanjus","admin.pages.MarketPlacePage.submit.plugin.link":"Po\u0161alji dodatak","admin.pages.MarketPlacePage.submit.provider.link":"Po\u0161alji davatelja","admin.pages.MarketPlacePage.subtitle":"Iskoristite Strapi vi\u0161e","admin.pages.MarketPlacePage.tab-group.label":"Dodaci i pru\u017Eatelji usluga za Strapi",anErrorOccurred:m,"app.component.CopyToClipboard.label":"Kopiraj u me\u0111uspremnik","app.component.search.label":"Tra\u017Ei {target}","app.component.table.duplicate":"Duplikat {target}","app.component.table.edit":"Uredi {target}","app.component.table.read":"Pro\u010Ditaj {target}","app.component.table.select.one-entry":"Odaberi {target}","app.component.table.view":"{target} detalji","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Pro\u010Ditajte najnovije vijesti o Strapi i ekosustavu.","app.components.BlockLink.code":"Primjeri koda","app.components.BlockLink.code.content":"U\u010Dite testiranjem stvarnih projekata koje je razvila zajednica.","app.components.BlockLink.documentation.content":"Otkrijte osnovne koncepte, vodi\u010De i upute.","app.components.BlockLink.tutorial":"Tutorijali","app.components.BlockLink.tutorial.content":"Slijedite upute korak po korak za kori\u0161tenje i prilagodbu Strapija.","app.components.BlockLink.cloud":"Strapi Cloud","app.components.BlockLink.cloud.content":"Potpuno sastavljaju\u0107a platforma za suradnju za pove\u0107anje brzine va\u0161eg tima.","app.components.Button.cancel":"Odustani","app.components.Button.confirm":"Potvrdi","app.components.Button.reset":"Poni\u0161ti","app.components.ComingSoonPage.comingSoon":"Uskoro","app.components.ConfirmDialog.title":"Potvrda","app.components.DownloadInfo.download":"Preuzimanje u tijeku...","app.components.DownloadInfo.text":"Ovo bi moglo potrajati minutu. Hvala na strpljenju.","app.components.EmptyAttributes.title":"Jo\u0161 nema polja","app.components.EmptyStateLayout.content-document":"Nije prona\u0111en sadr\u017Eaj","app.components.EmptyStateLayout.content-permissions":"Nemate dopu\u0161tenja za pristup tom sadr\u017Eaju","app.components.GuidedTour.CM.create.content":"<p>Stvorite sav sadr\u017Eaj i upravljajte njime ovdje u Upravitelju sadr\u017Eaja.</p><p>Primjer: Uzimaju\u0107i dalje primjer web stranice bloga, mo\u017Eete napisati \u010Clanak, spremite ga i objavite kako \u017Eele.</p><p>\u{1F4A1} Brzi savjet \u2014 ne zaboravite pritisnuti Objavi na sadr\u017Eaju koji stvorite.</p>","app.components.GuidedTour.CM.create.title":"\u26A1\uFE0F Stvorite sadr\u017Eaj","app.components.GuidedTour.CM.success.content":"<p>Fenomenalno, jo\u0161 jedan posljednji korak!</p><b>\u{1F680} Pogledajte sadr\u017Eaj na djelu</b>","app.components.GuidedTour.CM.success.cta.title":"Testirajte API","app.components.GuidedTour.CM.success.title":"Korak 2: Zavr\u0161eno \u2705","app.components.GuidedTour.CTB.create.content":"<p>Vrste zbirki poma\u017Eu vam u upravljanju s nekoliko unosa, pojedina\u010Dne vrste prikladne su za upravljanje samo jednim unosom.</p> <p>Primjer: Za web stranicu bloga, \u010Clanci bi bili vrsta zbirke dok bi po\u010Detna stranica bila jedna vrsta.</p>","app.components.GuidedTour.CTB.create.cta.title":"Izradite vrstu zbirke","app.components.GuidedTour.CTB.create.title":"\u{1F9E0} Stvorite prvu vrstu zbirke","app.components.GuidedTour.CTB.success.content":"<p>Odli\u010Dno!</p><b>\u26A1\uFE0F \u0160to biste \u017Eeljeli podijeliti sa svijetom?</b>","app.components.GuidedTour.CTB.success.title":"1. korak: Zavr\u0161eno \u2705","app.components.GuidedTour.apiTokens.create.content":"<p>Ovdje generirajte token za provjeru autenti\u010Dnosti i dohvatite sadr\u017Eaj koji ste upravo stvorili.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"Generiraj API token","app.components.GuidedTour.apiTokens.create.title":"\u{1F680} Pogledajte sadr\u017Eaj na djelu","app.components.GuidedTour.apiTokens.success.content":"<p>Pogledajte sadr\u017Eaj na djelu postavljanjem HTTP zahtjeva:</p><ul><li><p>Na ovaj URL: <light>https: //'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Sa zaglavljem: <light>Autorizacija: nositelj '<' YOUR_API_TOKEN'>'</light></p></li></ul><p>Za vi\u0161e na\u010Dina interakcije sa sadr\u017Eajem pogledajte <documentationLink>dokumentaciju</documentationLink>.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Povratak na po\u010Detnu stranicu","app.components.GuidedTour.apiTokens.success.title":"Korak 3: Zavr\u0161eno \u2705","app.components.GuidedTour.create-content":"Stvori sadr\u017Eaj","app.components.GuidedTour.home.CM.title":"\u26A1\uFE0F \u0160to biste \u017Eeljeli podijeliti sa svijetom?","app.components.GuidedTour.home.CTB.cta.title":"Idite na Graditelj vrste sadr\u017Eaja","app.components.GuidedTour.home.CTB.title":"\u{1F9E0} Izgradite strukturu sadr\u017Eaja","app.components.GuidedTour.home.apiTokens.cta.title":"Testirajte API","app.components.GuidedTour.skip":"Presko\u010Di obilazak","app.components.GuidedTour.title":"3 koraka za po\u010Detak","app.components.HomePage.button.blog":"Pogledajte vi\u0161e na blogu","app.components.HomePage.community":"Pridru\u017Eite se zajednici","app.components.HomePage.community.content":"Razgovarajte s \u010Dlanovima tima, suradnicima i programerima na razli\u010Ditim kanalima.","app.components.HomePage.create":"Stvorite svoju prvu vrstu sadr\u017Eaja","app.components.HomePage.roadmap":"Pogledajte na\u0161u mapu puta","app.components.HomePage.welcome":"Dobro do\u0161li na brod \u{1F44B}","app.components.HomePage.welcome.again":"Dobro do\u0161li \u{1F44B}","app.components.HomePage.welcomeBlock.content":"\u010Cestitamo! Prijavljeni ste kao prvi administrator. Kako biste otkrili mo\u0107ne zna\u010Dajke koje nudi Strapi, preporu\u010Dujemo vam da napravite svoju prvu vrstu sadr\u017Eaja!","app.components.HomePage.welcomeBlock.content.again":"Nadamo se da napredujete na svom projektu! Slobodno pro\u010Ditajte najnovije vijesti o Strapi. Dajemo sve od sebe da pobolj\u0161amo proizvod na temelju va\u0161ih povratnih informacija.","app.components.HomePage.welcomeBlock.content.issues":"problemi.","app.components.HomePage.welcomeBlock.content.raise":" ili podi\u0107i ","app.components.ImgPreview.hint":"Povucite i ispustite svoju datoteku u ovo podru\u010Dje ili {search} datoteku za prijenos","app.components.ImgPreview.hint.browse":"pregledaj","app.components.InputFile.newFile":"Dodaj novu datoteku","app.components.InputFileDetails.open":"Otvori u novoj kartici","app.components.InputFileDetails.originalName":"Originalni naziv:","app.components.InputFileDetails.remove":"Ukloni ovu datoteku","app.components.InputFileDetails.size":"Veli\u010Dina:","app.components.InstallPluginPage.Download.description":"Preuzimanje i instaliranje dodatka mo\u017Ee potrajati nekoliko sekundi.","app.components.InstallPluginPage.Download.title":"Preuzimanje...","app.components.InstallPluginPage.description":"Pro\u0161irite svoju aplikaciju bez napora.","app.components.LeftMenu.collapse":"Suzmi navigacijsku traku","app.components.LeftMenu.expand":"Pro\u0161iri navigacijsku traku","app.components.LeftMenu.general":"Op\u0107enito","app.components.LeftMenu.logo.alt":"Logotip aplikacije","app.components.LeftMenu.logout":"Odjava","app.components.LeftMenu.navbrand.title":"Nadzorna plo\u010Da Strapi","app.components.LeftMenu.navbrand.workplace":"Radno mjesto","app.components.LeftMenu.plugins":"Dodaci","app.components.LeftMenuFooter.help":"Pomo\u0107","app.components.LeftMenuFooter.poweredBy":"Pokre\u0107e ","app.components.LeftMenuLinkContainer.collectionTypes":"Vrste zbirki","app.components.LeftMenuLinkContainer.configuration":"Konfiguracije","app.components.LeftMenuLinkContainer.general":"Op\u0107enito","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Jo\u0161 nema instaliranih dodataka","app.components.LeftMenuLinkContainer.plugins":"Dodaci","app.components.LeftMenuLinkContainer.singleTypes":"Pojedina\u010Dne vrste","app.components.ListPluginsPage.deletePlugin.description":"Deinstalacija dodatka mo\u017Ee potrajati nekoliko sekundi.","app.components.ListPluginsPage.deletePlugin.title":"Deinstaliranje","app.components.ListPluginsPage.description":"Popis instaliranih dodataka u projektu.","app.components.ListPluginsPage.helmet.title":"Popis dodataka","app.components.Logout.logout":"Odjava","app.components.Logout.profile":"Profil","app.components.MarketplaceBanner":"Otkrijte dodatke koje je izradila zajednica i jo\u0161 mnogo sjajnih stvari za pokretanje va\u0161eg projekta na Strapi Marketplaceu.","app.components.MarketplaceBanner.image.alt":"Logotip rakete Strapi","app.components.MarketplaceBanner.link":"Provjerite sada","app.components.NotFoundPage.back":"Natrag na po\u010Detnu stranicu","app.components.NotFoundPage.description":"Nije prona\u0111eno","app.components.Official":"Slu\u017Ebeno","app.components.Onboarding.help.button":"Gumb za pomo\u0107","app.components.Onboarding.label.completed":"% dovr\u0161eno","app.components.Onboarding.link.build-content":"Izgradite arhitekturu sadr\u017Eaja","app.components.Onboarding.link.manage-content":"Dodaj i upravljaj sadr\u017Eajem","app.components.Onboarding.link.manage-media":"Upravljanje medijima","app.components.Onboarding.link.more-videos":"Pogledajte vi\u0161e videa","app.components.Onboarding.title":"Videozapisi za po\u010Detak","app.components.PluginCard.Button.label.download":"Preuzimanje","app.components.PluginCard.Button.label.install":"Ve\u0107 instalirano","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"Zna\u010Dajka automatskog ponovnog u\u010Ditavanja mora biti omogu\u0107ena. Pokrenite svoju aplikaciju s `yarn development`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Razumijem!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Iz sigurnosnih razloga, dodatak se mo\u017Ee preuzeti samo u razvojnom okru\u017Eenju.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Preuzimanje je nemogu\u0107e","app.components.PluginCard.compatible":"Kompatibilno s va\u0161om aplikacijom","app.components.PluginCard.compatibleCommunity":"Kompatibilno sa zajednicom","app.components.PluginCard.more-details":"Vi\u0161e detalja","app.components.ToggleCheckbox.off-label":"NE","app.components.ToggleCheckbox.on-label":"DA","app.components.Users.MagicLink.connect":"Kopirajte i podijelite ovu vezu da biste dali pristup ovom korisniku","app.components.Users.MagicLink.connect.sso":"Po\u0161aljite ovu vezu korisniku, prva prijava mo\u017Ee se izvr\u0161iti putem SSO davatelja","app.components.Users.ModalCreateBody.block-title.details":"Podaci o korisniku","app.components.Users.ModalCreateBody.block-title.roles":"Korisni\u010Dke uloge","app.components.Users.ModalCreateBody.block-title.roles.description":"Korisnik mo\u017Ee imati jednu ili vi\u0161e uloga","app.components.Users.SortPicker.button-label":"Razvrstaj po","app.components.Users.SortPicker.sortby.email_asc":"E-po\u0161ta (A do Z)","app.components.Users.SortPicker.sortby.email_desc":"E-po\u0161ta (Z do A)","app.components.Users.SortPicker.sortby.firstname_asc":"Ime (A do Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Ime (Z do A)","app.components.Users.SortPicker.sortby.lastname_asc":"Prezime (A do Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Prezime (Z do A)","app.components.Users.SortPicker.sortby.username_asc":"Korisni\u010Dko ime (A do Z)","app.components.Users.SortPicker.sortby.username_desc":"Korisni\u010Dko ime (Z do A)","app.components.listPlugins.button":"Dodaj novi dodatak","app.components.listPlugins.title.none":"Nije instaliran nijedan dodatak","app.components.listPluginsPage.deletePlugin.error":"Do\u0161lo je do pogre\u0161ke prilikom deinstalacije dodatka","app.containers.App.notification.error.init":"Do\u0161lo je do pogre\u0161ke prilikom tra\u017Eenja API-ja","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Ako ne primite ovu vezu, obratite se svom administratoru.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"Mo\u017Ee pro\u0107i nekoliko minuta prije nego \u0161to primimo va\u0161u vezu za oporavak lozinke.","app.containers.AuthPage.ForgotPasswordSuccess.title":"Poslana e-po\u0161ta","app.containers.Users.EditPage.form.active.label":"Aktivno","app.containers.Users.EditPage.header.label":"Uredi {name}","app.containers.Users.EditPage.header.label-loading":"Uredi korisnika","app.containers.Users.EditPage.roles-bloc-title":"Dodijeljene uloge","app.containers.Users.ModalForm.footer.button-success":"Pozovi korisnika","app.links.configure-view":"Konfigurirajte prikaz","app.page.not.found":"Ups! \u010Cini se da ne mo\u017Eemo prona\u0107i stranicu koju tra\u017Eite...","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Dodaj filter","app.utils.close-label":"Zatvori","app.utils.defaultMessage":" ","app.utils.delete":"Izbri\u0161i","app.utils.duplicate":"Duplikat","app.utils.edit":"Uredi","app.utils.errors.file-too-big.message":"Datoteka je prevelika","app.utils.filter-value":"Vrijednost filtra","app.utils.filters":"Filtri","app.utils.notify.data-loaded":"{target} je u\u010Ditan","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Objavi","app.utils.refresh":"Osvje\u017Ei","app.utils.select-all":"Odaberi sve","app.utils.select-field":"Odaberi polje","app.utils.select-filter":"Odaberi filtar","app.utils.unpublish":"Poni\u0161ti objavu","app.utils.published":"Objavljeno","app.utils.ready-to-publish":"Spremno za objavu",clearLabel:c,"coming.soon":"Ovaj sadr\u017Eaj je trenutno u izradi i vratit \u0107e se za nekoliko tjedana!","component.Input.error.validation.integer":"Vrijednost mora biti cijeli broj","components.AutoReloadBlocker.description":"Pokreni Strapi s jednom od sljede\u0107ih naredbi:","components.AutoReloadBlocker.header":"Za ovaj dodatak potrebna je zna\u010Dajka ponovnog u\u010Ditavanja.","components.ErrorBoundary.title":"Ne\u0161to nije u redu...","components.FilterOptions.FILTER_TYPES.$contains":"sadr\u017Ei (razli\u010Dito velika i mala slova)","components.FilterOptions.FILTER_TYPES.$endsWith":"zavr\u0161ava s","components.FilterOptions.FILTER_TYPES.$eq":"je","components.FilterOptions.FILTER_TYPES.$gt":"je ve\u0107e od","components.FilterOptions.FILTER_TYPES.$gte":"je ve\u0107e ili jednako","components.FilterOptions.FILTER_TYPES.$lt":"ni\u017Ei je od","components.FilterOptions.FILTER_TYPES.$lte":"ni\u017Ei je ili jednak","components.FilterOptions.FILTER_TYPES.$ne":"nije","components.FilterOptions.FILTER_TYPES.$notContains":"ne sadr\u017Ei (razli\u010Dito na velika i mala slova)","components.FilterOptions.FILTER_TYPES.$notNull":"nije nula","components.FilterOptions.FILTER_TYPES.$null":"je null","components.FilterOptions.FILTER_TYPES.$startsWith":"po\u010Dinje s","components.Input.error.attribute.key.taken":"Ova vrijednost ve\u0107 postoji","components.Input.error.attribute.sameKeyAndName":"Ne mo\u017Ee biti jednako","components.Input.error.attribute.taken":"Ovaj naziv polja ve\u0107 postoji","components.Input.error.contain.lowercase":"Lozinka mora sadr\u017Eavati barem jedno malo slovo","components.Input.error.contain.number":"Lozinka mora sadr\u017Eavati barem jedan broj","components.Input.error.contain.uppercase":"Lozinka mora sadr\u017Eavati barem jedno veliko slovo","components.Input.error.contentTypeName.taken":"Ovo ime ve\u0107 postoji","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"Lozinke se ne podudaraju","components.Input.error.validation.email":"Ovo je neva\u017Ee\u0107a e-po\u0161ta","components.Input.error.validation.json":"Ovo ne odgovara JSON formatu","components.Input.error.validation.lowercase":"Vrijednost mora biti niz s malim slovima","components.Input.error.validation.max":"Vrijednost je previsoka.","components.Input.error.validation.maxLength":"Vrijednost je preduga\u010Dka.","components.Input.error.validation.min":"Vrijednost je preniska.","components.Input.error.validation.minLength":"Vrijednost je prekratka.","components.Input.error.validation.minSupMax":"Ne mo\u017Ee biti superioran","components.Input.error.validation.regex":"Vrijednost ne odgovara regularnom izrazu.","components.Input.error.validation.required":"Ova vrijednost je obavezna.","components.Input.error.validation.unique":"Ova vrijednost je ve\u0107 kori\u0161tena.","components.InputSelect.option.placeholder":"(odaberite)","components.ListRow.empty":"Nema podataka za prikaz.","components.NotAllowedInput.text":"Nema dopu\u0161tenja za pregled ovog polja","components.OverlayBlocker.description":"Koristite zna\u010Dajku koja zahtijeva ponovno pokretanje poslu\u017Eitelja. Pri\u010Dekajte dok se poslu\u017Eitelj ne pokrene.","components.OverlayBlocker.description.serverError":"Poslu\u017Eitelj se trebao ponovno pokrenuti, provjerite svoje zapise na terminalu.","components.OverlayBlocker.title":"\u010Ceka se ponovno pokretanje...","components.OverlayBlocker.title.serverError":"Ponovno pokretanje traje du\u017Ee od o\u010Dekivanog","components.PageFooter.select":"Unosi po stranici","components.ProductionBlocker.description":"Iz sigurnosnih razloga moramo onemogu\u0107iti ovaj dodatak u drugim okru\u017Eenjima.","components.ProductionBlocker.header":"Ovaj dodatak je dostupan samo u razvoju.","components.TableHeader.sort":"Sortiraj po {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Na\u010Din ozna\u010Davanja","components.Wysiwyg.ToggleMode.preview-mode":"Na\u010Din pregleda","components.Wysiwyg.collapse":"Sa\u017Emi","components.Wysiwyg.selectOptions.H1":"Naslov H1","components.Wysiwyg.selectOptions.H2":"Naslov H2","components.Wysiwyg.selectOptions.H3":"Naslov H3","components.Wysiwyg.selectOptions.H4":"Naslov H4","components.Wysiwyg.selectOptions.H5":"Naslov H5","components.Wysiwyg.selectOptions.H6":"Naslov H6","components.Wysiwyg.selectOptions.title":"Dodajte naslov","components.WysiwygBottomControls.charactersIndicators":"znakovi","components.WysiwygBottomControls.fullscreen":"Pro\u0161iri","components.WysiwygBottomControls.uploadFiles":"Povucite i ispustite datoteke, zalijepite ih iz me\u0111uspremnika ili {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"odaberi ih","components.pagination.go-to":"Idi na stranicu {page}","components.pagination.go-to-next":"Idi na sljede\u0107u stranicu","components.pagination.go-to-previous":"Idi na prethodnu stranicu","components.pagination.remaining-links":"I {number} drugih veza","components.popUpWarning.button.cancel":"Ne, odustani","components.popUpWarning.button.confirm":"Da, potvrdi","components.popUpWarning.message":"Jeste li sigurni da \u017Eelite ovo izbrisati?","components.popUpWarning.title":"Molimo potvrdite","content-manager.App.schemas.data-loaded":"Sheme su uspje\u0161no u\u010Ditane","content-manager.EditRelations.title":"Relacijski podaci","content-manager.HeaderLayout.button.label-add-entry":"Stvori novi unos","content-manager.api.id":"API ID","content-manager.apiError.This attribute must be unique":"{field} mora biti jedinstveno","content-manager.components.AddFilterCTA.add":"Filtri","content-manager.components.AddFilterCTA.hide":"Filtri","content-manager.components.DragHandle-label":"Povucite","content-manager.components.DraggableAttr.edit":"Kliknite za ure\u0111ivanje","content-manager.components.DraggableCard.delete.field":"Izbri\u0161i {item}","content-manager.components.DraggableCard.edit.field":"Uredi {item}","content-manager.components.DraggableCard.move.field":"Premjesti {item}","content-manager.components.DynamicZone.ComponentPicker-label":"Odaberi jednu komponentu","content-manager.components.DynamicZone.add-component":"Dodajte komponentu u {componentName}","content-manager.components.DynamicZone.delete-label":"Izbri\u0161i {name}","content-manager.components.DynamicZone.error-message":"Komponenta sadr\u017Ei pogre\u0161ku(e)","content-manager.components.DynamicZone.missing-components":"Postoji {number, plural, =0 {nedostaju # komponente} jedan {nedostaje # komponenta} drugi {nedostaju # komponente}}","content-manager.components.DynamicZone.move-down-label":"Pomakni komponentu dolje","content-manager.components.DynamicZone.move-up-label":"Premjesti komponentu gore","content-manager.components.DynamicZone.pick-compo":"Odaberi jednu komponentu","content-manager.components.DynamicZone.required":"Komponenta je potrebna","content-manager.components.EmptyAttributesBlock.button":"Idi na stranicu postavki","content-manager.components.EmptyAttributesBlock.description":"Mo\u017Eete promijeniti svoje postavke","content-manager.components.FieldItem.linkToComponentLayout":"Postavite izgled komponente","content-manager.components.FieldSelect.label":"Dodajte polje","content-manager.components.FilterOptions.button.apply":"Primijeni","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Primijeni","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"O\u010Disti sve","content-manager.components.FiltersPickWrapper.PluginHeader.description":"Postavite uvjete koji \u0107e se primijeniti za filtriranje unosa","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Filtri","content-manager.components.FiltersPickWrapper.hide":"Sakrij","content-manager.components.LeftMenu.Search.label":"Tra\u017Ei vrstu sadr\u017Eaja","content-manager.components.LeftMenu.collection-types":"Vrste zbirki","content-manager.components.LeftMenu.single-types":"Pojedina\u010Dne vrste","content-manager.components.LimitSelect.itemsPerPage":"Stavke po stranici","content-manager.components.NotAllowedInput.text":"Nema dopu\u0161tenja za pregled ovog polja","content-manager.components.RelationInput.icon-button-aria-label":"Povucite","content-manager.components.RepeatableComponent.error-message":"Komponenta(e) sadr\u017Ei gre\u0161ku(e)","content-manager.components.Search.placeholder":"Tra\u017Ei unos...","content-manager.components.Select.draft-info-title":"Stanje: Nacrt","content-manager.components.Select.publish-info-title":"Stanje: Objavljeno","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Prilagodite kako \u0107e izgledati prikaz za ure\u0111ivanje.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Definirajte postavke prikaza popisa.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Konfigurirajte prikaz \u2014 {name}","content-manager.components.TableDelete.delete":"Izbri\u0161i sve","content-manager.components.TableDelete.deleteSelected":"Izbri\u0161i odabrano","content-manager.components.TableDelete.label":"{number, plural, one {# unos} other {# unosa}} odabrano","content-manager.components.TableEmpty.withFilters":"Nema {contentType} s primijenjenim filtrima...","content-manager.components.TableEmpty.withSearch":"Ne postoji {contentType} koji odgovara pretrazi ({search})...","content-manager.components.TableEmpty.withoutFilter":"Nema {contentType}...","content-manager.components.empty-repeatable":"Jo\u0161 nema unosa. Kliknite na gumb ispod da biste ga dodali.","content-manager.components.notification.info.maximum-requirement":"Ve\u0107 ste dosegli maksimalan broj polja","content-manager.components.notification.info.minimum-requirement":"Dodano je polje koje odgovara minimalnom zahtjevu","content-manager.components.repeatable.reorder.error":"Do\u0161lo je do pogre\u0161ke prilikom promjene redoslijeda polja va\u0161e komponente, poku\u0161ajte ponovno","content-manager.components.reset-entry":"Poni\u0161ti unos","content-manager.components.uid.apply":"primijeni","content-manager.components.uid.available":"Dostupno","content-manager.components.uid.regenerate":"Ponovo generiraj","content-manager.components.uid.suggested":"predlo\u017Eeno","content-manager.components.uid.unavailable":"Nedostupno","content-manager.containers.Edit.Link.Layout":"Konfigurirajte izgled","content-manager.containers.Edit.Link.Model":"Uredite vrstu zbirke","content-manager.containers.Edit.addAnItem":"Dodajte stavku...","content-manager.containers.Edit.clickToJump":"Kliknite za skok na unos","content-manager.containers.Edit.delete":"Izbri\u0161i","content-manager.containers.Edit.delete-entry":"Izbri\u0161i ovaj unos","content-manager.containers.Edit.editing":"Ure\u0111ivanje...","content-manager.containers.Edit.information":"Informacije","content-manager.containers.Edit.information.by":"Od","content-manager.containers.Edit.information.created":"Stvoreno","content-manager.containers.Edit.information.draftVersion":"nacrt verzije","content-manager.containers.Edit.information.editing":"Ure\u0111ivanje","content-manager.containers.Edit.information.lastUpdate":"Posljednje a\u017Euriranje","content-manager.containers.Edit.information.publishedVersion":"objavljena verzija","content-manager.containers.Edit.pluginHeader.title.new":"Stvori unos","content-manager.containers.Edit.reset":"Poni\u0161ti","content-manager.containers.Edit.returnList":"Povratak na popis","content-manager.containers.Edit.seeDetails":"Detalji","content-manager.containers.Edit.submit":"Spremi","content-manager.containers.EditSettingsView.modal-form.edit-field":"Uredite polje","content-manager.containers.EditView.add.new-entry":"Dodajte unos","content-manager.containers.EditView.notification.errors":"Obrazac sadr\u017Ei neke pogre\u0161ke","content-manager.containers.Home.introduction":"Za ure\u0111ivanje va\u0161ih unosa idite na odre\u0111enu vezu u lijevom izborniku. Ovaj dodatak nema pravilan na\u010Din za ure\u0111ivanje postavki i jo\u0161 uvijek je u aktivnom razvoju.","content-manager.containers.Home.pluginHeaderDescription":"Upravljajte svojim unosima putem mo\u0107nog i lijepog su\u010Delja.","content-manager.containers.Home.pluginHeaderTitle":"Upravitelj sadr\u017Eaja","content-manager.containers.List.draft":"Nacrt","content-manager.containers.List.errorFetchRecords":"Pogre\u0161ka","content-manager.containers.List.published":"Objavljeno","content-manager.containers.ListPage.displayedFields":"Prikazana polja","content-manager.containers.ListPage.items":"{number, plural, =0 {items} one {item} other {items}}","content-manager.containers.ListPage.table-headers.publishedAt":"Stanje","content-manager.containers.ListSettingsView.modal-form.edit-label":"Uredi {fieldName}","content-manager.containers.SettingPage.add.field":"Umetni drugo polje","content-manager.containers.SettingPage.add.relational-field":"Umetni drugo povezano polje","content-manager.containers.SettingPage.attributes":"Polja atributa","content-manager.containers.SettingPage.attributes.description":"Definirajte redoslijed atributa","content-manager.containers.SettingPage.editSettings.description":"Povucite i ispustite polja za izradu izgleda","content-manager.containers.SettingPage.editSettings.entry.title":"Naslov unosa","content-manager.containers.SettingPage.editSettings.entry.title.description":"Postavite prikazano polje va\u0161eg unosa","content-manager.containers.SettingPage.editSettings.relation-field.description":"Postavite prikazano polje u prikazu za ure\u0111ivanje i prikazu popisa","content-manager.containers.SettingPage.editSettings.title":"Uredi prikaz (postavke)","content-manager.containers.SettingPage.layout":"Izgled","content-manager.containers.SettingPage.listSettings.description":"Konfigurirajte opcije za ovu vrstu zbirke","content-manager.containers.SettingPage.listSettings.title":"Prikaz popisa (postavke)","content-manager.containers.SettingPage.pluginHeaderDescription":"Konfigurirajte specifi\u010Dne postavke za ovu vrstu zbirke","content-manager.containers.SettingPage.relations":"Povezana polja","content-manager.containers.SettingPage.settings":"Postavke","content-manager.containers.SettingPage.view":"Prikaz","content-manager.containers.SettingViewModel.pluginHeader.title":"Upravitelj sadr\u017Eaja \u2014 {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Konfigurirajte odre\u0111ene postavke","content-manager.containers.SettingsPage.Block.contentType.title":"Vrste zbirki","content-manager.containers.SettingsPage.Block.generalSettings.description":"Konfigurirajte zadane opcije za svoje vrste zbirki","content-manager.containers.SettingsPage.Block.generalSettings.title":"Op\u0107enito","content-manager.containers.SettingsPage.pluginHeaderDescription":"Konfigurirajte postavke za sve svoje vrste i grupe zbirki","content-manager.containers.SettingsView.list.subtitle":"Konfigurirajte izgled i prikaz vrsta i grupa zbirki","content-manager.containers.SettingsView.list.title":"Konfiguracije prikaza","content-manager.dnd.cancel-item":"{item}, odba\u010Dena. Ponovna narud\u017Eba otkazana.","content-manager.dnd.drop-item":"{item}, ispu\u0161teno. Kona\u010Dna pozicija na popisu: {position}.","content-manager.dnd.grab-item":"{item}, preuzeto. Trenuta\u010Dni polo\u017Eaj na popisu: {position}. Pritisnite strelicu gore i dolje za promjenu polo\u017Eaja, razmaknicu za ispu\u0161tanje, Escape za odustajanje.","content-manager.dnd.instructions":"Pritisnite razmaknicu za preuzimanje i promjenu redoslijeda","content-manager.dnd.reorder":"{item}, premje\u0161tena. Nova pozicija na popisu: {position}.","content-manager.edit-settings-view.link-to-ctb.components":"Uredite komponentu","content-manager.edit-settings-view.link-to-ctb.content-types":"Uredite vrstu sadr\u017Eaja","content-manager.emptyAttributes.button":"Idi na alat za izradu vrste zbirke","content-manager.emptyAttributes.description":"Dodajte svoje prvo polje svojoj vrsti zbirke","content-manager.emptyAttributes.title":"Jo\u0161 nema polja","content-manager.error.attribute.key.taken":"Ova vrijednost ve\u0107 postoji","content-manager.error.attribute.sameKeyAndName":"Ne mo\u017Ee biti jednako","content-manager.error.attribute.taken":"Ovaj naziv polja ve\u0107 postoji","content-manager.error.contentTypeName.taken":"Ovo ime ve\u0107 postoji","content-manager.error.model.fetch":"Do\u0161lo je do gre\u0161ke tijekom dohva\u0107anja konfiguracije modela.","content-manager.error.record.create":"Do\u0161lo je do gre\u0161ke tijekom stvaranja zapisa.","content-manager.error.record.delete":"Do\u0161lo je do gre\u0161ke tijekom brisanja zapisa.","content-manager.error.record.fetch":"Do\u0161lo je do gre\u0161ke tijekom dohva\u0107anja zapisa.","content-manager.error.record.update":"Do\u0161lo je do pogre\u0161ke tijekom a\u017Euriranja zapisa.","content-manager.error.records.count":"Do\u0161lo je do gre\u0161ke tijekom dohva\u0107anja zapisa broja.","content-manager.error.records.fetch":"Do\u0161lo je do gre\u0161ke tijekom dohva\u0107anja zapisa.","content-manager.error.schema.generation":"Do\u0161lo je do pogre\u0161ke tijekom generiranja sheme.","content-manager.error.validation.json":"Ovo nije JSON","content-manager.error.validation.max":"Vrijednost je previsoka.","content-manager.error.validation.maxLength":"Vrijednost je preduga\u010Dka.","content-manager.error.validation.min":"Vrijednost je preniska.","content-manager.error.validation.minLength":"Vrijednost je prekratka.","content-manager.error.validation.minSupMax":"Ne mo\u017Ee biti superioran","content-manager.error.validation.regex":"Vrijednost ne odgovara regularnom izrazu.","content-manager.error.validation.required":"Ovaj unos vrijednosti je obavezan.","content-manager.form.Input.bulkActions":"Omogu\u0107i skupne akcije","content-manager.form.Input.defaultSort":"Zadani atribut sortiranja","content-manager.form.Input.description":"Opis","content-manager.form.Input.description.placeholder":"Ime za prikaz u profilu","content-manager.form.Input.editable":"Polje za ure\u0111ivanje","content-manager.form.Input.filters":"Omogu\u0107i filtre","content-manager.form.Input.hint.character.unit":"{maxValue, plural, one { znak} other { znaka}}","content-manager.form.Input.hint.minMaxDivider":" / ","content-manager.form.Input.hint.text":"{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {maks. {maks} }}{unit}{br}{description}","content-manager.form.Input.label":"Oznaka","content-manager.form.Input.label.inputDescription":"Ova vrijednost nadja\u010Dava oznaku prikazanu u zaglavlju tablice","content-manager.form.Input.pageEntries":"Unosi po stranici","content-manager.form.Input.pageEntries.inputDescription":"Napomena: ovu vrijednost mo\u017Eete nadja\u010Dati na stranici s postavkama vrste zbirke.","content-manager.form.Input.placeholder":"\u010Cuvar mjesta","content-manager.form.Input.placeholder.placeholder":"Moja sjajna vrijednost","content-manager.form.Input.search":"Omogu\u0107i pretra\u017Eivanje","content-manager.form.Input.search.field":"Omogu\u0107i pretra\u017Eivanje u ovom polju","content-manager.form.Input.sort.field":"Omogu\u0107i sortiranje u ovom polju","content-manager.form.Input.sort.order":"Zadani redoslijed sortiranja","content-manager.form.Input.wysiwyg":"Prika\u017Ei kao WYSIWYG","content-manager.global.displayedFields":"Prikazana polja","content-manager.groups":"Grupe","content-manager.groups.numbered":"Grupe ({number})","content-manager.header.name":"Sadr\u017Eaj","content-manager.link-to-ctb":"Uredite model","content-manager.models":"Vrste zbirki","content-manager.models.numbered":"Vrste zbirki ({number})","content-manager.notification.error.displayedFields":"Potrebno vam je najmanje jedno prikazano polje","content-manager.notification.error.relationship.fetch":"Do\u0161lo je do pogre\u0161ke tijekom dohva\u0107anja odnosa.","content-manager.notification.info.SettingPage.disableSort":"Morate imati jedan atribut s dopu\u0161tenim sortiranjem","content-manager.notification.info.minimumFields":"Morate imati prikazano barem jedno polje","content-manager.notification.upload.error":"Do\u0161lo je do pogre\u0161ke prilikom u\u010Ditavanja va\u0161ih datoteka","content-manager.pageNotFound":"Stranica nije prona\u0111ena","content-manager.pages.ListView.header-subtitle":"{number, plural, =0 {# unosa} one {# unos} other {# unosa}} prona\u0111eno","content-manager.pages.NoContentType.button":"Stvorite svoj prvi Content-Type","content-manager.pages.NoContentType.text":"Jo\u0161 nemate nikakav sadr\u017Eaj, preporu\u010Dujemo vam da napravite svoj prvi Content-Type.","content-manager.permissions.not-allowed.create":"Nije vam dopu\u0161teno kreirati dokument","content-manager.permissions.not-allowed.update":"Nije vam dopu\u0161teno vidjeti ovaj dokument","content-manager.plugin.description.long":"Brz na\u010Din za pregled, ure\u0111ivanje i brisanje podataka u va\u0161oj bazi podataka.","content-manager.plugin.description.short":"Brz na\u010Din za pregled, ure\u0111ivanje i brisanje podataka u va\u0161oj bazi podataka.","content-manager.popUpWarning.bodyMessage.contentType.delete":"Jeste li sigurni da \u017Eelite izbrisati Content-Type?","content-manager.popUpWarning.bodyMessage.contentType.delete.all":"Jeste li sigurni da \u017Eelite izbrisati sve vrste sadr\u017Eaja?","content-manager.popUpWarning.warning.has-draft-relations.title":"Potvrda","content-manager.popUpWarning.warning.publish-question":"\u017Delite li i dalje objavljivati?","content-manager.popUpWarning.warning.unpublish":"Ako ne objavite ovaj sadr\u017Eaj, automatski \u0107e se pretvoriti u skicu.","content-manager.popUpWarning.warning.unpublish-question":"Jeste li sigurni da to ne \u017Eelite objaviti?","content-manager.popUpWarning.warning.updateAllSettings":"Ovo \u0107e promijeniti sve va\u0161e postavke","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Da, objavi","content-manager.popUpwarning.warning.has-draft-relations.message":"<b>{count, plural, one { odnos je } other { odnosi su } }</b> jo\u0161 nije objavljen i mo\u017Ee dovesti do neo\u010Dekivanih pona\u0161anje.","content-manager.popover.display-relations.label":"Odnosi prikaza","content-manager.relation.add":"Dodaj relaciju","content-manager.relation.disconnect":"Ukloni","content-manager.relation.isLoading":"Relacije se u\u010Ditavaju","content-manager.relation.loadMore":"U\u010Ditaj vi\u0161e","content-manager.relation.notAvailable":"Nema dostupnih odnosa","content-manager.relation.publicationState.draft":"Nacrt","content-manager.relation.publicationState.published":"Objavljeno","content-manager.select.currently.selected":"{count} trenutno odabrano","content-manager.success.record.delete":"Izbrisano","content-manager.success.record.publish":"Objavljeno","content-manager.success.record.save":"Spremljeno","content-manager.success.record.unpublish":"Neobjavljeno","content-manager.utils.data-loaded":"{number, plural, =1 {unos} other {unosi}} su uspje\u0161no u\u010Ditani",dark:d,"form.button.continue":"Nastavi","form.button.done":"Gotovo","global.actions":"Akcije","global.auditLogs":"Dnevnici revizije","global.back":"Natrag","global.cancel":"Otka\u017Ei","global.change-password":"Promijeni lozinku","global.content-manager":"Upravitelj sadr\u017Eaja","global.continue":"Nastavi","global.delete":"Izbri\u0161i","global.delete-target":"Izbri\u0161i {target}","global.description":"Opis","global.details":"Detalji","global.disabled":"Onemogu\u0107eno","global.documentation":"Dokumentacija","global.enabled":"Omogu\u0107eno","global.finish":"Zavr\u0161i","global.marketplace":"Tr\u017Enica","global.name":"Ime","global.none":"Ni\u0161ta","global.password":"Lozinka","global.plugins":"Dodaci","global.plugins.content-manager":"Upravitelj sadr\u017Eaja","global.plugins.content-manager.description":"Brz na\u010Din za pregled, ure\u0111ivanje i brisanje podataka u va\u0161oj bazi podataka.","global.plugins.content-type-builder":"Izrada vrste sadr\u017Eaja","global.plugins.content-type-builder.description":"Modelirajte strukturu podataka va\u0161eg API-ja. Stvorite nova polja i relacije u samo minuti. Datoteke se automatski stvaraju i a\u017Euriraju u va\u0161em projektu.","global.plugins.documentation":"Dokumentacija","global.plugins.documentation.description":"Stvorite OpenAPI dokument i vizualizirajte svoj API pomo\u0107u SWAGGER UI.","global.plugins.email":"E-po\u0161ta","global.plugins.email.description":"Konfigurirajte svoju aplikaciju za slanje e-po\u0161te.","global.plugins.graphql":"GraphQL","global.plugins.graphql.description":"Dodaje GraphQL krajnju to\u010Dku sa zadanim API metodama.","global.plugins.i18n":"Internacionalizacija","global.plugins.i18n.description":"Ovaj dodatak omogu\u0107uje stvaranje, \u010Ditanje i a\u017Euriranje sadr\u017Eaja na razli\u010Ditim jezicima, kako s Administratorske plo\u010De tako i iz API-ja.","global.plugins.sentry":"Stra\u017Ear","global.plugins.sentry.description":"Po\u0161alji Strapi doga\u0111aje pogre\u0161ke u Sentry.","global.plugins.upload":"Knji\u017Enica medija","global.plugins.upload.description":"Upravljanje medijskim datotekama.","global.plugins.users-permissions":"Uloge i dopu\u0161tenja","global.plugins.users-permissions.description":"Za\u0161titite svoj API potpunim postupkom provjere autenti\u010Dnosti koji se temelji na JWT-u. Ovaj dodatak tako\u0111er dolazi s ACL strategijom koja vam omogu\u0107uje upravljanje dopu\u0161tenjima izme\u0111u grupa korisnika.","global.profile":"Profil","global.prompt.unsaved":"Jeste li sigurni da \u017Eelite napustiti ovu stranicu? Sve va\u0161e izmjene bit \u0107e izgubljene","global.reset-password":"Poni\u0161ti lozinku","global.roles":"Uloge","global.save":"Spremi","global.search":"Tra\u017Ei","global.see-more":"Vidi vi\u0161e","global.select":"Odaberi","global.select-all-entries":"Odaberi sve unose","global.settings":"Postavke","global.type":"Tip","global.users":"Korisnici",light:g,"notification.contentType.relations.conflict":"Vrsta sadr\u017Eaja ima sukobljene odnose","notification.default.title":"Informacije:","notification.error":"Do\u0161lo je do pogre\u0161ke","notification.error.layout":"Nije mogu\u0107e dohvatiti izgled","notification.form.error.fields":"Obrazac sadr\u017Ei neke pogre\u0161ke","notification.form.success.fields":"Promjene su spremljene","notification.link-copied":"Veza kopirana u me\u0111uspremnik","notification.permission.not-allowed-read":"Nije vam dopu\u0161teno vidjeti ovaj dokument","notification.success.delete":"Stavka je izbrisana","notification.success.saved":"Spremljeno","notification.success.title":"Uspjeh:","notification.success.apitokencreated":"API token uspje\u0161no stvoren","notification.success.apitokenedited":"API token uspje\u0161no ure\u0111en","notification.success.transfertokencreated":"Transfer Token uspje\u0161no kreiran","notification.success.transfertokenedited":"Token prijenosa uspje\u0161no ure\u0111en","notification.error.tokennamenotunique":"Ime je ve\u0107 dodijeljeno drugom tokenu","notification.version.update.message":"Nova verzija Strapija je dostupna!","notification.warning.title":"Upozorenje:","notification.warning.404":"404 - Nije prona\u0111eno","notification.ee.warning.over-.message":"Dodajte mjesta {licenseLimitStatus, select, OVER_LIMIT {invite} AT_LIMIT {re-enable}} Korisnicima. Ako ste to ve\u0107 u\u010Dinili, ali se jo\u0161 ne odra\u017Eava na Strapi, provjerite za ponovno pokretanje aplikacije.","notification.ee.warning.at-seat-limit.title":"{licenseLimitStatus, odaberite, OVER_LIMIT {Over} AT_LIMIT {At}} ograni\u010Denje mjesta ({currentUserCount}/{permittedSeats})",or:j,"request.error.model.unknown":"Ovaj model ne postoji",selectButtonTitle:k,skipToContent:v,submit:b,"Settings.apiTokens.title":"API tokeni","Settings.apiTokens.lastHour":"posljednji sat","Settings.permissions.users.sso.provider.error":"Do\u0161lo je do pogre\u0161ke prilikom tra\u017Eenja SSO postavki","Settings.review-workflows.page.title":"Pregled tijekova rada","Settings.review-workflows.page.subtitle":"{count, plural, one {# stage} other {# stages}}","Settings.review-workflows.page.isLoading":"Tijek rada se u\u010Ditava","Settings.review-workflows.page.delete.confirm.body":"Svi unosi dodijeljeni izbrisanim fazama bit \u0107e premje\u0161teni u prethodnu fazu. Jeste li sigurni da \u017Eelite spremiti?","Settings.review-workflows.stage.name.label":"Naziv faze","Settings.sso.form.localAuthenticationLock.label":"Zaklju\u010Davanje lokalne provjere autenti\u010Dnosti","Settings.sso.form.localAuthenticationLock.description":"Odaberite uloge za koje \u017Eelite onemogu\u0107iti lokalnu autentifikaciju","Settings.webhooks.event.select":"Odaberi doga\u0111aj","Settings.webhooks.events.isLoading":"U\u010Ditavanje doga\u0111aja","Settings.webhooks.events.delete":"Izbri\u0161i webhook","Settings.webhooks.list.loading.success":"Webhooks su u\u010Ditani","Settings.webhooks.validation.name.required":"Ime je potrebno","Settings.webhooks.validation.name.regex":"Ime mora zapo\u010Deti slovom i sadr\u017Eavati samo slova, brojeve, razmake i podvlake","Settings.webhooks.validation.url.required":"Potreban je URL","Settings.webhooks.validation.url.regex":"Vrijednost mora biti va\u017Ee\u0107i URL","Settings.webhooks.validation.key":"Potreban je klju\u010D","Settings.webhooks.validation.value":"Vrijednost je obavezna","components.FilterOptions.FILTER_TYPES.$containsi":"sadr\u017Ei (neosjetljivo na velika i mala slova)","components.FilterOptions.FILTER_TYPES.$endsWithi":"zavr\u0161ava s (neosjetljivo na velika i mala slova)","components.FilterOptions.FILTER_TYPES.$eqi":"je (neosjetljivo na velika i mala slova)","components.FilterOptions.FILTER_TYPES.$nei":"nije (neosjetljivo na velika i mala slova)","components.FilterOptions.FILTER_TYPES.$notContainsi":"ne sadr\u017Ei (neosjetljivo na velika i mala slova)","components.FilterOptions.FILTER_TYPES.$startsWithi":"po\u010Dinje s (neosjetljivo na velika i mala slova)","components.Input.error.validation.email.withField":"{field} je neva\u017Ee\u0107a e-po\u0161ta","components.Input.error.validation.json.withField":"{field} ne odgovara JSON formatu","components.Input.error.validation.lowercase.withField":"{polje} mora biti niz s malim slovima","components.Input.error.validation.max.withField":"{polje} je previsoko.","components.Input.error.validation.maxLength.withField":"{polje} je predugo.","components.Input.error.validation.min.withField":"{polje} je prenisko.","components.Input.error.validation.minLength.withField":"{polje} je prekratko.","components.Input.error.validation.minSupMax.withField":"{polje} ne mo\u017Ee biti superiorno","components.Input.error.validation.regex.withField":"{polje} ne odgovara regularnom izrazu.","components.Input.error.validation.required.withField":"{polje} je obavezno.","components.Input.error.validation.unique.withField":"{polje} se ve\u0107 koristi.","content-manager.ListViewTable.relation-loaded":"Relacije su u\u010Ditane","content-manager.ListViewTable.relation-loading":"Relacije se u\u010Ditavaju","content-manager.ListViewTable.relation-more":"Ova relacija sadr\u017Ei vi\u0161e entiteta nego \u0161to je prikazano","content-manager.components.ListViewTable.row-line":"redak stavke {broj}","content-manager.bulk-publish.already-published":"Ve\u0107 objavljeno","content-manager.containers.ListPage.selectedEntriesModal.title":"Objavi unose","content-manager.containers.ListPage.selectedEntriesModal.selectedCount":"<b>{readyToPublishCount}</b> {readyToPublishCount, mno\u017Eina, =0 {unosi} jedan {unos} drugi {unosi}} spremni za objavu. <b >{withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} \u010Dekaju akciju.","content-manager.containers.ListPage.selectedEntriesModal.publishedCount":"<b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} published. <b>{ withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} \u010Dekaju akciju.","content-manager.popUpWarning.bodyMessage.contentType.publish.all":"Jeste li sigurni da \u017Eelite objaviti ove unose?","content-manager.popUpWarning.bodyMessage.contentType.unpublish.all":"Jeste li sigurni da \u017Eelite poni\u0161titi objavu ovih unosa?","content-manager.popUpwarning.warning.bulk-has-draft-relations.message":"<b>{count} {count, mno\u017Eina, jedan { odnos } drugi { odnosi } } od {entiteta} { entiteta, mno\u017Eina , one { entry } other { entries } } {count, plural, one { is } other { are } }</b> jo\u0161 nije objavljeno i moglo bi dovesti do neo\u010Dekivanog pona\u0161anja. ","content-manager.reviewWorkflows.stage.label":"Faza pregleda","content-manager.success.record.publishing":"Objavljivanje...","content-manager.listView.validation.errors.title":"Potrebna radnja","content-manager.listView.validation.errors.message":"Provjerite jesu li sva polja valjana prije objave (obavezno polje, minimalno/maksimalno ograni\u010Denje znakova, itd.)","global.close":"Zatvori","notification.error.invalid.configuration":"Imate neva\u017Ee\u0107u konfiguraciju, provjerite svoj zapis poslu\u017Eitelja za vi\u0161e informacija.","Settings.permissions.auditLogs.not-available":"Audit Logs je dostupan samo kao dio Enterprise Edition. Nadogradite kako biste dobili prikaz svih aktivnosti koji se mo\u017Ee pretra\u017Eivati i filtrirati.","Settings.profile.form.section.experience.mode.option-system-label":"Koristi postavke sustava","Settings.review-workflows.not-available":"Pregled tijekova rada dostupan je samo kao dio Enterprise Edition. Nadogradite za stvaranje i upravljanje tijekovima rada.","Settings.sso.not-available":"SSO je dostupan samo kao dio izdanja za poduze\u0107a. Nadogradite da biste konfigurirali dodatne metode prijave i prijave za svoju administrativnu plo\u010Du.","app.components.NpsSurvey.banner-title":"Kolika je vjerojatnost da \u0107ete Strapi preporu\u010Diti prijatelju ili kolegi?","app.components.NpsSurvey.feedback-response":"Puno vam hvala na povratnim informacijama!","app.components.NpsSurvey.feedback-question":"Imate li kakav prijedlog za pobolj\u0161anja?","app.components.NpsSurvey.submit-feedback":"Po\u0161alji povratne informacije","app.components.NpsSurvey.dismiss-survey-label":"Odbaci anketu","app.components.NpsSurvey.no-recommendation":"Nije vjerojatno","app.components.NpsSurvey.happy-to-recommend":"Iznimno vjerojatno","components.ViewSettings.tooltip":"Prika\u017Ei postavke","components.Blocks.modifiers.bold":"Podebljano","components.Blocks.modifiers.italic":"Kurziv","components.Blocks.modifiers.underline":"Podcrtano","components.Blocks.modifiers.strikethrough":"Precrtano","components.Blocks.modifiers.code":"Inline kod","components.Blocks.link":"Veza","components.Blocks.popover.text":"Tekst","components.Blocks.popover.text.placeholder":"Unesite tekst veze","components.Blocks.popover.link":"Veza","components.Blocks.popover.link.placeholder":"Primjer","components.Blocks.popover.link.error":"Molimo unesite valjanu vezu","components.Blocks.popover.save":"Spremi","components.Blocks.popover.cancel":"Odustani","components.Blocks.popover.remove":"Ukloni","components.Blocks.popover.edit":"Uredi","components.Blocks.blocks.selectBlock":"Odaberi blok","components.Blocks.blocks.text":"Tekst","components.Blocks.blocks.heading1":"Naslov 1","components.Blocks.blocks.heading2":"Naslov 2","components.Blocks.blocks.heading3":"Naslov 3","components.Blocks.blocks.heading4":"Naslov 4","components.Blocks.blocks.heading5":"Naslov 5","components.Blocks.blocks.heading6":"Naslov 6","components.Blocks.blocks.code":"Blok koda","components.Blocks.blocks.quote":"Citat","components.Blocks.blocks.image":"Slika","components.Blocks.blocks.unorderedList":"Popis s grafi\u010Dkim oznakama","components.Blocks.blocks.orderedList":"Numerirani popis","components.Blocks.dnd.instruction":"Za promjenu redoslijeda blokova, pritisnite Command ili Control zajedno sa Shift i tipkama sa strelicama gore ili dolje","components.Blocks.dnd.reorder":"{item}, premje\u0161teno. Nova pozicija u ure\u0111iva\u010Du: {position}.","components.Blocks.expand":"Pro\u0161iri","components.Blocks.collapse":"Sa\u017Emi","content-manager.containers.ListPage.autoCloneModal.header":"Duplikat","content-manager.containers.ListPage.autoCloneModal.title":"Ovaj unos ne mo\u017Ee se izravno duplicirati.","content-manager.containers.ListPage.autoCloneModal.description":"Stvorit \u0107e se novi unos s istim sadr\u017Eajem, ali \u0107ete morati promijeniti sljede\u0107a polja da biste ga spremili.","content-manager.containers.ListPage.autoCloneModal.create":"Stvori","content-manager.containers.ListPage.autoCloneModal.error.unique":"Identi\u010Dne vrijednosti u jedinstvenom polju nisu dopu\u0161tene.","content-manager.containers.ListPage.autoCloneModal.error.relation":"Dupliciranje relacije moglo bi je ukloniti iz izvornog unosa.","content-manager.components.Filters.usersSelect.label":"Pretra\u017Eite i odaberite korisnika prema kojem \u017Eelite filtrirati","global.fullname":"{ime} {prezime}","global.learn-more":"Saznajte vi\u0161e"}}}]);
