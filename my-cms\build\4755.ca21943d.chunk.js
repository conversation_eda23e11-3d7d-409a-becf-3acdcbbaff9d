"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4755],{64755:(e,s,t)=>{t.r(s),t.d(s,{ROUTES_EE:()=>o});const o=[...window.strapi.features.isEnabled(window.strapi.features.AUDIT_LOGS)?[{async Component(){const{ProtectedListPage:n}=await t.e(9).then(t.bind(t,9));return n},to:"/settings/audit-logs",exact:!0}]:[],...window.strapi.features.isEnabled(window.strapi.features.REVIEW_WORKFLOWS)?[{async Component(){const{ProtectedReviewWorkflowsPage:n}=await Promise.all([t.e(6006),t.e(7733)]).then(t.bind(t,27733));return n},to:"/settings/review-workflows",exact:!0},{async Component(){const{ReviewWorkflowsCreatePage:n}=await Promise.all([t.e(6006),t.e(3977),t.e(8491)]).then(t.bind(t,28491));return n},to:"/settings/review-workflows/create",exact:!0},{async Component(){const{ReviewWorkflowsEditPage:n}=await Promise.all([t.e(6006),t.e(3977),t.e(7018)]).then(t.bind(t,57018));return n},to:"/settings/review-workflows/:workflowId",exact:!0}]:[],...window.strapi.features.isEnabled(window.strapi.features.SSO)?[{async Component(){const{ProtectedSSO:n}=await t.e(9328).then(t.bind(t,89328));return n},to:"/settings/single-sign-on",exact:!0}]:[]]}}]);
