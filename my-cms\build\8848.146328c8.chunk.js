"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[8848],{98848:(O,_,s)=>{s.d(_,{PurchaseSingleSignOn:()=>M});var t=s(92132),a=s(94061),i=s(53563),e=s(42455),d=s(55356),g=s(38413),l=s(49654),r=s(3047),E=s(14595),o=s(54894);const M=()=>{const{formatMessage:n}=(0,o.A)();return(0,t.jsx)(e.P,{children:(0,t.jsxs)(g.g,{children:[(0,t.jsx)(d.Q,{title:n({id:"Settings.sso.title",defaultMessage:"Single Sign-On"}),subtitle:n({id:"Settings.sso.subTitle",defaultMessage:"Configure the settings for the Single Sign-On feature."})}),(0,t.jsx)(a.a,{paddingLeft:10,paddingRight:10,children:(0,t.jsx)(i.p,{icon:(0,t.jsx)(r.A,{width:"10rem"}),content:n({id:"Settings.sso.not-available",defaultMessage:"SSO is only available as part of a paid plan. Upgrade to configure additional sign-in & sign-up methods for your administration panel."}),action:(0,t.jsx)(l.z,{variant:"default",endIcon:(0,t.jsx)(E.A,{}),href:"https://strp.cc/46Fk1BA",isExternal:!0,target:"_blank",children:n({id:"global.learn-more",defaultMessage:"Learn more"})})})})]})})}}}]);
