"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4513],{34513:(l,e,i)=>{i.r(e),i.d(e,{default:()=>a,link:()=>t});const t="Link",a={link:t,"Settings.email.plugin.button.test-email":"Enviar e-mail de teste","Settings.email.plugin.label.defaultFrom":"E-mail do remetente padr\xE3o","Settings.email.plugin.label.defaultReplyTo":"E-mail de resposta padr\xE3o","Settings.email.plugin.label.provider":"Provedor de E-mail","Settings.email.plugin.label.testAddress":"E-mail do destinat\xE1rio","Settings.email.plugin.notification.config.error":"Falha ao recuperar a configura\xE7\xE3o de e-mail","Settings.email.plugin.notification.data.loaded":"Os dados de configura\xE7\xF5es de e-mail foram carregados","Settings.email.plugin.notification.test.error":"Falha ao enviar um e-mail de teste para {to}","Settings.email.plugin.notification.test.success":"Teste de e-mail bem-sucedido, verifique a caixa de correio {to}","Settings.email.plugin.placeholder.defaultFrom":"ex: Strapi No-Reply <<EMAIL>>","Settings.email.plugin.placeholder.defaultReplyTo":"ex: Strapi <<EMAIL>>","Settings.email.plugin.placeholder.testAddress":"ex: <EMAIL>","Settings.email.plugin.subTitle":"Teste as configura\xE7\xF5es do plug-in de e-mail","Settings.email.plugin.text.configuration":"O plugin \xE9 configurado atrav\xE9s do ficheiro {file}, acede a este {link} para a documenta\xE7\xE3o.","Settings.email.plugin.title":"Configura\xE7\xE3o","Settings.email.plugin.title.config":"Configura\xE7\xE3o","Settings.email.plugin.title.test":"Testar entrega de e-mail","SettingsNav.link.settings":"Defini\xE7\xF5es","SettingsNav.section-label":"Plug-in de e-mail","components.Input.error.validation.email":"Este \xE9 um e-mail inv\xE1lido"}}}]);
