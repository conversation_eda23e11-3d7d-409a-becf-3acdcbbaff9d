"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3106],{63106:(B,u,e)=>{e.r(u),e.d(u,{Analytics:()=>t,Documentation:()=>n,Email:()=>o,Password:()=>a,Provider:()=>i,ResetPasswordToken:()=>s,Role:()=>r,Username:()=>c,Users:()=>p,anErrorOccurred:()=>l,clearLabel:()=>m,default:()=>g,or:()=>F,skipToContent:()=>E,submit:()=>C});const t="\u5206\u6790",n="\u30C9\u30AD\u30E5\u30E1\u30F3\u30C6\u30FC\u30B7\u30E7\u30F3",o="E\u30E1\u30FC\u30EB",a="\u30D1\u30B9\u30EF\u30FC\u30C9",i="\u30D7\u30ED\u30D0\u30A4\u30C0",s="\u30D1\u30B9\u30EF\u30FC\u30C9\u30C8\u30FC\u30AF\u30F3\u3092\u30EA\u30BB\u30C3\u30C8",r="Role",c="\u30E6\u30FC\u30B6\u30FC\u540D",p="\u30E6\u30FC\u30B6\u30FC",l="Woops! Something went wrong. Please, try again.",m="Clear",F="OR",E="Skip to content",C="\u9001\u4FE1",g={Analytics:t,"Auth.components.Oops.text":"\u3042\u306A\u305F\u306E\u30A2\u30AB\u30A6\u30F3\u30C8\u306F\u51CD\u7D50\u3055\u308C\u307E\u3057\u305F","Auth.components.Oops.text.admin":"\u3053\u3061\u3089\u304C\u9593\u9055\u3044\u3067\u3042\u308C\u3070\u3001\u7BA1\u7406\u8005\u306B\u9023\u7D61\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Auth.components.Oops.title":"Oops...","Auth.form.button.forgot-password":"\u30E1\u30FC\u30EB\u3092\u9001\u4FE1","Auth.form.button.go-home":"\u30DB\u30FC\u30E0\u306B\u623B\u308B","Auth.form.button.login":"\u30ED\u30B0\u30A4\u30F3","Auth.form.button.login.providers.error":"\u9078\u629E\u3057\u305F\u30D7\u30ED\u30D0\u30A4\u30C0\u3092\u901A\u3057\u3066\u63A5\u7D9A\u3059\u308B\u3053\u3068\u306F\u3067\u304D\u307E\u305B\u3093\u3002","Auth.form.button.login.strapi":"Strapi\u3067\u30ED\u30B0\u30A4\u30F3\u3057\u307E\u3059","Auth.form.button.password-recovery":"\u30D1\u30B9\u30EF\u30FC\u30C9\u306E\u5FA9\u5143","Auth.form.button.register":"\u306F\u3058\u3081\u3088\u3046","Auth.form.confirmPassword.label":"\u30D1\u30B9\u30EF\u30FC\u30C9\u3092\u78BA\u8A8D","Auth.form.currentPassword.label":"\u73FE\u5728\u306E\u30D1\u30B9\u30EF\u30FC\u30C9","Auth.form.email.label":"E\u30E1\u30FC\u30EB","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"\u3042\u306A\u305F\u306E\u30A2\u30AB\u30A6\u30F3\u30C8\u306F\u7BA1\u7406\u8005\u304B\u3089\u30D6\u30ED\u30C3\u30AF\u3055\u308C\u3066\u3044\u307E\u3059","Auth.form.error.code.provide":"\u4E0D\u6B63\u306A\u30B3\u30FC\u30C9\u304C\u63D0\u4F9B\u3055\u308C\u307E\u3057\u305F","Auth.form.error.confirmed":"\u30E1\u30FC\u30EB\u304C\u78BA\u8A8D\u3067\u304D\u307E\u305B\u3093","Auth.form.error.email.invalid":"\u3053\u306E\u30E1\u30FC\u30EB\u306F\u7121\u52B9\u3067\u3059","Auth.form.error.email.provide":"\u3042\u306A\u305F\u306E\u30E6\u30FC\u30B6\u30FC\u540D\u307E\u305F\u306F\u30E1\u30FC\u30EB\u30A2\u30C9\u30EC\u30B9\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Auth.form.error.email.taken":"E\u30E1\u30FC\u30EB\u306F\u3059\u3067\u306B\u53D6\u5F97\u6E08\u307F\u3067\u3059","Auth.form.error.invalid":"\u8B58\u5225\u5B50\u307E\u305F\u306F\u30D1\u30B9\u30EF\u30FC\u30C9\u304C\u7121\u52B9\u3067\u3059\u3002","Auth.form.error.params.provide":"\u4E0D\u9069\u5207\u306A\u30D1\u30E9\u30E1\u30FC\u30BF\u304C\u6307\u5B9A\u3055\u308C\u307E\u3057\u305F\u3002","Auth.form.error.password.format":"\u3042\u306A\u305F\u306E\u30D1\u30B9\u30EF\u30FC\u30C9\u306B\u306F\u3001`$`\u30B7\u30F3\u30DC\u30EB\u30923\u56DE\u4EE5\u4E0A\u542B\u3081\u308B\u3053\u3068\u306F\u3067\u304D\u307E\u305B\u3093\u3002","Auth.form.error.password.local":"\u3053\u306E\u30E6\u30FC\u30B6\u30FC\u306F\u6C7A\u3057\u3066\u30ED\u30FC\u30AB\u30EB\u30D1\u30B9\u30EF\u30FC\u30C9\u3092\u8A2D\u5B9A\u3057\u307E\u305B\u3093\u3002\u30A2\u30AB\u30A6\u30F3\u30C8\u4F5C\u6210\u6642\u306B\u4F7F\u7528\u3057\u305F\u30D7\u30ED\u30D0\u30A4\u30C0\u7D4C\u7531\u3067\u30ED\u30B0\u30A4\u30F3\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Auth.form.error.password.matching":"\u30D1\u30B9\u30EF\u30FC\u30C9\u304C\u4E00\u81F4\u3057\u3066\u3044\u307E\u305B\u3093\u3002","Auth.form.error.password.provide":"\u30D1\u30B9\u30EF\u30FC\u30C9\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Auth.form.error.ratelimit":"\u8A66\u884C\u56DE\u6570\u304C\u591A\u3059\u304E\u308B\u5834\u5408\u306F\u3001\u3082\u3046\u4E00\u5EA6\u8A66\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Auth.form.error.user.not-exist":"\u3053\u306E\u30E1\u30FC\u30EB\u306F\u5B58\u5728\u3057\u307E\u305B\u3093\u3002","Auth.form.error.username.taken":"\u30E6\u30FC\u30B6\u30FC\u540D\u306F\u65E2\u306B\u4F7F\u308F\u308C\u3066\u3044\u307E\u3059\u3002","Auth.form.firstname.label":"\u540D","Auth.form.firstname.placeholder":"\u30AB\u30A4","Auth.form.forgot-password.email.label":"\u30E1\u30FC\u30EB\u30A2\u30C9\u30EC\u30B9\u3092\u5165\u529B","Auth.form.forgot-password.email.label.success":"E\u30E1\u30FC\u30EB\u304C\u6B63\u5E38\u306B\u9001\u4FE1\u3055\u308C\u307E\u3057\u305F","Auth.form.lastname.label":"\u59D3","Auth.form.lastname.placeholder":"\u30C9\u30A6","Auth.form.password.hide-password":"\u30D1\u30B9\u30EF\u30FC\u30C9\u3092\u975E\u8868\u793A","Auth.form.password.hint":"Password must contain at least 8 characters, 1 uppercase, 1 lowercase, and 1 number","Auth.form.password.show-password":"\u30D1\u30B9\u30EF\u30FC\u30C9\u3092\u8868\u793A","Auth.form.register.news.label":"\u65B0\u6A5F\u80FD\u3084\u4ECA\u5F8C\u306E\u6539\u5584\u306B\u3064\u3044\u3066\u306E\u6700\u65B0\u60C5\u5831\u3092\u53D7\u3051\u53D6\u308B(\u53D7\u3051\u53D6\u308B\u5834\u5408\u306F{terms}\u3068{policy}\u306B\u540C\u610F\u3057\u305F\u3053\u3068\u3068\u3057\u307E\u3059)\u3002","Auth.form.register.subtitle":"\u8A8D\u8A3C\u60C5\u5831\u306F\u3001\u7BA1\u7406\u30D1\u30CD\u30EB\u3067\u81EA\u5206\u3092\u8A8D\u8A3C\u3059\u308B\u305F\u3081\u306B\u306E\u307F\u4F7F\u7528\u3055\u308C\u307E\u3059\u3002\u4FDD\u5B58\u3055\u308C\u3066\u3044\u308B\u3059\u3079\u3066\u306E\u30C7\u30FC\u30BF\u306F\u81EA\u5206\u306E\u30C7\u30FC\u30BF\u30D9\u30FC\u30B9\u306B\u4FDD\u5B58\u3055\u308C\u307E\u3059\u3002","Auth.form.rememberMe.label":"\u8A18\u61B6\u3059\u308B","Auth.form.username.label":"\u30E6\u30FC\u30B6\u30FC\u540D","Auth.form.username.placeholder":"\u30AB\u30A4\u30FB\u30C9\u30A6","Auth.form.welcome.subtitle":"Strapi\u30A2\u30AB\u30A6\u30F3\u30C8\u306B\u30ED\u30B0\u30A4\u30F3","Auth.form.welcome.title":"\u3044\u3089\u3063\u3057\u3083\u3044\u307E\u305B\uFF01","Auth.link.forgot-password":"\u30D1\u30B9\u30EF\u30FC\u30C9\u3092\u304A\u5FD8\u308C\u3067\u3059\u304B\uFF1F","Auth.link.ready":"\u30B5\u30A4\u30F3\u30A4\u30F3\u3059\u308B\u6E96\u5099\u304C\u3067\u304D\u307E\u3057\u305F\u304B\uFF1F","Auth.link.signin":"\u30B5\u30A4\u30F3\u30A4\u30F3","Auth.link.signin.account":"\u3059\u3067\u306B\u30A2\u30AB\u30A6\u30F3\u30C8\u3092\u304A\u6301\u3061\u3067\u3059\u304B\uFF1F","Auth.login.sso.divider":"\u307E\u305F\u306F","Auth.login.sso.loading":"\u8A8D\u8A3C\u30D7\u30ED\u30D0\u30A4\u30C0\u3092\u8AAD\u307F\u8FBC\u307F\u4E2D...","Auth.login.sso.subtitle":"SSO\u3092\u4ECB\u3057\u3066\u30A2\u30AB\u30A6\u30F3\u30C8\u306B\u30ED\u30B0\u30A4\u30F3","Auth.privacy-policy-agreement.policy":"\u30D7\u30E9\u30A4\u30D0\u30B7\u30FC\u30DD\u30EA\u30B7\u30FC","Auth.privacy-policy-agreement.terms":"\u5229\u7528\u898F\u7D04","Content Manager":"\u30B3\u30F3\u30C6\u30F3\u30C4\u7BA1\u7406","Content Type Builder":"\u30B3\u30F3\u30C6\u30F3\u30C4\u30BF\u30A4\u30D7\u30D3\u30EB\u30C0",Documentation:n,Email:o,"Files Upload":"\u30D5\u30A1\u30A4\u30EB\u30A2\u30C3\u30D7\u30ED\u30FC\u30C9","HomePage.helmet.title":"\u30DB\u30FC\u30E0\u30DA\u30FC\u30B8","HomePage.roadmap":"\u30ED\u30FC\u30C9\u30DE\u30C3\u30D7\u3092\u898B\u308B","HomePage.welcome.congrats":"\u304A\u3081\u3067\u3068\u3046\u3054\u3056\u3044\u307E\u3059\uFF01","HomePage.welcome.congrats.content":"\u521D\u671F\u7BA1\u7406\u8005\u3068\u3057\u3066\u30ED\u30B0\u30A4\u30F3\u3057\u307E\u3057\u305F\u3002Strapi\u304C\u63D0\u4F9B\u3059\u308B\u5F37\u529B\u306A\u6A5F\u80FD\u3092\u63A2\u3059\u305F\u3081\u306B\u3001","HomePage.welcome.congrats.content.bold":"\u307E\u305A\u306F\u30B3\u30F3\u30C6\u30F3\u30C4\u30BF\u30A4\u30D7\u3092\u4F5C\u308B\u3053\u3068\u3092\u30AA\u30B9\u30B9\u30E1\u3057\u307E\u3059\u3002","Media Library":"\u30E1\u30C7\u30A3\u30A2\u30E9\u30A4\u30D6\u30E9\u30EA","New entry":"\u65B0\u898F\u6295\u7A3F",Password:a,Provider:i,ResetPasswordToken:s,Role:r,"Roles & Permissions":"\u30ED\u30FC\u30EB\u3068\u6A29\u9650","Roles.ListPage.notification.delete-all-not-allowed":"\u3044\u304F\u3064\u304B\u306E\u30ED\u30FC\u30EB\u306F\u30E6\u30FC\u30B6\u30FC\u306B\u3072\u3082\u3065\u3044\u3066\u3044\u308B\u305F\u3081\u524A\u9664\u3067\u304D\u307E\u305B\u3093\u3067\u3057\u305F","Roles.ListPage.notification.delete-not-allowed":"\u30E6\u30FC\u30B6\u30FC\u306B\u3072\u3082\u3065\u3044\u3066\u3044\u308B\u30ED\u30FC\u30EB\u306F\u524A\u9664\u3067\u304D\u307E\u305B\u3093","Roles.RoleRow.select-all":"Select {name} for bulk actions","Roles.components.List.empty.withSearch":"\u691C\u7D22\uFF08{}\uFF09\u306B\u5408\u81F4\u3059\u308B\u30ED\u30FC\u30EB\u306F\u3042\u308A\u307E\u305B\u3093\u2026","Settings.PageTitle":"\u8A2D\u5B9A - {name}","Settings.apiTokens.addFirstToken":"\u6700\u521D\u306EAPI\u30C8\u30FC\u30AF\u30F3\u3092\u8FFD\u52A0","Settings.apiTokens.addNewToken":"\u65B0\u3057\u3044API\u30C8\u30FC\u30AF\u30F3\u3092\u8FFD\u52A0","Settings.tokens.copy.editMessage":"\u30BB\u30AD\u30E5\u30EA\u30C6\u30A3\u4E0A\u306E\u7406\u7531\u304B\u3089\u3001\u30C8\u30FC\u30AF\u30F3\u306F\u4E00\u5EA6\u306B\u306E\u307F\u898B\u308B\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002","Settings.tokens.copy.editTitle":"\u3053\u306E\u30C8\u30FC\u30AF\u30F3\u306F\u3082\u3046\u30A2\u30AF\u30BB\u30B9\u3067\u304D\u307E\u305B\u3093\u3002","Settings.tokens.copy.lastWarning":"\u3053\u306E\u30C8\u30FC\u30AF\u30F3\u3092\u30B3\u30D4\u30FC\u3059\u308B\u3088\u3046\u306B\u3057\u3066\u304F\u3060\u3055\u3044\u3001\u4E00\u5EA6\u306E\u307F\u8868\u793A\u3055\u308C\u307E\u3059\u3002","Settings.apiTokens.create":"\u30A8\u30F3\u30C8\u30EA\u3092\u8FFD\u52A0","Settings.apiTokens.description":"List of generated tokens to consume the API","Settings.apiTokens.emptyStateLayout":"You don\u2019t have any content yet...","Settings.tokens.notification.copied":"Token copied to clipboard.","Settings.apiTokens.title":"API\u30C8\u30FC\u30AF\u30F3","Settings.tokens.types.full-access":"Full access","Settings.tokens.types.read-only":"Read-only","Settings.application.description":"\u30D7\u30ED\u30B8\u30A7\u30AF\u30C8\u306E\u8A73\u7D30\u3092\u898B\u308B","Settings.application.edition-title":"\u73FE\u5728\u306E\u30D7\u30E9\u30F3","Settings.application.get-help":"Get help","Settings.application.link-pricing":"\u3059\u3079\u3066\u306E\u4FA1\u683C\u3092\u898B\u308B","Settings.application.link-upgrade":"\u30D7\u30ED\u30B8\u30A7\u30AF\u30C8\u3092\u30A2\u30C3\u30D7\u30B0\u30EC\u30FC\u30C9\u3059\u308B","Settings.application.node-version":"Node \u30D0\u30FC\u30B8\u30E7\u30F3","Settings.application.strapi-version":"Strapi \u30D0\u30FC\u30B8\u30E7\u30F3","Settings.application.strapiVersion":"Strapi \u30D0\u30FC\u30B8\u30E7\u30F3","Settings.application.title":"\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3","Settings.error":"\u30A8\u30E9\u30FC","Settings.global":"\u30B0\u30ED\u30FC\u30D0\u30EB\u8A2D\u5B9A","Settings.permissions":"\u7BA1\u7406\u30D1\u30CD\u30EB","Settings.permissions.category":"{category}\u306E\u6A29\u9650\u8A2D\u5B9A","Settings.permissions.category.plugins":"{category}\u30D7\u30E9\u30B0\u30A4\u30F3\u306E\u6A29\u9650\u8A2D\u5B9A","Settings.permissions.conditions.anytime":"\u3044\u3064\u3067\u3082","Settings.permissions.conditions.apply":"\u9069\u7528","Settings.permissions.conditions.can":"\u53EF\u80FD","Settings.permissions.conditions.conditions":"\u6761\u4EF6\u3092\u5B9A\u7FA9","Settings.permissions.conditions.links":"\u30EA\u30F3\u30AF","Settings.permissions.conditions.no-actions":"\u6761\u4EF6\u3092\u5B9A\u7FA9\u3059\u308B\u524D\u306B\u6700\u521D\u306B\u4F5C\u696D\uFF08\u4F5C\u6210\u30FB\u95B2\u89A7\u30FB\u66F4\u65B0\u7B49\uFF09\u3092\u9078\u629E\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002","Settings.permissions.conditions.none-selected":"Anytime","Settings.permissions.conditions.or":"OR","Settings.permissions.conditions.when":"WHEN","Settings.permissions.select-all-by-permission":"Select all {label} permissions","Settings.permissions.select-by-permission":"Select {label} permission","Settings.permissions.users.create":"\u65B0\u3057\u3044\u30E6\u30FC\u30B6\u30FC\u3092\u4F5C\u6210","Settings.permissions.users.email":"E\u30E1\u30FC\u30EB","Settings.permissions.users.firstname":"\u540D","Settings.permissions.users.lastname":"\u59D3","Settings.permissions.users.form.sso":"Connect with SSO","Settings.permissions.users.form.sso.description":"When enabled (ON), users can login via SSO","Settings.permissions.users.listview.header.subtitle":"All the users who have access to the Strapi admin panel","Settings.permissions.users.tabs.label":"Tabs Permissions","Settings.profile.form.notify.data.loaded":"Your profile data has been loaded","Settings.profile.form.section.experience.clear.select":"Clear the interface language selected","Settings.profile.form.section.experience.here":"documentation","Settings.profile.form.section.experience.interfaceLanguage":"Interface language","Settings.profile.form.section.experience.interfaceLanguage.hint":"This will only display your own interface in the chosen language.","Settings.profile.form.section.experience.interfaceLanguageHelp":"Selection will change the interface language only for you. Please refer to this {here} to make other languages available for your team.","Settings.profile.form.section.experience.title":"Experience","Settings.profile.form.section.helmet.title":"\u30E6\u30FC\u30B6\u30FC\u30D7\u30ED\u30D5\u30A3\u30FC\u30EB","Settings.profile.form.section.profile.page.title":"\u30D7\u30ED\u30D5\u30A3\u30FC\u30EB\u30DA\u30FC\u30B8","Settings.roles.create.description":"\u30ED\u30FC\u30EB\u306B\u4ED8\u4E0E\u3055\u308C\u305F\u6A29\u5229\u3092\u5B9A\u7FA9\u3059\u308B","Settings.roles.create.title":"\u30ED\u30FC\u30EB\u3092\u4F5C\u6210","Settings.roles.created":"\u30ED\u30FC\u30EB\u304C\u4F5C\u6210\u3055\u308C\u307E\u3057\u305F","Settings.roles.edit.title":"\u30ED\u30FC\u30EB\u3092\u7DE8\u96C6","Settings.roles.form.button.users-with-role":"\u3053\u306E\u30ED\u30FC\u30EB\u3092\u6301\u3064\u30E6\u30FC\u30B6\u30FC","Settings.roles.form.created":"\u4F5C\u6210\u3055\u308C\u307E\u3057\u305F","Settings.roles.form.description":"\u30ED\u30FC\u30EB\u306E\u540D\u524D\u3068\u8AAC\u660E","Settings.roles.form.permission.property-label":"{label}\u6A29\u9650","Settings.roles.form.permissions.attributesPermissions":"\u30D5\u30A3\u30FC\u30EB\u30C9\u6A29\u9650","Settings.roles.form.permissions.create":"\u4F5C\u6210","Settings.roles.form.permissions.delete":"\u524A\u9664","Settings.roles.form.permissions.publish":"\u516C\u958B","Settings.roles.form.permissions.read":"\u95B2\u89A7","Settings.roles.form.permissions.update":"\u66F4\u65B0","Settings.roles.list.button.add":"\u65B0\u3057\u3044\u30ED\u30FC\u30EB\u3092\u8FFD\u52A0","Settings.roles.list.description":"\u30ED\u30FC\u30EB\u306E\u4E00\u89A7","Settings.roles.title.singular":"\u30ED\u30FC\u30EB","Settings.sso.description":"Configure the settings for the Single Sign-On feature.","Settings.sso.form.defaultRole.description":"It will attach the new authenticated user to the selected role","Settings.sso.form.defaultRole.description-not-allowed":"You need to have the permission to read the admin roles","Settings.sso.form.defaultRole.label":"Default role","Settings.sso.form.registration.description":"Create new user on SSO login if no account exists","Settings.sso.form.registration.label":"Auto-registration","Settings.sso.title":"Single Sign-On","Settings.webhooks.create":"webhook\u3092\u4F5C\u6210","Settings.webhooks.create.header":"\u30D8\u30C3\u30C0\u30FC\u306E\u65B0\u898F\u4F5C\u6210","Settings.webhooks.created":"Webhook\u304C\u4F5C\u6210\u3055\u308C\u307E\u3057\u305F","Settings.webhooks.event.publish-tooltip":"\u3053\u306E\u30A4\u30D9\u30F3\u30C8\u306F\u4E0B\u66F8\u304D\u30FB\u516C\u958B\u306E\u30B7\u30B9\u30C6\u30E0\u304C\u6709\u52B9\u306B\u306A\u3063\u305F\u30B3\u30F3\u30C6\u30F3\u30C4\u3067\u306E\u307F\u767A\u751F\u3057\u307E\u3059","Settings.webhooks.events.create":"\u4F5C\u6210","Settings.webhooks.events.update":"\u66F4\u65B0","Settings.webhooks.form.events":"\u30A4\u30D9\u30F3\u30C8","Settings.webhooks.form.headers":"\u30D8\u30C3\u30C0\u30FC","Settings.webhooks.form.url":"URL","Settings.webhooks.headers.remove":"Remove header row {number}","Settings.webhooks.key":"\u30AD\u30FC","Settings.webhooks.list.button.add":"webhook\u3092\u8FFD\u52A0","Settings.webhooks.list.description":"POST\u306E\u5909\u66F4\u901A\u77E5\u3092\u53D6\u5F97\u3059\u308B","Settings.webhooks.list.empty.description":"\u6700\u521D\u306Ewebhook\u3092\u3053\u306E\u30EA\u30B9\u30C8\u306B\u8FFD\u52A0\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Settings.webhooks.list.empty.link":"\u30C9\u30AD\u30E5\u30E1\u30F3\u30C8\u3092\u898B\u308B","Settings.webhooks.list.empty.title":"\u307E\u3060webhook\u306F\u3042\u308A\u307E\u305B\u3093","Settings.webhooks.list.th.actions":"actions","Settings.webhooks.list.th.status":"status","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# asset} other {# assets}} selected","Settings.webhooks.trigger":"\u30C8\u30EA\u30AC\u30FC","Settings.webhooks.trigger.cancel":"\u30C8\u30EA\u30AC\u30FC\u3092\u30AD\u30E3\u30F3\u30BB\u30EB\u3059\u308B","Settings.webhooks.trigger.pending":"\u30DA\u30F3\u30C7\u30A3\u30F3\u30B0\u4E2D\u2026","Settings.webhooks.trigger.save":"\u30C8\u30EA\u30AC\u30FC\u3092\u4FDD\u5B58\u3057\u3066\u304F\u3060\u3055\u3044","Settings.webhooks.trigger.success":"\u6210\u529F!","Settings.webhooks.trigger.success.label":"\u30C8\u30EA\u30AC\u30FC\u6210\u529F","Settings.webhooks.trigger.test":"\u30C6\u30B9\u30C8\u30C8\u30EA\u30AC\u30FC","Settings.webhooks.trigger.title":"\u30C8\u30EA\u30AC\u30FC\u524D\u306B\u4FDD\u5B58\u3059\u308B","Settings.webhooks.value":"\u5024",Username:c,Users:p,"Users & Permissions":"\u30E6\u30FC\u30B6\u30FC\u3068\u6A29\u9650","Users.components.List.empty":"\u30E6\u30FC\u30B6\u30FC\u304C\u3044\u307E\u305B\u3093\u2026","Users.components.List.empty.withFilters":"\u9069\u7528\u3055\u308C\u305F\u30D5\u30A3\u30EB\u30BF\u30FC\u306B\u30DE\u30C3\u30C1\u3059\u308B\u30E6\u30FC\u30B6\u30FC\u304C\u3044\u307E\u305B\u3093\u2026","Users.components.List.empty.withSearch":"\u691C\u7D22\uFF08{search}\uFF09\u306B\u5408\u81F4\u3059\u308B\u30E6\u30FC\u30B6\u30FC\u304C\u3044\u307E\u305B\u3093\u2026","admin.pages.MarketPlacePage.helmet":"\u30DE\u30FC\u30B1\u30C3\u30C8\u30D7\u30EC\u30A4\u30B9 - \u30D7\u30E9\u30B0\u30A4\u30F3","admin.pages.MarketPlacePage.submit.plugin.link":"\u30D7\u30E9\u30B0\u30A4\u30F3\u3092\u9001\u4FE1","admin.pages.MarketPlacePage.subtitle":"Get more out of Strapi",anErrorOccurred:l,"app.component.CopyToClipboard.label":"Copy to clipboard","app.component.search.label":"{target}\u3092\u691C\u7D22","app.component.table.duplicate":"{target}\u3092\u5FA9\u5143","app.component.table.edit":"{target}\u3092\u7DE8\u96C6","app.component.table.select.one-entry":"{target}\u3092\u9078\u629E","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Read the latest news about Strapi and the ecosystem.","app.components.BlockLink.code":"\u30B3\u30FC\u30C9\u4F8B","app.components.BlockLink.code.content":"Learn by testing real projects developed the community.","app.components.BlockLink.documentation.content":"Discover the essential concepts, guides and instructions.","app.components.BlockLink.tutorial":"Tutorials","app.components.BlockLink.tutorial.content":"Follow step-by-step instructions to use and customize Strapi.","app.components.Button.cancel":"\u30AD\u30E3\u30F3\u30BB\u30EB","app.components.Button.confirm":"\u78BA\u8A8D","app.components.Button.reset":"\u30EA\u30BB\u30C3\u30C8","app.components.ComingSoonPage.comingSoon":"\u8FD1\u65E5\u516C\u958B","app.components.ConfirmDialog.title":"\u78BA\u8A8D","app.components.DownloadInfo.download":"\u30C0\u30A6\u30F3\u30ED\u30FC\u30C9\u4E2D...","app.components.DownloadInfo.text":"\u3053\u308C\u306B\u306F\u6570\u5206\u304B\u304B\u308B\u3053\u3068\u304C\u3042\u308A\u307E\u3059\u3002 \u304A\u5F85\u3061\u304F\u3060\u3055\u3044","app.components.EmptyAttributes.title":"\u30D5\u30A3\u30FC\u30EB\u30C9\u306F\u307E\u3060\u3042\u308A\u307E\u305B\u3093","app.components.EmptyStateLayout.content-document":"No content found","app.components.EmptyStateLayout.content-permissions":"You don't have the permissions to access that content","app.components.HomePage.button.blog":"\u30D6\u30ED\u30B0\u3067\u3082\u3063\u3068\u898B\u308B","app.components.HomePage.community":"\u30B3\u30DF\u30E5\u30CB\u30C6\u30A3\u3067\u898B\u3064\u3051\u308B","app.components.HomePage.community.content":"\u7570\u306A\u308B\u30C1\u30E3\u30F3\u30CD\u30EB\u3067\u30C1\u30FC\u30E0\u30E1\u30F3\u30D0\u30FC\u3001\u30B3\u30F3\u30C8\u30EA\u30D3\u30E5\u30FC\u30BF\u30FC\u3084\u30C7\u30D9\u30ED\u30C3\u30D1\u30FC\u3068\u8B70\u8AD6\u3059\u308B","app.components.HomePage.create":"\u6700\u521D\u306E\u30B3\u30F3\u30C6\u30F3\u30C4\u30BF\u30A4\u30D7\u3092\u4F5C\u6210\u3059\u308B","app.components.HomePage.roadmap":"See our roadmap","app.components.HomePage.welcome":"\u30DC\u30FC\u30C9\u306B\u3088\u3046\u3053\u305D!","app.components.HomePage.welcome.again":"\u3088\u3046\u3053\u305D ","app.components.HomePage.welcomeBlock.content":"\u79C1\u305F\u3061\u306F\u30B3\u30DF\u30E5\u30CB\u30C6\u30A3\u30E1\u30F3\u30D0\u30FC\u306E\u3072\u3068\u308A\u3068\u3057\u3066\u3042\u306A\u305F\u3092\u304A\u5F85\u3061\u3057\u3066\u304A\u308A\u307E\u3059\u3002\u79C1\u305F\u3061\u306F\u5E38\u306B\u30D5\u30A3\u30FC\u30C9\u30D0\u30C3\u30AF\u3092\u6C42\u3081\u3066\u3044\u307E\u3059\u306E\u3067\u3001DM\u3092\u9001\u3063\u3066\u304F\u3060\u3055\u3044","app.components.HomePage.welcomeBlock.content.again":"\u3042\u306A\u305F\u306E\u30D7\u30ED\u30B8\u30A7\u30AF\u30C8\u3092\u9032\u6B69\u3055\u305B\u3066\u3044\u305F\u3060\u3051\u308C\u3070\u5E78\u3044\u3067\u3059... Strapi\u306B\u95A2\u3059\u308B\u6700\u65B0\u306E\u65B0\u60C5\u5831\u3092\u304A\u8AAD\u307F\u304F\u3060\u3055\u3044\u3002\u79C1\u305F\u3061\u306F\u3042\u306A\u305F\u306E\u30D5\u30A3\u30FC\u30C9\u30D0\u30C3\u30AF\u306B\u57FA\u3065\u3044\u3066\u88FD\u54C1\u3092\u6539\u5584\u3059\u308B\u305F\u3081\u306B\u6700\u5584\u3092\u5C3D\u304F\u3057\u3066\u3044\u307E\u3059\u3002","app.components.HomePage.welcomeBlock.content.issues":"\u554F\u984C","app.components.HomePage.welcomeBlock.content.raise":"\u307E\u305F\u306F\u4E0A\u3052\u308B","app.components.ImgPreview.hint":"\u30D5\u30A1\u30A4\u30EB\u3092\u30C9\u30E9\u30C3\u30B0&\u30C9\u30ED\u30C3\u30D7\u3001\u3082\u3057\u304F\u306F\u3001\u30A2\u30C3\u30D7\u30ED\u30FC\u30C9{browse}","app.components.ImgPreview.hint.browse":"\u30D6\u30E9\u30A6\u30BA","app.components.InputFile.newFile":"\u30D5\u30A1\u30A4\u30EB\u3092\u8FFD\u52A0","app.components.InputFileDetails.open":"\u5225\u306E\u30BF\u30D6\u3092\u958B\u304F","app.components.InputFileDetails.originalName":"\u30AA\u30EA\u30B8\u30CA\u30EB\u540D:","app.components.InputFileDetails.remove":"\u30D5\u30A1\u30A4\u30EB\u3092\u53D6\u308A\u9664\u304F","app.components.InputFileDetails.size":"\u30B5\u30A4\u30BA:","app.components.InstallPluginPage.Download.description":"It might take a few seconds to download and install the plugin.","app.components.InstallPluginPage.Download.title":"Downloading...","app.components.InstallPluginPage.description":"\u7C21\u5358\u306B\u30A2\u30D7\u30EA\u3092\u62E1\u5F35\u3059\u308B","app.components.LeftMenu.collapse":"Collapse the navbar","app.components.LeftMenu.expand":"Expand the navbar","app.components.LeftMenu.logout":"\u30ED\u30B0\u30A2\u30A6\u30C8","app.components.LeftMenuFooter.help":"Help","app.components.LeftMenuFooter.poweredBy":"Powered by ","app.components.LeftMenuLinkContainer.collectionTypes":"\u30B3\u30EC\u30AF\u30B7\u30E7\u30F3\u30BF\u30A4\u30D7","app.components.LeftMenuLinkContainer.configuration":"\u69CB\u6210","app.components.LeftMenuLinkContainer.general":"\u4E00\u822C","app.components.LeftMenuLinkContainer.noPluginsInstalled":"\u30D7\u30E9\u30B0\u30A4\u30F3\u304C\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u3055\u308C\u3066\u3044\u307E\u305B\u3093","app.components.LeftMenuLinkContainer.plugins":"\u30D7\u30E9\u30B0\u30A4\u30F3","app.components.LeftMenuLinkContainer.singleTypes":"\u30B7\u30F3\u30B0\u30EB\u30BF\u30A4\u30D7","app.components.ListPluginsPage.deletePlugin.description":"It might take a few seconds to uninstall the plugin.","app.components.ListPluginsPage.deletePlugin.title":"\u30A2\u30F3\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u4E2D","app.components.ListPluginsPage.description":"\u3053\u306E\u30D7\u30ED\u30B8\u30A7\u30AF\u30C8\u3067\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u3055\u308C\u305F\u30D7\u30E9\u30B0\u30A4\u30F3\u4E00\u89A7","app.components.ListPluginsPage.helmet.title":"\u30D7\u30E9\u30B0\u30A4\u30F3\u4E00\u89A7","app.components.Logout.logout":"\u30ED\u30B0\u30A2\u30A6\u30C8","app.components.Logout.profile":"\u30D7\u30ED\u30D5\u30A3\u30FC\u30EB","app.components.MarketplaceBanner":"Discover plugins built by the community, and many more awesome things to kickstart your project, on Strapi Awesome.","app.components.MarketplaceBanner.image.alt":"a strapi rocket logo","app.components.MarketplaceBanner.link":"Check it out now","app.components.NotFoundPage.back":"\u30DB\u30FC\u30E0\u30DA\u30FC\u30B8\u306B\u623B\u308B","app.components.NotFoundPage.description":"\u898B\u3064\u304B\u308A\u307E\u305B\u3093","app.components.Official":"\u30AA\u30D5\u30A3\u30B7\u30E3\u30EB","app.components.Onboarding.help.button":"Help button","app.components.Onboarding.label.completed":"% completed","app.components.Onboarding.title":"Get Started Videos","app.components.PluginCard.Button.label.download":"\u30C0\u30A6\u30F3\u30ED\u30FC\u30C9","app.components.PluginCard.Button.label.install":"\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"The autoReload feature needs to be enabled. Please start your app with `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"I understand!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"For security reasons, a plugin can only be downloaded in a development environment.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Downloading is impossible","app.components.PluginCard.compatible":"\u30A2\u30D7\u30EA\u3068\u306E\u4E92\u63DB\u6027","app.components.PluginCard.compatibleCommunity":"\u30B3\u30DF\u30E5\u30CB\u30C6\u30A3\u3068\u306E\u4E92\u63DB\u6027","app.components.PluginCard.more-details":"\u8A73\u7D30\u3092\u898B\u308B","app.components.ToggleCheckbox.off-label":"False","app.components.ToggleCheckbox.on-label":"True","app.components.Users.MagicLink.connect":"Copy and share this link to give access to this user","app.components.Users.MagicLink.connect.sso":"Send this link to the user, the first login can be made via a SSO provider","app.components.Users.ModalCreateBody.block-title.details":"User details","app.components.Users.ModalCreateBody.block-title.roles":"User's roles","app.components.Users.ModalCreateBody.block-title.roles.description":"A user can have one or several roles","app.components.Users.SortPicker.button-label":"Sort by","app.components.Users.SortPicker.sortby.email_asc":"Email (A to Z)","app.components.Users.SortPicker.sortby.email_desc":"Email (Z to A)","app.components.Users.SortPicker.sortby.firstname_asc":"First name (A to Z)","app.components.Users.SortPicker.sortby.firstname_desc":"First name (Z to A)","app.components.Users.SortPicker.sortby.lastname_asc":"Last name (A to Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Last name (Z to A)","app.components.Users.SortPicker.sortby.username_asc":"Username (A to Z)","app.components.Users.SortPicker.sortby.username_desc":"Username (Z to A)","app.components.listPlugins.button":"\u30D7\u30E9\u30B0\u30A4\u30F3\u3092\u8FFD\u52A0","app.components.listPlugins.title.none":"\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u6E08\u307F\u306E\u30D7\u30E9\u30B0\u30A4\u30F3\u306F\u3042\u308A\u307E\u305B\u3093","app.components.listPluginsPage.deletePlugin.error":"\u30A2\u30F3\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","app.containers.App.notification.error.init":"API\u306E\u30EA\u30AF\u30A8\u30B9\u30C8\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"If you do not receive this link, please contact your administrator.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"It can take a few minutes to receive your password recovery link.","app.containers.AuthPage.ForgotPasswordSuccess.title":"Email sent","app.containers.Users.EditPage.form.active.label":"Active","app.containers.Users.EditPage.header.label":"{name}\u3092\u7DE8\u96C6","app.containers.Users.EditPage.header.label-loading":"\u30E6\u30FC\u30B6\u30FC\u3092\u7DE8\u96C6","app.containers.Users.EditPage.roles-bloc-title":"Attributed roles","app.containers.Users.ModalForm.footer.button-success":"\u30E6\u30FC\u30B6\u30FC\u3092\u62DB\u5F85","app.links.configure-view":"\u8868\u793A\u8A2D\u5B9A","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Add filter","app.utils.close-label":"\u9589\u3058\u308B","app.utils.defaultMessage":" ","app.utils.duplicate":"\u5FA9\u5143","app.utils.edit":"\u7DE8\u96C6","app.utils.errors.file-too-big.message":"The file is too big","app.utils.filter-value":"Filter value","app.utils.filters":"\u30D5\u30A3\u30EB\u30BF\u30FC","app.utils.notify.data-loaded":"The {target} has loaded","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Publish","app.utils.select-all":"\u3059\u3079\u3066\u3092\u9078\u629E","app.utils.select-field":"Select field","app.utils.select-filter":"Select filter","app.utils.unpublish":"Unpublish",clearLabel:m,"coming.soon":"This content is currently under construction and will be back in a few weeks!","component.Input.error.validation.integer":"The value must be an integer","components.AutoReloadBlocker.description":"Run Strapi with one of the following commands:","components.AutoReloadBlocker.header":"\u30D7\u30E9\u30B0\u30A4\u30F3\u3092\u6709\u52B9\u5316\u3059\u308B\u306B\u306F\u30EA\u30ED\u30FC\u30C9\u304C\u5FC5\u8981\u3067\u3059","components.ErrorBoundary.title":"\u306A\u306B\u304B\u304C\u9593\u9055\u3063\u3066\u3044\u307E\u3059...","components.FilterOptions.FILTER_TYPES.$contains":"\u3092\u542B\u3080","components.FilterOptions.FILTER_TYPES.$containsi":"\u3092\u542B\u3080 (case insensitive)","components.FilterOptions.FILTER_TYPES.$endsWith":"\u3067\u7D42\u308F\u308B","components.FilterOptions.FILTER_TYPES.$endsWithi":"\u3067\u7D42\u308F\u308B (case insensitive)","components.FilterOptions.FILTER_TYPES.$eq":"\u540C\u7B49","components.FilterOptions.FILTER_TYPES.$eqi":"\u540C\u7B49 (case insensitive)","components.FilterOptions.FILTER_TYPES.$gt":"is greater than","components.FilterOptions.FILTER_TYPES.$gte":"is greater than or equal to","components.FilterOptions.FILTER_TYPES.$lt":"is lower than","components.FilterOptions.FILTER_TYPES.$lte":"is lower than or equal to","components.FilterOptions.FILTER_TYPES.$ne":"\u7B49\u3057\u304F\u3042\u308A\u307E\u305B\u3093","components.FilterOptions.FILTER_TYPES.$nei":"\u7B49\u3057\u304F\u3042\u308A\u307E\u305B\u3093 (case insensitive)","components.FilterOptions.FILTER_TYPES.$notContains":"\u542B\u307E\u308C\u3066\u3044\u306A\u3044","components.FilterOptions.FILTER_TYPES.$notContainsi":"\u542B\u307E\u308C\u3066\u3044\u306A\u3044 (case insensitive)","components.FilterOptions.FILTER_TYPES.$notNull":"is not null","components.FilterOptions.FILTER_TYPES.$null":"is null","components.FilterOptions.FILTER_TYPES.$startsWith":"\u76AE\u5207\u308A\u306B","components.FilterOptions.FILTER_TYPES.$startsWithi":"\u76AE\u5207\u308A\u306B (case insensitive)","components.Input.error.attribute.key.taken":"\u3053\u306E\u5024\u306F\u3059\u3067\u306B\u5B58\u5728\u3057\u3066\u3044\u307E\u3059","components.Input.error.attribute.sameKeyAndName":"\u7B49\u3057\u304F\u3042\u308A\u307E\u305B\u3093","components.Input.error.attribute.taken":"\u3053\u306E\u30D5\u30A3\u30FC\u30EB\u30C9\u540D\u306F\u3059\u3067\u306B\u5B58\u5728\u3057\u307E\u3059","components.Input.error.contain.lowercase":"Password must contain at least one lowercase character","components.Input.error.contain.number":"Password must contain at least one number","components.Input.error.contain.uppercase":"Password must contain at least one uppercase character","components.Input.error.contentTypeName.taken":"\u3053\u306E\u540D\u524D\u306F\u3059\u3067\u306B\u5B58\u5728\u3057\u307E\u3059","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"\u30D1\u30B9\u30EF\u30FC\u30C9\u304C\u4E00\u81F4\u3057\u307E\u305B\u3093","components.Input.error.validation.email":"E-mail\u30A2\u30C9\u30EC\u30B9\u3067\u306F\u3042\u308A\u307E\u305B\u3093","components.Input.error.validation.json":"JSON\u30D5\u30A9\u30FC\u30DE\u30C3\u30C8\u3067\u306F\u3042\u308A\u307E\u305B\u3093","components.Input.error.validation.max":"\u5024\u304C\u5927\u304D\u3059\u304E\u307E\u3059 {max}","components.Input.error.validation.maxLength":"\u5024\u304C\u9577\u3059\u304E\u307E\u3059 {max}","components.Input.error.validation.min":"\u5024\u304C\u5C0F\u3055\u3059\u304E\u307E\u3059 {min}","components.Input.error.validation.minLength":"\u5024\u304C\u77ED\u3059\u304E\u307E\u3059 {min}","components.Input.error.validation.minSupMax":"\u5024\u304C\u8D85\u904E\u3057\u3066\u3044\u307E\u3059","components.Input.error.validation.regex":"\u5024\u304C\u6B63\u898F\u8868\u73FE\u3068\u4E00\u81F4\u3057\u307E\u305B\u3093","components.Input.error.validation.required":"\u3053\u306E\u5024\u306F\u5FC5\u9808\u9805\u76EE\u3067\u3059","components.Input.error.validation.unique":"\u3053\u306E\u5024\u306F\u3059\u3067\u306B\u5B58\u5728\u3057\u307E\u3059","components.InputSelect.option.placeholder":"\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044","components.ListRow.empty":"\u8868\u793A\u3059\u308B\u30C7\u30FC\u30BF\u304C\u3042\u308A\u307E\u305B\u3093","components.NotAllowedInput.text":"No permissions to see this field","components.OverlayBlocker.description":"\u30B5\u30FC\u30D0\u30FC\u306E\u30EA\u30B9\u30BF\u30FC\u30C8\u304C\u5FC5\u8981\u306A\u6A5F\u80FD\u3092\u4F7F\u7528\u3057\u3066\u3044\u307E\u3059\u3002\u30B5\u30FC\u30D0\u30FC\u304C\u8D77\u52D5\u3059\u308B\u307E\u3067\u304A\u5F85\u3061\u4E0B\u3055\u3044","components.OverlayBlocker.description.serverError":"The server should have restarted, please check your logs in the terminal.","components.OverlayBlocker.title":"\u30EA\u30B9\u30BF\u30FC\u30C8\u3092\u5F85\u3063\u3066\u3044\u307E\u3059...","components.OverlayBlocker.title.serverError":"The restart is taking longer than expected","components.PageFooter.select":"\u30DA\u30FC\u30B8\u6BCE\u306B\u8868\u793A\u3059\u308B\u6295\u7A3F\u6570","components.ProductionBlocker.description":"\u3053\u306E\u30D7\u30E9\u30B0\u30A4\u30F3\u306F\u3001\u5B89\u5168\u306E\u305F\u3081\u3001\u4ED6\u306E\u74B0\u5883\u3067\u306F\u7121\u52B9\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059","components.ProductionBlocker.header":"\u3053\u306E\u30D7\u30E9\u30B0\u30A4\u30F3\u306F\u30C7\u30D9\u30ED\u30C3\u30D7\u74B0\u5883\u3067\u306E\u307F\u5229\u7528\u3067\u304D\u307E\u3059","components.Search.placeholder":"\u691C\u7D22...","components.TableHeader.sort":"Sort on {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Markdown mode","components.Wysiwyg.ToggleMode.preview-mode":"Preview mode","components.Wysiwyg.collapse":"Collapse","components.Wysiwyg.selectOptions.H1":"\u30BF\u30A4\u30C8\u30EB H1","components.Wysiwyg.selectOptions.H2":"\u30BF\u30A4\u30C8\u30EB H2","components.Wysiwyg.selectOptions.H3":"\u30BF\u30A4\u30C8\u30EB H3","components.Wysiwyg.selectOptions.H4":"\u30BF\u30A4\u30C8\u30EB H4","components.Wysiwyg.selectOptions.H5":"\u30BF\u30A4\u30C8\u30EB H5","components.Wysiwyg.selectOptions.H6":"\u30BF\u30A4\u30C8\u30EB H6","components.Wysiwyg.selectOptions.title":"\u30BF\u30A4\u30C8\u30EB\u3092\u8FFD\u52A0\u3059\u308B","components.WysiwygBottomControls.charactersIndicators":"\u30AD\u30E3\u30E9\u30AF\u30BF\u30FC","components.WysiwygBottomControls.fullscreen":"\u5E83\u3052\u308B","components.WysiwygBottomControls.uploadFiles":"\u30D5\u30A1\u30A4\u30EB\u3092\u30C9\u30E9\u30C3\u30B0\uFF06\u30C9\u30ED\u30C3\u30D7, \u30AF\u30EA\u30C3\u30D7\u30DC\u30FC\u30C9\u304B\u3089\u30DA\u30FC\u30B9\u30C8 \u3082\u3057\u304F\u306F {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"\u9078\u3076","components.pagination.go-to":"Go to page {page}","components.pagination.go-to-next":"Go to next page","components.pagination.go-to-previous":"Go to previous page","components.pagination.remaining-links":"And {number} other links","components.popUpWarning.button.cancel":"No, cancel","components.popUpWarning.button.confirm":"Yes, confirm","components.popUpWarning.message":"\u672C\u5F53\u306B\u524A\u9664\u3057\u307E\u3059\u304B?","components.popUpWarning.title":"\u78BA\u8A8D\u3057\u3066\u304F\u3060\u3055\u3044","content-manager.App.schemas.data-loaded":"The schemas have been successfully loaded","content-manager.ListViewTable.relation-loaded":"The relations have been loaded","content-manager.EditRelations.title":"\u30EA\u30EC\u30FC\u30B7\u30E7\u30CA\u30EB\u30C7\u30FC\u30BF","content-manager.HeaderLayout.button.label-add-entry":"Create new entry","content-manager.api.id":"API ID","content-manager.components.AddFilterCTA.add":"\u30D5\u30A3\u30EB\u30BF","content-manager.components.AddFilterCTA.hide":"\u30D5\u30A3\u30EB\u30BF","content-manager.components.DragHandle-label":"Drag","content-manager.components.DraggableAttr.edit":"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u7DE8\u96C6","content-manager.components.DraggableCard.delete.field":"{item}\u3092\u524A\u9664","content-manager.components.DraggableCard.edit.field":"{item}\u3092\u7DE8\u96C6","content-manager.components.DraggableCard.move.field":"Move {item}","content-manager.components.ListViewTable.row-line":"item line {number}","content-manager.components.DynamicZone.ComponentPicker-label":"Pick one component","content-manager.components.DynamicZone.add-component":"Add a component to {componentName}","content-manager.components.DynamicZone.delete-label":"{name}\u3092\u524A\u9664","content-manager.components.DynamicZone.error-message":"The component contains error(s)","content-manager.components.DynamicZone.missing-components":"There {number, plural, =0 {are # missing components} one {is # missing component} other {are # missing components}}","content-manager.components.DynamicZone.move-down-label":"Move component down","content-manager.components.DynamicZone.move-up-label":"Move component up","content-manager.components.DynamicZone.pick-compo":"Pick one component","content-manager.components.DynamicZone.required":"Component is required","content-manager.components.EmptyAttributesBlock.button":"\u8A2D\u5B9A\u30DA\u30FC\u30B8\u306B\u79FB\u52D5","content-manager.components.EmptyAttributesBlock.description":"\u8A2D\u5B9A\u3092\u5909\u66F4\u3059\u308B\u3053\u3068\u304C\u3067\u304D\u307E\u3059","content-manager.components.FieldItem.linkToComponentLayout":"Set the component's layout","content-manager.components.FieldSelect.label":"Add a field","content-manager.components.FilterOptions.button.apply":"\u9069\u7528","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"\u9069\u7528","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"\u3059\u3079\u3066\u30AF\u30EA\u30A2","content-manager.components.FiltersPickWrapper.PluginHeader.description":"\u30A8\u30F3\u30C8\u30EA\u3092\u30D5\u30A3\u30EB\u30BF\u30EA\u30F3\u30B0\u3059\u308B\u305F\u3081\u306E\u6761\u4EF6\u3092\u8A2D\u5B9A\u3059\u308B","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"\u30D5\u30A3\u30EB\u30BF","content-manager.components.FiltersPickWrapper.hide":"\u96A0\u3059","content-manager.components.LeftMenu.Search.label":"Search for a content type","content-manager.components.LeftMenu.collection-types":"Collection Types","content-manager.components.LeftMenu.single-types":"Single Types","content-manager.components.LimitSelect.itemsPerPage":"\u30DA\u30FC\u30B8\u3042\u305F\u308A\u306E\u30A2\u30A4\u30C6\u30E0\u6570","content-manager.components.NotAllowedInput.text":"No permissions to see this field","content-manager.components.RepeatableComponent.error-message":"The component(s) contain error(s)","content-manager.components.Search.placeholder":"\u30A8\u30F3\u30C8\u30EA\u3092\u691C\u7D22\u3059\u308B...","content-manager.components.Select.draft-info-title":"State: Draft","content-manager.components.Select.publish-info-title":"State: Published","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Customize how the edit view will look like.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Define the settings of the list view.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Configure the view - {name}","content-manager.components.TableDelete.delete":"\u3059\u3079\u3066\u524A\u9664","content-manager.components.TableDelete.deleteSelected":"\u9078\u629E\u3092\u524A\u9664\u3057\u307E\u3059","content-manager.components.TableDelete.label":"{number, plural, one {# entry} other {# entries}} selected","content-manager.components.TableEmpty.withFilters":"\u9069\u7528\u3055\u308C\u305F\u30D5\u30A3\u30EB\u30BF\u306B\u306F{contentType}\u306F\u3042\u308A\u307E\u305B\u3093...","content-manager.components.TableEmpty.withSearch":"\u691C\u7D22\u306B\u5BFE\u5FDC\u3059\u308B{contentType}\u306F\u3042\u308A\u307E\u305B\u3093\uFF08{search}\uFF09...","content-manager.components.TableEmpty.withoutFilter":"{contentType}\u306F\u3042\u308A\u307E\u305B\u3093...","content-manager.components.empty-repeatable":"No entry yet. Click on the button below to add one.","content-manager.components.notification.info.maximum-requirement":"You have already reached the maximum number of fields","content-manager.components.notification.info.minimum-requirement":"A field has been added to match the minimum requirement","content-manager.components.repeatable.reorder.error":"An error occurred while reordering your component's field, please try again","content-manager.components.reset-entry":"Reset entry","content-manager.components.uid.apply":"apply","content-manager.components.uid.available":"Available","content-manager.components.uid.regenerate":"Regenerate","content-manager.components.uid.suggested":"suggested","content-manager.components.uid.unavailable":"Unavailable","content-manager.containers.Edit.Link.Layout":"Configure the layout","content-manager.containers.Edit.Link.Model":"Edit the collection-type","content-manager.containers.Edit.addAnItem":"\u30A2\u30A4\u30C6\u30E0\u3092\u8FFD\u52A0\u3059\u308B...","content-manager.containers.Edit.clickToJump":"\u30AF\u30EA\u30C3\u30AF\u3059\u308B\u3068\u30A8\u30F3\u30C8\u30EA\u306B\u30B8\u30E3\u30F3\u30D7\u3057\u307E\u3059","content-manager.containers.Edit.delete":"\u524A\u9664","content-manager.containers.Edit.delete-entry":"Delete this entry","content-manager.containers.Edit.editing":"\u7DE8\u96C6...","content-manager.containers.Edit.information":"Information","content-manager.containers.Edit.information.by":"By","content-manager.containers.Edit.information.created":"Created","content-manager.containers.Edit.information.draftVersion":"draft version","content-manager.containers.Edit.information.editing":"Editing","content-manager.containers.Edit.information.lastUpdate":"Last update","content-manager.containers.Edit.information.publishedVersion":"published version","content-manager.containers.Edit.pluginHeader.title.new":"Create an entry","content-manager.containers.Edit.reset":"\u30EA\u30BB\u30C3\u30C8","content-manager.containers.Edit.returnList":"\u30EA\u30B9\u30C8\u306B\u623B\u308B","content-manager.containers.Edit.seeDetails":"\u8A73\u7D30","content-manager.containers.Edit.submit":"\u4FDD\u5B58","content-manager.containers.EditSettingsView.modal-form.edit-field":"Edit the field","content-manager.containers.EditView.add.new-entry":"Add an entry","content-manager.containers.EditView.notification.errors":"The form contains some errors","content-manager.containers.Home.introduction":"\u3042\u306A\u305F\u306E\u30A8\u30F3\u30C8\u30EA\u30FC\u3092\u7DE8\u96C6\u3059\u308B\u306B\u306F\u3001\u5DE6\u5074\u306E\u30E1\u30CB\u30E5\u30FC\u306E\u7279\u5B9A\u306E\u30EA\u30F3\u30AF\u306B\u884C\u304D\u307E\u3059\u3002\u3053\u306E\u30D7\u30E9\u30B0\u30A4\u30F3\u306F\u8A2D\u5B9A\u3092\u7DE8\u96C6\u3059\u308B\u9069\u5207\u306A\u65B9\u6CD5\u304C\u306A\u304F\u3001\u307E\u3060\u30A2\u30AF\u30C6\u30A3\u30D6\u306A\u958B\u767A\u4E2D\u3067\u3059","content-manager.containers.Home.pluginHeaderDescription":"\u30D1\u30EF\u30D5\u30EB\u3067\u7F8E\u3057\u3044\u30A4\u30F3\u30BF\u30FC\u30D5\u30A7\u30A4\u30B9\u3067\u30A8\u30F3\u30C8\u30EA\u3092\u7BA1\u7406\u3057\u307E\u3059\u3002","content-manager.containers.Home.pluginHeaderTitle":"\u30B3\u30F3\u30C6\u30F3\u30C4 \u30DE\u30CD\u30FC\u30B8\u30E3","content-manager.containers.List.draft":"Draft","content-manager.containers.List.errorFetchRecords":"\u30A8\u30E9\u30FC","content-manager.containers.List.published":"Published","content-manager.containers.ListPage.displayedFields":"\u30D5\u30A3\u30FC\u30EB\u30C9\u304C\u8868\u793A\u3055\u308C\u307E\u3057\u305F","content-manager.containers.ListPage.items":"{number, plural, =0 {items} one {item} other {items}}","content-manager.containers.ListPage.table-headers.publishedAt":"State","content-manager.containers.ListSettingsView.modal-form.edit-label":"Edit {fieldName}","content-manager.containers.SettingPage.add.field":"Insert another field","content-manager.containers.SettingPage.attributes":"\u5C5E\u6027\u30D5\u30A3\u30FC\u30EB\u30C9","content-manager.containers.SettingPage.attributes.description":"\u5C5E\u6027\u306E\u9806\u5E8F\u3092\u5B9A\u7FA9\u3059\u308B","content-manager.containers.SettingPage.editSettings.description":"\u30D5\u30A3\u30FC\u30EB\u30C9\u3092\u30C9\u30E9\u30C3\u30B0\u30A2\u30F3\u30C9\u30C9\u30ED\u30C3\u30D7\u3057\u3066\u30EC\u30A4\u30A2\u30A6\u30C8\u3092\u4F5C\u6210\u3059\u308B","content-manager.containers.SettingPage.editSettings.entry.title":"Entry title","content-manager.containers.SettingPage.editSettings.entry.title.description":"Set the displayed field of your entry","content-manager.containers.SettingPage.editSettings.relation-field.description":"Set the displayed field in both the edit and list views","content-manager.containers.SettingPage.editSettings.title":"\u7DE8\u96C6 (\u8A2D\u5B9A)","content-manager.containers.SettingPage.layout":"Layout","content-manager.containers.SettingPage.listSettings.description":"Configure the options for this collection type","content-manager.containers.SettingPage.listSettings.title":"\u4E00\u89A7 (\u8A2D\u5B9A)","content-manager.containers.SettingPage.pluginHeaderDescription":"Configure the specific settings for this Collection Type","content-manager.containers.SettingPage.settings":"\u8A2D\u5B9A","content-manager.containers.SettingPage.view":"View","content-manager.containers.SettingViewModel.pluginHeader.title":"\u30B3\u30F3\u30C6\u30F3\u30C4\u7BA1\u7406 - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"\u7279\u5B9A\u306E\u8A2D\u5B9A\u3092\u69CB\u6210\u3059\u308B","content-manager.containers.SettingsPage.Block.contentType.title":"Collection Types","content-manager.containers.SettingsPage.Block.generalSettings.description":"Configure the default options for your Collection Types","content-manager.containers.SettingsPage.Block.generalSettings.title":"\u4E00\u822C","content-manager.containers.SettingsPage.pluginHeaderDescription":"Configure the settings for all your Collection types and Groups","content-manager.containers.SettingsView.list.subtitle":"Configure the layout and display of your Collection types and groups","content-manager.containers.SettingsView.list.title":"Display configurations","content-manager.edit-settings-view.link-to-ctb.components":"Edit the component","content-manager.edit-settings-view.link-to-ctb.content-types":"Edit the content type","content-manager.emptyAttributes.button":"Go to collection type builder","content-manager.emptyAttributes.description":"Add your first field to your Collection Type","content-manager.emptyAttributes.title":"\u30D5\u30A3\u30FC\u30EB\u30C9\u306F\u307E\u3060\u3042\u308A\u307E\u305B\u3093","content-manager.error.attribute.key.taken":"\u3053\u306E\u5024\u306F\u65E2\u306B\u5B58\u5728\u3057\u307E\u3059","content-manager.error.attribute.sameKeyAndName":"\u540C\u3058\u306B\u3059\u308B\u3053\u3068\u306F\u3067\u304D\u307E\u305B\u3093","content-manager.error.attribute.taken":"\u3053\u306E\u30D5\u30A3\u30FC\u30EB\u30C9\u540D\u306F\u65E2\u306B\u5B58\u5728\u3057\u307E\u3059","content-manager.error.contentTypeName.taken":"\u3053\u306E\u540D\u524D\u306F\u65E2\u306B\u5B58\u5728\u3057\u307E\u3059","content-manager.error.model.fetch":"\u30E2\u30C7\u30EB\u306E\u8A2D\u5B9A\u30D5\u30A7\u30C3\u30C1\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","content-manager.error.record.create":"\u30EC\u30B3\u30FC\u30C9\u306E\u4F5C\u6210\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","content-manager.error.record.delete":"\u30EC\u30B3\u30FC\u30C9\u306E\u524A\u9664\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","content-manager.error.record.fetch":"\u30EC\u30B3\u30FC\u30C9\u306E\u53D6\u5F97\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","content-manager.error.record.update":"\u30EC\u30B3\u30FC\u30C9\u306E\u66F4\u65B0\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","content-manager.error.records.count":"\u30AB\u30A6\u30F3\u30C8\u30EC\u30B3\u30FC\u30C9\u306E\u30D5\u30A7\u30C3\u30C1\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F.","content-manager.error.records.fetch":"\u30EC\u30B3\u30FC\u30C9\u306E\u53D6\u5F97\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","content-manager.error.schema.generation":"\u30B9\u30AD\u30FC\u30DE\u306E\u751F\u6210\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","content-manager.error.validation.json":"\u3053\u308C\u306FJSON\u3067\u306F\u3042\u308A\u307E\u305B\u3093","content-manager.error.validation.max":"\u5024\u304C\u9AD8\u3059\u304E\u307E\u3059","content-manager.error.validation.maxLength":"\u5024\u304C\u9577\u3059\u304E\u307E\u3059","content-manager.error.validation.min":"\u5024\u304C\u4F4E\u3059\u304E\u307E\u3059","content-manager.error.validation.minLength":"\u5024\u304C\u5C0F\u3055\u3059\u304E\u307E\u3059","content-manager.error.validation.minSupMax":"\u512A\u308C\u3066\u3044\u308B\u3053\u3068\u306F\u3067\u304D\u307E\u305B\u3093","content-manager.error.validation.regex":"\u5024\u306F\u6B63\u898F\u8868\u73FE\u3068\u4E00\u81F4\u3057\u307E\u305B\u3093","content-manager.error.validation.required":"\u3053\u306E\u5024\u306E\u5165\u529B\u306F\u5FC5\u9808\u3067\u3059","content-manager.form.Input.bulkActions":"\u4E00\u62EC\u51E6\u7406\u3092\u6709\u52B9\u306B\u3059\u308B","content-manager.form.Input.defaultSort":"\u30C7\u30D5\u30A9\u30EB\u30C8\u306E\u30BD\u30FC\u30C8\u5C5E\u6027","content-manager.form.Input.description":"\u8AAC\u660E\u6587","content-manager.form.Input.description.placeholder":"\u30D7\u30ED\u30D5\u30A3\u30FC\u30EB\u306E\u8868\u793A\u540D","content-manager.form.Input.editable":"\u7DE8\u96C6\u53EF\u80FD\u306A\u30D5\u30A3\u30FC\u30EB\u30C9","content-manager.form.Input.filters":"\u30D5\u30A3\u30EB\u30BF\u3092\u6709\u52B9\u306B\u3059\u308B","content-manager.form.Input.label":"Label","content-manager.form.Input.label.inputDescription":"\u3053\u306E\u5024\u306F\u3001\u30C6\u30FC\u30D6\u30EB\u306E\u5148\u982D\u306B\u8868\u793A\u3055\u308C\u308B\u30E9\u30D9\u30EB","content-manager.form.Input.pageEntries":"1\u30DA\u30FC\u30B8\u3042\u305F\u308A\u306E\u30A8\u30F3\u30C8\u30EA\u6570","content-manager.form.Input.pageEntries.inputDescription":"Note: You can override this value in the Collection Type settings page.","content-manager.form.Input.placeholder":"\u30D7\u30EC\u30FC\u30B9\u30DB\u30EB\u30C0\u30FC","content-manager.form.Input.placeholder.placeholder":"My awesome value","content-manager.form.Input.search":"\u691C\u7D22\u3092\u6709\u52B9\u306B\u3059\u308B","content-manager.form.Input.search.field":"\u3053\u306E\u30D5\u30A3\u30FC\u30EB\u30C9\u3067\u691C\u7D22\u3092\u6709\u52B9\u306B\u3059\u308B","content-manager.form.Input.sort.field":"\u3053\u306E\u30D5\u30A3\u30FC\u30EB\u30C9\u3067\u30BD\u30FC\u30C8\u3092\u6709\u52B9\u306B\u3059\u308B","content-manager.form.Input.sort.order":"Default sort order","content-manager.form.Input.wysiwyg":"Display as WYSIWYG","content-manager.global.displayedFields":"Displayed Fields","content-manager.groups":"Groups","content-manager.groups.numbered":"Groups ({number})","content-manager.header.name":"Content","content-manager.link-to-ctb":"Edit the model","content-manager.models":"Collection Types","content-manager.models.numbered":"Collection Types ({number})","content-manager.notification.error.displayedFields":"\u5C11\u306A\u304F\u3068\u30821\u3064\u306E\u8868\u793A\u30D5\u30A3\u30FC\u30EB\u30C9\u304C\u5FC5\u8981\u3067\u3059","content-manager.notification.error.relationship.fetch":"\u30EA\u30EC\u30FC\u30B7\u30E7\u30F3\u30B7\u30C3\u30D7\u30D5\u30A7\u30C3\u30C1\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","content-manager.notification.info.SettingPage.disableSort":"\u4E26\u3079\u66FF\u3048\u3092\u8A31\u53EF\u3059\u308B\u5C5E\u6027\u304C1\u3064\u5FC5\u8981\u3067\u3059","content-manager.notification.info.minimumFields":"You need to have at least one field displayed","content-manager.notification.upload.error":"An error has occurred while uploading your files","content-manager.pageNotFound":"\u30DA\u30FC\u30B8\u304C\u898B\u3064\u304B\u308A\u307E\u305B\u3093","content-manager.pages.ListView.header-subtitle":"{number, plural, =0 {# entries} one {# entry} other {# entries}} found","content-manager.pages.NoContentType.button":"Create your first Content-Type","content-manager.pages.NoContentType.text":"You don't have any content yet, we recommend you to create your first Content-Type.","content-manager.permissions.not-allowed.create":"You are not allowed to create a document","content-manager.permissions.not-allowed.update":"You are not allowed to see this document","content-manager.plugin.description.long":"\u30C7\u30FC\u30BF\u30D9\u30FC\u30B9\u5185\u306E\u30C7\u30FC\u30BF\u3092\u8868\u793A\u3001\u7DE8\u96C6\u3001\u524A\u9664\u3059\u308B\u305F\u3081\u306E\u65B9\u6CD5\u3002","content-manager.plugin.description.short":"\u30C7\u30FC\u30BF\u30D9\u30FC\u30B9\u5185\u306E\u30C7\u30FC\u30BF\u3092\u8868\u793A\u3001\u7DE8\u96C6\u3001\u524A\u9664\u3059\u308B\u305F\u3081\u306E\u65B9\u6CD5\u3002","content-manager.popover.display-relations.label":"Display relations","content-manager.success.record.delete":"\u524A\u9664","content-manager.success.record.publish":"Published","content-manager.success.record.save":"\u4FDD\u5B58","content-manager.success.record.unpublish":"Unpublished","content-manager.utils.data-loaded":"The {number, plural, =1 {entry has} other {entries have}} successfully been loaded","content-manager.popUpWarning.warning.publish-question":"Do you still want to publish?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Yes, publish","form.button.done":"\u5B8C\u4E86","global.prompt.unsaved":"\u30DA\u30FC\u30B8\u304B\u3089\u96E2\u308C\u3066\u3082\u3044\u3044\u3067\u3059\u304B\uFF1F\u7DE8\u96C6\u4E2D\u306E\u3082\u306E\u306F\u5168\u3066\u5931\u308F\u308C\u307E\u3059","notification.contentType.relations.conflict":"\u30B3\u30F3\u30C6\u30F3\u30C4\u30BF\u30A4\u30D7\u304C\u30EA\u30EC\u30FC\u30B7\u30E7\u30F3\u3068\u7AF6\u5408\u3057\u3066\u3044\u307E\u3059","notification.default.title":"Information:","notification.error":"\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F","notification.error.layout":"\u30EC\u30A4\u30A2\u30A6\u30C8\u3092\u5FA9\u65E7\u3067\u304D\u307E\u305B\u3093\u3067\u3057\u305F","notification.form.error.fields":"\u30D5\u30A9\u30FC\u30E0\u306B\u540C\u3058\u30A8\u30E9\u30FC\u304C\u3042\u308A\u307E\u3059","notification.form.success.fields":"\u4FDD\u5B58\u3055\u308C\u307E\u3057\u305F","notification.link-copied":"\u30AF\u30EA\u30C3\u30D7\u30DC\u30FC\u30C9\u306B\u30EA\u30F3\u30AF\u3092\u30B3\u30D4\u30FC\u3057\u307E\u3057\u305F","notification.permission.not-allowed-read":"You are not allowed to see this document","notification.success.delete":"\u30A2\u30A4\u30C6\u30E0\u306F\u524A\u9664\u3055\u308C\u307E\u3057\u305F","notification.success.saved":"Saved","notification.success.title":"Success:","notification.version.update.message":"WHEN","notification.warning.title":"Warning:",or:F,"request.error.model.unknown":"\u30E2\u30C7\u30EB\u304C\u5B58\u5728\u3057\u307E\u305B\u3093",skipToContent:E,submit:C}}}]);
