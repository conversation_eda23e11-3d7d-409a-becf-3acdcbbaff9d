"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[632],{632:(N,g,s)=>{s.d(g,{ProtectedEditPage:()=>ts});var a=s(92132),e=s(21272),E=s(94061),P=s(85963),i=s(90151),c=s(68074),C=s(4198),f=s(55356),D=s(38413),O=s(83997),h=s(30893),U=s(21610),t=s(55506),I=s(46270),W=s(54514),p=s(61535),r=s(79275),_=s(54894),d=s(17703),u=s(71389),A=s(12083),l=s(43543),n=s(66248),T=s(82803),Y=s(97146),J=s(15126),X=s(63299),M=s(67014),ds=s(59080),ls=s(14718),Es=s(82437),Ms=s(5790),ms=s(35223),Ps=s(5409),cs=s(74930),Ds=s(2600),Os=s(48940),gs=s(41286),hs=s(56336),As=s(13426),Ls=s(84624),vs=s(77965),Cs=s(54257),fs=s(71210),Us=s(51187),Is=s(39404),us=s(58692),Bs=s(501),Rs=s(57646),Ws=s(23120),ps=s(44414),Ts=s(25962),Ks=s(14664),xs=s(42588),js=s(90325),ys=s(62785),Ss=s(87443),zs=s(41032),Ns=s(22957),Ys=s(93179),Fs=s(73055),Vs=s(15747),$s=s(85306),Qs=s(26509),Gs=s(32058),Hs=s(81185),Zs=s(82261),Js=s(55151),Xs=s(79077),bs=s(85071);const q=A.Ik().shape({...Y.C,isActive:A.lc(),roles:A.YO().min(1,t.iW.required).required(t.iW.required)}),b=["email","firstname","lastname","username","isActive","roles"],ss=()=>{const{formatMessage:o}=(0,_.A)(),B=(0,d.W5)("/settings/users/:id")?.params?.id??"",{push:R}=(0,d.W6)(),m=(0,t.hN)(),{lockApp:V,unlockApp:$}=(0,t.MA)(),w=(0,l.p)(T.M,async()=>(await s.e(3153).then(s.bind(s,43153))).MagicLinkEE),{_unstableFormatAPIError:Q,_unstableFormatValidationErrors:es}=(0,t.wq)(),k=(0,l.j)(n.s),{isLoading:os,allowedActions:{canUpdate:G}}=(0,t.ec)({read:k.settings?.users.read??[],update:k.settings?.users.update??[]}),[ns]=(0,l.M)();(0,t.L4)();const{data:is,error:y,isLoading:rs}=(0,l.k)({id:B},{refetchOnMountOrArgChange:!0}),[S]=is?.users??[];if(e.useEffect(()=>{y&&(y.name==="UnauthorizedError"?(m({type:"info",message:{id:"notification.permission.not-allowed-read",defaultMessage:"You are not allowed to see this document"}}),R("/")):m({type:"warning",message:{id:"notification.error",defaultMessage:Q(y)}}))},[y,Q,R,m]),rs||!w||os)return(0,a.jsxs)(D.g,{"aria-busy":"true",children:[(0,a.jsx)(t.x7,{name:"Users"}),(0,a.jsx)(f.Q,{primaryAction:(0,a.jsx)(P.$,{disabled:!0,startIcon:(0,a.jsx)(W.A,{}),type:"button",size:"L",children:o({id:"global.save",defaultMessage:"Save"})}),title:o({id:"app.containers.Users.EditPage.header.label-loading",defaultMessage:"Edit user"}),navigationAction:(0,a.jsx)(U.N,{as:u.k2,startIcon:(0,a.jsx)(I.A,{}),to:"/settings/users?pageSize=10&page=1&sort=firstname",children:o({id:"global.back",defaultMessage:"Back"})})}),(0,a.jsx)(C.s,{children:(0,a.jsx)(t.Bl,{})})]});const z={...r(S,b),roles:S.roles.map(({id:L})=>L),password:"",confirmPassword:""},_s=async(L,K)=>{V?.();const{confirmPassword:H,password:x,...Z}=L,v=await ns({id:B,...Z,password:x===""?void 0:x});"error"in v&&(0,l.x)(v.error)?(v.error.name==="ValidationError"&&K.setErrors(es(v.error)),m({type:"warning",message:Q(v.error)})):(m({type:"success",message:o({id:"notification.success.saved",defaultMessage:"Saved"})}),K.setValues({...r(L,b),password:"",confirmPassword:""})),$?.()};return(0,a.jsxs)(D.g,{children:[(0,a.jsx)(t.x7,{name:"Users"}),(0,a.jsx)(p.l1,{onSubmit:_s,initialValues:z,validateOnChange:!1,validationSchema:q,children:({errors:L,values:K,handleChange:H,isSubmitting:x,dirty:Z})=>(0,a.jsxs)(t.lV,{children:[(0,a.jsx)(f.Q,{primaryAction:(0,a.jsx)(P.$,{disabled:x||!G?!0:!Z,startIcon:(0,a.jsx)(W.A,{}),loading:x,type:"submit",size:"L",children:o({id:"global.save",defaultMessage:"Save"})}),title:o({id:"app.containers.Users.EditPage.header.label",defaultMessage:"Edit {name}"},{name:z.username||(0,n.g)(z?.firstname??"",z.lastname)}),navigationAction:(0,a.jsx)(U.N,{as:u.k2,startIcon:(0,a.jsx)(I.A,{}),to:"/settings/users?pageSize=10&page=1&sort=firstname",children:o({id:"global.back",defaultMessage:"Back"})})}),(0,a.jsxs)(C.s,{children:[S?.registrationToken&&(0,a.jsx)(E.a,{paddingBottom:6,children:(0,a.jsx)(w,{registrationToken:S.registrationToken})}),(0,a.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:7,children:[(0,a.jsx)(E.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,a.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,a.jsx)(h.o,{variant:"delta",as:"h2",children:o({id:"app.components.Users.ModalCreateBody.block-title.details",defaultMessage:"Details"})}),(0,a.jsx)(i.x,{gap:5,children:as.map(v=>v.map(j=>(0,a.jsx)(c.E,{...j.size,children:(0,a.jsx)(t.ah,{...j,disabled:!G,error:L[j.name],onChange:H,value:K[j.name]})},j.name)))})]})}),(0,a.jsx)(E.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,a.jsxs)(O.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,a.jsx)(h.o,{variant:"delta",as:"h2",children:o({id:"global.roles",defaultMessage:"User's role"})}),(0,a.jsx)(i.x,{gap:5,children:(0,a.jsx)(c.E,{col:6,xs:12,children:(0,a.jsx)(T.S,{disabled:!G,error:L.roles,onChange:H,value:K.roles})})})]})})]})]})]})})]})},as=[[{intlLabel:{id:"Auth.form.firstname.label",defaultMessage:"First name"},name:"firstname",placeholder:{id:"Auth.form.firstname.placeholder",defaultMessage:"e.g. Kai"},type:"text",size:{col:6,xs:12},required:!0},{intlLabel:{id:"Auth.form.lastname.label",defaultMessage:"Last name"},name:"lastname",placeholder:{id:"Auth.form.lastname.placeholder",defaultMessage:"e.g. Doe"},type:"text",size:{col:6,xs:12}}],[{intlLabel:{id:"Auth.form.email.label",defaultMessage:"Email"},name:"email",placeholder:{id:"Auth.form.email.placeholder",defaultMessage:"e.g. <EMAIL>"},type:"email",size:{col:6,xs:12},required:!0},{intlLabel:{id:"Auth.form.username.label",defaultMessage:"Username"},name:"username",placeholder:{id:"Auth.form.username.placeholder",defaultMessage:"e.g. Kai_Doe"},type:"text",size:{col:6,xs:12}}],[{intlLabel:{id:"global.password",defaultMessage:"Password"},name:"password",type:"password",size:{col:6,xs:12},autoComplete:"new-password"},{intlLabel:{id:"Auth.form.confirmPassword.label",defaultMessage:"Password confirmation"},name:"confirmPassword",type:"password",size:{col:6,xs:12},autoComplete:"new-password"}],[{intlLabel:{id:"Auth.form.active.label",defaultMessage:"Active"},name:"isActive",type:"bool",size:{col:6,xs:12}}]],ts=()=>{const o=(0,t.hN)(),F=(0,l.j)(n.s),{isLoading:B,allowedActions:{canRead:R,canUpdate:m}}=(0,t.ec)({read:F.settings?.users.read??[],update:F.settings?.users.update??[]}),{state:V}=(0,d.zy)(),$=V?.from??"/";return e.useEffect(()=>{B||!R&&!m&&o({type:"info",message:{id:"notification.permission.not-allowed-read",defaultMessage:"You are not allowed to see this document"}})},[B,R,m,o]),B?(0,a.jsx)(t.Bl,{}):!R&&!m?(0,a.jsx)(d.rd,{to:$}):(0,a.jsx)(ss,{})}},82803:(N,g,s)=>{s.d(g,{M:()=>U,S:()=>t,a:()=>h});var a=s(92132),e=s(54894),E=s(43543),P=s(88353),i=s(56654),c=s(55506),C=s(90625),f=s(84795),D=s(63891),O=s(85071);const h=({children:r,target:_})=>{const d=(0,c.hN)(),{formatMessage:u}=(0,e.A)(),{copy:A}=(0,c.iD)(),l=u({id:"app.component.CopyToClipboard.label",defaultMessage:"Copy to clipboard"}),n=async()=>{await A(_)&&d({type:"info",message:{id:"notification.link-copied"}})};return(0,a.jsx)(c.bQ,{endAction:(0,a.jsx)(P.K,{label:l,noBorder:!0,icon:(0,a.jsx)(C.A,{}),onClick:n}),title:_,titleEllipsis:!0,subtitle:r,icon:(0,a.jsx)("span",{style:{fontSize:32},children:"\u2709\uFE0F"}),iconBackground:"neutral100"})},U=({registrationToken:r})=>{const{formatMessage:_}=(0,e.A)(),d=`${window.location.origin}${(0,E.K)()}/auth/register?registrationToken=${r}`;return(0,a.jsx)(h,{target:d,children:_({id:"app.components.Users.MagicLink.connect",defaultMessage:"Copy and share this link to give access to this user"})})},t=({disabled:r,error:_,onChange:d,value:u})=>{const{isLoading:A,roles:l}=(0,O.u)(),{formatMessage:n}=(0,e.A)(),T=_?n({id:_,defaultMessage:_}):"",Y=n({id:"app.components.Users.ModalCreateBody.block-title.roles",defaultMessage:"User's roles"}),J=n({id:"app.components.Users.ModalCreateBody.block-title.roles.description",defaultMessage:"A user can have one or several roles"}),X=n({id:"app.components.Select.placeholder",defaultMessage:"Select"});return(0,a.jsx)(i.KF,{id:"roles",disabled:r,error:T,hint:J,label:Y,name:"roles",onChange:M=>{d({target:{name:"roles",value:M}})},placeholder:X,startIcon:A?(0,a.jsx)(p,{}):void 0,value:u.map(M=>M.toString()),withTags:!0,required:!0,children:l.map(M=>(0,a.jsx)(i.fe,{value:M.id.toString(),children:n({id:`global.${M.code}`,defaultMessage:M.name})},M.id))})},I=(0,D.i7)`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
`,W=D.Ay.div`
  animation: ${I} 2s infinite linear;
`,p=()=>(0,a.jsx)(W,{children:(0,a.jsx)(f.A,{})})},85071:(N,g,s)=>{s.d(g,{u:()=>i});var a=s(21272),e=s(55506),E=s(54894),P=s(43543);const i=(c={},C)=>{const{locale:f}=(0,E.A)(),D=(0,e.QM)(f,{sensitivity:"base"}),{data:O,error:h,isError:U,isLoading:t,refetch:I}=(0,P.z)(c,C);return{roles:a.useMemo(()=>[...O??[]].sort((p,r)=>D.compare(p.name,r.name)),[O,D]),error:h,isError:U,isLoading:t,refetch:I}}},97146:(N,g,s)=>{s.d(g,{C:()=>E});var a=s(55506),e=s(12083);const E={firstname:e.Yj().trim().required(a.iW.required),lastname:e.Yj(),email:e.Yj().email(a.iW.email).lowercase().required(a.iW.required),username:e.Yj().nullable(),password:e.Yj().min(8,a.iW.minLength).matches(/[a-z]/,"components.Input.error.contain.lowercase").matches(/[A-Z]/,"components.Input.error.contain.uppercase").matches(/\d/,"components.Input.error.contain.number"),confirmPassword:e.Yj().min(8,a.iW.minLength).oneOf([e.KR("password"),null],"components.Input.error.password.noMatch").when("password",(P,i)=>P?i.required(a.iW.required):i)}}}]);
