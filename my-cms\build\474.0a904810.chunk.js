"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[474],{474:(v,e,o)=>{o.r(e),o.d(e,{Analytics:()=>n,Documentation:()=>a,Email:()=>t,Password:()=>i,Provider:()=>r,ResetPasswordToken:()=>s,Role:()=>c,Username:()=>m,Users:()=>d,anErrorOccurred:()=>u,clearLabel:()=>g,dark:()=>l,default:()=>f,light:()=>p,or:()=>x,skipToContent:()=>E,submit:()=>b});const n="Monitoramento",a="Documenta\xE7\xE3o",t="E-mail",i="Senha",r="Provedor",s="Redefinir o token de senha",c="Fun\xE7\xE3o",p="Claro",l="Escuro",m="Nome de usu\xE1rio",d="Usu\xE1rios",u="Ops! Algo deu errado. Por favor, tente novamente.",g="Limpar",x="OU",E="Pular para o conte\xFAdo",b="Enviar",f={Analytics:n,"Auth.components.Oops.text":"Sua conta foi suspensa.","Auth.components.Oops.text.admin":"Se isso foi um erro, por favor, contate seu administrador.","Auth.components.Oops.title":"Ops...","Auth.form.active.label":"Ativo","Auth.form.button.forgot-password":"Enviar e-mail","Auth.form.button.go-home":"VOLTAR PARA O IN\xCDCIO","Auth.form.button.login":"Entrar","Auth.form.button.login.providers.error":"N\xE3o foi poss\xEDvel conectar voc\xEA pelo provedor selecionado.","Auth.form.button.login.strapi":"Entrar com Strapi","Auth.form.button.password-recovery":"Recupera\xE7\xE3o de Senha","Auth.form.button.register":"Pronto para come\xE7ar","Auth.form.confirmPassword.label":"Confirma\xE7\xE3o de senha","Auth.form.currentPassword.label":"Senha atual","Auth.form.email.label":"E-mail","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"Sua conta foi bloqueada pelo administrador.","Auth.form.error.code.provide":"C\xF3digo incorreto fornecido.","Auth.form.error.confirmed":"O email da sua conta n\xE3o foi confirmado.","Auth.form.error.email.invalid":"Este email \xE9 inv\xE1lido.","Auth.form.error.email.provide":"Por favor, forne\xE7a seu nome de usu\xE1rio ou seu e-mail.","Auth.form.error.email.taken":"O email j\xE1 foi utilizado","Auth.form.error.invalid":"Identificador ou senha inv\xE1lida.","Auth.form.error.params.provide":"Params incorretos fornecidos.","Auth.form.error.password.format":"Sua senha n\xE3o pode conter o s\xEDmbolo` $ `mais de tr\xEAs vezes.","Auth.form.error.password.local":"Este usu\xE1rio nunca definiu uma senha local, por favor fa\xE7a o login atrav\xE9s do provedor usado durante a cria\xE7\xE3o da conta.","Auth.form.error.password.matching":"As senhas n\xE3o coincidem.","Auth.form.error.password.provide":"Por favor, forne\xE7a sua senha","Auth.form.error.ratelimit":"Muitas tentativas, tente novamente em um minuto.","Auth.form.error.user.not-exist":"Este e-mail n\xE3o existe.","Auth.form.error.username.taken":"Nome de usu\xE1rio j\xE1 foi obtido","Auth.form.firstname.label":"Primeiro nome","Auth.form.firstname.placeholder":"ex: Kai","Auth.form.forgot-password.email.label":"Digite seu email","Auth.form.forgot-password.email.label.success":"E-mail enviado com sucesso para","Auth.form.lastname.label":"\xDAltimo nome","Auth.form.lastname.placeholder":"ex: Doe","Auth.form.password.hide-password":"Esconder senha","Auth.form.password.hint":"A senha deve conter pelo menos 8 caracteres, 1 letra mai\xFAscula, 1 letra min\xFAscula, e 1 n\xFAmero","Auth.form.password.show-password":"Mostrar senha","Auth.form.register.news.label":"Mantenha-me atualizado sobre os novos recursos e as pr\xF3ximas melhorias (ao fazer isso, voc\xEA aceita os {terms} e a {policy}).","Auth.form.register.subtitle":"Suas credenciais s\xE3o utilizadas somente para autenticar voc\xEA ao painel de admin. Todos os dados ser\xE3o salvos na sua pr\xF3pria base de dados.","Auth.form.rememberMe.label":"Lembre-se de mim","Auth.form.username.label":"Nome de usu\xE1rio","Auth.form.username.placeholder":"Kai Doe","Auth.form.welcome.subtitle":"Entrar na sua conta do Strapi","Auth.form.welcome.title":"Bem-vindo(a)!","Auth.link.forgot-password":"Esqueceu sua senha?","Auth.link.ready":"Pronto para logar?","Auth.link.signin":"Entrar","Auth.link.signin.account":"J\xE1 tem uma conta?","Auth.login.sso.divider":"Ou entre com","Auth.login.sso.loading":"Carregando provedores...","Auth.login.sso.subtitle":"Entre na sua conta com SSO","Auth.privacy-policy-agreement.policy":"pol\xEDtica de privacidade","Auth.privacy-policy-agreement.terms":"termos","Auth.reset-password.title":"Redefinir senha","Content Manager":"Gerenciador de conte\xFAdo","Content Type Builder":"Criador de Tipos de Conte\xFAdo",Documentation:a,Email:t,"Files Upload":"Enviar arquivos","HomePage.helmet.title":"Pagina inicial","HomePage.roadmap":"Veja nosso roadmap","HomePage.welcome.congrats":"Parab\xE9ns!","HomePage.welcome.congrats.content":"Voc\xEA est\xE1 logado como o primeiro administrador. Para descobrir os recursos avan\xE7ados fornecidos pelo Strapi,","HomePage.welcome.congrats.content.bold":"n\xF3s recomendados que voc\xEA crie o seu primeiro Tipo de Conte\xFAdo.","Media Library":"Biblioteca de M\xEDdia","New entry":"Novo registro",Password:i,Provider:r,ResetPasswordToken:s,Role:c,"Roles & Permissions":"Fun\xE7\xF5es e Permiss\xF5es","Roles.ListPage.notification.delete-all-not-allowed":"Algumas fun\xE7\xF5es n\xE3o puderam ser removidos por estarem associadas a alguns usu\xE1rios","Roles.ListPage.notification.delete-not-allowed":"A fun\xE7\xE3o n\xE3o pode ser removida se ainda estiver associada a algum usu\xE1rio","Roles.RoleRow.select-all":"Selecione {name} para a\xE7\xF5es em massa","Roles.RoleRow.user-count":"{number, plural, =0 {#  user} um {#  user} outros {# users}}","Roles.components.List.empty.withSearch":"N\xE3o existe uma fun\xE7\xE3o correspondente \xE0 busca ({search})...","Settings.PageTitle":"Configura\xE7\xF5es - {name}","Settings.apiTokens.addFirstToken":"Adicione sua primeira chave de API","Settings.apiTokens.addNewToken":"Adicionar nova chave de API","Settings.tokens.copy.editMessage":"Por motivos de seguran\xE7a, voc\xEA s\xF3 poder\xE1 ver sua chave uma \xFAnica vez.","Settings.tokens.copy.editTitle":"Essa chave n\xE3o est\xE1 mais acess\xEDvel.","Settings.tokens.copy.lastWarning":"Certifique-se de copiar esta chave, pois n\xE3o ser\xE1 poss\xEDvel v\xEA-la novamente!","Settings.apiTokens.create":"Adicionar","Settings.apiTokens.description":"lista de chaves geradas para consumo da API","Settings.apiTokens.emptyStateLayout":"Voc\xEA n\xE3o tem nada aqui ainda...","Settings.tokens.notification.copied":"Chave copiada pra \xE1rea de transfer\xEAncia.","Settings.apiTokens.title":"Chaves de API","Settings.tokens.types.full-access":"Acesso total","Settings.tokens.types.read-only":"Somente leitura","Settings.application.description":"Informa\xE7\xF5es globais do painel administrativo","Settings.application.edition-title":"plano atual","Settings.application.get-help":"Ajuda","Settings.application.link-pricing":"Ver todos os pre\xE7os","Settings.application.link-upgrade":"Atualize seu painel administrativo","Settings.application.node-version":"vers\xE3o do node","Settings.application.strapi-version":"vers\xE3o do strapi","Settings.application.strapiVersion":"vers\xE3o do strapi","Settings.application.title":"Vis\xE3o geral","Settings.application.customization":"Customiza\xE7\xE3o","Settings.application.customization.carousel.title":"Logo","Settings.application.customization.carousel.change-action":"Alterar logotipo","Settings.application.customization.carousel.reset-action":"Redefinir logotipo","Settings.application.customization.carousel-slide.label":"Slide do logotipo","Settings.application.customization.carousel-hint":"Alterar o logotipo do painel de administra\xE7\xE3o (Dimens\xE3o m\xE1xima: {dimension}x{dimension}, Tamanho m\xE1x.: {size}KB)","Settings.application.customization.modal.cancel":"Cancelar","Settings.application.customization.modal.upload":"Enviar","Settings.application.customization.modal.tab.label":"Como voc\xEA deseja enviar seus arquivos?","Settings.application.customization.modal.upload.from-computer":"Do computador","Settings.application.customization.modal.upload.file-validation":"Dimens\xE3o m\xE1xima: {dimension}x{dimension}, Tamanho m\xE1ximo: {size}KB","Settings.application.customization.modal.upload.error-format":"Formato incorreto (formatos aceitos: jpeg, jpg, png, svg).","Settings.application.customization.modal.upload.error-size":"O arquivo enviado \xE9 muito grande (dimens\xE3o m\xE1xima: {dimension}x{dimension}, tamanho m\xE1ximo do arquivo: {size}KB)","Settings.application.customization.modal.upload.error-network":"Erro ao enviar o arquivo","Settings.application.customization.modal.upload.cta.browse":"Procurar arquivo","Settings.application.customization.modal.upload.drag-drop":"Arraste e solte o arquivo aqui ou","Settings.application.customization.modal.upload.from-url":"Da URL","Settings.application.customization.modal.upload.from-url.input-label":"URL","Settings.application.customization.modal.upload.next":"Pr\xF3ximo","Settings.application.customization.modal.pending":"Logotipo pendente","Settings.application.customization.modal.pending.choose-another":"Escolha outro logotipo","Settings.application.customization.modal.pending.title":"Logotipo pronto para ser enviado","Settings.application.customization.modal.pending.subtitle":"Gerencie o logotipo escolhido antes de fazer o upload","Settings.application.customization.modal.pending.upload":"Enviar","Settings.application.customization.modal.pending.card-badge":"imagem","Settings.error":"Erro","Settings.global":"Configura\xE7\xF5es Globais","Settings.permissions":"Painel administrativo","Settings.permissions.category":"Configura\xE7\xF5es de permiss\xE3o da categoria {category}","Settings.permissions.category.plugins":"Configura\xE7\xF5es de permiss\xE3o da extens\xE3o {category}","Settings.permissions.conditions.anytime":"A qualquer hora","Settings.permissions.conditions.apply":"Aplicar","Settings.permissions.conditions.can":"Pode","Settings.permissions.conditions.conditions":"Definir condi\xE7\xF5es","Settings.permissions.conditions.links":"Links","Settings.permissions.conditions.no-actions":"Voc\xEA precisa selecionar a\xE7\xF5es (criar, ler, editar, ...) antes de definir uma condi\xE7\xE3o.","Settings.permissions.conditions.none-selected":"A qualquer hora","Settings.permissions.conditions.or":"OU","Settings.permissions.conditions.when":"Quando","Settings.permissions.select-all-by-permission":"Selecionar todas as permiss\xF5es de {label}","Settings.permissions.select-by-permission":"Selecionar permiss\xE3o de {label}","Settings.permissions.users.create":"Convidar novo usu\xE1rio","Settings.permissions.users.email":"E-mail","Settings.permissions.users.firstname":"Primeiro nome","Settings.permissions.users.lastname":"\xDAltimo nome","Settings.permissions.users.form.sso":"Conectar com SSO","Settings.permissions.users.form.sso.description":"Quando ativado, usu\xE1rios podem se conectar com SSO","Settings.permissions.users.listview.header.subtitle":"Todos os usu\xE1rios com acesso ao painel administrativo","Settings.permissions.users.tabs.label":"Permiss\xF5es de Abas","Settings.profile.form.notify.data.loaded":"Os dados do seu pefil foram carregados","Settings.profile.form.section.experience.clear.select":"Limpar a linguagem da interface selecionada","Settings.profile.form.section.experience.here":"aqui","Settings.profile.form.section.experience.interfaceLanguage":"Linguagem da interface","Settings.profile.form.section.experience.interfaceLanguage.hint":"Somente sua interface ser\xE1 exibida com a linguagem selecionada.","Settings.profile.form.section.experience.interfaceLanguageHelp":"A nova linguagem seleciona s\xF3 ser\xE1 exibida a voc\xEA. Por favor, leia essa {documentation} para disponibilizar outras linguagens para sua equipe.","Settings.profile.form.section.experience.mode.label":"Modo de interface","Settings.profile.form.section.experience.mode.hint":"Selecione o modo de interface que voc\xEA deseja usar","Settings.profile.form.section.experience.mode.option-label":"modo {name}",light:p,dark:l,"Settings.profile.form.section.experience.title":"Experi\xEAncia","Settings.profile.form.section.helmet.title":"Perfil do usu\xE1rio","Settings.profile.form.section.profile.page.title":"P\xE1gina do perfil","Settings.roles.create.description":"Defina as permiss\xF5es desse papel","Settings.roles.create.title":"Criar uma fun\xE7\xE3o","Settings.roles.created":"Fun\xE7\xE3o criada","Settings.roles.edit.title":"Editar uma fun\xE7\xE3o","Settings.roles.form.button.users-with-role":"{number, plural, =0 {# usu\xE1rios} um {# usu\xE1rio} outros {# usu\xE1rios}} com esse papel","Settings.roles.form.created":"Criado","Settings.roles.form.description":"Nome e descri\xE7\xE3o da fun\xE7\xE3o","Settings.roles.form.permission.property-label":"Permiss\xF5es de {label}","Settings.roles.form.permissions.attributesPermissions":"Permiss\xF5es de campos","Settings.roles.form.permissions.create":"Criar","Settings.roles.form.permissions.delete":"Remover","Settings.roles.form.permissions.publish":"Publicar","Settings.roles.form.permissions.read":"Ler","Settings.roles.form.permissions.update":"Editar","Settings.roles.list.button.add":"Adicionar novo papel","Settings.roles.list.description":"lista de pap\xE9is","Settings.roles.title.singular":"fun\xE7\xE3o","Settings.sso.description":"Configura\xE7\xF5es da funcionalidade de Single Sign-On.","Settings.sso.form.defaultRole.description":"Isso ir\xE1 atribuir o novo usu\xE1rio ao papel selecionado","Settings.sso.form.defaultRole.description-not-allowed":"Voc\xEA precisa de permiss\xE3o para ver os pap\xE9is administrativos","Settings.sso.form.defaultRole.label":"Fun\xE7\xE3o padr\xE3o","Settings.sso.form.registration.description":"Criar novo usu\xE1rio ao logar por SSO e nenhuma conta existe previamente","Settings.sso.form.registration.label":"Inscri\xE7\xE3o autom\xE1tica","Settings.sso.title":"Single Sign-On","Settings.webhooks.create":"Criar um webhook","Settings.webhooks.create.header":"Adicionar um novo header","Settings.webhooks.created":"Webhook criado","Settings.webhooks.event.publish-tooltip":"Esse evento s\xF3 est\xE1 dispon\xEDvel para conte\xFAdos com a funcionalidade de Rascunho/Publicado habilitada","Settings.webhooks.events.create":"Criar","Settings.webhooks.events.update":"Atualizar","Settings.webhooks.form.events":"Eventos","Settings.webhooks.form.headers":"Headers","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"Remover header {number}","Settings.webhooks.key":"Chave","Settings.webhooks.list.button.add":"Adicionar novo webhook","Settings.webhooks.list.description":"Receba notifica\xE7\xF5es de mudan\xE7as POST.","Settings.webhooks.list.empty.description":"Adicione o seu primeiro a essa lista.","Settings.webhooks.list.empty.link":"Veja nossa documenta\xE7\xE3o","Settings.webhooks.list.empty.title":"Nenhum webhook adicionado ainda","Settings.webhooks.list.th.actions":"a\xE7\xF5es","Settings.webhooks.list.th.status":"estado","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# elemento} other {# elementos}} selecionado(s)","Settings.webhooks.trigger":"Disparo","Settings.webhooks.trigger.cancel":"Cancelar disparo","Settings.webhooks.trigger.pending":"Pendente\u2026","Settings.webhooks.trigger.save":"Por favor salve para disparar","Settings.webhooks.trigger.success":"Sucesso!","Settings.webhooks.trigger.success.label":"Disparo realizado com sucesso","Settings.webhooks.trigger.test":"Disparo de teste","Settings.webhooks.trigger.title":"Salvar antes do Disparo","Settings.webhooks.value":"Valor","Usecase.back-end":"Desenvolvedor Back-end","Usecase.button.skip":"Pular esta pergunta","Usecase.content-creator":"Criador de Conte\xFAdo","Usecase.front-end":"Desenvolvedor Front-end","Usecase.full-stack":"Desenvolvedor Full-stack","Usecase.input.work-type":"Qual \xE9 o seu tipo de trabalho?","Usecase.notification.success.project-created":"Projeto criado com sucesso","Usecase.other":"Outro","Usecase.title":"Conte-nos um pouco mais sobre voc\xEA",Username:m,Users:d,"Users & Permissions":"Usu\xE1rios & Permiss\xF5es","Users.components.List.empty":"N\xE3o h\xE1 usu\xE1rios...","Users.components.List.empty.withFilters":"N\xE3o h\xE1 usu\xE1rios correspondentes aos filtros...","Users.components.List.empty.withSearch":"N\xE3o h\xE1 usu\xE1rios correspondentes \xE0 busca ({search})...","admin.pages.MarketPlacePage.helmet":"Loja - Extens\xF5es","admin.pages.MarketPlacePage.offline.title":"Voc\xEA est\xE1 offline","admin.pages.MarketPlacePage.offline.subtitle":"Voc\xEA precisa estar online para ver as extens\xF5es","admin.pages.MarketPlacePage.plugins":"Extens\xF5es","admin.pages.MarketPlacePage.plugin.copy":"Copiar comando de instala\xE7\xE3o","admin.pages.MarketPlacePage.plugin.copy.success":"Comando de instala\xE7\xE3o pronto para ser colado em seu terminal","admin.pages.MarketPlacePage.plugin.info":"Saber mais","admin.pages.MarketPlacePage.plugin.info.label":"Saiba mais sobre {pluginName}","admin.pages.MarketPlacePage.plugin.info.text":"Saber mais","admin.pages.MarketPlacePage.plugin.installed":"Instalado","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"Criado por Strapi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"Extens\xE3o verificada por Strapi","admin.pages.MarketPlacePage.providers":"Fornecedores","admin.pages.MarketPlacePage.search.clear":"Limpar busca","admin.pages.MarketPlacePage.search.empty":'Sem resultado para "{target}"',"admin.pages.MarketPlacePage.search.placeholder":"Pesquisar","admin.pages.MarketPlacePage.submit.plugin.link":"Submeta sua extens\xE3o","admin.pages.MarketPlacePage.submit.provider.link":"Submeta provedor","admin.pages.MarketPlacePage.subtitle":"Fa\xE7a mais com o Strapi","admin.pages.MarketPlacePage.tab-group.label":"Plugins e Provedores para Strapi","admin.pages.MarketPlacePage.missingPlugin.title":"Falta-lhe um plugin?","admin.pages.MarketPlacePage.missingPlugin.description":"Diga-nos qual extens\xE3o voc\xEA est\xE1 procurando e informaremos nossos desenvolvedores de extens\xF5es da comunidade caso eles estejam em busca de inspira\xE7\xE3o!",anErrorOccurred:u,"app.component.CopyToClipboard.label":"Copiar pra \xE1rea de transfer\xEAncia","app.component.search.label":"Buscar por {target}","app.component.table.duplicate":"Duplicar {target}","app.component.table.edit":"Editar {target}","app.component.table.select.one-entry":"Selecionar {target}","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Leia as \xFAltimas not\xEDcias do Strapi e seu ecossistema.","app.components.BlockLink.code":"C\xF3digos de Exemplo","app.components.BlockLink.code.content":"Aprenda testando projetos desenvolvidos pela comunidade.","app.components.BlockLink.documentation.content":"Descubra os conceitos essenciais, guias e instru\xE7\xF5es.","app.components.BlockLink.tutorial":"Tutoriais","app.components.BlockLink.tutorial.content":"Siga o passo-a-passo para usar e customizar o Strapi.","app.components.Button.cancel":"Cancelar","app.components.Button.confirm":"Confirmar","app.components.Button.reset":"Resetar","app.components.ComingSoonPage.comingSoon":"Em breve","app.components.ConfirmDialog.title":"Confirma\xE7\xE3o","app.components.DownloadInfo.download":"Transfer\xEAncia em andamento...","app.components.DownloadInfo.text":"Isto poder\xE1 levar alguns minutos. Obrigado pela sua paci\xEAncia","app.components.EmptyAttributes.title":"Ainda n\xE3o existem campos","app.components.EmptyStateLayout.content-document":"Nenhum conte\xFAdo encontrado","app.components.EmptyStateLayout.content-permissions":"Voc\xEA n\xE3o tem permiss\xE3o para acessar esse conte\xFAdo","app.components.GuidedTour.CM.create.content":"<p>Crie e gerencie todo o conte\xFAdo aqui no Gerenciador de Conte\xFAdos.</p><p>Ex: Levando ainda mais o exemplo do site do Blog, pode-se escrever um artigo, salv\xE1-lo e public\xE1-lo como quiser.</p><p>\u{1F4A1} Dica r\xE1pida - N\xE3o se esque\xE7a de clicar em publicar no conte\xFAdo que voc\xEA criar.</p>","app.components.GuidedTour.CM.create.title":"\u26A1\uFE0F Crie seu primeiro conte\xFAdo","app.components.GuidedTour.CM.success.content":"<p>Incr\xEDvel, falta um \xFAltimo passo!</p><b>\u{1F680}  Ver conte\xFAdo em a\xE7\xE3o</b>","app.components.GuidedTour.CM.success.cta.title":"Teste a API","app.components.GuidedTour.CM.success.title":"Passo 2: Conclu\xEDdo \u2705","app.components.GuidedTour.CTB.create.content":"<p>Os tipos de cole\xE7\xE3o ajudam a gerenciar v\xE1rias entradas, os tipos \xFAnicos s\xE3o adequados para gerenciar apenas uma entrada.</p> <p>Ex: Para um site de blog, os artigos seriam um tipo de cole\xE7\xE3o, enquanto uma p\xE1gina inicial seria um tipo \xFAnico.</p>","app.components.GuidedTour.CTB.create.cta.title":"Criar um Tipo de Cole\xE7\xE3o","app.components.GuidedTour.CTB.create.title":"\u{1F9E0} Criar um primeiro tipo de cole\xE7\xE3o","app.components.GuidedTour.CTB.success.content":"<p>Bom trabalho!</p><b>\u26A1\uFE0F O que voc\xEA gostaria de compartilhar com o mundo?</b>","app.components.GuidedTour.CTB.success.title":"Passo 1: Conclu\xEDdo \u2705","app.components.GuidedTour.apiTokens.create.content":"<p>Gere um token de autentica\xE7\xE3o aqui e recupere o conte\xFAdo que voc\xEA acabou de criar.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"Gerar um token de API","app.components.GuidedTour.apiTokens.create.title":"\u{1F680} Visualizar conte\xFAdo em a\xE7\xE3o ","app.components.GuidedTour.apiTokens.success.content":"<p>Visualize o conte\xFAdo em a\xE7\xE3o fazendo uma solicita\xE7\xE3o HTTP:</p><ul><li><p>Para esta URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Com o cabe\xE7alho: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Para mais formas de interagir com o conte\xFAdo, consulte a <documentationLink>documenta\xE7\xE3o</documentationLink>.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Voltar para a p\xE1gina inicial","app.components.GuidedTour.apiTokens.success.title":"Passo 3: Conclu\xEDdo \u2705","app.components.GuidedTour.create-content":"Crie um conte\xFAdo","app.components.GuidedTour.home.CM.title":"\u26A1\uFE0F O que voc\xEA gostaria de compartilhar com o mundo?","app.components.GuidedTour.home.CTB.cta.title":"Ir para o Criador de tipos de conte\xFAdo","app.components.GuidedTour.home.CTB.title":"\u{1F9E0} Construir a estrutura de conte\xFAdo","app.components.GuidedTour.home.apiTokens.cta.title":"Testar a API","app.components.GuidedTour.skip":"Pular o tutorial","app.components.GuidedTour.title":"3 passos para come\xE7ar","app.components.HomePage.button.blog":"Veja mais no blog","app.components.HomePage.community":"Nossa comunidade na web","app.components.HomePage.community.content":"Converse com membros da equipe, colaboradores e desenvolvedores em diversos canais.","app.components.HomePage.create":"Crie seu primeiro Tipo de Conte\xFAdo","app.components.HomePage.roadmap":"Veja nosso roadmap","app.components.HomePage.welcome":"Bem-vindo(a) a bordo \u{1F44B}","app.components.HomePage.welcome.again":"Bem-vindo(a) \u{1F44B}","app.components.HomePage.welcomeBlock.content":"Estamos muito felizes em t\xEA-lo(a) como um membro da nossa comunidade. Estamos sempre querendo saber sua opini\xE3o, ent\xE3o fique a vontade em nos enviar uma mensagem em privado no ","app.components.HomePage.welcomeBlock.content.again":"Desejamos que voc\xEA esteja progredindo em seu projeto... Fique por dentro das \xFAltimas novidades sobre o Strapi. Estamos sempre dando o nosso melhor para melhorar o produto sempre baseando-se em sua opini\xE3o.","app.components.HomePage.welcomeBlock.content.issues":"problemas.","app.components.HomePage.welcomeBlock.content.raise":" ou aponte ","app.components.ImgPreview.hint":"Arraste e solte o seu arquivo sobre a \xE1rea ou {browse} um arquivo para fazer o envio","app.components.ImgPreview.hint.browse":"selecione","app.components.InputFile.newFile":"Adicionar um novo arquivo","app.components.InputFileDetails.open":"Abrir numa nova aba","app.components.InputFileDetails.originalName":"Nome original:","app.components.InputFileDetails.remove":"Remova este arquivo","app.components.InputFileDetails.size":"Tamanho:","app.components.InstallPluginPage.Download.description":"Pode demorar alguns segundos para baixar e instalar a extens\xE3o.","app.components.InstallPluginPage.Download.title":"Baixando...","app.components.InstallPluginPage.description":"Estenda seu aplicativo sem esfor\xE7o.","app.components.LeftMenu.collapse":"Recolher barra de navega\xE7\xE3o","app.components.LeftMenu.expand":"Expandir barra de navega\xE7\xE3o","app.components.LeftMenu.general":"Geral","app.components.LeftMenu.logout":"Sair","app.components.LeftMenu.logo.alt":"logo da aplica\xE7\xE3o","app.components.LeftMenu.plugins":"Extens\xF5es","app.components.LeftMenu.navbrand.title":"Painel do Strapi","app.components.LeftMenu.navbrand.workplace":"Local de trabalho","app.components.LeftMenuFooter.help":"Ajuda","app.components.LeftMenuFooter.poweredBy":"Mantido por ","app.components.LeftMenuLinkContainer.collectionTypes":"Tipos de cole\xE7\xE3o","app.components.LeftMenuLinkContainer.configuration":"Configura\xE7\xF5es","app.components.LeftMenuLinkContainer.general":"Geral","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Nenhuma extens\xE3o instalada ainda","app.components.LeftMenuLinkContainer.plugins":"Extens\xF5es","app.components.LeftMenuLinkContainer.singleTypes":"Tipos singulares","app.components.ListPluginsPage.deletePlugin.description":"Pode demorar alguns segundos para desinstalar a extens\xE3o.","app.components.ListPluginsPage.deletePlugin.title":"Desinstalando","app.components.ListPluginsPage.description":"Lista de extens\xF5es instaladas no projeto.","app.components.ListPluginsPage.helmet.title":"Lista de extens\xF5es","app.components.Logout.logout":"Sair","app.components.Logout.profile":"Perfil","app.components.MarketplaceBanner":"Descubra extens\xF5es da comunidade, e muito mais para dar in\xEDcio ao seu projeto, em Strapi Awesome.","app.components.MarketplaceBanner.image.alt":"um logo de foguete do strapi","app.components.MarketplaceBanner.link":"Ver agora","app.components.NotFoundPage.back":"Voltar \xE0 p\xE1gina inicial","app.components.NotFoundPage.description":"N\xE3o encontrado","app.components.Official":"Oficial","app.components.Onboarding.help.button":"Bot\xE3o de ajuda","app.components.Onboarding.label.completed":"% conclu\xEDdo","app.components.Onboarding.title":"V\xEDdeos de Introdu\xE7\xE3o","app.components.PluginCard.Button.label.download":"Baixar","app.components.PluginCard.Button.label.install":"J\xE1 instalado","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"O recurso autoReload precisa estar ativado. Por favor, inicie seu aplicativo com `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Eu compreendo!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Por motivos de seguran\xE7a, uma extens\xE3o s\xF3 pode ser baixada no ambiente de desenvolvimento.","app.components.PluginCard.PopUpWarning.install.impossible.title":"N\xE3o \xE9 poss\xEDvel baixar","app.components.PluginCard.compatible":"Compat\xEDvel com a sua aplica\xE7\xE3o","app.components.PluginCard.compatibleCommunity":"Compat\xEDvel com a comunidade","app.components.PluginCard.more-details":"Mais detalhes","app.components.ToggleCheckbox.off-label":"Desativado","app.components.ToggleCheckbox.on-label":"Ativado","app.components.Users.MagicLink.connect":"Copie e compartilhe esse link para dar acesso ao usu\xE1rio","app.components.Users.MagicLink.connect.sso":"Envie esse link para o usu\xE1rio. O primeiro login pode ser feito por um provedor de SSO","app.components.Users.ModalCreateBody.block-title.details":"Detalhes do usu\xE1rio","app.components.Users.ModalCreateBody.block-title.roles":"Pap\xE9is do usu\xE1rio","app.components.Users.ModalCreateBody.block-title.roles.description":"Um usu\xE1rio pode ter um ou mais pap\xE9is","app.components.Users.SortPicker.button-label":"Ordenar por","app.components.Users.SortPicker.sortby.email_asc":"E-mail (A a Z)","app.components.Users.SortPicker.sortby.email_desc":"E-mail (Z a A)","app.components.Users.SortPicker.sortby.firstname_asc":"Primeiro nome (A a Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Primeiro nome (Z a A)","app.components.Users.SortPicker.sortby.lastname_asc":"\xDAltimo nome (A a Z)","app.components.Users.SortPicker.sortby.lastname_desc":"\xDAltimo nome (Z a A)","app.components.Users.SortPicker.sortby.username_asc":"Nome de usu\xE1rio (A a Z)","app.components.Users.SortPicker.sortby.username_desc":"Nome de usu\xE1rio (Z a A)","app.components.listPlugins.button":"Adicionar nova Extens\xE3o","app.components.listPlugins.title.none":"Nenhuma extens\xE3o instalada","app.components.listPluginsPage.deletePlugin.error":"Ocorreu um erro ao desinstalar extens\xE3o","app.containers.App.notification.error.init":"Ocorreu um erro ao solicitar a API","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Se voc\xEA n\xE3o receber esse link, por favor contate seu administrador.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"Pode levar alguns minutos para receber seu link de recupera\xE7\xE3o de senha.","app.containers.AuthPage.ForgotPasswordSuccess.title":"E-mail enviado","app.containers.Users.EditPage.form.active.label":"Ativo","app.containers.Users.EditPage.header.label":"Editar {name}","app.containers.Users.EditPage.header.label-loading":"Editar usu\xE1rio","app.containers.Users.EditPage.roles-bloc-title":"Pap\xE9is atribu\xEDdos","app.containers.Users.ModalForm.footer.button-success":"Convidar usu\xE1rio","app.links.configure-view":"Configurar a visualiza\xE7\xE3o","app.page.not.found":"Ops! N\xE3o conseguimos encontrar a p\xE1gina que voc\xEA est\xE1 procurando...","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Adicionar filtro","app.utils.close-label":"Fechar","app.utils.defaultMessage":" ","app.utils.duplicate":"Duplicar","app.utils.edit":"Editar","app.utils.errors.file-too-big.message":"O arquivo \xE9 muito grande","app.utils.filter-value":"Filtrar valor","app.utils.filters":"Filtros","app.utils.notify.data-loaded":"{target} foi carregado","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Publicar","app.utils.select-all":"Selecionar todos","app.utils.select-field":"Selecionar campo","app.utils.select-filter":"Selecionar filtro","app.utils.unpublish":"Despublicar",clearLabel:g,"coming.soon":"Esse conte\xFAdo est\xE1 sendo constru\xEDdo e voltar\xE1 dentro de algumas semanas!","component.Input.error.validation.integer":"O valor deve ser um inteiro","components.AutoReloadBlocker.description":"Execute o Strapi com um dos seguintes comandos:","components.AutoReloadBlocker.header":"Auto recarregamento \xE9 necess\xE1rio para esta extens\xE3o.","components.ErrorBoundary.title":"Algo deu errado...","components.FilterOptions.FILTER_TYPES.$contains":"cont\xE9m","components.FilterOptions.FILTER_TYPES.$containsi":"cont\xE9m (n\xE3o diferencia mai\xFAsculas de min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$endsWith":"termina com","components.FilterOptions.FILTER_TYPES.$endsWithi":"termina com (n\xE3o diferencia mai\xFAsculas de min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$eq":"\xE9 igual a","components.FilterOptions.FILTER_TYPES.$eqi":"\xE9 igual a (n\xE3o diferencia mai\xFAsculas de min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$gt":"\xE9 maior que","components.FilterOptions.FILTER_TYPES.$gte":"\xE9 maior que ou igual a","components.FilterOptions.FILTER_TYPES.$lt":"\xE9 menor que","components.FilterOptions.FILTER_TYPES.$lte":"\xE9 menor que ou igual a","components.FilterOptions.FILTER_TYPES.$ne":"\xE9 diferente de","components.FilterOptions.FILTER_TYPES.$nei":"\xE9 diferente de (n\xE3o diferencia mai\xFAsculas de min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$notContains":"n\xE3o cont\xE9m","components.FilterOptions.FILTER_TYPES.$notContainsi":"n\xE3o cont\xE9m (n\xE3o diferencia mai\xFAsculas de min\xFAsculas)","components.FilterOptions.FILTER_TYPES.$notNull":"n\xE3o \xE9 vazio","components.FilterOptions.FILTER_TYPES.$null":"\xE9 vazio","components.FilterOptions.FILTER_TYPES.$startsWith":"come\xE7a com","components.FilterOptions.FILTER_TYPES.$startsWithi":"come\xE7a com (n\xE3o diferencia mai\xFAsculas de min\xFAsculas)","components.Input.error.attribute.key.taken":"Este valor j\xE1 existe","components.Input.error.attribute.sameKeyAndName":"N\xE3o pode ser igual","components.Input.error.attribute.taken":"O nome deste campo j\xE1 existe","components.Input.error.contain.lowercase":"A senha deve conter pelo menos uma letra min\xFAscula","components.Input.error.contain.number":"A senha deve conter pelo menos um n\xFAmero","components.Input.error.contain.uppercase":"A senha deve conter pelo menos uma letra mai\xFAscula","components.Input.error.contentTypeName.taken":"Este tipo de conte\xFAdo j\xE1 existe","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"As senhas n\xE3o conferem","components.Input.error.validation.email":"Isto n\xE3o \xE9 um endere\xE7o de e-mail","components.Input.error.validation.json":"Isto n\xE3o corresponde ao formato JSON","components.Input.error.validation.lowercase":"O valor deve ser um texto com letras min\xFAsculas","components.Input.error.validation.max":"O valor \xE9 muito alto {max}.","components.Input.error.validation.maxLength":"O valor \xE9 muito longo {max}.","components.Input.error.validation.min":"O valor \xE9 muito baixo {min}.","components.Input.error.validation.minLength":"O valor \xE9 muito curto {min}.","components.Input.error.validation.minSupMax":"N\xE3o pode ser superior","components.Input.error.validation.regex":"O valor n\xE3o corresponde ao regex.","components.Input.error.validation.required":"Este valor \xE9 obrigat\xF3rio.","components.Input.error.validation.unique":"Este valor j\xE1 foi usado.","components.InputSelect.option.placeholder":"Escolha aqui","components.ListRow.empty":"N\xE3o existe nenhum registro para ser exibido","components.NotAllowedInput.text":"Sem permiss\xF5es para ver esse campo","components.OverlayBlocker.description":"Voc\xEA est\xE1 usando um recurso que precisa que o servidor seja reiniciado. Por favor, aguarde at\xE9 que o servidor esteja totalmente reiniciado.","components.OverlayBlocker.description.serverError":"O servidor deve ter sido reiniciado. Verifique seus registros no terminal.","components.OverlayBlocker.title":"Aguardando pela reinicializa\xE7\xE3o...","components.OverlayBlocker.title.serverError":"A reinicializa\xE7\xE3o levou mais tempo que o esperado","components.PageFooter.select":"registros por p\xE1gina","components.ProductionBlocker.description":"Por motivos de seguran\xE7a, temos que desativar esta extens\xE3o em outros ambientes.","components.ProductionBlocker.header":"Esta extens\xE3o est\xE1 dispon\xEDvel apenas em modo de desenvolvimento.","components.Search.placeholder":"Buscar...","components.TableHeader.sort":"Ordenar por {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Modo de edi\xE7\xE3o","components.Wysiwyg.ToggleMode.preview-mode":"Modo de visualiza\xE7\xE3o","components.Wysiwyg.collapse":"Fechar","components.Wysiwyg.selectOptions.H1":"T\xEDtulo H1","components.Wysiwyg.selectOptions.H2":"T\xEDtulo H2","components.Wysiwyg.selectOptions.H3":"T\xEDtulo H3","components.Wysiwyg.selectOptions.H4":"T\xEDtulo H4","components.Wysiwyg.selectOptions.H5":"T\xEDtulo H5","components.Wysiwyg.selectOptions.H6":"T\xEDtulo H6","components.Wysiwyg.selectOptions.title":"Adicionar um t\xEDtulo","components.WysiwygBottomControls.charactersIndicators":"caracteres","components.WysiwygBottomControls.fullscreen":"Expandir","components.WysiwygBottomControls.uploadFiles":"Arraste e solte arquivos, cole na \xE1rea de transfer\xEAncia ou {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"Selecione-os","components.pagination.go-to":"Ir pra p\xE1gina {page}","components.pagination.go-to-next":"Ir pra pr\xF3xima p\xE1gina","components.pagination.go-to-previous":"Ir pra p\xE1gina anterior","components.pagination.remaining-links":"E {number} outros links","components.popUpWarning.button.cancel":"N\xE3o, cancelar","components.popUpWarning.button.confirm":"Sim, confirmar","components.popUpWarning.message":"Tem certeza que deseja remover isso?","components.popUpWarning.title":"Por favor, confirme","content-manager.App.schemas.data-loaded":"Os esquemas foram carregados com sucesso","content-manager.ListViewTable.relation-loaded":"Os relacionamentos foram carregados","content-manager.ListViewTable.relation-loading":"As rela\xE7\xF5es est\xE3o carregando","content-manager.ListViewTable.relation-more":"Esta rela\xE7\xE3o cont\xE9m mais entidades do que as exibidas","content-manager.EditRelations.title":"Dados relacionais","content-manager.HeaderLayout.button.label-add-entry":"Criar novo registro","content-manager.api.id":"API ID","content-manager.components.AddFilterCTA.add":"Filtros","content-manager.components.AddFilterCTA.hide":"Filtros","content-manager.components.DragHandle-label":"Arrastar","content-manager.components.DraggableAttr.edit":"Clique para editar","content-manager.components.DraggableCard.delete.field":"Remover {item}","content-manager.components.DraggableCard.edit.field":"Editar {item}","content-manager.components.DraggableCard.move.field":"Mover {item}","content-manager.components.ListViewTable.row-line":"item {number}","content-manager.components.DynamicZone.ComponentPicker-label":"Selecione um componente","content-manager.components.DynamicZone.add-component":"Adicionar componente a {componentName}","content-manager.components.DynamicZone.delete-label":"Remover {name}","content-manager.components.DynamicZone.error-message":"O componente cont\xE9m erro(s)","content-manager.components.DynamicZone.missing-components":"H\xE1 {number, plural, =0 {# componentes faltando} one {# componente faltando} other {# componentes faltando}}","content-manager.components.DynamicZone.move-down-label":"Mover pra cima","content-manager.components.DynamicZone.move-up-label":"Mover pra baixo","content-manager.components.DynamicZone.pick-compo":"Selecione um componente","content-manager.components.DynamicZone.required":"Um componente \xE9 necess\xE1rio","content-manager.components.EmptyAttributesBlock.button":"Ir para p\xE1gina de configura\xE7\xF5es","content-manager.components.EmptyAttributesBlock.description":"Voc\xEA pode alterar suas configura\xE7\xF5es","content-manager.components.FieldItem.linkToComponentLayout":"Definir layout do componente","content-manager.components.FieldSelect.label":"Adicionar um campo","content-manager.components.FilterOptions.button.apply":"Aplicar","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Aplicar","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Limpar tudo","content-manager.components.FiltersPickWrapper.PluginHeader.description":"Definir as condi\xE7\xF5es a serem aplicadas para filtrar os registros","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Filtros","content-manager.components.FiltersPickWrapper.hide":"Esconder","content-manager.components.LeftMenu.Search.label":"Procurar um tipo de conte\xFAdo","content-manager.components.LeftMenu.collection-types":"Tipos de Cole\xE7\xE3o","content-manager.components.LeftMenu.single-types":"Tipos Singulares","content-manager.components.LimitSelect.itemsPerPage":"Registros por p\xE1gina","content-manager.components.NotAllowedInput.text":"Sem permiss\xE3o para ver esse campo","content-manager.components.RepeatableComponent.error-message":"Um ou mais componentes cont\xEAm erros","content-manager.components.Search.placeholder":"Buscar registro...","content-manager.components.Select.draft-info-title":"Estado: Rascunho","content-manager.components.Select.publish-info-title":"Estado: Publicado","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Customizar visualiza\xE7\xE3o de edi\xE7\xE3o.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Customizar visualiza\xE7\xE3o de lista.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Customizar visualiza\xE7\xE3o - {name}","content-manager.components.TableDelete.delete":"Remover tudo","content-manager.components.TableDelete.deleteSelected":"Remover selecionado","content-manager.components.TableDelete.label":"{number, plural, one {# registro} other {# registros}} selecionados","content-manager.components.TableEmpty.withFilters":"Nenhum {contentType} com os filtros aplicados...","content-manager.components.TableEmpty.withSearch":"Nenhum {contentType} encontrado na pesquisa ({search})...","content-manager.components.TableEmpty.withoutFilter":"Nenhum {contentType}...","content-manager.components.empty-repeatable":"Nenhum registro ainda. Clique no bot\xE3o abaixo para adicionar um.","content-manager.components.notification.info.maximum-requirement":"Voc\xEA atingiu o n\xFAmero m\xE1ximo de campos","content-manager.components.notification.info.minimum-requirement":"Um campo foi criado para atender aos requisitos m\xEDnimos","content-manager.components.repeatable.reorder.error":"Um erro ocorreu ao reordenar o campo do seu componente. Por favor, tente novamente.","content-manager.components.reset-entry":"Reiniciar","content-manager.components.uid.apply":"aplicar","content-manager.components.uid.available":"Dispon\xEDvel","content-manager.components.uid.regenerate":"Regerar","content-manager.components.uid.suggested":"sugerido","content-manager.components.uid.unavailable":"Indispon\xEDvel","content-manager.containers.Edit.Link.Layout":"Configurar o layout","content-manager.containers.Edit.Link.Model":"Editar o tipo de cole\xE7\xE3o","content-manager.containers.Edit.addAnItem":"Adicione um item...","content-manager.containers.Edit.clickToJump":"Clique para pular para o registro","content-manager.containers.Edit.delete":"Remover","content-manager.containers.Edit.delete-entry":"Remover este registro","content-manager.containers.Edit.editing":"Editando...","content-manager.containers.Edit.information":"Informa\xE7\xE3o","content-manager.containers.Edit.information.by":"Por","content-manager.containers.Edit.information.created":"Criado","content-manager.containers.Edit.information.draftVersion":"vers\xE3o rascunho","content-manager.containers.Edit.information.editing":"Editando","content-manager.containers.Edit.information.lastUpdate":"\xDAltima atualiza\xE7\xE3o","content-manager.containers.Edit.information.publishedVersion":"vers\xE3o publicada","content-manager.containers.Edit.pluginHeader.title.new":"Criar um registro","content-manager.containers.Edit.reset":"Reiniciar","content-manager.containers.Edit.returnList":"Retornar \xE0 lista","content-manager.containers.Edit.seeDetails":"Detalhes","content-manager.containers.Edit.submit":"Salvar","content-manager.containers.EditSettingsView.modal-form.edit-field":"Editar o campo","content-manager.containers.EditView.add.new-entry":"Adicionar um registro","content-manager.containers.EditView.notification.errors":"O formul\xE1rio cont\xE9m erros","content-manager.containers.Home.introduction":"Para editar seus registros, acesse o link espec\xEDfico no menu \xE0 esquerda. Esta extens\xE3o n\xE3o permite editar configura\xE7\xF5es, ainda est\xE1 em desenvolvimento.","content-manager.containers.Home.pluginHeaderDescription":"Gerencie seus registros atrav\xE9s de uma interface poderosa e elegante.","content-manager.containers.Home.pluginHeaderTitle":"Gest\xE3o de conte\xFAdos","content-manager.containers.List.draft":"Rascunho","content-manager.containers.List.errorFetchRecords":"Erro","content-manager.containers.List.published":"Publicado","content-manager.containers.ListPage.displayedFields":"Campos exibidos","content-manager.containers.ListPage.items":"{number, plural, =0 {itens} one {item} other {itens}}","content-manager.containers.ListPage.table-headers.publishedAt":"Estado","content-manager.containers.ListSettingsView.modal-form.edit-label":"Editar {fieldName}","content-manager.containers.SettingPage.add.field":"Criar outro campo","content-manager.containers.SettingPage.attributes":"Atributos","content-manager.containers.SettingPage.attributes.description":"Define a ordem dos atributos","content-manager.containers.SettingPage.editSettings.description":"Arraste e solte os campos para construir o layout","content-manager.containers.SettingPage.editSettings.entry.title":"T\xEDtulo do registro","content-manager.containers.SettingPage.editSettings.entry.title.description":"Defina o campo exibido do registro","content-manager.containers.SettingPage.editSettings.relation-field.description":"Defina o campo exibido nas visualiza\xE7\xF5es de edi\xE7\xE3o e lista","content-manager.containers.SettingPage.editSettings.title":"Editar (configura\xE7\xF5es)","content-manager.containers.SettingPage.layout":"Layout","content-manager.containers.SettingPage.listSettings.description":"Configurar as op\xE7\xF5es desse tipo de cole\xE7\xE3o","content-manager.containers.SettingPage.listSettings.title":"Lista (configura\xE7\xF5es)","content-manager.containers.SettingPage.pluginHeaderDescription":"Configurar as op\xE7\xF5es espec\xEDficas desse tipo de cole\xE7\xE3o","content-manager.containers.SettingPage.settings":"Configura\xE7\xF5es","content-manager.containers.SettingPage.view":"Visualiza\xE7\xE3o","content-manager.containers.SettingViewModel.pluginHeader.title":"Gest\xE3o de Conte\xFAdo - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Defina as configura\xE7\xF5es espec\xEDficas","content-manager.containers.SettingsPage.Block.contentType.title":"Tipos de Cole\xE7\xE3o","content-manager.containers.SettingsPage.Block.generalSettings.description":"Configurar op\xE7\xF5es padr\xE3o para seus Tipos de Cole\xE7\xE3o","content-manager.containers.SettingsPage.Block.generalSettings.title":"Geral","content-manager.containers.SettingsPage.pluginHeaderDescription":"Configurar op\xE7\xF5es para todos os seus Tipos de Cole\xE7\xE3o e Grupos","content-manager.containers.SettingsView.list.subtitle":"Configurar layout e exibi\xE7\xE3o dos seus Tipos de Cole\xE7\xE3o e Grupos","content-manager.containers.SettingsView.list.title":"Exibir configura\xE7\xF5es","content-manager.edit-settings-view.link-to-ctb.components":"Editar o componente","content-manager.edit-settings-view.link-to-ctb.content-types":"Editar o tipo de conte\xFAdo","content-manager.emptyAttributes.button":"Ir para o criador de Tipo de Cole\xE7\xE3o","content-manager.emptyAttributes.description":"Adicione o primeiro campo ao seu Tipo de Cole\xE7\xE3o","content-manager.emptyAttributes.title":"Nenhum campo","content-manager.error.attribute.key.taken":"Este valor j\xE1 existe","content-manager.error.attribute.sameKeyAndName":"N\xE3o pode ser igual","content-manager.error.attribute.taken":"O nome deste campo j\xE1 existe","content-manager.error.contentTypeName.taken":"Este nome j\xE1 existe","content-manager.error.model.fetch":"Ocorreu um erro durante a configura\xE7\xE3o dos modelos de busca.","content-manager.error.record.create":"Ocorreu um erro durante a cria\xE7\xE3o de registro.","content-manager.error.record.delete":"Ocorreu um erro durante a remo\xE7\xE3o do registro.","content-manager.error.record.fetch":"Ocorreu um erro durante o registro de busca.","content-manager.error.record.update":"Ocorreu um erro durante a atualiza\xE7\xE3o do registro.","content-manager.error.records.count":"Ocorreu um erro durante a contagem de registros da buscar.","content-manager.error.records.fetch":"Ocorreu um erro durante os registros de busca.","content-manager.error.schema.generation":"Ocorreu um erro durante a gera\xE7\xE3o dos esquemas.","content-manager.error.validation.json":"Isto n\xE3o corresponde com o formato JSON","content-manager.error.validation.max":"O valor \xE9 muito alto.","content-manager.error.validation.maxLength":"O valor \xE9 muito logo.","content-manager.error.validation.min":"O valor \xE9 muito baixo.","content-manager.error.validation.minLength":"O valor \xE9 muito curto.","content-manager.error.validation.minSupMax":"N\xE3o pode ser superior","content-manager.error.validation.regex":"Este valor n\xE3o corresponde ao regex.","content-manager.error.validation.required":"O valor deste registro \xE9 obrigat\xF3rio.","content-manager.form.Input.bulkActions":"Habilitar a\xE7\xF5es em lote","content-manager.form.Input.defaultSort":"Atributo de ordena\xE7\xE3o padr\xE3o","content-manager.form.Input.description":"Descri\xE7\xE3o","content-manager.form.Input.description.placeholder":"Nome exibido no perfil","content-manager.form.Input.editable":"Campo edit\xE1vel","content-manager.form.Input.filters":"Habilitar filtros","content-manager.form.Input.label":"R\xF3tulo","content-manager.form.Input.label.inputDescription":"Este valor substitui o r\xF3tulo apresentado no cabe\xE7alho da tabela","content-manager.form.Input.pageEntries":"Entradas por p\xE1gina","content-manager.form.Input.pageEntries.inputDescription":"Nota: Voce pode redefinir esse valor na p\xE1gina de configura\xE7\xE3o dos Tipos de Cole\xE7\xE3o.","content-manager.form.Input.placeholder":"Placeholder","content-manager.form.Input.placeholder.placeholder":"Meu valor incr\xEDvel","content-manager.form.Input.search":"Habilitar busca","content-manager.form.Input.search.field":"Habilitar busca neste campo","content-manager.form.Input.sort.field":"Habilitar ordena\xE7\xE3o neste campo","content-manager.form.Input.sort.order":"Ordena\xE7\xE3o padr\xE3o","content-manager.form.Input.wysiwyg":"Mostrar como WYSIWYG","content-manager.global.displayedFields":"Campos exibidos","content-manager.groups":"Grupos","content-manager.groups.numbered":"Grupos ({number})","content-manager.header.name":"Conte\xFAdo","content-manager.link-to-ctb":"Editar o modelo","content-manager.models":"Tipos de Cole\xE7\xE3o","content-manager.models.numbered":"Tipos de Cole\xE7\xE3o ({number})","content-manager.notification.error.displayedFields":"Voc\xEA precisa ao menos um campo exibido","content-manager.notification.error.relationship.fetch":"Ocorreu um erro durante a busca do relacionamento.","content-manager.notification.info.SettingPage.disableSort":"Voc\xEA precisa de um atributo com permiss\xE3o de ordena\xE7\xE3o","content-manager.notification.info.minimumFields":"Voc\xEA precisa ter pelo menos um campo exibido","content-manager.notification.upload.error":"Ocorreu um erro ao fazer upload dos seus arquivos","content-manager.pageNotFound":"P\xE1gina n\xE3o encontrada","content-manager.pages.ListView.header-subtitle":"{number, plural, =0 {# registros encontrados} one {# registro encontrado} other {# registros encontrados}}","content-manager.pages.NoContentType.button":"Criar seu primeiro Tipo de Conte\xFAdo","content-manager.pages.NoContentType.text":"Voc\xEA ainda n\xE3o tem nenhum conte\xFAdo. Recomendamos que voc\xEA crie seu primeiro Tipo de Conte\xFAdo.","content-manager.permissions.not-allowed.create":"Voc\xEA n\xE3o tem permiss\xE3o para criar documentos","content-manager.permissions.not-allowed.update":"Voc\xEA n\xE3o tem permiss\xE3o para ver esse documento","content-manager.plugin.description.long":"Maneira r\xE1pida de ver, editar e excluir os dados em seu banco de dados.","content-manager.plugin.description.short":"Maneira r\xE1pida de ver, editar e excluir os dados em seu banco de dados.","content-manager.popover.display-relations.label":"Exibir relacionamentos","content-manager.select.currently.selected":"{count} selecionado","content-manager.success.record.delete":"Removido","content-manager.success.record.publish":"Publicado","content-manager.success.record.save":"Salvo","content-manager.success.record.unpublish":"Despublicado","content-manager.utils.data-loaded":"{number, plural, =1 {O registro foi carregado} other {Os registros foram carregados}} com sucesso","content-manager.apiError.This attribute must be unique":"{field} deve ser \xFAnico","content-manager.popUpWarning.warning.publish-question":"Voc\xEA ainda quer publicar esse conte\xFAdo?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Sim, publicar","content-manager.popUpwarning.warning.has-draft-relations.message":"<b>{count, plural, =0 { conte\xFAdos relacionados n\xE3o est\xE3o publicados} one { conte\xFAdo relacionado n\xE3o est\xE1 publicado} other { conte\xFAdos relacionados n\xE3o est\xE3o publicados}}</b>.<br></br>Isso pode acarretar em links quebrados e erros em seu projeto.","form.button.continue":"Continuar","form.button.done":"Pronto","global.search":"Pesquisar","global.actions":"A\xE7\xF5es","global.active":"Ativo","global.inactive":"Inativo","global.back":"Voltar","global.cancel":"Cancelar","global.change-password":"Alterar senha","global.content-manager":"Gerenciador de Conte\xFAdo","global.continue":"Continuar","global.delete":"Deletar","global.delete-target":"Deletar {target}","global.description":"Descri\xE7\xE3o","global.details":"Detalhes","global.disabled":"Desabilitado","global.documentation":"Documenta\xE7\xE3o","global.enabled":"Habilitado","global.finish":"Finalizar","global.marketplace":"Loja","global.name":"Nome","global.none":"Nenhum","global.password":"Senha","global.plugins":"Extens\xF5es","global.plugins.content-manager":"Gerenciador de Conte\xFAdo","global.plugins.content-manager.description":"Maneira r\xE1pida de ver, editar e excluir os dados em seu banco de dados.","global.plugins.content-type-builder":"Criador de Tipos de Conte\xFAdo","global.plugins.content-type-builder.description":"Modele a estrutura de dados da sua API. Crie novos campos e rela\xE7\xF5es em apenas um minuto. Os arquivos s\xE3o criados e atualizados automaticamente em seu projeto.","global.plugins.email":"Email","global.plugins.email.description":"Configure seu aplicativo para enviar emails.","global.plugins.upload":"Biblioteca de m\xEDdia","global.plugins.upload.description":"Gerenciamento de arquivos de m\xEDdia.","global.plugins.graphql":"GraphQL","global.plugins.graphql.description":"Adiciona o endpoint GraphQL com m\xE9todos de API padr\xE3o.","global.plugins.documentation":"Documenta\xE7\xE3o","global.plugins.documentation.description":"Crie um documento OpenAPI e visualize sua API com SWAGGER UI.","global.plugins.i18n":"Internacionaliza\xE7\xE3o","global.plugins.i18n.description":"Esta extens\xE3o permite criar, ler e atualizar conte\xFAdo em diferentes idiomas, tanto do Painel Administrativo quanto da API.","global.plugins.sentry":"Sentry","global.plugins.sentry.description":"Envie eventos de erro do Strapi para o Sentry.","global.plugins.users-permissions":"Fun\xE7\xF5es e permiss\xF5es","global.plugins.users-permissions.description":"Proteja sua API com um processo de autentica\xE7\xE3o completo baseado em JWT. Esta extens\xE3o tamb\xE9m vem com uma estrat\xE9gia de ACL que permite gerenciar as permiss\xF5es entre os grupos de usu\xE1rios.","global.profile":"Perfil","global.prompt.unsaved":"Voc\xEA tem certeza que deseja sair desta p\xE1gina? Todas as suas modifica\xE7\xF5es ser\xE3o perdidas","global.reset-password":"Redefinir senha","global.roles":"Fun\xE7\xF5es","global.save":"Salvar","global.see-more":"Ver mais","global.select":"Selecionar","global.select-all-entries":"Selecionar todas as entradas","global.settings":"Configura\xE7\xF5es","global.strapi-super-admin":"Super Admin","global.strapi-editor":"Editor","global.strapi-author":"Autor","global.table.header.email":"Email","global.table.header.firstname":"Nome","global.table.header.isActive":"Status do usu\xE1rio","global.table.header.lastname":"Sobrenome","global.table.header.roles":"Fun\xE7\xF5es","global.table.header.username":"Usu\xE1rio","global.type":"Tipo","global.users":"Usu\xE1rios","notification.contentType.relations.conflict":"Tipo de conte\xFAdo tem relacionamentos conflitantes","notification.default.title":"Informa\xE7\xE3o:","notification.error":"Ocorreu um erro","notification.error.layout":"N\xE3o foi poss\xEDvel recuperar o layout","notification.form.error.fields":"O formul\xE1rio cont\xE9m alguns erros","notification.form.success.fields":"Mudan\xE7as salvas","notification.link-copied":"Link copiado pra \xE1rea de transfer\xEAncia","notification.permission.not-allowed-read":"Voc\xEA n\xE3o tem permiss\xE3o para ver esse documento","notification.success.delete":"O item foi removido","notification.success.saved":"Salvo","notification.success.title":"Sucesso:","notification.version.update.message":"Uma nova vers\xE3o do Strapi est\xE1 dispon\xEDvel!","notification.warning.title":"Aviso:","notification.warning.404":"404 - P\xE1gina n\xE3o encontrada",or:x,"request.error.model.unknown":"Este modelo n\xE3o existe",skipToContent:E,submit:b}}}]);
