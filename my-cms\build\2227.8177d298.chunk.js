"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[2227],{52227:(r,u,t)=>{t.r(u),t.d(u,{default:()=>i,from:()=>e});const e="\u0645\u0646",i={"attribute.boolean":"\u0645\u0646\u0637\u0642\u064A","attribute.date":"\u062A\u0627\u0631\u064A\u062E","attribute.email":"\u0628\u0631\u064A\u062F \u0627\u0644\u0625\u0643\u062A\u0631\u0648\u0646\u064A","attribute.enumeration":"\u062A\u0639\u062F\u0627\u062F","attribute.json":"JSON","attribute.media":"\u0648\u0633\u0627\u0626\u0637","attribute.password":"\u0643\u0644\u0645\u0629 \u0633\u0631","attribute.relation":"\u0639\u0644\u0627\u0642\u0629","attribute.text":"\u0646\u0635","form.attribute.item.customColumnName":"\u0623\u0633\u0645\u0627\u0621 \u0627\u0644\u0623\u0639\u0645\u062F\u0629 \u0627\u0644\u0645\u062E\u0635\u0635\u0629","form.attribute.item.customColumnName.description":"\u064A\u0641\u064A\u062F \u0630\u0644\u0643 \u0641\u064A \u0625\u0639\u0627\u062F\u0629 \u062A\u0633\u0645\u064A\u0629 \u0623\u0633\u0645\u0627\u0621 \u0623\u0639\u0645\u062F\u0629 \u0642\u0627\u0639\u062F\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A \u0628\u062A\u0646\u0633\u064A\u0642 \u0623\u0643\u062B\u0631 \u0634\u0645\u0648\u0644\u0627\u064B \u0644\u0627\u0633\u062A\u062C\u0627\u0628\u0627\u062A \u0648\u0627\u062C\u0647\u0629 \u0628\u0631\u0645\u062C\u0629 \u0627\u0644\u062A\u0637\u0628\u064A\u0642\u0627\u062A ( API )","form.attribute.item.defineRelation.fieldName":"\u0627\u0633\u0645 \u0627\u0644\u062D\u0642\u0644","form.attribute.item.enumeration.graphql":"\u062A\u062C\u0627\u0648\u0632 \u0627\u0644\u0627\u0633\u0645 \u0644\u0640 GraphQL","form.attribute.item.enumeration.graphql.description":"\u064A\u0633\u0645\u062D \u0644\u0643 \u0628\u062A\u062C\u0627\u0648\u0632 \u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0630\u064A \u062A\u0645 \u0625\u0646\u0634\u0627\u0624\u0647 \u0627\u0641\u062A\u0631\u0627\u0636\u064A\u064B\u0627 \u0644\u0640 GraphQL","form.attribute.item.enumeration.rules":"Values (one line per value)","form.attribute.item.maximum":"\u0627\u0642\u0635\u0649 \u0642\u064A\u0645\u0629","form.attribute.item.maximumLength":"\u0623\u0642\u0635\u0649 \u0637\u0648\u0644","form.attribute.item.minimum":"\u0623\u062F\u0646\u0649 \u0642\u064A\u0645\u0629","form.attribute.item.minimumLength":"\u0623\u062F\u0646\u0649 \u0637\u0648\u0644","form.attribute.item.number.type":"\u062A\u0646\u0633\u064A\u0642 \u0627\u0644\u0631\u0642\u0645","form.attribute.item.number.type.decimal":"\u0639\u062F\u062F \u0639\u0634\u0631\u064A (\u0645\u062B\u0627\u0644: 2.22)","form.attribute.item.number.type.float":"\u0639\u062F\u062F \u0639\u0627\u0626\u0645 (\u0645\u062B\u0627\u0644: 3.33333333)","form.attribute.item.number.type.integer":"\u0639\u062F\u062F \u0635\u062D\u064A\u062D (\u0645\u062B\u0627\u0644: 10)","form.attribute.item.requiredField":"\u0627\u0644\u062D\u0642\u0644 \u0645\u0637\u0644\u0648\u0628","form.attribute.item.requiredField.description":"\u0644\u0646 \u062A\u062A\u0645\u0643\u0646 \u0645\u0646 \u0625\u0646\u0634\u0627\u0621 \u0625\u062F\u062E\u0627\u0644 \u0625\u0630\u0627 \u0643\u0627\u0646 \u0647\u0630\u0627 \u0627\u0644\u062D\u0642\u0644 \u0641\u0627\u0631\u063A\u064B\u0627","form.attribute.item.uniqueField":"\u062D\u0642\u0644 \u0641\u0631\u064A\u062F","form.attribute.item.uniqueField.description":"\u0644\u0646 \u062A\u062A\u0645\u0643\u0646 \u0645\u0646 \u0625\u0646\u0634\u0627\u0621 \u0625\u062F\u062E\u0627\u0644 \u0625\u0630\u0627 \u0643\u0627\u0646 \u0647\u0646\u0627\u0643 \u0625\u062F\u062E\u0627\u0644 \u062D\u0627\u0644\u064A \u0628\u0645\u062D\u062A\u0648\u0649 \u0645\u062A\u0637\u0627\u0628\u0642","form.attribute.settings.default":"\u0627\u0644\u0642\u064A\u0645\u0629 \u0627\u0644\u0623\u0641\u062A\u0631\u0627\u0636\u064A\u0629","form.button.cancel":"\u0627\u0644\u063A\u0627\u0621",from:e,"modelPage.attribute.relationWith":"\u0639\u0644\u0627\u0642\u0629 \u0645\u0639","plugin.description.long":"\u0642\u0645 \u0628\u062A\u062C\u0645\u064A\u0639 \u0647\u064A\u0643\u0644 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u062E\u0627\u0635 \u0628\u0640 API \u0627\u0644\u062E\u0627\u0635 \u0628\u0643. \u0625\u0646\u0634\u0627\u0621 \u062D\u0642\u0648\u0644 \u0648\u0639\u0644\u0627\u0642\u0627\u062A \u062C\u062F\u064A\u062F\u0629 \u0641\u064A \u062F\u0642\u064A\u0642\u0629 \u0648\u0627\u062D\u062F\u0629 \u0641\u0642\u0637. \u064A\u062A\u0645 \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0645\u0644\u0641\u0627\u062A \u0648\u062A\u062D\u062F\u064A\u062B\u0647\u0627 \u062A\u0644\u0642\u0627\u0626\u064A\u064B\u0627 \u0641\u064A \u0645\u0634\u0631\u0648\u0639\u0643.","plugin.description.short":"\u0642\u0645 \u0628\u062A\u062C\u0645\u064A\u0639 \u0647\u064A\u0643\u0644 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u062E\u0627\u0635 \u0628\u0640 API \u0627\u0644\u062E\u0627\u0635 \u0628\u0643.","popUpForm.navContainer.advanced":"\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0645\u062A\u0642\u062F\u0645\u0629","popUpForm.navContainer.base":"\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0642\u0627\u0639\u062F\u0629","popUpWarning.bodyMessage.contentType.delete":"\u0647\u0644 \u0623\u0646\u062A \u0645\u062A\u0623\u0643\u062F \u0645\u0646 \u0623\u0646\u0643 \u062A\u0631\u064A\u062F \u062D\u0630\u0641 \u0646\u0648\u0639 \u0627\u0644\u0645\u062D\u062A\u0648\u0649 \u0647\u0630\u0627\u061F","relation.attributeName.placeholder":"\u0645\u062B\u0627\u0644: \u0627\u0644\u0645\u0624\u0644\u0641, \u0627\u0644\u0641\u0626\u0629, \u0627\u0644\u0648\u0633\u0645","relation.manyToMany":"\u064A\u0645\u0644\u0643 \u0648\u064A\u0646\u062A\u0645 \u0644\u0644\u0643\u062B\u064A\u0631","relation.manyToOne":"\u064A\u0645\u0644\u0643 \u0627\u0644\u0643\u062B\u064A\u0631","relation.oneToMany":"\u064A\u0646\u062A\u0645\u064A \u0644\u0644\u0643\u062B\u064A\u0631","relation.oneToOne":"\u064A\u062A\u0634\u0627\u0631\u0643 \u0628\u0648\u0627\u062D\u062F","relation.oneWay":"\u064A\u0645\u062A\u0644\u0643 \u0648\u0627\u062D\u062F","modalForm.header.back":"\u062E\u0644\u0641"}}}]);
