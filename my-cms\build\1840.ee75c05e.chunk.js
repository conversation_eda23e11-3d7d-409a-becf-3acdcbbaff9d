"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[1840],{31840:(a,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i={"BoundRoute.title":"URL endpoint naviazan\xFD k","EditForm.inputSelect.description.role":"Prid\xE1 rolu k pou\u017E\xEDvate\u013Eovi.","EditForm.inputSelect.label.role":"Predvolen\xE1 rola pre autorizovan\xFDch pou\u017E\xEDvate\u013Eov","EditForm.inputToggle.description.email":"Zak\xE1za\u0165 pou\u017E\xEDvate\u013Eovi vytvori\u0165 viac \xFA\u010Dtov s rovnakou e-mailovou adresou pre r\xF4znych poskytovate\u013Eov.","EditForm.inputToggle.description.email-confirmation":"Ak je povolen\xE9 (ON), registrovan\xED pou\u017E\xEDvatelia dostan\xFA potvrdzovac\xED e-mail.","EditForm.inputToggle.description.email-confirmation-redirection":"URL na ktor\xFA bude pou\u017E\xEDvate\u013E presmerovan\xFD po potvrden\xED e-mailu.","EditForm.inputToggle.description.email-reset-password":"URL pre nastavenie nov\xE9ho hesla","EditForm.inputToggle.description.sign-up":"Ak je zak\xE1zan\xE9 (OFF), registr\xE1cie nebud\xFA povolen\xE9. Nikto sa nebude m\xF4c\u0165 registrova\u0165 bez oh\u013Eadu na zvolen\xE9ho poskytovate\u013Ea.","EditForm.inputToggle.label.email":"Iba jedno konto pre jednu e-mailov\xFA adresu","EditForm.inputToggle.label.email-confirmation":"Povoli\u0165 potvrdzovanie e-mailov\xFDch adries","EditForm.inputToggle.label.email-confirmation-redirection":"URL pre potvrdenie e-mailovej adresy","EditForm.inputToggle.label.email-reset-password":"URL pre obnovu hesla","EditForm.inputToggle.label.sign-up":"Povoli\u0165 registr\xE1cie","HeaderNav.link.advancedSettings":"Pokro\u010Dil\xE9 nastavenia","HeaderNav.link.emailTemplates":"\u0160abl\xF3ny emailov","HeaderNav.link.providers":"Poskytovatelia","Plugin.permissions.plugins.description":"Zvo\u013Ete akcie, ktor\xE9 maj\xFA by\u0165 povolen\xE9 pre plugin {name}.","Plugins.header.description":"Zobrazuj\xFA sa iba akcie naviazan\xE9 na URL endpoint.","Plugins.header.title":"Opr\xE1vnenia","Policies.header.hint":"Vyberte akciu a kliknite na ikonku nastaven\xED pre zobrazenie naviazanej URL","Policies.header.title":"Pokro\u010Dil\xE9 nastavenia","PopUpForm.Email.email_templates.inputDescription":"Ak si nie ste ist\xFD ako pou\u017E\xEDva\u0165 premenn\xE9, {link}","PopUpForm.Email.options.from.email.label":"E-mail odosielate\u013Ea","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Meno odosielate\u013Ea","PopUpForm.Email.options.from.name.placeholder":"Janko Hra\u0161ko","PopUpForm.Email.options.message.label":"Obsah e-mailu","PopUpForm.Email.options.object.label":"Predmet","PopUpForm.Email.options.response_email.label":"Odpoveda\u0165 na e-mail","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"Ak je zak\xE1zan\xE9, pou\u017E\xEDvatelia nebud\xFA m\xF4c\u0165 pou\u017Ei\u0165 tohto poskytovate\u013Ea.","PopUpForm.Providers.enabled.label":"Povoli\u0165","PopUpForm.Providers.key.label":"Client ID","PopUpForm.Providers.key.placeholder":"TEXT","PopUpForm.Providers.redirectURL.front-end.label":"URL presmerovania do va\u0161ej aplik\xE1cie","PopUpForm.Providers.secret.label":"Client Secret","PopUpForm.Providers.secret.placeholder":"TEXT","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Upravi\u0165 \u0161abl\xF3ny e-mailov","notification.success.submit":"Nastavenia boli ulo\u017Een\xE9","plugin.description.long":"Zabezpe\u010Dte va\u0161e API pomocou JWT tokenov. Tento plugin taktie\u017E podporuje ACL z\xE1znamy, ktor\xE9 umo\u017E\u0148uj\xFA spravova\u0165 opr\xE1vnenia v r\xE1mci skup\xEDn pou\u017E\xEDvate\u013Eov.","plugin.description.short":"Zabezpe\u010Dte va\u0161e API pomocou JWT tokenov","plugin.name":"Roly a opr\xE1vnenia"}}}]);
