(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[6248],{1231:L=>{const D=Object.freeze({loose:!0}),a=Object.freeze({}),O=i=>i?typeof i!="object"?D:i:a;L.exports=O},1660:(L,D,a)=>{const O=a(85968),i=(g,m,M=!1)=>{if(g instanceof O)return g;try{return new O(g,m)}catch(H){if(!M)return null;throw H}};L.exports=i},39100:(L,D,a)=>{const O=a(85968),i=(g,m,M)=>new O(g,M).compare(new O(m,M));L.exports=i},46087:L=>{const D=/^[0-9]+$/,a=(i,g)=>{const m=D.test(i),M=D.test(g);return m&&M&&(i=+i,g=+g),i===g?0:m&&!M?-1:M&&!m?1:i<g?-1:1},O=(i,g)=>a(g,i);L.exports={compareIdentifiers:a,rcompareIdentifiers:O}},47180:L=>{const D=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...a)=>console.error("SEMVER",...a):()=>{};L.exports=D},55151:(L,D,a)=>{const O=a(39100),i=(g,m,M)=>O(g,m,M)<0;L.exports=i},66248:(L,D,a)=>{"use strict";a.r(D),a.d(D,{A:()=>hs,L:()=>rt,N:()=>Oe,S:()=>F,V:()=>oe,g:()=>xe,s:()=>ct});var O={};a.r(O),a.d(O,{FILE:()=>$e,HTML:()=>Ue,TEXT:()=>Ge,URL:()=>Xe});var i=a(92132),g=a(21272),m=a(55506),M=a(55151),H=a(79077),l=a(43543),u=a(88761),ae=a(48940),v=a(89356),h=a(21382);function S(e,t,n){return t.split(".").reduce((s,r)=>s&&s[r]?s[r]:n||null,e)}function f(e,t){return e.filter(n=>n!==t)}function R(e){return typeof e=="string"}function X(e){return typeof e=="object"}function z(e,t){const n=new Map,s=o=>{n.set(o,n.has(o)?n.get(o)+1:1)};e.forEach(s),t.forEach(s);const r=[];return n.forEach((o,d)=>{o===1&&r.push(d)}),r}function ce(e,t){return e.filter(n=>t.indexOf(n)>-1)}const B="dnd-core/INIT_COORDS",_="dnd-core/BEGIN_DRAG",de="dnd-core/PUBLISH_DRAG_SOURCE",ee="dnd-core/HOVER",te="dnd-core/DROP",ne="dnd-core/END_DRAG";function De(e,t){return{type:B,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}const gt={type:B,payload:{clientOffset:null,sourceClientOffset:null}};function ft(e){return function(n=[],s={publishSource:!0}){const{publishSource:r=!0,clientOffset:o,getSourceClientOffset:d}=s,c=e.getMonitor(),p=e.getRegistry();e.dispatch(De(o)),mt(n,c,p);const E=Tt(n,c);if(E==null){e.dispatch(gt);return}let I=null;if(o){if(!d)throw new Error("getSourceClientOffset must be defined");Et(d),I=d(E)}e.dispatch(De(o,I));const C=p.getSource(E).beginDrag(c,E);if(C==null)return;vt(C),p.pinSource(E);const A=p.getSourceType(E);return{type:_,payload:{itemType:A,item:C,sourceId:E,clientOffset:o||null,sourceClientOffset:I||null,isSourcePublic:!!r}}}}function mt(e,t,n){(0,h.V)(!t.isDragging(),"Cannot call beginDrag while dragging."),e.forEach(function(s){(0,h.V)(n.getSource(s),"Expected sourceIds to be registered.")})}function Et(e){(0,h.V)(typeof e=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function vt(e){(0,h.V)(X(e),"Item must be an object.")}function Tt(e,t){let n=null;for(let s=e.length-1;s>=0;s--)if(t.canDragSource(e[s])){n=e[s];break}return n}function yt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function It(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){yt(e,r,n[r])})}return e}function St(e){return function(n={}){const s=e.getMonitor(),r=e.getRegistry();xt(s),bt(s).forEach((d,c)=>{const p=Ot(d,c,r,s),E={type:te,payload:{dropResult:It({},n,p)}};e.dispatch(E)})}}function xt(e){(0,h.V)(e.isDragging(),"Cannot call drop while not dragging."),(0,h.V)(!e.didDrop(),"Cannot call drop twice during one drag operation.")}function Ot(e,t,n,s){const r=n.getTarget(e);let o=r?r.drop(s,e):void 0;return Dt(o),typeof o>"u"&&(o=t===0?{}:s.getDropResult()),o}function Dt(e){(0,h.V)(typeof e>"u"||X(e),"Drop result must either be an object or undefined.")}function bt(e){const t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t}function Nt(e){return function(){const n=e.getMonitor(),s=e.getRegistry();jt(n);const r=n.getSourceId();return r!=null&&(s.getSource(r,!0).endDrag(n,r),s.unpinSource()),{type:ne}}}function jt(e){(0,h.V)(e.isDragging(),"Cannot call endDrag while not dragging.")}function le(e,t){return t===null?e===null:Array.isArray(e)?e.some(n=>n===t):e===t}function Ct(e){return function(n,{clientOffset:s}={}){At(n);const r=n.slice(0),o=e.getMonitor(),d=e.getRegistry(),c=o.getItemType();return Rt(r,d,c),Lt(r,o,d),wt(r,o,d),{type:ee,payload:{targetIds:r,clientOffset:s||null}}}}function At(e){(0,h.V)(Array.isArray(e),"Expected targetIds to be an array.")}function Lt(e,t,n){(0,h.V)(t.isDragging(),"Cannot call hover while not dragging."),(0,h.V)(!t.didDrop(),"Cannot call hover after drop.");for(let s=0;s<e.length;s++){const r=e[s];(0,h.V)(e.lastIndexOf(r)===s,"Expected targetIds to be unique in the passed array.");const o=n.getTarget(r);(0,h.V)(o,"Expected targetIds to be registered.")}}function Rt(e,t,n){for(let s=e.length-1;s>=0;s--){const r=e[s],o=t.getTargetType(r);le(o,n)||e.splice(s,1)}}function wt(e,t,n){e.forEach(function(s){n.getTarget(s).hover(t,s)})}function Pt(e){return function(){if(e.getMonitor().isDragging())return{type:de}}}function Mt(e){return{beginDrag:ft(e),publishDragSource:Pt(e),hover:Ct(e),drop:St(e),endDrag:Nt(e)}}class kt{receiveBackend(t){this.backend=t}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const t=this,{dispatch:n}=this.store;function s(o){return(...d)=>{const c=o.apply(t,d);typeof c<"u"&&n(c)}}const r=Mt(this);return Object.keys(r).reduce((o,d)=>{const c=r[d];return o[d]=s(c),o},{})}dispatch(t){this.store.dispatch(t)}constructor(t,n){this.isSetUp=!1,this.handleRefCountChange=()=>{const s=this.store.getState().refCount>0;this.backend&&(s&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!s&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=t,this.monitor=n,t.subscribe(this.handleRefCountChange)}}function $t(e,t){return{x:e.x+t.x,y:e.y+t.y}}function be(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Xt(e){const{clientOffset:t,initialClientOffset:n,initialSourceClientOffset:s}=e;return!t||!n||!s?null:be($t(t,s),n)}function Gt(e){const{clientOffset:t,initialClientOffset:n}=e;return!t||!n?null:be(t,n)}const Q=[],ue=[];Q.__IS_NONE__=!0,ue.__IS_ALL__=!0;function Ut(e,t){return e===Q?!1:e===ue||typeof t>"u"?!0:ce(t,e).length>0}class Ft{subscribeToStateChange(t,n={}){const{handlerIds:s}=n;(0,h.V)(typeof t=="function","listener must be a function."),(0,h.V)(typeof s>"u"||Array.isArray(s),"handlerIds, when specified, must be an array of strings.");let r=this.store.getState().stateId;const o=()=>{const d=this.store.getState(),c=d.stateId;try{c===r||c===r+1&&!Ut(d.dirtyHandlerIds,s)||t()}finally{r=c}};return this.store.subscribe(o)}subscribeToOffsetChange(t){(0,h.V)(typeof t=="function","listener must be a function.");let n=this.store.getState().dragOffset;const s=()=>{const r=this.store.getState().dragOffset;r!==n&&(n=r,t())};return this.store.subscribe(s)}canDragSource(t){if(!t)return!1;const n=this.registry.getSource(t);return(0,h.V)(n,`Expected to find a valid source. sourceId=${t}`),this.isDragging()?!1:n.canDrag(this,t)}canDropOnTarget(t){if(!t)return!1;const n=this.registry.getTarget(t);if((0,h.V)(n,`Expected to find a valid target. targetId=${t}`),!this.isDragging()||this.didDrop())return!1;const s=this.registry.getTargetType(t),r=this.getItemType();return le(s,r)&&n.canDrop(this,t)}isDragging(){return Boolean(this.getItemType())}isDraggingSource(t){if(!t)return!1;const n=this.registry.getSource(t,!0);if((0,h.V)(n,`Expected to find a valid source. sourceId=${t}`),!this.isDragging()||!this.isSourcePublic())return!1;const s=this.registry.getSourceType(t),r=this.getItemType();return s!==r?!1:n.isDragging(this,t)}isOverTarget(t,n={shallow:!1}){if(!t)return!1;const{shallow:s}=n;if(!this.isDragging())return!1;const r=this.registry.getTargetType(t),o=this.getItemType();if(o&&!le(r,o))return!1;const d=this.getTargetIds();if(!d.length)return!1;const c=d.indexOf(t);return s?c===d.length-1:c>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return Boolean(this.store.getState().dragOperation.isSourcePublic)}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return Xt(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return Gt(this.store.getState().dragOffset)}constructor(t,n){this.store=t,this.registry=n}}const Ne=typeof global<"u"?global:self,je=Ne.MutationObserver||Ne.WebKitMutationObserver;function Ce(e){return function(){const n=setTimeout(r,0),s=setInterval(r,50);function r(){clearTimeout(n),clearInterval(s),e()}}}function Bt(e){let t=1;const n=new je(e),s=document.createTextNode("");return n.observe(s,{characterData:!0}),function(){t=-t,s.data=t}}const Ht=typeof je=="function"?Bt:Ce;class Vt{enqueueTask(t){const{queue:n,requestFlush:s}=this;n.length||(s(),this.flushing=!0),n[n.length]=t}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:t}=this;for(;this.index<t.length;){const n=this.index;if(this.index++,t[n].call(),this.index>this.capacity){for(let s=0,r=t.length-this.index;s<r;s++)t[s]=t[s+this.index];t.length-=this.index,this.index=0}}t.length=0,this.index=0,this.flushing=!1},this.registerPendingError=t=>{this.pendingErrors.push(t),this.requestErrorThrow()},this.requestFlush=Ht(this.flush),this.requestErrorThrow=Ce(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class zt{call(){try{this.task&&this.task()}catch(t){this.onError(t)}finally{this.task=null,this.release(this)}}constructor(t,n){this.onError=t,this.release=n,this.task=null}}class Wt{create(t){const n=this.freeTasks,s=n.length?n.pop():new zt(this.onError,r=>n[n.length]=r);return s.task=t,s}constructor(t){this.onError=t,this.freeTasks=[]}}const Ae=new Vt,Yt=new Wt(Ae.registerPendingError);function Kt(e){Ae.enqueueTask(Yt.create(e))}const he="dnd-core/ADD_SOURCE",pe="dnd-core/ADD_TARGET",ge="dnd-core/REMOVE_SOURCE",re="dnd-core/REMOVE_TARGET";function Zt(e){return{type:he,payload:{sourceId:e}}}function Qt(e){return{type:pe,payload:{targetId:e}}}function Jt(e){return{type:ge,payload:{sourceId:e}}}function qt(e){return{type:re,payload:{targetId:e}}}function _t(e){(0,h.V)(typeof e.canDrag=="function","Expected canDrag to be a function."),(0,h.V)(typeof e.beginDrag=="function","Expected beginDrag to be a function."),(0,h.V)(typeof e.endDrag=="function","Expected endDrag to be a function.")}function en(e){(0,h.V)(typeof e.canDrop=="function","Expected canDrop to be a function."),(0,h.V)(typeof e.hover=="function","Expected hover to be a function."),(0,h.V)(typeof e.drop=="function","Expected beginDrag to be a function.")}function fe(e,t){if(t&&Array.isArray(e)){e.forEach(n=>fe(n,!1));return}(0,h.V)(typeof e=="string"||typeof e=="symbol",t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var G;(function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"})(G||(G={}));let tn=0;function nn(){return tn++}function rn(e){const t=nn().toString();switch(e){case G.SOURCE:return`S${t}`;case G.TARGET:return`T${t}`;default:throw new Error(`Unknown Handler Role: ${e}`)}}function Le(e){switch(e[0]){case"S":return G.SOURCE;case"T":return G.TARGET;default:throw new Error(`Cannot parse handler ID: ${e}`)}}function Re(e,t){const n=e.entries();let s=!1;do{const{done:r,value:[,o]}=n.next();if(o===t)return!0;s=!!r}while(!s);return!1}class sn{addSource(t,n){fe(t),_t(n);const s=this.addHandler(G.SOURCE,t,n);return this.store.dispatch(Zt(s)),s}addTarget(t,n){fe(t,!0),en(n);const s=this.addHandler(G.TARGET,t,n);return this.store.dispatch(Qt(s)),s}containsHandler(t){return Re(this.dragSources,t)||Re(this.dropTargets,t)}getSource(t,n=!1){return(0,h.V)(this.isSourceId(t),"Expected a valid source ID."),n&&t===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(t)}getTarget(t){return(0,h.V)(this.isTargetId(t),"Expected a valid target ID."),this.dropTargets.get(t)}getSourceType(t){return(0,h.V)(this.isSourceId(t),"Expected a valid source ID."),this.types.get(t)}getTargetType(t){return(0,h.V)(this.isTargetId(t),"Expected a valid target ID."),this.types.get(t)}isSourceId(t){return Le(t)===G.SOURCE}isTargetId(t){return Le(t)===G.TARGET}removeSource(t){(0,h.V)(this.getSource(t),"Expected an existing source."),this.store.dispatch(Jt(t)),Kt(()=>{this.dragSources.delete(t),this.types.delete(t)})}removeTarget(t){(0,h.V)(this.getTarget(t),"Expected an existing target."),this.store.dispatch(qt(t)),this.dropTargets.delete(t),this.types.delete(t)}pinSource(t){const n=this.getSource(t);(0,h.V)(n,"Expected an existing source."),this.pinnedSourceId=t,this.pinnedSource=n}unpinSource(){(0,h.V)(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(t,n,s){const r=rn(t);return this.types.set(r,n),t===G.SOURCE?this.dragSources.set(r,s):t===G.TARGET&&this.dropTargets.set(r,s),r}constructor(t){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=t}}const on=(e,t)=>e===t;function an(e,t){return!e&&!t?!0:!e||!t?!1:e.x===t.x&&e.y===t.y}function cn(e,t,n=on){if(e.length!==t.length)return!1;for(let s=0;s<e.length;++s)if(!n(e[s],t[s]))return!1;return!0}function dn(e=Q,t){switch(t.type){case ee:break;case he:case pe:case re:case ge:return Q;case _:case de:case ne:case te:default:return ue}const{targetIds:n=[],prevTargetIds:s=[]}=t.payload,r=z(n,s);if(!(r.length>0||!cn(n,s)))return Q;const d=s[s.length-1],c=n[n.length-1];return d!==c&&(d&&r.push(d),c&&r.push(c)),r}function ln(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function un(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){ln(e,r,n[r])})}return e}const we={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function hn(e=we,t){const{payload:n}=t;switch(t.type){case B:case _:return{initialSourceClientOffset:n.sourceClientOffset,initialClientOffset:n.clientOffset,clientOffset:n.clientOffset};case ee:return an(e.clientOffset,n.clientOffset)?e:un({},e,{clientOffset:n.clientOffset});case ne:case te:return we;default:return e}}function pn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function W(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){pn(e,r,n[r])})}return e}const gn={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function fn(e=gn,t){const{payload:n}=t;switch(t.type){case _:return W({},e,{itemType:n.itemType,item:n.item,sourceId:n.sourceId,isSourcePublic:n.isSourcePublic,dropResult:null,didDrop:!1});case de:return W({},e,{isSourcePublic:!0});case ee:return W({},e,{targetIds:n.targetIds});case re:return e.targetIds.indexOf(n.targetId)===-1?e:W({},e,{targetIds:f(e.targetIds,n.targetId)});case te:return W({},e,{dropResult:n.dropResult,didDrop:!0,targetIds:[]});case ne:return W({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}}function mn(e=0,t){switch(t.type){case he:case pe:return e+1;case ge:case re:return e-1;default:return e}}function En(e=0){return e+1}function vn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Tn(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){vn(e,r,n[r])})}return e}function yn(e={},t){return{dirtyHandlerIds:dn(e.dirtyHandlerIds,{type:t.type,payload:Tn({},t.payload,{prevTargetIds:S(e,"dragOperation.targetIds",[])})}),dragOffset:hn(e.dragOffset,t),refCount:mn(e.refCount,t),dragOperation:fn(e.dragOperation,t),stateId:En(e.stateId)}}function In(e,t=void 0,n={},s=!1){const r=Sn(s),o=new Ft(r,new sn(r)),d=new kt(r,o),c=e(d,t,n);return d.receiveBackend(c),d}function Sn(e){const t=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return(0,v.y$)(yn,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}var xn=a(47182);function On(e,t){if(e==null)return{};var n=Dn(e,t),s,r;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)s=o[r],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(n[s]=e[s])}return n}function Dn(e,t){if(e==null)return{};var n={},s=Object.keys(e),r,o;for(o=0;o<s.length;o++)r=s[o],!(t.indexOf(r)>=0)&&(n[r]=e[r]);return n}let Pe=0;const se=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var bn=(0,g.memo)(function(t){var{children:n}=t,s=On(t,["children"]);const[r,o]=Nn(s);return(0,g.useEffect)(()=>{if(o){const d=Me();return++Pe,()=>{--Pe===0&&(d[se]=null)}}},[]),(0,i.jsx)(xn.M.Provider,{value:r,children:n})});function Nn(e){if("manager"in e)return[{dragDropManager:e.manager},!1];const t=jn(e.backend,e.context,e.options,e.debugMode),n=!e.context;return[t,n]}function jn(e,t=Me(),n,s){const r=t;return r[se]||(r[se]={dragDropManager:In(e,t,n,s)}),r[se]}function Me(){return typeof global<"u"?global:window}function ke(e){let t=null;return()=>(t==null&&(t=e()),t)}function Cn(e,t){return e.filter(n=>n!==t)}function An(e,t){const n=new Set,s=o=>n.add(o);e.forEach(s),t.forEach(s);const r=[];return n.forEach(o=>r.push(o)),r}class Ln{enter(t){const n=this.entered.length,s=r=>this.isNodeInDocument(r)&&(!r.contains||r.contains(t));return this.entered=An(this.entered.filter(s),[t]),n===0&&this.entered.length>0}leave(t){const n=this.entered.length;return this.entered=Cn(this.entered.filter(this.isNodeInDocument),t),n>0&&this.entered.length===0}reset(){this.entered=[]}constructor(t){this.entered=[],this.isNodeInDocument=t}}class Rn{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(t=>{Object.defineProperty(this.item,t,{configurable:!0,enumerable:!0,get(){return console.warn(`Browser doesn't allow reading "${t}" until the drop event.`),null}})})}loadDataTransfer(t){if(t){const n={};Object.keys(this.config.exposeProperties).forEach(s=>{const r=this.config.exposeProperties[s];r!=null&&(n[s]={value:r(t,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,n)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(t,n){return n===t.getSourceId()}endDrag(){}constructor(t){this.config=t,this.item={},this.initializeExposedProperties()}}const $e="__NATIVE_FILE__",Xe="__NATIVE_URL__",Ge="__NATIVE_TEXT__",Ue="__NATIVE_HTML__";function me(e,t,n){const s=t.reduce((r,o)=>r||e.getData(o),"");return s??n}const Ee={[$e]:{exposeProperties:{files:e=>Array.prototype.slice.call(e.files),items:e=>e.items,dataTransfer:e=>e},matchesTypes:["Files"]},[Ue]:{exposeProperties:{html:(e,t)=>me(e,t,""),dataTransfer:e=>e},matchesTypes:["Html","text/html"]},[Xe]:{exposeProperties:{urls:(e,t)=>me(e,t,"").split(`
`),dataTransfer:e=>e},matchesTypes:["Url","text/uri-list"]},[Ge]:{exposeProperties:{text:(e,t)=>me(e,t,""),dataTransfer:e=>e},matchesTypes:["Text","text/plain"]}};function wn(e,t){const n=Ee[e];if(!n)throw new Error(`native type ${e} has no configuration`);const s=new Rn(n);return s.loadDataTransfer(t),s}function ve(e){if(!e)return null;const t=Array.prototype.slice.call(e.types||[]);return Object.keys(Ee).filter(n=>{const s=Ee[n];return s?.matchesTypes?s.matchesTypes.some(r=>t.indexOf(r)>-1):!1})[0]||null}const Pn=ke(()=>/firefox/i.test(navigator.userAgent)),Fe=ke(()=>Boolean(window.safari));class Be{interpolate(t){const{xs:n,ys:s,c1s:r,c2s:o,c3s:d}=this;let c=n.length-1;if(t===n[c])return s[c];let p=0,E=d.length-1,I;for(;p<=E;){I=Math.floor(.5*(p+E));const A=n[I];if(A<t)p=I+1;else if(A>t)E=I-1;else return s[I]}c=Math.max(0,E);const $=t-n[c],C=$*$;return s[c]+r[c]*$+o[c]*C+d[c]*$*C}constructor(t,n){const{length:s}=t,r=[];for(let y=0;y<s;y++)r.push(y);r.sort((y,k)=>t[y]<t[k]?-1:1);const o=[],d=[],c=[];let p,E;for(let y=0;y<s-1;y++)p=t[y+1]-t[y],E=n[y+1]-n[y],d.push(p),o.push(E),c.push(E/p);const I=[c[0]];for(let y=0;y<d.length-1;y++){const k=c[y],w=c[y+1];if(k*w<=0)I.push(0);else{p=d[y];const P=d[y+1],T=p+P;I.push(3*T/((T+P)/k+(T+p)/w))}}I.push(c[c.length-1]);const $=[],C=[];let A;for(let y=0;y<I.length-1;y++){A=c[y];const k=I[y],w=1/d[y],P=k+I[y+1]-A-A;$.push((A-k-P)*w),C.push(P*w*w)}this.xs=t,this.ys=n,this.c1s=I,this.c2s=$,this.c3s=C}}const Mn=1;function He(e){const t=e.nodeType===Mn?e:e.parentElement;if(!t)return null;const{top:n,left:s}=t.getBoundingClientRect();return{x:s,y:n}}function ie(e){return{x:e.clientX,y:e.clientY}}function kn(e){var t;return e.nodeName==="IMG"&&(Pn()||!(!((t=document.documentElement)===null||t===void 0)&&t.contains(e)))}function $n(e,t,n,s){let r=e?t.width:n,o=e?t.height:s;return Fe()&&e&&(o/=window.devicePixelRatio,r/=window.devicePixelRatio),{dragPreviewWidth:r,dragPreviewHeight:o}}function Xn(e,t,n,s,r){const o=kn(t),c=He(o?e:t),p={x:n.x-c.x,y:n.y-c.y},{offsetWidth:E,offsetHeight:I}=e,{anchorX:$,anchorY:C}=s,{dragPreviewWidth:A,dragPreviewHeight:y}=$n(o,t,E,I),k=()=>{let pt=new Be([0,.5,1],[p.y,p.y/I*y,p.y+y-I]).interpolate(C);return Fe()&&o&&(pt+=(window.devicePixelRatio-1)*y),pt},w=()=>new Be([0,.5,1],[p.x,p.x/E*A,p.x+A-E]).interpolate($),{offsetX:P,offsetY:T}=r,V=P===0||P,ps=T===0||T;return{x:V?P:w(),y:ps?T:k()}}class Gn{get window(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}get document(){var t;return!((t=this.globalContext)===null||t===void 0)&&t.document?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var t;return((t=this.optionsArgs)===null||t===void 0?void 0:t.rootElement)||this.window}constructor(t,n){this.ownerDocument=null,this.globalContext=t,this.optionsArgs=n}}function Un(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ve(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){Un(e,r,n[r])})}return e}class Fn{profile(){var t,n;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((t=this.dragStartSourceIds)===null||t===void 0?void 0:t.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((n=this.dragOverTargetIds)===null||n===void 0?void 0:n.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const t=this.rootElement;if(t!==void 0){if(t.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");t.__isReactDndBackendSetUp=!0,this.addEventListeners(t)}}teardown(){const t=this.rootElement;if(t!==void 0&&(t.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var n;(n=this.window)===null||n===void 0||n.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(t,n,s){return this.sourcePreviewNodeOptions.set(t,s),this.sourcePreviewNodes.set(t,n),()=>{this.sourcePreviewNodes.delete(t),this.sourcePreviewNodeOptions.delete(t)}}connectDragSource(t,n,s){this.sourceNodes.set(t,n),this.sourceNodeOptions.set(t,s);const r=d=>this.handleDragStart(d,t),o=d=>this.handleSelectStart(d);return n.setAttribute("draggable","true"),n.addEventListener("dragstart",r),n.addEventListener("selectstart",o),()=>{this.sourceNodes.delete(t),this.sourceNodeOptions.delete(t),n.removeEventListener("dragstart",r),n.removeEventListener("selectstart",o),n.setAttribute("draggable","false")}}connectDropTarget(t,n){const s=d=>this.handleDragEnter(d,t),r=d=>this.handleDragOver(d,t),o=d=>this.handleDrop(d,t);return n.addEventListener("dragenter",s),n.addEventListener("dragover",r),n.addEventListener("drop",o),()=>{n.removeEventListener("dragenter",s),n.removeEventListener("dragover",r),n.removeEventListener("drop",o)}}addEventListeners(t){t.addEventListener&&(t.addEventListener("dragstart",this.handleTopDragStart),t.addEventListener("dragstart",this.handleTopDragStartCapture,!0),t.addEventListener("dragend",this.handleTopDragEndCapture,!0),t.addEventListener("dragenter",this.handleTopDragEnter),t.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),t.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),t.addEventListener("dragover",this.handleTopDragOver),t.addEventListener("dragover",this.handleTopDragOverCapture,!0),t.addEventListener("drop",this.handleTopDrop),t.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(t){t.removeEventListener&&(t.removeEventListener("dragstart",this.handleTopDragStart),t.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),t.removeEventListener("dragend",this.handleTopDragEndCapture,!0),t.removeEventListener("dragenter",this.handleTopDragEnter),t.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),t.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),t.removeEventListener("dragover",this.handleTopDragOver),t.removeEventListener("dragover",this.handleTopDragOverCapture,!0),t.removeEventListener("drop",this.handleTopDrop),t.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const t=this.monitor.getSourceId(),n=this.sourceNodeOptions.get(t);return Ve({dropEffect:this.altKeyPressed?"copy":"move"},n||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const t=this.monitor.getSourceId(),n=this.sourcePreviewNodeOptions.get(t);return Ve({anchorX:.5,anchorY:.5,captureDraggingState:!1},n||{})}isDraggingNativeItem(){const t=this.monitor.getItemType();return Object.keys(O).some(n=>O[n]===t)}beginDragNativeItem(t,n){this.clearCurrentDragSourceNode(),this.currentNativeSource=wn(t,n),this.currentNativeHandle=this.registry.addSource(t,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(t){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=t;const n=1e3;this.mouseMoveTimeoutTimer=setTimeout(()=>{var s;return(s=this.rootElement)===null||s===void 0?void 0:s.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},n)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var t;(t=this.window)===null||t===void 0||t.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(t,n){t.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(n))}handleDragEnter(t,n){this.dragEnterTargetIds.unshift(n)}handleDragOver(t,n){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(n)}handleDrop(t,n){this.dropTargetIds.unshift(n)}constructor(t,n,s){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=r=>{const o=this.sourceNodes.get(r);return o&&He(o)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=r=>Boolean(r&&this.document&&this.document.body&&this.document.body.contains(r)),this.endDragIfSourceWasRemovedFromDOM=()=>{const r=this.currentDragSourceNode;r==null||this.isNodeInDocument(r)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=r=>{this.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(r||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{this.hoverRafId!==null&&typeof cancelAnimationFrame<"u"&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=r=>{if(r.defaultPrevented)return;const{dragStartSourceIds:o}=this;this.dragStartSourceIds=null;const d=ie(r);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(o||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:d});const{dataTransfer:c}=r,p=ve(c);if(this.monitor.isDragging()){if(c&&typeof c.setDragImage=="function"){const I=this.monitor.getSourceId(),$=this.sourceNodes.get(I),C=this.sourcePreviewNodes.get(I)||$;if(C){const{anchorX:A,anchorY:y,offsetX:k,offsetY:w}=this.getCurrentSourcePreviewNodeOptions(),V=Xn($,C,d,{anchorX:A,anchorY:y},{offsetX:k,offsetY:w});c.setDragImage(C,V.x,V.y)}}try{c?.setData("application/json",{})}catch{}this.setCurrentDragSourceNode(r.target);const{captureDraggingState:E}=this.getCurrentSourcePreviewNodeOptions();E?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(p)this.beginDragNativeItem(p);else{if(c&&!c.types&&(r.target&&!r.target.hasAttribute||!r.target.hasAttribute("draggable")))return;r.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=r=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var o;(o=this.currentNativeSource)===null||o===void 0||o.loadDataTransfer(r.dataTransfer)}if(!this.enterLeaveCounter.enter(r.target)||this.monitor.isDragging())return;const{dataTransfer:c}=r,p=ve(c);p&&this.beginDragNativeItem(p,c)},this.handleTopDragEnter=r=>{const{dragEnterTargetIds:o}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=r.altKey,o.length>0&&this.actions.hover(o,{clientOffset:ie(r)}),o.some(c=>this.monitor.canDropOnTarget(c))&&(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=r=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var o;(o=this.currentNativeSource)===null||o===void 0||o.loadDataTransfer(r.dataTransfer)}},this.handleTopDragOver=r=>{const{dragOverTargetIds:o}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect="none");return}this.altKeyPressed=r.altKey,this.lastClientOffset=ie(r),this.scheduleHover(o),(o||[]).some(c=>this.monitor.canDropOnTarget(c))?(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?r.preventDefault():(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=r=>{this.isDraggingNativeItem()&&r.preventDefault(),this.enterLeaveCounter.leave(r.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=r=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var o;r.preventDefault(),(o=this.currentNativeSource)===null||o===void 0||o.loadDataTransfer(r.dataTransfer)}else ve(r.dataTransfer)&&r.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=r=>{const{dropTargetIds:o}=this;this.dropTargetIds=[],this.actions.hover(o,{clientOffset:ie(r)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=r=>{const o=r.target;typeof o.dragDrop=="function"&&(o.tagName==="INPUT"||o.tagName==="SELECT"||o.tagName==="TEXTAREA"||o.isContentEditable||(r.preventDefault(),o.dragDrop()))},this.options=new Gn(n,s),this.actions=t.getActions(),this.monitor=t.getMonitor(),this.registry=t.getRegistry(),this.enterLeaveCounter=new Ln(this.isNodeInDocument)}}const Bn=function(t,n,s){return new Fn(t,n,s)};var Y=a(71389),U=a(17703),x=a(83997),b=a(30893),j=a(94061),ze=a(69599),We=a(11341),Hn=a(88353),Te=a(85963),J=a(58805),ye=a(48653),Vn=a(12493),K=a(98765),zn=a(81387),Wn=a(49654),N=a(63891);const Ye=(0,g.createContext)(!1),q=()=>(0,g.useContext)(Ye),Yn=(0,N.Ay)(x.s)`
  width: ${({condensed:e})=>e?"max-content":`${224/16}rem`};
  border-right: 1px solid ${({theme:e})=>e.colors.neutral150};
`,Kn=({condensed:e=!1,...t})=>(0,i.jsx)(Ye.Provider,{value:e,children:(0,i.jsx)(Yn,{alignItems:"normal",as:"nav",background:"neutral0",condensed:e,direction:"column",height:"100vh",position:"sticky",top:0,zIndex:2,...t})});var Ie=a(85329);const Ke=N.Ay.div`
  border-radius: ${({theme:e})=>e.borderRadius};

  svg,
  img {
    height: ${({condensed:e})=>e?`${40/16}rem`:`${32/16}rem`};
    width: ${({condensed:e})=>e?`${40/16}rem`:`${32/16}rem`};
  }
`,Zn=(0,N.Ay)(Ie.s)`
  text-decoration: unset;
  color: inherit;
`,Qn=g.forwardRef(({workplace:e,title:t,icon:n,...s},r)=>{const o=q();return s.to=s?.to??"/",o?(0,i.jsx)(Ie.s,{ref:r,...s,children:(0,i.jsx)(j.a,{paddingLeft:3,paddingRight:3,paddingTop:4,paddingBottom:4,children:(0,i.jsxs)(Ke,{condensed:!0,children:[n,(0,i.jsxs)(K.s,{children:[(0,i.jsx)("span",{children:t}),(0,i.jsx)("span",{children:e})]})]})})}):(0,i.jsx)(Zn,{ref:r,...s,children:(0,i.jsx)(j.a,{paddingLeft:3,paddingRight:3,paddingTop:4,paddingBottom:4,children:(0,i.jsxs)(x.s,{children:[(0,i.jsx)(Ke,{"aria-hidden":!0,tabIndex:-1,children:n}),(0,i.jsxs)(j.a,{paddingLeft:2,children:[(0,i.jsxs)(b.o,{fontWeight:"bold",textColor:"neutral800",as:"span",children:[t,(0,i.jsx)(K.s,{as:"span",children:e})]}),(0,i.jsx)(b.o,{variant:"pi",as:"p",textColor:"neutral600","aria-hidden":!0,children:e})]})]})})})}),Jn=(0,N.Ay)(j.a)`
  flex-grow: 1;
  overflow-y: auto;
`,qn=({children:e,spacing:t=4,horizontal:n=!1,...s})=>(0,i.jsx)(Jn,{paddingLeft:3,paddingRight:2,paddingTop:3,paddingBottom:8,children:(0,i.jsx)(x.s,{as:"ul",gap:t,direction:n?"row":"column",alignItems:n?"center":"stretch",...s,children:g.Children.map(e,(r,o)=>(0,i.jsx)("li",{children:r},o))})});var _n=a(10573),er=a(79739);const Ze=(0,N.Ay)(j.a)`
  svg {
    width: 1rem;
    height: 1rem;
  }
`,Qe=(0,N.Ay)(Ie.s)`
  position: relative;
  text-decoration: none;
  display: block;
  border-radius: ${({theme:e})=>e.borderRadius};
  background: ${({theme:e})=>e.colors.neutral0};

  ${b.o} {
    color: ${({theme:e})=>e.colors.neutral600};
  }

  svg path {
    fill: ${({theme:e})=>e.colors.neutral500};
  }

  &:hover {
    background: ${({theme:e})=>e.colors.neutral100};

    ${b.o} {
      color: ${({theme:e})=>e.colors.neutral700};
    }

    svg path {
      fill: ${({theme:e})=>e.colors.neutral600};
    }
  }

  &.active {
    background: ${({theme:e})=>e.colors.primary100};

    svg path {
      fill: ${({theme:e})=>e.colors.primary600};
    }

    ${b.o} {
      color: ${({theme:e})=>e.colors.primary600};
      font-weight: 500;
    }
  }
`,Je=(0,N.Ay)(x.s)`
  padding: ${({theme:e})=>`${e.spaces[2]} ${e.spaces[3]}`};
`,qe=(0,N.Ay)(_n.E)`
  ${({theme:e,condensed:t})=>t&&`
	  position: absolute;
    // Values based on visual aspect 
    top: -${e.spaces[3]};
    right:  -${e.spaces[1]};
  `}

  ${b.o} {
    //find a solution to remove !important
    color: ${({theme:e})=>e.colors.neutral0} !important;
    line-height: 0;
  }

  display: flex;
  justify-content: center;
  align-items: center;
  min-width: ${({theme:e})=>e.spaces[6]};
  height: ${({theme:e})=>e.spaces[5]};
  padding: ${({theme:e})=>`0 ${e.spaces[2]}`};
  border-radius: ${({theme:e})=>e.spaces[10]};
  background: ${({theme:e})=>e.colors.primary600};
`,Se=g.forwardRef(({children:e,icon:t,badgeContent:n,badgeAriaLabel:s,...r},o)=>q()?(0,i.jsx)(Qe,{ref:o,...r,children:(0,i.jsx)(er.m,{position:"right",label:e,children:(0,i.jsxs)(Je,{as:"span",justifyContent:"center",children:[(0,i.jsx)(Ze,{"aria-hidden":!0,paddingRight:0,as:"span",children:t}),n&&(0,i.jsx)(qe,{condensed:!0,"aria-label":s,children:n})]})})}):(0,i.jsx)(Qe,{ref:o,...r,children:(0,i.jsxs)(Je,{as:"span",justifyContent:"space-between",children:[(0,i.jsxs)(x.s,{children:[(0,i.jsx)(Ze,{"aria-hidden":!0,paddingRight:3,as:"span",children:t}),(0,i.jsx)(b.o,{children:e})]}),n&&(0,i.jsx)(qe,{justifyContent:"center","aria-label":s,children:n})]})})),_e=({label:e,children:t,horizontal:n=!1,spacing:s=2,...r})=>q()?(0,i.jsxs)(x.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,i.jsxs)(j.a,{paddingTop:1,paddingBottom:1,background:"neutral0",hasRadius:!0,as:"span",children:[(0,i.jsx)(ye.c,{}),(0,i.jsx)(K.s,{children:(0,i.jsx)("span",{children:e})})]}),(0,i.jsx)(x.s,{as:"ul",gap:s,direction:n?"row":"column",alignItems:n?"center":"stretch",...r,children:g.Children.map(t,(d,c)=>(0,i.jsx)("li",{children:d},c))})]}):(0,i.jsxs)(x.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,i.jsx)(j.a,{paddingTop:1,paddingBottom:1,background:"neutral0",paddingRight:3,paddingLeft:3,hasRadius:!0,as:"span",children:(0,i.jsx)(b.o,{variant:"sigma",textColor:"neutral600",children:e})}),(0,i.jsx)(x.s,{as:"ul",gap:s,direction:n?"row":"column",alignItems:n?"center":"stretch",...r,children:g.Children.map(t,(d,c)=>(0,i.jsx)("li",{children:d},c))})]}),tr=({children:e})=>(0,i.jsx)(j.a,{position:"relative",children:e});var et=a(18809);const nr=(0,N.Ay)(j.a)`
  border-top: 1px solid ${({theme:e})=>e.colors.neutral150};
`,rr=g.forwardRef(({src:e,children:t,initials:n,...s},r)=>{const o=q();return(0,i.jsx)(nr,{paddingTop:3,paddingBottom:3,paddingLeft:5,paddingRight:5,...s,children:(0,i.jsxs)(x.s,{as:"button",justifyContent:o?"center":void 0,ref:r,children:[e?(0,i.jsx)(et.e,{src:e,alt:"","aria-hidden":!0}):(0,i.jsx)(et.g,{children:n}),o?(0,i.jsx)(K.s,{children:(0,i.jsx)("span",{children:t})}):(0,i.jsx)(j.a,{width:`${130/16}rem`,paddingLeft:2,as:"span",children:(0,i.jsx)(b.o,{ellipsis:!0,textColor:"neutral600",children:t})})]})})});var sr=a(16991),ir=a(72156);const or=(0,N.Ay)(x.s).attrs(e=>({justifyContent:"center",...e}))`
  background: ${({theme:e})=>e.colors.neutral0};
  border: 1px solid ${({theme:e})=>e.colors.neutral150};
  border-radius: ${({theme:e})=>e.borderRadius};
  position: absolute;
  bottom: ${(9+4)/16}rem; // 9 is the height of the svg and 4 is the padding below
  right: ${({theme:e,condensed:t})=>t?0:e.spaces[5]};
  transform: ${({condensed:e})=>e?"translateX(50%)":void 0};
  z-index: 2;
  width: ${18/16}rem;
  height: ${25/16}rem;

  svg {
    width: ${6/16}rem;
    height: ${9/16}rem;
  }
`,ar=({children:e,...t})=>{const n=q();return(0,i.jsxs)(or,{as:"button",condensed:n,...t,children:[(0,i.jsx)(J.I,{as:n?sr.A:ir.A,"aria-hidden":!0,color:"neutral600"}),(0,i.jsx)(K.s,{children:e})]})};var cr=a(54514),tt=a(68802),nt=a(72417),dr=a(29047),lr=a(66980),ur=a(28442),hr=a(39527),pr=a(57574),gr=a(98052),fr=a(54429),mr=a(64486),Er=a(38910),vr=a(21396),Tr=a(20415),yr=a(2600),Z=a(54894),Ir=a(51187),Sr=a(82437);const xr={name:"@strapi/admin",version:"4.25.22",description:"Strapi Admin",repository:{type:"git",url:"git://github.com/strapi/strapi.git"},license:"SEE LICENSE IN LICENSE",author:{name:"Strapi Solutions SAS",email:"<EMAIL>",url:"https://strapi.io"},maintainers:[{name:"Strapi Solutions SAS",email:"<EMAIL>",url:"https://strapi.io"}],exports:{".":{types:"./index.d.ts",source:"./index.js",require:"./index.js",default:"./index.js"},"./strapi-admin":{types:"./dist/admin/src/index.d.ts",source:"./admin/src/index.ts",import:"./dist/admin/index.mjs",require:"./dist/admin/index.js",default:"./dist/admin/index.js"},"./strapi-server":{types:"./dist/server/src/index.d.ts",source:"./server/src/index.js",require:"./strapi-server.js",default:"./strapi-server.js"},"./cli":{source:"./_internal/index.ts",import:"./dist/cli.mjs",require:"./dist/cli.js",default:"./dist/cli.js"},"./package.json":"./package.json"},files:["./dist","strapi-server.js","index.js","index.d.ts"],scripts:{build:"pack-up build",clean:"run -T rimraf ./dist",lint:"run -T eslint .","test:front":"run -T cross-env IS_EE=true jest --config ./jest.config.front.js","test:front:watch":"run -T cross-env IS_EE=true jest --config ./jest.config.front.js --watchAll","test:ts":"run -T tsc -p tsconfig.json","test:ts:back":"run -T tsc --noEmit -p server/tsconfig.json","test:ts:front":"run -T tsc -p admin/tsconfig.json && run -T tsc -p ee/admin/tsconfig.json","test:unit":"run -T jest","test:unit:watch":"run -T jest --watch",watch:"pack-up watch"},dependencies:{"@casl/ability":"6.5.0","@pmmmwh/react-refresh-webpack-plugin":"0.5.11","@radix-ui/react-context":"1.0.1","@radix-ui/react-toolbar":"1.0.4","@reduxjs/toolkit":"1.9.7","@strapi/design-system":"1.19.0","@strapi/helper-plugin":"4.25.22","@strapi/icons":"1.19.0","@strapi/permissions":"4.25.22","@strapi/provider-audit-logs-local":"4.25.22","@strapi/types":"4.25.22","@strapi/typescript-utils":"4.25.22","@strapi/utils":"4.25.22","@vitejs/plugin-react-swc":"3.5.0",axios:"1.8.2",bcryptjs:"2.4.3",boxen:"5.1.2",browserslist:"^4.22.2","browserslist-to-esbuild":"1.2.0",chalk:"^4.1.2",chokidar:"3.5.3",codemirror5:"npm:codemirror@^5.65.11","cross-env":"^7.0.3","css-loader":"^6.9.0","date-fns":"2.30.0",dotenv:"14.2.0",esbuild:"0.19.11","esbuild-loader":"^2.21.0","esbuild-register":"3.5.0",execa:"5.1.1","fast-deep-equal":"3.1.3","find-root":"1.1.0","fork-ts-checker-webpack-plugin":"9.0.2",formik:"2.4.0","fractional-indexing":"3.2.0","fs-extra":"10.0.0","highlight.js":"^10.4.1",history:"^4.9.0","html-webpack-plugin":"5.6.0",immer:"9.0.19",inquirer:"8.2.5",invariant:"^2.2.4","is-localhost-ip":"2.0.0","js-cookie":"2.2.1",jsonwebtoken:"9.0.0",koa:"2.16.1","koa-bodyparser":"4.4.1","koa-compose":"4.1.0","koa-passport":"5.0.0","koa-static":"5.0.0","koa2-ratelimit":"^1.1.2",lodash:"4.17.21","markdown-it":"^12.3.2","markdown-it-abbr":"^1.0.4","markdown-it-container":"^3.0.0","markdown-it-deflist":"^2.1.0","markdown-it-emoji":"^2.0.0","markdown-it-footnote":"^3.0.3","markdown-it-ins":"^3.0.1","markdown-it-mark":"^3.0.1","markdown-it-sub":"^1.0.0","markdown-it-sup":"1.0.0","mini-css-extract-plugin":"2.7.7","node-schedule":"2.1.1",ora:"5.4.1",outdent:"0.8.0","p-map":"4.0.0","passport-local":"1.0.0",pluralize:"8.0.0",prettier:"2.8.4","prop-types":"^15.8.1",punycode:"2.3.1",qs:"6.11.1","react-dnd":"16.0.1","react-dnd-html5-backend":"16.0.1","react-error-boundary":"3.1.4","react-helmet":"^6.1.0","react-intl":"6.4.1","react-is":"^18.2.0","react-query":"3.39.3","react-redux":"8.1.1","react-refresh":"0.14.0","react-select":"5.7.0","react-window":"1.8.8","read-pkg-up":"7.0.1","resolve-from":"5.0.0",rimraf:"3.0.2","sanitize-html":"2.13.0",semver:"7.5.4",sift:"16.0.1",slate:"0.94.1","slate-history":"0.93.0","slate-react":"0.98.3","style-loader":"3.3.4",typescript:"5.2.2",vite:"5.1.8",webpack:"^5.89.0","webpack-bundle-analyzer":"^4.10.1","webpack-dev-middleware":"6.1.2","webpack-hot-middleware":"2.26.0",yup:"0.32.9"},devDependencies:{"@strapi/admin-test-utils":"4.25.22","@strapi/data-transfer":"4.25.22","@strapi/pack-up":"4.23.0","@strapi/plugin-content-manager":"4.25.22","@strapi/strapi":"4.25.22","@testing-library/dom":"9.2.0","@testing-library/react":"14.0.0","@testing-library/user-event":"14.4.3","@types/codemirror5":"npm:@types/codemirror@^5.60.15","@types/find-root":"1.1.3","@types/invariant":"2.2.36","@types/jest":"29.5.2","@types/js-cookie":"3.0.5","@types/jsonwebtoken":"9.0.3","@types/koa-passport":"6.0.1","@types/lodash":"^4.14.191","@types/markdown-it":"13.0.7","@types/markdown-it-container":"2.0.9","@types/markdown-it-emoji":"2.0.4","@types/markdown-it-footnote":"3.0.3","@types/passport-local":"1.0.36","@types/pluralize":"0.0.32","@types/punycode":"2.1.4","@types/react-window":"1.8.8","@types/sanitize-html":"2.11.0","@types/webpack-bundle-analyzer":"4.6.3","@types/webpack-hot-middleware":"2.25.9",msw:"1.3.0",react:"^18.2.0","react-dom":"^18.2.0","react-router-dom":"5.3.4","styled-components":"5.3.3"},peerDependencies:{"@strapi/data-transfer":"^4.16.0","@strapi/strapi":"^4.3.4",react:"^17.0.0 || ^18.0.0","react-dom":"^17.0.0 || ^18.0.0","react-router-dom":"^5.2.0","styled-components":"^5.2.1"},engines:{node:">=18.0.0 <=20.x.x",npm:">=6.0.0"},nx:{targets:{build:{outputs:["{projectRoot}/build"]}}}};function xe(e,t=""){return[e,t].filter(n=>n).join(" ")}const Or={bufferToHex(e){return[...new Uint8Array(e)].map(t=>t.toString(16).padStart(2,"0")).join("")},async digestMessage(e){const t=new TextEncoder().encode(e),n=await crypto.subtle.digest("SHA-256",t);return this.bufferToHex(n)}};async function Dr(e){if(!e||!e.email)return null;try{return await Or.digestMessage(e.email)}catch{return null}}const rt={contentTypeBuilder:{home:{title:{id:"app.components.GuidedTour.home.CTB.title",defaultMessage:"\u{1F9E0} Build the content structure"},cta:{title:{id:"app.components.GuidedTour.home.CTB.cta.title",defaultMessage:"Go to the Content type Builder"},type:"REDIRECT",target:"/plugins/content-type-builder"},trackingEvent:"didClickGuidedTourHomepageContentTypeBuilder"},create:{title:{id:"app.components.GuidedTour.CTB.create.title",defaultMessage:"\u{1F9E0} Create a first Collection type"},content:{id:"app.components.GuidedTour.CTB.create.content",defaultMessage:"<p>Collection types help you manage several entries, Single types are suitable to manage only one entry.</p> <p>Ex: For a Blog website, Articles would be a Collection type whereas a Homepage would be a Single type.</p>"},cta:{title:{id:"app.components.GuidedTour.CTB.create.cta.title",defaultMessage:"Build a Collection type"},type:"CLOSE"},trackingEvent:"didClickGuidedTourStep1CollectionType"},success:{title:{id:"app.components.GuidedTour.CTB.success.title",defaultMessage:"Step 1: Completed \u2705"},content:{id:"app.components.GuidedTour.CTB.success.content",defaultMessage:"<p>Good going!</p><b>\u26A1\uFE0F What would you like to share with the world?</b>"},cta:{title:{id:"app.components.GuidedTour.create-content",defaultMessage:"Create content"},type:"REDIRECT",target:"/content-manager"},trackingEvent:"didCreateGuidedTourCollectionType"}},contentManager:{home:{title:{id:"app.components.GuidedTour.home.CM.title",defaultMessage:"\u26A1\uFE0F What would you like to share with the world?"},cta:{title:{id:"app.components.GuidedTour.create-content",defaultMessage:"Create content"},type:"REDIRECT",target:"/content-manager"},trackingEvent:"didClickGuidedTourHomepageContentManager"},create:{title:{id:"app.components.GuidedTour.CM.create.title",defaultMessage:"\u26A1\uFE0F Create content"},content:{id:"app.components.GuidedTour.CM.create.content",defaultMessage:"<p>Create and manage all the content here in the Content Manager.</p><p>Ex: Taking the Blog website example further, one can write an Article, save and publish it as they like.</p><p>\u{1F4A1} Quick tip - Don't forget to hit publish on the content you create.</p>"},cta:{title:{id:"app.components.GuidedTour.create-content",defaultMessage:"Create content"},type:"CLOSE"},trackingEvent:"didClickGuidedTourStep2ContentManager"},success:{title:{id:"app.components.GuidedTour.CM.success.title",defaultMessage:"Step 2: Completed \u2705"},content:{id:"app.components.GuidedTour.CM.success.content",defaultMessage:"<p>Awesome, one last step to go!</p><b>\u{1F680}  See content in action</b>"},cta:{title:{id:"app.components.GuidedTour.CM.success.cta.title",defaultMessage:"Test the API"},type:"REDIRECT",target:"/settings/api-tokens"},trackingEvent:"didCreateGuidedTourEntry"}},apiTokens:{home:{title:{id:"app.components.GuidedTour.apiTokens.create.title",defaultMessage:"\u{1F680} See content in action"},cta:{title:{id:"app.components.GuidedTour.home.apiTokens.cta.title",defaultMessage:"Test the API"},type:"REDIRECT",target:"/settings/api-tokens"},trackingEvent:"didClickGuidedTourHomepageApiTokens"},create:{title:{id:"app.components.GuidedTour.apiTokens.create.title",defaultMessage:"\u{1F680} See content in action"},content:{id:"app.components.GuidedTour.apiTokens.create.content",defaultMessage:"<p>Generate an authentication token here and retrieve the content you just created.</p>"},cta:{title:{id:"app.components.GuidedTour.apiTokens.create.cta.title",defaultMessage:"Generate an API Token"},type:"CLOSE"},trackingEvent:"didClickGuidedTourStep3ApiTokens"},success:{title:{id:"app.components.GuidedTour.apiTokens.success.title",defaultMessage:"Step 3: Completed \u2705"},content:{id:"app.components.GuidedTour.apiTokens.success.content",defaultMessage:"<p>See content in action by making an HTTP request:</p><ul><li><p>To this URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>With the header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>For more ways to interact with content, see the <documentationLink>documentation</documentationLink>.</p>"},trackingEvent:"didGenerateGuidedTourApiTokens"}}},F={IS_DONE:"IS_DONE",IS_ACTIVE:"IS_ACTIVE",IS_NOT_DONE:"IS_NOT_DONE"},Oe=({children:e,state:t,...n})=>t===F.IS_DONE||t===F.IS_ACTIVE?(0,i.jsx)(x.s,{background:"primary600",padding:2,borderRadius:"50%",width:(0,m.a8)(30),height:(0,m.a8)(30),justifyContent:"center",...n,children:t===F.IS_DONE?(0,i.jsx)(cr.A,{"aria-hidden":!0,width:(0,m.a8)(16),color:"neutral0"}):(0,i.jsx)(b.o,{fontWeight:"semiBold",textColor:"neutral0",children:e})}):(0,i.jsx)(x.s,{borderColor:"neutral500",borderWidth:"1px",borderStyle:"solid",padding:2,borderRadius:"50%",width:(0,m.a8)(30),height:(0,m.a8)(30),justifyContent:"center",...n,children:(0,i.jsx)(b.o,{fontWeight:"semiBold",textColor:"neutral600",children:e})}),oe=({state:e,...t})=>(0,i.jsx)(j.a,{width:(0,m.a8)(2),height:"100%",background:e===F.IS_NOT_DONE?"neutral300":"primary500",hasRadius:!0,minHeight:e===F.IS_ACTIVE?(0,m.a8)(85):(0,m.a8)(65),...t}),br=()=>{const{currentStep:e,guidedTourState:t,setCurrentStep:n,setStepState:s,isGuidedTourVisible:r,setSkipped:o}=(0,m.Cx)(),{formatMessage:d}=(0,Z.A)(),{trackUsage:c}=(0,m.z1)();if(!e||!r)return null;const p=yr(rt,e),E=Object.keys(t),[I,$]=e.split("."),C=E.indexOf(I),A=Object.keys(t[I]).indexOf($),y=C<E.length-1,k=A<Object.keys(t[I]).length-1,w=()=>{s(e,!0),p&&c(p.trackingEvent),n(null)},P=()=>{o(!0),n(null),c("didSkipGuidedtour")};return(0,i.jsx)(ze.Z,{children:(0,i.jsx)(Nr,{onClick:w,padding:8,justifyContent:"center",children:(0,i.jsx)(We.s,{onEscape:w,children:(0,i.jsxs)(x.s,{direction:"column",alignItems:"stretch",background:"neutral0",width:(0,m.a8)(660),shadow:"popupShadow",hasRadius:!0,padding:4,gap:8,role:"dialog","aria-modal":!0,onClick:T=>T.stopPropagation(),children:[(0,i.jsx)(x.s,{justifyContent:"flex-end",children:(0,i.jsx)(Hn.K,{onClick:w,"aria-label":d({id:"app.utils.close-label",defaultMessage:"Close"}),children:(0,i.jsx)(tt.A,{})})}),(0,i.jsx)(j.a,{paddingLeft:7,paddingRight:7,paddingBottom:!k&&!y?8:0,children:(0,i.jsx)(jr,{title:p&&"title"in p?p.title:void 0,cta:p&&"cta"in p?p.cta:void 0,onCtaClick:w,sectionIndex:C,stepIndex:A,hasSectionAfter:y,children:p&&"content"in p&&(0,i.jsx)(Cr,{...p.content})})}),!(!k&&!y)&&(0,i.jsx)(x.s,{justifyContent:"flex-end",children:(0,i.jsx)(Te.$,{variant:"tertiary",onClick:P,children:d({id:"app.components.GuidedTour.skip",defaultMessage:"Skip the tour"})})})]})})})})},Nr=(0,N.Ay)(x.s)`
  position: fixed;
  z-index: 4;
  inset: 0;
  /* this is theme.colors.neutral800 with opacity */
  background: ${({theme:e})=>`${e.colors.neutral800}1F`};
`,jr=({title:e,children:t,cta:n,onCtaClick:s,sectionIndex:r,stepIndex:o,hasSectionAfter:d})=>{const{formatMessage:c}=(0,Z.A)(),p=r>0,E=o>0,I=r+1;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(x.s,{alignItems:"stretch",children:[(0,i.jsx)(x.s,{marginRight:8,justifyContent:"center",minWidth:(0,m.a8)(30),children:p&&(0,i.jsx)(oe,{state:F.IS_DONE,minHeight:(0,m.a8)(24)})}),(0,i.jsx)(b.o,{variant:"sigma",textColor:"primary600",children:c({id:"app.components.GuidedTour.title",defaultMessage:"3 steps to get started"})})]}),(0,i.jsxs)(x.s,{children:[(0,i.jsx)(x.s,{marginRight:8,minWidth:(0,m.a8)(30),children:(0,i.jsx)(Oe,{state:E?F.IS_DONE:F.IS_ACTIVE,paddingTop:3,paddingBottom:3,children:r+1})}),e&&(0,i.jsx)(b.o,{variant:"alpha",fontWeight:"bold",textColor:"neutral800",as:"h3",id:"title",children:c(e)})]}),(0,i.jsxs)(x.s,{alignItems:"stretch",children:[(0,i.jsx)(x.s,{marginRight:8,direction:"column",justifyContent:"center",minWidth:(0,m.a8)(30),children:d&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(oe,{state:F.IS_DONE}),E&&(0,i.jsx)(Oe,{state:F.IS_ACTIVE,paddingTop:3,children:I+1})]})}),(0,i.jsxs)(j.a,{children:[t,n&&(n.target?(0,i.jsx)(Wn.z,{as:Y.k2,endIcon:(0,i.jsx)(nt.A,{}),onClick:s,to:n.target,children:c(n.title)}):(0,i.jsx)(Te.$,{endIcon:(0,i.jsx)(nt.A,{}),onClick:s,children:c(n.title)}))]})]}),E&&d&&(0,i.jsx)(j.a,{paddingTop:3,children:(0,i.jsx)(x.s,{marginRight:8,justifyContent:"center",width:(0,m.a8)(30),children:(0,i.jsx)(oe,{state:F.IS_DONE,minHeight:(0,m.a8)(24)})})})]})},Cr=({id:e,defaultMessage:t})=>{const{formatMessage:n}=(0,Z.A)();return(0,i.jsx)(x.s,{direction:"column",alignItems:"stretch",gap:4,paddingBottom:6,children:n({id:e,defaultMessage:t},{documentationLink:Ar,b:Lr,p:Rr,light:wr,ul:Pr,li:kr})})},Ar=e=>(0,i.jsx)(b.o,{as:"a",textColor:"primary600",target:"_blank",rel:"noopener noreferrer",href:"https://docs.strapi.io/developer-docs/latest/developer-resources/database-apis-reference/rest-api.html#api-parameters",children:e}),Lr=e=>(0,i.jsx)(b.o,{fontWeight:"semiBold",children:e}),Rr=e=>(0,i.jsx)(b.o,{children:e}),wr=e=>(0,i.jsx)(b.o,{textColor:"neutral600",children:e}),Pr=e=>(0,i.jsx)(j.a,{paddingLeft:6,children:(0,i.jsx)("ul",{children:e})}),Mr=N.Ay.li`
  list-style: disc;
  &::marker {
    color: ${({theme:e})=>e.colors.neutral800};
  }
`,kr=e=>(0,i.jsx)(Mr,{children:e}),$r=(0,N.Ay)(j.a)`
  width: ${150/16}rem;
  position: absolute;
  bottom: ${({theme:e})=>e.spaces[9]};
  left: ${({theme:e})=>e.spaces[5]};
`,st=(0,N.Ay)(Y.k2)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-decoration: none;
  padding: ${({theme:e})=>`${e.spaces[2]} ${e.spaces[4]}`};
  border-radius: ${({theme:e})=>e.spaces[1]};

  &:hover {
    background: ${({theme:e,logout:t})=>t?e.colors.danger100:e.colors.primary100};
    text-decoration: none;
  }

  svg {
    path {
      fill: ${({theme:e})=>e.colors.danger600};
    }
  }
`,Xr=(0,N.Ay)(j.a)`
  div:nth-child(2) {
    /* remove badge background color */
    background: transparent;
  }
`,Gr=(0,N.Ay)(J.I)`
  &&& {
    path {
      fill: ${({theme:e})=>e.colors.warning500};
    }
  }
`,Ur=({generalSectionLinks:e,pluginsSectionLinks:t})=>{const n=g.useRef(null),[s,r]=g.useState(!1),{logos:{menu:o}}=(0,l.u)("LeftMenu"),[d,c]=(0,m.ud)("navbar-condensed",!1),{userDisplayName:p}=(0,m.Xe)(),{formatMessage:E}=(0,Z.A)(),{trackUsage:I}=(0,m.z1)(),{pathname:$}=(0,U.zy)(),{logout:C}=(0,l.a)("Logout"),A=p.split(" ").map(T=>T.substring(0,1)).join("").substring(0,2),y=()=>r(T=>!T),k=T=>{!T.currentTarget.contains(T.relatedTarget)&&T.relatedTarget?.parentElement?.id!=="main-nav-user-button"&&r(!1)},w=T=>{I("willNavigate",{from:$,to:T})},P=E({id:"app.components.LeftMenu.navbrand.title",defaultMessage:"Strapi Dashboard"});return(0,i.jsxs)(Kn,{condensed:d,children:[(0,i.jsx)(Qn,{as:Y.k2,workplace:E({id:"app.components.LeftMenu.navbrand.workplace",defaultMessage:"Workplace"}),title:P,icon:(0,i.jsx)("img",{src:o.custom?.url||o.default,alt:E({id:"app.components.LeftMenu.logo.alt",defaultMessage:"Application logo"})})}),(0,i.jsx)(ye.c,{}),(0,i.jsxs)(qn,{children:[(0,i.jsx)(Se,{as:Y.k2,to:"/content-manager",icon:(0,i.jsx)(dr.A,{}),onClick:()=>w("/content-manager"),children:E({id:"global.content-manager",defaultMessage:"Content manager"})}),t.length>0?(0,i.jsx)(_e,{label:E({id:"app.components.LeftMenu.plugins",defaultMessage:"Plugins"}),children:t.map(T=>{const V=T.icon;return(0,i.jsx)(Xr,{children:(0,i.jsx)(Se,{as:Y.k2,to:T.to,icon:(0,i.jsx)(V,{}),onClick:()=>w(T.to),badgeContent:T?.lockIcon?(0,i.jsx)(Gr,{width:`${15/16}rem`,height:`${15/16}rem`,as:lr.A}):void 0,children:E(T.intlLabel)})},T.to)})}):null,e.length>0?(0,i.jsx)(_e,{label:E({id:"app.components.LeftMenu.general",defaultMessage:"General"}),children:e.map(T=>{const V=T.icon;return(0,i.jsx)(Se,{as:Y.k2,badgeContent:T.notificationsCount&&T.notificationsCount>0?T.notificationsCount.toString():void 0,to:T.to,icon:(0,i.jsx)(V,{}),onClick:()=>w(T.to),children:E(T.intlLabel)},T.to)})}):null]}),(0,i.jsxs)(tr,{children:[(0,i.jsx)(rr,{id:"main-nav-user-button",ref:n,onClick:y,initials:A,children:p}),s&&(0,i.jsx)($r,{onBlur:k,padding:1,shadow:"tableShadow",background:"neutral0",hasRadius:!0,children:(0,i.jsx)(We.s,{onEscape:y,children:(0,i.jsxs)(x.s,{direction:"column",alignItems:"stretch",gap:0,children:[(0,i.jsx)(st,{tabIndex:0,onClick:y,to:"/me",children:(0,i.jsx)(b.o,{children:E({id:"global.profile",defaultMessage:"Profile"})})}),(0,i.jsxs)(st,{tabIndex:0,onClick:C,to:"/auth/login",children:[(0,i.jsx)(b.o,{textColor:"danger600",children:E({id:"app.components.LeftMenu.logout",defaultMessage:"Logout"})}),(0,i.jsx)(ur.A,{})]})]})})}),(0,i.jsx)(ar,{onClick:()=>c(T=>!T),children:E(d?{id:"app.components.LeftMenu.expand",defaultMessage:"Expand the navbar"}:{id:"app.components.LeftMenu.collapse",defaultMessage:"Collapse the navbar"})})]})]})},Fr="data:image/png;base64,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",Br=()=>{const e=g.useRef(null),[t,n]=g.useState(!1),{formatMessage:s}=(0,Z.A)(),{communityEdition:r}=(0,m.Xe)(),o=()=>{n(c=>!c)},d=[...Kr,{label:{id:"Settings.application.get-help",defaultMessage:"Get help"},icon:hr.A,href:r?"https://discord.strapi.io":"https://support.strapi.io/support/home"}];return(0,i.jsxs)(j.a,{as:"aside",position:"fixed",bottom:2,right:2,children:[(0,i.jsx)(Hr,{"aria-label":s(t?{id:"app.components.Onboarding.help.button-close",defaultMessage:"Close help menu"}:{id:"app.components.Onboarding.help.button",defaultMessage:"Open help menu"}),onClick:o,ref:e,children:(0,i.jsx)(J.I,{as:t?tt.A:pr.A,color:"buttonNeutral0"})}),t&&(0,i.jsx)(ze.Z,{children:(0,i.jsxs)(Vn.UC,{padding:0,onDismiss:o,source:e,placement:"top-end",spacing:12,children:[(0,i.jsxs)(x.s,{justifyContent:"space-between",paddingBottom:5,paddingRight:6,paddingLeft:6,paddingTop:6,children:[(0,i.jsx)(it,{fontWeight:"bold",children:s({id:"app.components.Onboarding.title",defaultMessage:"Get started videos"})}),(0,i.jsx)(ot,{as:"a",href:at.href,target:"_blank",rel:"noreferrer noopener",variant:"pi",textColor:"primary600",children:s(at.label)})]}),(0,i.jsx)(ye.c,{}),Yr.map(({href:c,duration:p,label:E},I)=>(0,i.jsxs)(zr,{as:"a",href:c,target:"_blank",rel:"noreferrer noopener",hasRadius:!0,paddingTop:4,paddingBottom:4,paddingLeft:6,paddingRight:11,children:[(0,i.jsx)(j.a,{paddingRight:5,children:(0,i.jsx)(b.o,{textColor:"neutral200",variant:"alpha",children:I+1})}),(0,i.jsxs)(j.a,{position:"relative",children:[(0,i.jsx)(Wr,{src:Fr,alt:""}),(0,i.jsx)(Vr,{position:"absolute",top:"50%",left:"50%",background:"primary600",borderRadius:"50%",justifyContent:"center",width:6,height:6,children:(0,i.jsx)(J.I,{as:gr.A,color:"buttonNeutral0",width:3,height:3})})]}),(0,i.jsxs)(x.s,{direction:"column",alignItems:"start",paddingLeft:4,children:[(0,i.jsx)(b.o,{fontWeight:"bold",children:s(E)}),(0,i.jsx)(K.s,{children:":"}),(0,i.jsx)(b.o,{textColor:"neutral600",variant:"pi",children:p})]})]},c)),(0,i.jsx)(x.s,{direction:"column",alignItems:"stretch",gap:2,paddingLeft:5,paddingTop:2,paddingBottom:5,children:d.map(({label:c,href:p,icon:E})=>(0,i.jsxs)(x.s,{gap:3,children:[(0,i.jsx)(J.I,{as:E,color:"primary600"}),(0,i.jsx)(ot,{as:"a",href:p,target:"_blank",rel:"noreferrer noopener",variant:"sigma",textColor:"primary700",children:s(c)})]},p))})]})})]})},Hr=(0,N.Ay)(Te.$)`
  border-radius: 50%;
  padding: ${({theme:e})=>e.spaces[3]};
  /* Resetting 2rem height defined by Button component */
  height: 100%;
`,Vr=(0,N.Ay)(x.s)`
  transform: translate(-50%, -50%);
`,zr=(0,N.Ay)(x.s)`
  text-decoration: none;

  :focus-visible {
    outline-offset: ${({theme:e})=>`-${e.spaces[1]}`};
  }

  :hover {
    background: ${({theme:e})=>e.colors.primary100};

    /* Hover style for the number displayed */
    ${b.o}:first-child {
      color: ${({theme:e})=>e.colors.primary500};
    }

    /* Hover style for the label */
    ${b.o}:nth-child(1) {
      color: ${({theme:e})=>e.colors.primary600};
    }
  }
`,Wr=N.Ay.img`
  width: ${({theme:e})=>e.spaces[10]};
  height: ${({theme:e})=>e.spaces[8]};
  /* Same overlay used in ModalLayout */
  background: ${({theme:e})=>`${e.colors.neutral800}1F`};
  border-radius: ${({theme:e})=>e.borderRadius};
`,it=(0,N.Ay)(b.o)`
  /* line height of label and watch more to 1 so they can be better aligned visually */
  line-height: 1;
`,ot=(0,N.Ay)(it)`
  text-decoration: none;

  :hover {
    text-decoration: underline;
  }
`,Yr=[{label:{id:"app.components.Onboarding.link.build-content",defaultMessage:"Build a content architecture"},href:"https://www.youtube.com/watch?v=G9GjN0RxhkE",duration:"5:48"},{label:{id:"app.components.Onboarding.link.manage-content",defaultMessage:"Add & manage content"},href:"https://www.youtube.com/watch?v=DEZw4KbybAI",duration:"3:18"},{label:{id:"app.components.Onboarding.link.manage-media",defaultMessage:"Manage media"},href:"https://www.youtube.com/watch?v=-61MuiMQb38",duration:"3:41"}],at={href:"https://www.youtube.com/playlist?list=PL7Q0DQYATmvidz6lEmwE5nIcOAYagxWqq",label:{id:"app.components.Onboarding.link.more-videos",defaultMessage:"Watch more videos"}},Kr=[{label:{id:"global.documentation",defaultMessage:"documentation"},href:"https://docs.strapi.io",icon:fr.A},{label:{id:"app.static.links.cheatsheet",defaultMessage:"cheatsheet"},href:"https://strapi-showcase.s3-us-west-2.amazonaws.com/CheatSheet.pdf",icon:mr.A}],ct=(0,l.c)(e=>e.admin_app.permissions),Zr=()=>{const{allPermissions:e}=(0,m.r5)(),{shouldUpdateStrapi:t}=(0,m.Xe)(),{menu:n}=(0,m.vD)(),s=(0,Sr.d4)(ct),[r,o]=g.useState({generalSectionLinks:[{icon:Er.A,intlLabel:{id:"global.plugins",defaultMessage:"Plugins"},to:"/list-plugins",permissions:s.marketplace?.main??[]},{icon:vr.A,intlLabel:{id:"global.marketplace",defaultMessage:"Marketplace"},to:"/marketplace",permissions:s.marketplace?.main??[]},{icon:Tr.A,intlLabel:{id:"global.settings",defaultMessage:"Settings"},to:"/settings",permissions:[],notificationsCount:0}],pluginsSectionLinks:[],isLoading:!0}),d=g.useRef(r.generalSectionLinks);return g.useEffect(()=>{async function c(){const p=await Jr(e,n),E=await Qr(e,d.current,t);o(I=>({...I,generalSectionLinks:E,pluginsSectionLinks:p,isLoading:!1}))}c()},[o,d,e,n,s,t]),r},Qr=async(e,t,n=!1)=>{const s=await Promise.all(t.map(({permissions:c})=>(0,m.v$)(e,c))),r=t.filter((c,p)=>s[p]),o=r.findIndex(c=>c.to==="/settings");if(o===-1)return[];const d=Ir(r);return d[o].notificationsCount=n?1:0,d},Jr=async(e,t)=>{const n=await Promise.all(t.map(({permissions:r})=>(0,m.v$)(e,r)));return t.filter((r,o)=>n[o])},qr=(0,N.Ay)(j.a)`
  flex: 1;
`,_r=({children:e,sideNav:t})=>{const{formatMessage:n}=(0,Z.A)();return(0,i.jsxs)(j.a,{background:"neutral100",children:[(0,i.jsx)(zn.o,{children:n({id:"skipToContent",defaultMessage:"Skip to content"})}),(0,i.jsxs)(x.s,{alignItems:"flex-start",children:[t,(0,i.jsx)(qr,{children:e})]})]})},es=g.lazy(()=>Promise.resolve().then(a.bind(a,43543)).then(e=>e.a0).then(e=>({default:e.App}))),ts=g.lazy(()=>a.e(6213).then(a.bind(a,86213)).then(e=>({default:e.HomePage}))),ns=g.lazy(()=>a.e(5293).then(a.bind(a,25293)).then(e=>({default:e.ProtectedInstalledPluginsPage}))),rs=g.lazy(()=>a.e(3902).then(a.bind(a,43902)).then(e=>({default:e.ProtectedMarketplacePage}))),dt=g.lazy(()=>Promise.resolve().then(a.bind(a,43543)).then(e=>e.$).then(({NotFoundPage:e})=>({default:e}))),ss=g.lazy(()=>a.e(2301).then(a.bind(a,92301)).then(({InternalErrorPage:e})=>({default:e}))),is=g.lazy(()=>a.e(1314).then(a.bind(a,51314)).then(e=>({default:e.ProfilePage}))),lt=g.lazy(()=>a.e(1595).then(a.bind(a,51595)).then(e=>({default:e.SettingsPage}))),os=()=>{const{trackUsage:e}=(0,m.z1)(),{isLoading:t,generalSectionLinks:n,pluginsSectionLinks:s}=Zr(),{menu:r}=(0,m.vD)(),{showTutorials:o}=(0,l.u)("Admin");(0,l.b)(()=>{e("didAccessAuthenticatedAdministration")});const d=g.useMemo(()=>r.filter(c=>c.Component).map(({to:c,Component:p,exact:E})=>(0,l.d)(p,c,E)),[r]);return t?(0,i.jsx)(m.Bl,{}):(0,i.jsx)(bn,{backend:Bn,children:(0,i.jsxs)(_r,{sideNav:(0,i.jsx)(Ur,{generalSectionLinks:n,pluginsSectionLinks:s}),children:[(0,i.jsx)(g.Suspense,{fallback:(0,i.jsx)(m.Bl,{}),children:(0,i.jsxs)(U.dO,{children:[(0,i.jsx)(U.qh,{path:"/",component:ts,exact:!0}),(0,i.jsx)(U.qh,{path:"/me",component:is,exact:!0}),(0,i.jsx)(U.qh,{path:"/content-manager",component:es}),d,(0,i.jsx)(U.qh,{path:"/settings/:settingId",component:lt}),(0,i.jsx)(U.qh,{path:"/settings",component:lt,exact:!0}),(0,i.jsx)(U.qh,{path:"/marketplace",children:(0,i.jsx)(rs,{})}),(0,i.jsx)(U.qh,{path:"/list-plugins",exact:!0,children:(0,i.jsx)(ns,{})}),(0,i.jsx)(U.qh,{path:"/404",component:dt}),(0,i.jsx)(U.qh,{path:"/500",component:ss}),(0,i.jsx)(U.qh,{path:"",component:dt})]})}),(0,i.jsx)(br,{}),o&&(0,i.jsx)(Br,{})]})})},as=()=>{const{plugins:e}=(0,m.vD)(),[{plugins:t},n]=g.useReducer(cs,ut,()=>ds(e)),s=g.useRef(o=>{n({type:"SET_PLUGIN_READY",pluginId:o})});if(Object.keys(t).some(o=>t[o].isReady===!1)){const o=Object.keys(t).reduce((d,c)=>{const p=t[c].initializer;if(p){const E=t[c].pluginId;d.push((0,i.jsx)(p,{setPlugin:s.current},E))}return d},[]);return(0,i.jsxs)(i.Fragment,{children:[o,(0,i.jsx)(m.Bl,{})]})}return(0,i.jsx)(os,{})},ut={plugins:{}},cs=(e=ut,t)=>(0,u.Ay)(e,n=>{switch(t.type){case"SET_PLUGIN_READY":{ae(n,["plugins",t.pluginId,"isReady"],!0);break}default:return n}}),ds=e=>({plugins:Object.keys(e).reduce((t,n)=>(t[n]={...e[n]},t),{})}),ht=xr.version,ls=()=>{const{setGuidedTourVisibility:e}=(0,m.Cx)(),{user:t}=(0,l.a)("AuthenticatedApp"),n=t,[s,r]=g.useState(()=>n?n.username||xe(n.firstname??"",n.lastname):"");g.useEffect(()=>{r(n?n.username||xe(n.firstname??"",n.lastname):"")},[n]);const[o,d]=g.useState(),{showReleaseNotification:c}=(0,l.u)("AuthenticatedApp"),{data:p,isLoading:E}=(0,l.e)(),{data:I,isLoading:$,refetch:C}=(0,l.f)(),[A,y]=g.useState(ht);g.useEffect(()=>{c&&fetch("https://api.github.com/repos/strapi/strapi/releases/latest").then(async P=>{if(!P.ok)throw new Error;const T=await P.json();if(!T.tag_name)throw new Error;y(T.tag_name)}).catch(()=>{})},[c]);const k=t?.roles;if(g.useEffect(()=>{k&&k.find(({code:T})=>T==="strapi-super-admin")&&p?.autoReload&&e(!0)},[k,p?.autoReload,e]),g.useEffect(()=>{Dr(n).then(P=>{P&&d(P)})},[n]),E||$)return(0,i.jsx)(m.Bl,{});const w=()=>{C()};return(0,i.jsx)(m.Bc,{...p,userId:o,latestStrapiReleaseTag:A,setUserDisplayName:r,shouldUpdateStrapi:us(ht,A),userDisplayName:s,children:(0,i.jsxs)(l.R,{permissions:I??[],refetchPermissions:w,children:[(0,i.jsx)(l.N,{}),(0,i.jsx)(as,{})]})})},us=(e,t="")=>!H(e)||!H(t)?!1:M(e,t),hs=Object.freeze(Object.defineProperty({__proto__:null,AuthenticatedApp:ls},Symbol.toStringTag,{value:"Module"}))},66980:(L,D,a)=>{"use strict";a.d(D,{A:()=>g});var O=a(92132);const i=m=>(0,O.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 22 30",...m,children:(0,O.jsx)("path",{fill:"#000",d:"m21.731 14.683-14 15a1 1 0 0 1-1.711-.875l1.832-9.167L.65 16.936a1 1 0 0 1-.375-1.625l14-15a1 1 0 0 1 1.71.875l-1.837 9.177 7.204 2.7a1 1 0 0 1 .375 1.62h.005Z"})}),g=i},79077:(L,D,a)=>{const O=a(1660),i=(g,m)=>{const M=O(g,m);return M?M.version:null};L.exports=i},85968:(L,D,a)=>{const O=a(47180),{MAX_LENGTH:i,MAX_SAFE_INTEGER:g}=a(97718),{safeRe:m,t:M}=a(88618),H=a(1231),{compareIdentifiers:l}=a(46087);class u{constructor(v,h){if(h=H(h),v instanceof u){if(v.loose===!!h.loose&&v.includePrerelease===!!h.includePrerelease)return v;v=v.version}else if(typeof v!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof v}".`);if(v.length>i)throw new TypeError(`version is longer than ${i} characters`);O("SemVer",v,h),this.options=h,this.loose=!!h.loose,this.includePrerelease=!!h.includePrerelease;const S=v.trim().match(h.loose?m[M.LOOSE]:m[M.FULL]);if(!S)throw new TypeError(`Invalid Version: ${v}`);if(this.raw=v,this.major=+S[1],this.minor=+S[2],this.patch=+S[3],this.major>g||this.major<0)throw new TypeError("Invalid major version");if(this.minor>g||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>g||this.patch<0)throw new TypeError("Invalid patch version");S[4]?this.prerelease=S[4].split(".").map(f=>{if(/^[0-9]+$/.test(f)){const R=+f;if(R>=0&&R<g)return R}return f}):this.prerelease=[],this.build=S[5]?S[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(v){if(O("SemVer.compare",this.version,this.options,v),!(v instanceof u)){if(typeof v=="string"&&v===this.version)return 0;v=new u(v,this.options)}return v.version===this.version?0:this.compareMain(v)||this.comparePre(v)}compareMain(v){return v instanceof u||(v=new u(v,this.options)),l(this.major,v.major)||l(this.minor,v.minor)||l(this.patch,v.patch)}comparePre(v){if(v instanceof u||(v=new u(v,this.options)),this.prerelease.length&&!v.prerelease.length)return-1;if(!this.prerelease.length&&v.prerelease.length)return 1;if(!this.prerelease.length&&!v.prerelease.length)return 0;let h=0;do{const S=this.prerelease[h],f=v.prerelease[h];if(O("prerelease compare",h,S,f),S===void 0&&f===void 0)return 0;if(f===void 0)return 1;if(S===void 0)return-1;if(S===f)continue;return l(S,f)}while(++h)}compareBuild(v){v instanceof u||(v=new u(v,this.options));let h=0;do{const S=this.build[h],f=v.build[h];if(O("prerelease compare",h,S,f),S===void 0&&f===void 0)return 0;if(f===void 0)return 1;if(S===void 0)return-1;if(S===f)continue;return l(S,f)}while(++h)}inc(v,h,S){switch(v){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",h,S);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",h,S);break;case"prepatch":this.prerelease.length=0,this.inc("patch",h,S),this.inc("pre",h,S);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",h,S),this.inc("pre",h,S);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const f=Number(S)?1:0;if(!h&&S===!1)throw new Error("invalid increment argument: identifier is empty");if(this.prerelease.length===0)this.prerelease=[f];else{let R=this.prerelease.length;for(;--R>=0;)typeof this.prerelease[R]=="number"&&(this.prerelease[R]++,R=-2);if(R===-1){if(h===this.prerelease.join(".")&&S===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(f)}}if(h){let R=[h,f];S===!1&&(R=[h]),l(this.prerelease[0],h)===0?isNaN(this.prerelease[1])&&(this.prerelease=R):this.prerelease=R}break}default:throw new Error(`invalid increment argument: ${v}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}L.exports=u},88618:(L,D,a)=>{const{MAX_SAFE_COMPONENT_LENGTH:O,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:g}=a(97718),m=a(47180);D=L.exports={};const M=D.re=[],H=D.safeRe=[],l=D.src=[],u=D.t={};let ae=0;const v="[a-zA-Z0-9-]",h=[["\\s",1],["\\d",g],[v,i]],S=R=>{for(const[X,z]of h)R=R.split(`${X}*`).join(`${X}{0,${z}}`).split(`${X}+`).join(`${X}{1,${z}}`);return R},f=(R,X,z)=>{const ce=S(X),B=ae++;m(R,B,X),u[R]=B,l[B]=X,M[B]=new RegExp(X,z?"g":void 0),H[B]=new RegExp(ce,z?"g":void 0)};f("NUMERICIDENTIFIER","0|[1-9]\\d*"),f("NUMERICIDENTIFIERLOOSE","\\d+"),f("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${v}*`),f("MAINVERSION",`(${l[u.NUMERICIDENTIFIER]})\\.(${l[u.NUMERICIDENTIFIER]})\\.(${l[u.NUMERICIDENTIFIER]})`),f("MAINVERSIONLOOSE",`(${l[u.NUMERICIDENTIFIERLOOSE]})\\.(${l[u.NUMERICIDENTIFIERLOOSE]})\\.(${l[u.NUMERICIDENTIFIERLOOSE]})`),f("PRERELEASEIDENTIFIER",`(?:${l[u.NUMERICIDENTIFIER]}|${l[u.NONNUMERICIDENTIFIER]})`),f("PRERELEASEIDENTIFIERLOOSE",`(?:${l[u.NUMERICIDENTIFIERLOOSE]}|${l[u.NONNUMERICIDENTIFIER]})`),f("PRERELEASE",`(?:-(${l[u.PRERELEASEIDENTIFIER]}(?:\\.${l[u.PRERELEASEIDENTIFIER]})*))`),f("PRERELEASELOOSE",`(?:-?(${l[u.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${l[u.PRERELEASEIDENTIFIERLOOSE]})*))`),f("BUILDIDENTIFIER",`${v}+`),f("BUILD",`(?:\\+(${l[u.BUILDIDENTIFIER]}(?:\\.${l[u.BUILDIDENTIFIER]})*))`),f("FULLPLAIN",`v?${l[u.MAINVERSION]}${l[u.PRERELEASE]}?${l[u.BUILD]}?`),f("FULL",`^${l[u.FULLPLAIN]}$`),f("LOOSEPLAIN",`[v=\\s]*${l[u.MAINVERSIONLOOSE]}${l[u.PRERELEASELOOSE]}?${l[u.BUILD]}?`),f("LOOSE",`^${l[u.LOOSEPLAIN]}$`),f("GTLT","((?:<|>)?=?)"),f("XRANGEIDENTIFIERLOOSE",`${l[u.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),f("XRANGEIDENTIFIER",`${l[u.NUMERICIDENTIFIER]}|x|X|\\*`),f("XRANGEPLAIN",`[v=\\s]*(${l[u.XRANGEIDENTIFIER]})(?:\\.(${l[u.XRANGEIDENTIFIER]})(?:\\.(${l[u.XRANGEIDENTIFIER]})(?:${l[u.PRERELEASE]})?${l[u.BUILD]}?)?)?`),f("XRANGEPLAINLOOSE",`[v=\\s]*(${l[u.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[u.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[u.XRANGEIDENTIFIERLOOSE]})(?:${l[u.PRERELEASELOOSE]})?${l[u.BUILD]}?)?)?`),f("XRANGE",`^${l[u.GTLT]}\\s*${l[u.XRANGEPLAIN]}$`),f("XRANGELOOSE",`^${l[u.GTLT]}\\s*${l[u.XRANGEPLAINLOOSE]}$`),f("COERCE",`(^|[^\\d])(\\d{1,${O}})(?:\\.(\\d{1,${O}}))?(?:\\.(\\d{1,${O}}))?(?:$|[^\\d])`),f("COERCERTL",l[u.COERCE],!0),f("LONETILDE","(?:~>?)"),f("TILDETRIM",`(\\s*)${l[u.LONETILDE]}\\s+`,!0),D.tildeTrimReplace="$1~",f("TILDE",`^${l[u.LONETILDE]}${l[u.XRANGEPLAIN]}$`),f("TILDELOOSE",`^${l[u.LONETILDE]}${l[u.XRANGEPLAINLOOSE]}$`),f("LONECARET","(?:\\^)"),f("CARETTRIM",`(\\s*)${l[u.LONECARET]}\\s+`,!0),D.caretTrimReplace="$1^",f("CARET",`^${l[u.LONECARET]}${l[u.XRANGEPLAIN]}$`),f("CARETLOOSE",`^${l[u.LONECARET]}${l[u.XRANGEPLAINLOOSE]}$`),f("COMPARATORLOOSE",`^${l[u.GTLT]}\\s*(${l[u.LOOSEPLAIN]})$|^$`),f("COMPARATOR",`^${l[u.GTLT]}\\s*(${l[u.FULLPLAIN]})$|^$`),f("COMPARATORTRIM",`(\\s*)${l[u.GTLT]}\\s*(${l[u.LOOSEPLAIN]}|${l[u.XRANGEPLAIN]})`,!0),D.comparatorTrimReplace="$1$2$3",f("HYPHENRANGE",`^\\s*(${l[u.XRANGEPLAIN]})\\s+-\\s+(${l[u.XRANGEPLAIN]})\\s*$`),f("HYPHENRANGELOOSE",`^\\s*(${l[u.XRANGEPLAINLOOSE]})\\s+-\\s+(${l[u.XRANGEPLAINLOOSE]})\\s*$`),f("STAR","(<|>)?=?\\s*\\*"),f("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),f("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},97718:L=>{const D="2.0.0",O=Number.MAX_SAFE_INTEGER||9007199254740991,i=16,g=256-6,m=["major","premajor","minor","preminor","patch","prepatch","prerelease"];L.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:i,MAX_SAFE_BUILD_LENGTH:g,MAX_SAFE_INTEGER:O,RELEASE_TYPES:m,SEMVER_SPEC_VERSION:D,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}}}]);
