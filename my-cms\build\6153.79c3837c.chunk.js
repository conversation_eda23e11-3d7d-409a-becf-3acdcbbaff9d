"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[6153],{28026:(Z,k,t)=>{t.d(k,{u:()=>P});var e=t(43543);const f=e.n.injectEndpoints({endpoints:E=>({getWebhooks:E.query({query:a=>({url:`/admin/webhooks/${a?.id??""}`,method:"GET"}),transformResponse:a=>Array.isArray(a.data)?a.data:[a.data],providesTags:(a,h,M)=>typeof M=="object"&&"id"in M?[{type:"Webhook",id:M.id}]:[...a?.map(({id:O})=>({type:"Webhook",id:O}))??[],{type:"Webhook",id:"LIST"}]}),createWebhook:E.mutation({query:a=>({url:"/admin/webhooks",method:"POST",data:a}),transformResponse:a=>a.data,invalidatesTags:[{type:"Webhook",id:"LIST"}]}),updateWebhook:E.mutation({query:({id:a,...h})=>({url:`/admin/webhooks/${a}`,method:"PUT",data:h}),transformResponse:a=>a.data,invalidatesTags:(a,h,{id:M})=>[{type:"Webhook",id:M}]}),triggerWebhook:E.mutation({query:a=>({url:`/admin/webhooks/${a}/trigger`,method:"POST"}),transformResponse:a=>a.data}),deleteManyWebhooks:E.mutation({query:a=>({url:"/admin/webhooks/batch-delete",method:"POST",data:a}),transformResponse:a=>a.data,invalidatesTags:(a,h,{ids:M})=>M.map(O=>({type:"Webhook",id:O}))})}),overrideExisting:!1}),{useGetWebhooksQuery:U,useCreateWebhookMutation:C,useUpdateWebhookMutation:S,useTriggerWebhookMutation:B,useDeleteManyWebhooksMutation:K}=f,P=(E=void 0,a)=>{const{data:h,isLoading:M,error:O}=U(E,a),[w,{error:D}]=C(),[V,{error:T}]=S(),[F]=B(),[z]=K();return{webhooks:h,isLoading:M,error:O||D||T,createWebhook:w,updateWebhook:V,triggerWebhook:F,deleteManyWebhooks:z}}},46153:(Z,k,t)=>{t.r(k),t.d(k,{E:()=>Y,a:()=>N,b:()=>Ue});var e=t(92132),f=t(21272),U=t(50215),C=t(94061),S=t(85963),B=t(4181),K=t(76517),P=t(80782),E=t(8361),a=t(90151),h=t(68074),M=t(4198),O=t(55356),w=t(43064),D=t(38413),V=t(46462),T=t(81621),F=t(66809),z=t(379),J=t(84843),p=t(83997),le=t(18629),G=t(7537),b=t(30893),de=t(98765),W=t(55506),ce=t(71389),q=t(17703),$=t(43543),ee=t(98898),ge=t(66248),he=t(21610),ue=t(46270),se=t(54514),te=t(68802),_e=t(84795),Ee=t(98052),me=t(5194),v=t(61535),I=t(54894),A=t(12083),pe=t(89032),Q=t(63891),fe=t(28026);const[Me,ne]=(0,pe.q)("WebhookEvent"),ye=({children:s})=>{const{formatMessage:n}=(0,I.A)(),{collectionTypes:r,isLoading:c}=(0,ee.u)(),o=f.useMemo(()=>r.some(i=>i.options?.draftAndPublish===!0),[r]),g=n({id:"Settings.webhooks.form.events",defaultMessage:"Events"});return(0,e.jsx)(Me,{isDraftAndPublish:o,children:(0,e.jsxs)(p.s,{direction:"column",alignItems:"stretch",gap:1,children:[(0,e.jsx)(E.d,{"aria-hidden":!0,children:g}),c&&(0,e.jsx)(w.a,{children:n({id:"Settings.webhooks.events.isLoading",defaultMessage:"Events loading"})}),(0,e.jsx)(xe,{"aria-label":g,children:s})]})})},xe=(0,Q.Ay)(V.X)`
  tbody tr:nth-child(odd) {
    background: ${({theme:s})=>s.colors.neutral100};
  }

  thead th span {
    color: ${({theme:s})=>s.colors.neutral500};
  }

  td,
  th {
    padding-block-start: ${({theme:s})=>s.spaces[3]};
    padding-block-end: ${({theme:s})=>s.spaces[3]};
    width: 10%;
    vertical-align: middle;
    text-align: center;
  }

  tbody tr td:first-child {
    /**
     * Add padding to the start of the first column to avoid the checkbox appearing
     * too close to the edge of the table
     */
    padding-inline-start: ${({theme:s})=>s.spaces[2]};
  }
`,je=s=>{const n=[{id:"Settings.webhooks.events.create",defaultMessage:"Create"},{id:"Settings.webhooks.events.update",defaultMessage:"Update"},{id:"app.utils.delete",defaultMessage:"Delete"}];return s&&(n.push({id:"app.utils.publish",defaultMessage:"Publish"}),n.push({id:"app.utils.unpublish",defaultMessage:"Unpublish"})),n},Pe=({getHeaders:s=je})=>{const{isDraftAndPublish:n}=ne("Headers"),{formatMessage:r}=(0,I.A)(),c=s(n);return(0,e.jsx)(F.r,{children:(0,e.jsxs)(J.N,{children:[(0,e.jsx)(T.e,{children:(0,e.jsx)(de.s,{children:r({id:"Settings.webhooks.event.select",defaultMessage:"Select event"})})}),c.map(o=>["app.utils.publish","app.utils.unpublish"].includes(o?.id??"")?(0,e.jsx)(T.e,{title:r({id:"Settings.webhooks.event.publish-tooltip",defaultMessage:"This event only exists for content with draft & publish enabled"}),children:(0,e.jsx)(b.o,{variant:"sigma",textColor:"neutral600",children:r(o)})},o.id):(0,e.jsx)(T.e,{children:(0,e.jsx)(b.o,{variant:"sigma",textColor:"neutral600",children:r(o)})},o.id))]})})},ve=({providedEvents:s})=>{const{isDraftAndPublish:n}=ne("Body"),r=s||Oe(n),{values:c,handleChange:o}=(0,v.j7)(),g="events",i=c.events,m=[],y=i.reduce((l,d)=>{const _=d.split(".")[0];return l[_]||(l[_]=[]),l[_].push(d),l},{}),u=({target:{name:l,value:d}})=>{const _=new Set(i);d?_.add(l):_.delete(l),o({target:{name:g,value:Array.from(_)}})},x=({target:{name:l,value:d}})=>{const _=new Set(i);d?r[l].forEach(R=>{m.includes(R)||_.add(R)}):r[l].forEach(R=>_.delete(R)),o({target:{name:g,value:Array.from(_)}})};return(0,e.jsx)(z.f,{children:Object.entries(r).map(([l,d])=>(0,e.jsx)(re,{disabledEvents:m,name:l,events:d,inputValue:y[l],handleSelect:u,handleSelectAll:x},l))})},Oe=s=>{const n=["entry.create","entry.update","entry.delete"];return s&&n.push("entry.publish","entry.unpublish"),{entry:n,media:["media.create","media.update","media.delete"]}},re=({disabledEvents:s=[],name:n,events:r=[],inputValue:c=[],handleSelect:o,handleSelectAll:g})=>{const{formatMessage:i}=(0,I.A)(),m=r.filter(d=>!s.includes(d)),y=c.length>0,u=c.length===m.length,x=({target:{name:d}})=>{g({target:{name:d,value:!u}})},l=5;return(0,e.jsxs)(J.N,{children:[(0,e.jsx)(T.N,{children:(0,e.jsx)(B.S,{indeterminate:y&&!u,"aria-label":i({id:"global.select-all-entries",defaultMessage:"Select all entries"}),name:n,onChange:x,value:u,children:De(n)})}),r.map(d=>(0,e.jsx)(T.N,{children:(0,e.jsx)(U.J,{disabled:s.includes(d),"aria-label":d,name:d,value:c.includes(d),onValueChange:_=>o({target:{name:d,value:_}})})},d)),r.length<l&&(0,e.jsx)(T.N,{colSpan:l-r.length})]})},De=s=>s.replace(/-/g," ").split(" ").map(n=>n.charAt(0).toUpperCase()+n.slice(1).toLowerCase()).join(" "),N={Root:ye,Headers:Pe,Body:ve,Row:re},be=()=>(0,e.jsxs)(N.Root,{children:[(0,e.jsx)(N.Headers,{}),(0,e.jsx)(N.Body,{})]}),Ae=()=>{const{formatMessage:s}=(0,I.A)(),{values:n,errors:r}=(0,v.j7)();return(0,e.jsxs)(p.s,{direction:"column",alignItems:"stretch",gap:1,children:[(0,e.jsx)(E.d,{children:s({id:"Settings.webhooks.form.headers",defaultMessage:"Headers"})}),(0,e.jsx)(C.a,{padding:8,background:"neutral100",hasRadius:!0,children:(0,e.jsx)(v.ED,{validateOnChange:!1,name:"headers",render:({push:c,remove:o})=>(0,e.jsxs)(a.x,{gap:4,children:[n.headers.map((g,i)=>{const m=r.headers?.[i],y=typeof m=="object"?m.key:void 0,u=typeof m=="object"?m.value:void 0;return(0,e.jsxs)(f.Fragment,{children:[(0,e.jsx)(h.E,{col:6,children:(0,e.jsx)(v.D0,{as:Ce,name:`headers.${i}.key`,"aria-label":`row ${i+1} key`,label:s({id:"Settings.webhooks.key",defaultMessage:"Key"}),error:y})}),(0,e.jsx)(h.E,{col:6,children:(0,e.jsxs)(p.s,{alignItems:"flex-end",children:[(0,e.jsx)(C.a,{style:{flex:1},children:(0,e.jsx)(v.D0,{as:G.k,name:`headers.${i}.value`,"aria-label":`row ${i+1} value`,label:s({id:"Settings.webhooks.value",defaultMessage:"Value"}),error:u})}),(0,e.jsx)(p.s,{paddingLeft:2,style:{alignSelf:"center"},paddingTop:u?0:5,children:(0,e.jsx)(W.yX,{disabled:n.headers.length===1,onClick:()=>o(i),label:s({id:"Settings.webhooks.headers.remove",defaultMessage:"Remove header row {number}"},{number:i+1})})})]})})]},`${i}.${g.key}`)}),(0,e.jsx)(h.E,{col:12,children:(0,e.jsx)(le.Q,{type:"button",onClick:()=>{c({key:"",value:""})},startIcon:(0,e.jsx)(me.A,{}),children:s({id:"Settings.webhooks.create.header",defaultMessage:"Create new header"})})})]})})})]})},Ce=({name:s,onChange:n,value:r,...c})=>{const{values:{headers:o}}=(0,v.j7)(),[g,i]=f.useState([...ae]);f.useEffect(()=>{const u=ae.filter(x=>!o?.some(l=>l.key!==r&&l.key===x));i(u)},[o,r]);const m=u=>{n({target:{name:s,value:u}})},y=u=>{i(x=>[...x,u]),m(u)};return(0,e.jsx)(K.nP,{...c,onClear:()=>m(""),onChange:m,onCreateOption:y,placeholder:"",value:r,children:g.map(u=>(0,e.jsx)(P.j,{value:u,children:u},u))})},ae=["A-IM","Accept","Accept-Charset","Accept-Encoding","Accept-Language","Accept-Datetime","Access-Control-Request-Method","Access-Control-Request-Headers","Authorization","Cache-Control","Connection","Content-Length","Content-Type","Cookie","Date","Expect","Forwarded","From","Host","If-Match","If-Modified-Since","If-None-Match","If-Range","If-Unmodified-Since","Max-Forwards","Origin","Pragma","Proxy-Authorization","Range","Referer","TE","User-Agent","Upgrade","Via","Warning"],Te=({isPending:s,onCancel:n,response:r})=>{const{statusCode:c,message:o}=r??{},{formatMessage:g}=(0,I.A)();return(0,e.jsx)(C.a,{background:"neutral0",padding:5,shadow:"filterShadow",hasRadius:!0,children:(0,e.jsxs)(a.x,{gap:4,style:{alignItems:"center"},children:[(0,e.jsx)(h.E,{col:3,children:(0,e.jsx)(b.o,{children:g({id:"Settings.webhooks.trigger.test",defaultMessage:"Test-trigger"})})}),(0,e.jsx)(h.E,{col:3,children:(0,e.jsx)(We,{isPending:s,statusCode:c})}),(0,e.jsx)(h.E,{col:6,children:s?(0,e.jsx)(p.s,{justifyContent:"flex-end",children:(0,e.jsx)("button",{onClick:n,type:"button",children:(0,e.jsxs)(p.s,{gap:2,alignItems:"center",children:[(0,e.jsx)(b.o,{textColor:"neutral400",children:g({id:"Settings.webhooks.trigger.cancel",defaultMessage:"cancel"})}),(0,e.jsx)(H,{as:te.A,color:"neutral400"})]})})}):(0,e.jsx)(Ie,{statusCode:c,message:o})})]})})},H=Q.Ay.svg(({theme:s,color:n})=>`
  width: ${12/16}rem;
  height: ${12/16}rem;


  ${n?(0,Q.AH)`
          path {
            fill: ${s.colors[n]};
          }
        `:""}
`),We=({isPending:s,statusCode:n})=>{const{formatMessage:r}=(0,I.A)();return s||!n?(0,e.jsxs)(p.s,{gap:2,alignItems:"center",children:[(0,e.jsx)(H,{as:_e.A}),(0,e.jsx)(b.o,{children:r({id:"Settings.webhooks.trigger.pending",defaultMessage:"pending"})})]}):n>=200&&n<300?(0,e.jsxs)(p.s,{gap:2,alignItems:"center",children:[(0,e.jsx)(H,{as:se.A,color:"success700"}),(0,e.jsx)(b.o,{children:r({id:"Settings.webhooks.trigger.success",defaultMessage:"success"})})]}):n>=300?(0,e.jsxs)(p.s,{gap:2,alignItems:"center",children:[(0,e.jsx)(H,{as:te.A,color:"danger700"}),(0,e.jsxs)(b.o,{children:[r({id:"Settings.error",defaultMessage:"error"})," ",n]})]}):null},Ie=({statusCode:s,message:n})=>{const{formatMessage:r}=(0,I.A)();return s?s>=200&&s<300?(0,e.jsx)(p.s,{justifyContent:"flex-end",children:(0,e.jsx)(b.o,{textColor:"neutral600",ellipsis:!0,children:r({id:"Settings.webhooks.trigger.success.label",defaultMessage:"Trigger succeeded"})})}):s>=300?(0,e.jsx)(p.s,{justifyContent:"flex-end",children:(0,e.jsx)(p.s,{maxWidth:(0,W.a8)(250),justifyContent:"flex-end",title:n,children:(0,e.jsx)(b.o,{ellipsis:!0,textColor:"neutral600",children:n})})}):null:null},Re=({handleSubmit:s,triggerWebhook:n,isCreating:r,isTriggering:c,triggerResponse:o,data:g})=>{const{formatMessage:i}=(0,I.A)(),[m,y]=f.useState(!1),u=(0,$.p)(be,async()=>(await t.e(7063).then(t.bind(t,27063))).EventsTableEE),x=d=>Object.keys(d).length?Object.entries(d).map(([_,R])=>({key:_,value:R})):[{key:"",value:""}],l=(0,v.Wx)({initialValues:{name:g?.name||"",url:g?.url||"",headers:x(g?.headers||{}),events:g?.events||[]},async onSubmit(d,_){await s(d,_),_.resetForm({values:d})},validationSchema:Le({formatMessage:i}),validateOnChange:!1,validateOnBlur:!1});return u?(0,e.jsx)(v.QI,{value:l,children:(0,e.jsxs)(W.lV,{children:[(0,e.jsx)(O.Q,{primaryAction:(0,e.jsxs)(p.s,{gap:2,children:[(0,e.jsx)(S.$,{onClick:()=>{n(),y(!0)},variant:"tertiary",startIcon:(0,e.jsx)(Ee.A,{}),disabled:r||c,size:"L",children:i({id:"Settings.webhooks.trigger",defaultMessage:"Trigger"})}),(0,e.jsx)(S.$,{startIcon:(0,e.jsx)(se.A,{}),type:"submit",size:"L",disabled:!l.dirty,loading:l.isSubmitting,children:i({id:"global.save",defaultMessage:"Save"})})]}),title:r?i({id:"Settings.webhooks.create",defaultMessage:"Create a webhook"}):g?.name,navigationAction:(0,e.jsx)(he.N,{as:ce.k2,startIcon:(0,e.jsx)(ue.A,{}),to:"/settings/webhooks",children:i({id:"global.back",defaultMessage:"Back"})})}),(0,e.jsx)(M.s,{children:(0,e.jsxs)(p.s,{direction:"column",alignItems:"stretch",gap:4,children:[m&&(0,e.jsx)(Te,{isPending:c,response:o,onCancel:()=>y(!1)}),(0,e.jsx)(C.a,{background:"neutral0",padding:8,shadow:"filterShadow",hasRadius:!0,children:(0,e.jsxs)(p.s,{direction:"column",alignItems:"stretch",gap:6,children:[(0,e.jsxs)(a.x,{gap:6,children:[(0,e.jsx)(h.E,{col:6,children:(0,e.jsx)(v.D0,{as:G.k,name:"name",error:l.errors.name,label:i({id:"global.name",defaultMessage:"Name"}),required:!0})}),(0,e.jsx)(h.E,{col:12,children:(0,e.jsx)(v.D0,{as:G.k,name:"url",error:l.errors.url,label:i({id:"Settings.roles.form.input.url",defaultMessage:"Url"}),required:!0})})]}),(0,e.jsx)(Ae,{}),(0,e.jsx)(u,{})]})})]})})]})}):null},ke=/(^$)|(^[A-Za-z][_0-9A-Za-z ]*$)/,Se=/(^$)|((https?:\/\/.*)(d*)\/?(.*))/,Le=({formatMessage:s})=>A.Ik().shape({name:A.Yj().required(s({id:"Settings.webhooks.validation.name.required",defaultMessage:"Name is required"})).matches(ke,s({id:"Settings.webhooks.validation.name.regex",defaultMessage:"The name must start with a letter and only contain letters, numbers, spaces and underscores"})),url:A.Yj().required(s({id:"Settings.webhooks.validation.url.required",defaultMessage:"Url is required"})).matches(Se,s({id:"Settings.webhooks.validation.url.regex",defaultMessage:"The value must be a valid Url"})),headers:A.RZ(n=>{const r=A.YO();if(n.length===1){const{key:c,value:o}=n[0];if(!c&&!o)return r}return r.of(A.Ik().shape({key:A.Yj().required(s({id:"Settings.webhooks.validation.key",defaultMessage:"Key is required"})),value:A.Yj().required(s({id:"Settings.webhooks.validation.value",defaultMessage:"Value is required"}))}))}),events:A.YO()}),oe=s=>({...s,headers:s.headers.reduce((n,{key:r,value:c})=>(r!==""&&(n[r]=c),n),{})}),Y=()=>{const n=(0,q.W5)("/settings/webhooks/:id")?.params.id,r=n==="create",{replace:c}=(0,q.W6)(),o=(0,W.hN)(),{_unstableFormatAPIError:g,_unstableFormatValidationErrors:i}=(0,W.wq)(),m=f.useCallback(g,[]),{isLoading:y}=(0,ee.u)(),[u,x]=f.useState(!1),[l,d]=f.useState(),{isLoading:_,webhooks:R,error:X,createWebhook:Be,updateWebhook:Ke,triggerWebhook:we}=(0,fe.u)({id:n},{skip:r});f.useEffect(()=>{X&&o({type:"warning",message:m(X)})},[X,o,m]);const $e=async()=>{try{x(!0);const L=await we(n);if("error"in L){o({type:"warning",message:g(L.error)});return}d(L.data)}catch{o({type:"warning",message:{id:"notification.error",defaultMessage:"An error occurred"}})}finally{x(!1)}},Ne=async(L,ie)=>{try{if(r){const j=await Be(oe(L));if("error"in j){(0,$.x)(j.error)&&j.error.name==="ValidationError"?ie.setErrors(i(j.error)):o({type:"warning",message:g(j.error)});return}o({type:"success",message:{id:"Settings.webhooks.created"}}),c(`/settings/webhooks/${j.data.id}`)}else{const j=await Ke({id:n,...oe(L)});if("error"in j){(0,$.x)(j.error)&&j.error.name==="ValidationError"?ie.setErrors(i(j.error)):o({type:"warning",message:g(j.error)});return}o({type:"success",message:{id:"notification.form.success.fields"}})}}catch{o({type:"warning",message:{id:"notification.error",defaultMessage:"An error occurred"}})}};if(_||y)return(0,e.jsx)(W.Bl,{});const[He]=R??[];return(0,e.jsxs)(D.g,{children:[(0,e.jsx)(W.x7,{name:"Webhooks"}),(0,e.jsx)(Re,{data:He,handleSubmit:Ne,triggerWebhook:$e,isCreating:r,isTriggering:u,triggerResponse:l})]})},Ue=Object.freeze(Object.defineProperty({__proto__:null,EditPage:Y,ProtectedEditPage:()=>{const s=(0,$.j)(ge.s);return(0,e.jsx)(W.kz,{permissions:s.settings?.webhooks.update,children:(0,e.jsx)(Y,{})})}},Symbol.toStringTag,{value:"Module"}))},98898:(Z,k,t)=>{t.d(k,{u:()=>K});var e=t(21272),f=t(55506),U=t(43543);const C=U.n.injectEndpoints({endpoints:P=>({getComponents:P.query({query:()=>({url:"/content-manager/components",method:"GET"}),transformResponse:E=>E.data}),getContentTypes:P.query({query:()=>({url:"/content-manager/content-types",method:"GET"}),transformResponse:E=>E.data})}),overrideExisting:!1}),{useGetComponentsQuery:S,useGetContentTypesQuery:B}=C;function K(){const{_unstableFormatAPIError:P}=(0,f.wq)(),E=(0,f.hN)(),a=S(),h=B();e.useEffect(()=>{h.error&&E({type:"warning",message:P(h.error)})},[h.error,P,E]),e.useEffect(()=>{a.error&&E({type:"warning",message:P(a.error)})},[a.error,P,E]);const M=a.isLoading||h.isLoading,O=e.useMemo(()=>(h?.data??[]).filter(D=>D.kind==="collectionType"&&D.isDisplayed),[h?.data]),w=e.useMemo(()=>(h?.data??[]).filter(D=>D.kind!=="collectionType"&&D.isDisplayed),[h?.data]);return{isLoading:M,components:e.useMemo(()=>a?.data??[],[a?.data]),collectionTypes:O,singleTypes:w}}}}]);
