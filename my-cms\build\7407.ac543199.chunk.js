"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[7407],{59788:(u,e,n)=>{n.r(e),n.d(e,{Analytics:()=>o,Documentation:()=>t,Email:()=>i,Password:()=>a,Provider:()=>r,ResetPasswordToken:()=>s,Role:()=>l,Username:()=>c,Users:()=>p,default:()=>m});const o="Analytics",t="Documentazione",i="Email",a="Password",r="Provider",s="Reimposta Token Password",l="Ruolo",c="Nome utente",p="Utenti",m={Analytics:o,"Auth.components.Oops.text":"Il tuo account \xE8 stato sospeso","Auth.form.button.forgot-password":"Invia email","Auth.form.button.go-home":"TORNA ALLA HOME","Auth.form.button.login":"Accedi","Auth.form.button.register":"Inizia adesso","Auth.form.confirmPassword.label":"Conferma Password","Auth.form.email.label":"Email","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"Il tuo account \xE8 stato bloccato dall'amministratore.","Auth.form.error.code.provide":"Codice fornito non corretto.","Auth.form.error.confirmed":"L'email del tuo account non \xE8 stata confermata.","Auth.form.error.email.invalid":"Questa email non \xE8 valida.","Auth.form.error.email.provide":"Per favore inserisci il tuo nome utente o la tua email.","Auth.form.error.email.taken":"Email gi\xE0 utilizzata.","Auth.form.error.invalid":"Identificatore o password non valida.","Auth.form.error.params.provide":"I parametri forniti non sono corretti.","Auth.form.error.password.format":"La tua password non pu\xF2 contenere il simbolo `$` per pi\xF9 di tre volte.","Auth.form.error.password.local":"Questo utente non ha mai impostato una password locale, accedi gentilmente tramite il provider usato durante la creazione dell'account","Auth.form.error.password.matching":"La password non corrisponde.","Auth.form.error.password.provide":"Per favore fornisci la tua password","Auth.form.error.ratelimit":"Troppi tentativi, riprova tra un minuto.","Auth.form.error.user.not-exist":"Questa email non esiste.","Auth.form.error.username.taken":"Nome utente gi\xE0 utilizzato.","Auth.form.firstname.label":"Nome","Auth.form.firstname.placeholder":"Kai","Auth.form.forgot-password.email.label":"Inserisci la tua email","Auth.form.forgot-password.email.label.success":"Email inviata correttamente","Auth.form.lastname.label":"Cognome","Auth.form.lastname.placeholder":"Doe","Auth.form.register.news.label":"Tienimi aggiornato in merito a nuove funzionalit\xE0 e futuri sviluppi (cos\xEC facendo accetti {terms} e {policy}).","Auth.form.rememberMe.label":"Ricordami","Auth.form.username.label":"Nome utente","Auth.form.username.placeholder":"Kai Doe","Auth.link.forgot-password":"Password dimenticata?","Auth.link.ready":"Sei pronto per accedere?","Auth.link.signin":"Accedi","Auth.link.signin.account":"Hai gi\xE0 un account?","Auth.privacy-policy-agreement.policy":"privacy policy","Auth.privacy-policy-agreement.terms":"termini","Content Manager":"Gestione Contenuti","Content Type Builder":"Content-Types Builder",Documentation:t,Email:i,"Files Upload":"Caricamento Files","HomePage.helmet.title":"Homepage","HomePage.roadmap":"Guarda la nostra roadmap","HomePage.welcome.congrats":"Congratulazioni!","HomePage.welcome.congrats.content":"Ti sei loggato come primo amministratore. Per scoprire le funzionalit\xE0 di Strapi,","HomePage.welcome.congrats.content.bold":"ti consigliamo di creare la tua prima Collezione.","Media Library":"Libreria media","New entry":"Nuovo elemento",Password:a,Provider:r,ResetPasswordToken:s,Role:l,"Roles & Permissions":"Ruoli e permessi","Roles.ListPage.notification.delete-all-not-allowed":"Alcuni ruoli non possono essere eleminati poich\xE9 sono associati agli utenti","Roles.ListPage.notification.delete-not-allowed":"Un ruolo non pu\xF2 essere eliminato se associato ad utenti","Roles.components.List.empty.withSearch":"Nessun ruolo corrisponde alla ricerca ({search})...","Settings.PageTitle":"Impostazioni - {name}","Settings.application.description":"Vedi i dettagli del tuo progetto","Settings.application.edition-title":"PIANO ATTUALE","Settings.application.link-pricing":"Vedi tutti i prezzi","Settings.application.link-upgrade":"Aggiorna il tuo progetto","Settings.application.node-version":"VERSIONE NODE","Settings.application.strapi-version":"VERSIONE STRAPI","Settings.application.title":"Applicazione","Settings.error":"Errore","Settings.global":"Impostazioni Globali","Settings.permissions":"Pannello di amministazione","Settings.permissions.category":"Impostazioni permessi per la categoria {category}","Settings.permissions.category.plugins":"Permissions settings for the {category} plugin","Settings.permissions.conditions.anytime":"In ogni momento","Settings.permissions.conditions.apply":"Applica","Settings.permissions.conditions.can":"Pu\xF2","Settings.permissions.conditions.conditions":"Definisci le condizioni","Settings.permissions.conditions.links":"Link","Settings.permissions.conditions.no-actions":"Non ci sono azioni","Settings.permissions.conditions.or":"Oppure","Settings.permissions.conditions.when":"Quando","Settings.permissions.users.create":"Crea nuovo utente","Settings.permissions.users.email":"Email","Settings.permissions.users.firstname":"Nome","Settings.permissions.users.lastname":"Cognome","Settings.roles.create.description":"Definisci permessi del ruolo","Settings.roles.create.title":"Crea ruolo","Settings.roles.created":"Ruolo creato","Settings.roles.edit.title":"Modifica ruolo","Settings.roles.form.button.users-with-role":"Utenti con questo ruolo","Settings.roles.form.created":"Creato","Settings.roles.form.description":"Nome e descrizione ruolo","Settings.roles.form.permissions.attributesPermissions":"Permessi per i campi","Settings.roles.form.permissions.create":"Crea","Settings.roles.form.permissions.delete":"Elimina","Settings.roles.form.permissions.publish":"Pubblica","Settings.roles.form.permissions.read":"Leggi","Settings.roles.form.permissions.update":"Aggiorna","Settings.roles.list.button.add":"Aggiungi nuovo ruolo","Settings.roles.list.description":"Lista dei ruoli","Settings.roles.title.singular":"Ruolo","Settings.webhooks.create":"Crea un webhook","Settings.webhooks.create.header":"Crea un nuovo header","Settings.webhooks.created":"Webhook creato","Settings.webhooks.event.publish-tooltip":"Evento disponibile solo per contenuti con gestione stati Bozza/Pubblicazione abilitati","Settings.webhooks.events.create":"Crea","Settings.webhooks.events.update":"Aggiorna","Settings.webhooks.form.events":"Eventi","Settings.webhooks.form.headers":"Headers","Settings.webhooks.form.url":"Url","Settings.webhooks.key":"Chiave","Settings.webhooks.list.button.add":"Aggiungi nuovo webhook","Settings.webhooks.list.description":"Ricevi notifiche di cambiamenti in POST.","Settings.webhooks.list.empty.description":"Aggiungi il primo alla lista","Settings.webhooks.list.empty.link":"Leggi la documentazione","Settings.webhooks.list.empty.title":"Non ci sono webhooks","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.trigger":"Trigger","Settings.webhooks.trigger.cancel":"Annulla trigger","Settings.webhooks.trigger.pending":"In corso\u2026","Settings.webhooks.trigger.save":"Salva trigger","Settings.webhooks.trigger.success":"Successo!","Settings.webhooks.trigger.success.label":"Trigger eseguito","Settings.webhooks.trigger.test":"Test trigger","Settings.webhooks.trigger.title":"Salva prima di eseguire trigger","Settings.webhooks.value":"Valore",Username:c,Users:p,"Users & Permissions":"Utenti & Permessi","Users.components.List.empty":"Non ci sono utenti...","Users.components.List.empty.withFilters":"Nessun utente trovato con i filtri applicati...","Users.components.List.empty.withSearch":"Nessun utente corrisponde alla ricerca ({search})...","app.components.BlockLink.code":"Esempi di codice","app.components.Button.cancel":"Annulla","app.components.Button.reset":"Ripristina","app.components.ComingSoonPage.comingSoon":"In arrivo","app.components.DownloadInfo.download":"Download in corso...","app.components.DownloadInfo.text":"Potrebbe volerci un minuto. Grazie della pazienza.","app.components.EmptyAttributes.title":"Campi non ancora presenti.","app.components.HomePage.button.blog":"LEGGI DI PI\xD9 SUL BLOG","app.components.HomePage.community":"Trova la community sul web","app.components.HomePage.community.content":"Discuti con i membri del team, i contributori e gli sviluppatori tramite i nostri canali.","app.components.HomePage.create":"Crea il tuo primo Content-Type","app.components.HomePage.welcome":"Benvenuto a bordo!","app.components.HomePage.welcome.again":"Benvenuto ","app.components.HomePage.welcomeBlock.content":"Siamo felici di averti come membro della comunit\xE0. Siamo costantemente alla ricerca di feedback, quindi sentitevi liberi di inviarci messaggi diretti su ","app.components.HomePage.welcomeBlock.content.again":"Speriamo che tu stia facendo progressi sul tuo progetto ... Sentiti libero di leggere l'ultima novit\xE0 riguardo Strapi. Stiamo dando il massimo per migliorare il prodotto in base al tuo feedback.","app.components.HomePage.welcomeBlock.content.issues":"problemi.","app.components.HomePage.welcomeBlock.content.raise":" o solleva ","app.components.ImgPreview.hint":"Trascina il tuo file in quest'area o {browse} un file da caricare.","app.components.ImgPreview.hint.browse":"cerca","app.components.InputFile.newFile":"Aggiungi nuovo file","app.components.InputFileDetails.open":"Apri in una nuova tab","app.components.InputFileDetails.originalName":"Nome originale:","app.components.InputFileDetails.remove":"Rimuovi questo file","app.components.InputFileDetails.size":"Dimensione:","app.components.InstallPluginPage.Download.description":"Il download e l'installazione del plugin potrebbero richiedere qualche secondo.","app.components.InstallPluginPage.Download.title":"Scaricando...","app.components.InstallPluginPage.description":"Estendi la tua app senza sforzi.","app.components.LeftMenuFooter.help":"Supporto","app.components.LeftMenuFooter.poweredBy":"Offerto da ","app.components.LeftMenuLinkContainer.collectionTypes":"Collezioni","app.components.LeftMenuLinkContainer.configuration":"Configurazioni","app.components.LeftMenuLinkContainer.general":"Generale","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Nessun plugin ancora installato","app.components.LeftMenuLinkContainer.plugins":"Plugins","app.components.LeftMenuLinkContainer.singleTypes":"Entit\xE0 singole","app.components.ListPluginsPage.deletePlugin.description":"L'installazione del plugin potrebbe richiedere qualche secondo.","app.components.ListPluginsPage.deletePlugin.title":"Disinstalla","app.components.ListPluginsPage.description":"Lista dei plugin installati nel progetto.","app.components.ListPluginsPage.helmet.title":"Lista plugin","app.components.Logout.logout":"Disconnetti","app.components.Logout.profile":"Profilo","app.components.NotFoundPage.back":"Torna alla home","app.components.NotFoundPage.description":"Non trovato","app.components.Official":"Ufficiale","app.components.Onboarding.label.completed":"% completato","app.components.Onboarding.title":"Video di introduzione","app.components.PluginCard.Button.label.download":"Download","app.components.PluginCard.Button.label.install":"Gi\xE0 installato","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"La funzione autoReload necessit\xE0 di essere abilitata. Per favore, avvia la app con il comando `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Ho capito!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Per ragioni di sicurezza, il plugin puo essere scaricato solo in ambiente di sviluppo.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Impossibile scaricare","app.components.PluginCard.compatible":"Compatibile con la tua app","app.components.PluginCard.compatibleCommunity":"Compatibile con la comunit\xE0","app.components.PluginCard.more-details":"Pi\xF9 dettagli","app.components.Users.MagicLink.connect":"Invia link all'utente per connettersi.","app.components.Users.ModalCreateBody.block-title.details":"Dettagli","app.components.Users.ModalCreateBody.block-title.roles":"Ruoli utente","app.components.Users.SortPicker.button-label":"Ordina per","app.components.Users.SortPicker.sortby.email_asc":"Email (A - Z)","app.components.Users.SortPicker.sortby.email_desc":"Email (Z - A)","app.components.Users.SortPicker.sortby.firstname_asc":"Noma (A - Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Nome (Z - A)","app.components.Users.SortPicker.sortby.lastname_asc":"Cognome (A - Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Cognome (Z - A)","app.components.Users.SortPicker.sortby.username_asc":"Nome utente (A - Z)","app.components.Users.SortPicker.sortby.username_desc":"Nome utente (Z - A)","app.components.listPlugins.button":"Aggiungi nuovo plugin","app.components.listPlugins.title.none":"Nessun plugin installato","app.components.listPluginsPage.deletePlugin.error":"Si \xE8 verificato un errore durante l'installazione del plugin","app.containers.App.notification.error.init":"Si \xE8 verificato un errore durante la richiesta dell'API","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Se non ricevi questo link, contatta l'amministratore.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"La ricezione del link per reimpostare la password potrebbere richiedere qualche secondo.","app.containers.AuthPage.ForgotPasswordSuccess.title":"Email inviata","app.containers.Users.EditPage.form.active.label":"Attivo","app.containers.Users.EditPage.header.label":"Modifica {name}","app.containers.Users.EditPage.header.label-loading":"Modifica utente","app.containers.Users.EditPage.roles-bloc-title":"Ruoli assegnati","app.containers.Users.ModalForm.footer.button-success":"Crea utente","app.links.configure-view":"Configura la visualizzazione","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Aggiungi filtro","app.utils.defaultMessage":" ","app.utils.errors.file-too-big.message":"Dimensioni file troppo grandi","app.utils.filters":"Filtri","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Pubblica","app.utils.select-all":"Seleziona tutti","app.utils.unpublish":"Converti in bozza","component.Input.error.validation.integer":"Il valore deve essere un intero","components.AutoReloadBlocker.description":"Avvia Strapi con uno dei seguenti comandi:","components.AutoReloadBlocker.header":"Ricarica funzionalit\xE0 \xE8 richiesto per questo plugin.","components.ErrorBoundary.title":"Qualcosa \xE8 andato storto...","components.Input.error.attribute.key.taken":"Valore gi\xE0 esistente","components.Input.error.attribute.sameKeyAndName":"Non pu\xF2 essere uguale","components.Input.error.attribute.taken":"Nome campo gi\xE0 esistente","components.Input.error.contain.lowercase":"Password deve contenere almeno una carattere minuscolo","components.Input.error.contain.number":"Password deve contenere almeno un numero","components.Input.error.contain.uppercase":"Password deve contenere almeno una carattere maiuscolo","components.Input.error.contentTypeName.taken":"Nome gi\xE0 esistente","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"La password non corrisponde","components.Input.error.validation.email":"Non \xE8 un'email","components.Input.error.validation.json":"Formato JSON non corrisponde","components.Input.error.validation.max":"Valore troppo alto {max}.","components.Input.error.validation.maxLength":"Valore troppo lungo {max}.","components.Input.error.validation.min":"Valore troppo basso {min}.","components.Input.error.validation.minLength":"Valore troppo corto {min}.","components.Input.error.validation.minSupMax":"Non pu\xF2 essere superiore","components.Input.error.validation.regex":"Questo valore non coincide con la regex.","components.Input.error.validation.required":"Valore obbligatorio.","components.Input.error.validation.unique":"Questo valore \xE8 gi\xE0 usato","components.InputSelect.option.placeholder":"Seleziona","components.ListRow.empty":"Non ci sono dati da mostrare.","components.OverlayBlocker.description":"Stai utilizzando una funzionalit\xE0 che necessita del riavvio del server. Per favore, attendi che il server ritorni attivo.","components.OverlayBlocker.description.serverError":"Il server deve essere riavviato, per favore controlla i tuoi log nel terminale.","components.OverlayBlocker.title":"Attendo il riavvio...","components.OverlayBlocker.title.serverError":"Il riavvio sta impiegando pi\xF9 del previsto","components.PageFooter.select":"elementi per pagina","components.ProductionBlocker.description":"Per ragioni di sicurezza dobbiamo disabilitare questo plugin in altri ambienti.","components.ProductionBlocker.header":"Questo plugin \xE8 disponibile solo in sviluppo.","components.Search.placeholder":"Cerca...","components.Wysiwyg.collapse":"Chiudi","components.Wysiwyg.selectOptions.H1":"Titolo H1","components.Wysiwyg.selectOptions.H2":"Titolo H2","components.Wysiwyg.selectOptions.H3":"Titolo H3","components.Wysiwyg.selectOptions.H4":"Titolo H4","components.Wysiwyg.selectOptions.H5":"Titolo H5","components.Wysiwyg.selectOptions.H6":"Titolo H6","components.Wysiwyg.selectOptions.title":"Aggiungi un titolo","components.WysiwygBottomControls.charactersIndicators":"caratteri","components.WysiwygBottomControls.fullscreen":"Espandi","components.WysiwygBottomControls.uploadFiles":"Trascina & rilascia file, incolla dagli appunti o {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"selezionali","components.popUpWarning.button.cancel":"No, annulla","components.popUpWarning.button.confirm":"S\xEC, conferma","components.popUpWarning.message":"Sei sicuro di volerlo cancellare?","components.popUpWarning.title":"Conferma richiesta","content-manager.EditRelations.title":"Dati relazionali","content-manager.api.id":"API ID","content-manager.components.AddFilterCTA.add":"Filtri","content-manager.components.AddFilterCTA.hide":"Filtri","content-manager.components.DraggableAttr.edit":"Clicca per modificare","content-manager.components.DynamicZone.pick-compo":"Scegli un componente","content-manager.components.DynamicZone.required":"Componente richiesto","content-manager.components.EmptyAttributesBlock.button":"Vai alla pagina delle impostazioni","content-manager.components.EmptyAttributesBlock.description":"Puoi cambiare le tue impostazioni","content-manager.components.FieldItem.linkToComponentLayout":"Modifica impaginazione componente","content-manager.components.FilterOptions.button.apply":"Applica","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Applica","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Cancella tutto","content-manager.components.FiltersPickWrapper.PluginHeader.description":"Imposta le condizioni da applicare per filtrare gli elementi","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Filtri","content-manager.components.FiltersPickWrapper.hide":"Nascondi","content-manager.components.LimitSelect.itemsPerPage":"Elementi per pagina","content-manager.components.NotAllowedInput.text":"Non hai il permesso di vedere questo campo","content-manager.components.Search.placeholder":"Ricerca elementi...","content-manager.components.Select.draft-info-title":"Stato: Bozza","content-manager.components.Select.publish-info-title":"Stato: Pubblicato","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Personalizza l'aspetto della schermata di modifica.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Definisci le impostazioni delle liste di elementi.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Configura la vista - {name}","content-manager.components.TableDelete.delete":"Elimina tutti","content-manager.components.TableDelete.deleteSelected":"Elimina selezionato","content-manager.components.TableEmpty.withFilters":"Nessun {contentType} con questi filtri...","content-manager.components.TableEmpty.withSearch":"Nessun {contentType} corrispondente alla ricerca ({search})...","content-manager.components.TableEmpty.withoutFilter":"Non ci sono {contentType}...","content-manager.components.empty-repeatable":"Ancora nessun elemento. Clicca il pulsante sottostante per aggiungerne uno.","content-manager.components.notification.info.maximum-requirement":"Hai gi\xE0 raggiunto il massimo numero di campi","content-manager.components.notification.info.minimum-requirement":"\xC8 stato aggiunto un campo per soddisfare il requisito minimo","content-manager.components.repeatable.reorder.error":"Si \xE8 verificato un errore durante il riordinamento del campo del componente","content-manager.components.reset-entry":"Azzera elemento","content-manager.components.uid.apply":"applica","content-manager.components.uid.available":"disponibile","content-manager.components.uid.regenerate":"rigenera","content-manager.components.uid.suggested":"suggerito","content-manager.components.uid.unavailable":"non disponibile","content-manager.containers.Edit.Link.Layout":"Configura impaginazione","content-manager.containers.Edit.Link.Model":"Modifica la Collezione","content-manager.containers.Edit.addAnItem":"Aggiungi un elemento...","content-manager.containers.Edit.clickToJump":"Clicca per andare all'elemento","content-manager.containers.Edit.delete":"Elimina","content-manager.containers.Edit.delete-entry":"Elimina questo elemento","content-manager.containers.Edit.editing":"Modifica in corso...","content-manager.containers.Edit.information":"Informazioni","content-manager.containers.Edit.information.by":"Da","content-manager.containers.Edit.information.draftVersion":"versione bozza","content-manager.containers.Edit.information.editing":"Modifica","content-manager.containers.Edit.information.lastUpdate":"Aggiornato","content-manager.containers.Edit.information.publishedVersion":"versione pubblicata","content-manager.containers.Edit.pluginHeader.title.new":"Crea un elemento","content-manager.containers.Edit.reset":"Azzera","content-manager.containers.Edit.returnList":"Torna alla lista","content-manager.containers.Edit.seeDetails":"Dettagli","content-manager.containers.Edit.submit":"Salva","content-manager.containers.EditSettingsView.modal-form.edit-field":"Modifica campo","content-manager.containers.EditView.notification.errors":"Il form contiene degli errori","content-manager.containers.Home.introduction":"Per modificare le voci, visitare il link nel menu di sinistra. Questo plugin non ha un modo per modificare le impostazioni ed \xE8 ancora in fase di sviluppo attivo.","content-manager.containers.Home.pluginHeaderDescription":"Gestisci i tuoi dati attraverso un'interfaccia bella e potente.","content-manager.containers.Home.pluginHeaderTitle":"Gestore Contenuti","content-manager.containers.List.draft":"Bozza","content-manager.containers.List.errorFetchRecords":"Errore","content-manager.containers.List.published":"Pubblicato","content-manager.containers.ListPage.displayedFields":"Campi visualizzati","content-manager.containers.ListPage.table-headers.publishedAt":"Stato","content-manager.containers.ListSettingsView.modal-form.edit-label":"Modifica etichetta","content-manager.containers.SettingPage.add.field":"Inserisci nuovo campo","content-manager.containers.SettingPage.attributes":"Attributi","content-manager.containers.SettingPage.attributes.description":"Definisci l'ordine degli attributi","content-manager.containers.SettingPage.editSettings.description":"Sposta i campi per costruire il layout","content-manager.containers.SettingPage.editSettings.entry.title":"Titolo elemento","content-manager.containers.SettingPage.editSettings.entry.title.description":"Scegli quale campo mostrare dell'elemento","content-manager.containers.SettingPage.editSettings.relation-field.description":"Scegli il campo da mostrare nelle liste e durante la modifica","content-manager.containers.SettingPage.editSettings.title":"Modifica (impostazioni)","content-manager.containers.SettingPage.layout":"Layout","content-manager.containers.SettingPage.listSettings.description":"Scegli le opzioni per questa Collezione","content-manager.containers.SettingPage.listSettings.title":"Lista (impostazioni)","content-manager.containers.SettingPage.pluginHeaderDescription":"Configura le impostazioni specifiche per questa Collezione","content-manager.containers.SettingPage.settings":"Impostazioni","content-manager.containers.SettingPage.view":"Vista","content-manager.containers.SettingViewModel.pluginHeader.title":"Gestore Contenuti - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Configura le impostazioni specifiche","content-manager.containers.SettingsPage.Block.contentType.title":"Tipi Collezione","content-manager.containers.SettingsPage.Block.generalSettings.description":"Configura le impostazioni di default per le tue Collezioni","content-manager.containers.SettingsPage.Block.generalSettings.title":"Generali","content-manager.containers.SettingsPage.pluginHeaderDescription":"Configura le impostazioni per tutti i Tipi Collezione e i Gruppi","content-manager.containers.SettingsView.list.subtitle":"Configura il layout e la vista per i tuoi tipi Collezione e i gruppi","content-manager.containers.SettingsView.list.title":"Configurazioni vista","content-manager.emptyAttributes.button":"Vai al costruttore di collezioni","content-manager.emptyAttributes.description":"Aggiungi il primo campo al tuo Tipo Collezione","content-manager.emptyAttributes.title":"Nessun campo presente","content-manager.error.attribute.key.taken":"Questo valore esiste gi\xE0","content-manager.error.attribute.sameKeyAndName":"Non possono essere uguali","content-manager.error.attribute.taken":"Esiste gi\xE0 un campo con questo nome","content-manager.error.contentTypeName.taken":"Questo nome esiste gi\xE0","content-manager.error.model.fetch":"Si \xE8 verificato un errore durante il caricamento dei modelli di configurazione.","content-manager.error.record.create":"Si \xE8 verificato un errore durante la creazione dell'elemento.","content-manager.error.record.delete":"Si \xE8 verificato un errore durante la cancellazione dell'elemento.","content-manager.error.record.fetch":"Si \xE8 verificato un errore durante il caricamento dell'elemento.","content-manager.error.record.update":"Si \xE8 verificato un errore durante l'aggiornamento dell'elemento.","content-manager.error.records.count":"Si \xE8 verificato un errore durante il conteggio degli elementi.","content-manager.error.records.fetch":"Si \xE8 verificato un errore durante il caricamento degli elementi.","content-manager.error.schema.generation":"Si \xE8 verificato un errore durante la generazione dello schema.","content-manager.error.validation.json":"Non \xE8 un JSON","content-manager.error.validation.max":"Il valore \xE8 troppo alto.","content-manager.error.validation.maxLength":"Il valore \xE8 troppo lungo.","content-manager.error.validation.min":"Il valore \xE8 troppo basso.","content-manager.error.validation.minLength":"Il valore \xE8 troppo breve.","content-manager.error.validation.minSupMax":"Non pu\xF2 essere superiore","content-manager.error.validation.regex":"Il valore non corrisponde alla RegEx.","content-manager.error.validation.required":"Questo valore \xE8 richiesto.","content-manager.form.Input.bulkActions":"Abilita azioni in blocco","content-manager.form.Input.defaultSort":"Attributo di ordinamento di default","content-manager.form.Input.description":"Descrizione","content-manager.form.Input.description.placeholder":"Mostra nome nel profilo","content-manager.form.Input.editable":"Campo modificabile","content-manager.form.Input.filters":"Abilita filtri","content-manager.form.Input.label":"Etichetta","content-manager.form.Input.label.inputDescription":"Questo valore sovrascrive l'etichetta mostrata nell'intestazione della tabella","content-manager.form.Input.pageEntries":"Righe per pagina","content-manager.form.Input.pageEntries.inputDescription":"Nota: Puoi sovrascrivere questo valore nella pagina delle impostazioni del Tipo Collezione.","content-manager.form.Input.placeholder":"Segnaposto","content-manager.form.Input.placeholder.placeholder":"Il mio fantastico valore","content-manager.form.Input.search":"Abilita ricerca","content-manager.form.Input.search.field":"Abilita ricerca su questo campo","content-manager.form.Input.sort.field":"Abilita ordinamento su questo campo","content-manager.form.Input.wysiwyg":"Mostra come WYSIWYG","content-manager.global.displayedFields":"Campi visualizzati","content-manager.groups":"Gruppi","content-manager.groups.numbered":"Gruppi ({number})","content-manager.models":"Tipi Collezione","content-manager.models.numbered":"Tipi Collezione ({number})","content-manager.notification.error.displayedFields":"Devi avere almeno un campo visualizzato","content-manager.notification.error.relationship.fetch":"Si \xE8 verificato un errore durante il caricamento della relazione.","content-manager.notification.info.SettingPage.disableSort":"Devi avere almeno un attributo con ordinamento abilitato","content-manager.notification.info.minimumFields":"Devi avere almeno un campo visualizzato","content-manager.notification.upload.error":"Si \xE8 verificato un errore durante il caricamento dei file","content-manager.pageNotFound":"Pagina non trovata","content-manager.permissions.not-allowed.create":"Non sei autorizzato a creare documenti","content-manager.permissions.not-allowed.update":"Non sei autorizzato a vedere questo documento","content-manager.plugin.description.long":"Permette di vedere, modificare e cancellare i dati presenti nel database in modo veloce.","content-manager.plugin.description.short":"Permette di vedere, modificare e cancellare i dati presenti nel database in modo veloce.","content-manager.success.record.delete":"Eliminato","content-manager.success.record.publish":"Pubblicato","content-manager.success.record.save":"Salvato","content-manager.success.record.unpublish":"Non pubblicato","content-manager.popUpWarning.warning.publish-question":"Vuoi ancora pubblicarlo?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"S\xEC, pubblica","form.button.done":"Fatto","global.prompt.unsaved":"Sei sicuro di voler lasciare questa pagina? Tutte le modifiche effettuate verranno perse.","notification.contentType.relations.conflict":"Questo Tipo di Contenuto ha delle relazioni in conflitto","notification.error":"Si \xE8 verificato un errore","notification.error.layout":"Non \xE8 stato possibile recuperare il layout","notification.form.error.fields":"Il form contiene degli errori","notification.form.success.fields":"Modifiche salvate","notification.link-copied":"Link copiato negli appunti","notification.permission.not-allowed-read":"Non sei abilitato a visualizzare questo documento","notification.success.delete":"L'elemento \xE8 stato eliminato","notification.success.saved":"Salvato","notification.version.update.message":"Una nuova versione di Strapi \xE8 disponibile!","request.error.model.unknown":"Modello inesistente"}}}]);
