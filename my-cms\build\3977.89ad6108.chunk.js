"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3977],{11614:($,T,t)=>{t.d(T,{u:()=>M});var r=t(85140);function M(s={}){const{id:x="",...D}=s,{data:S,isLoading:E}=(0,r.c)({id:x,populate:"stages",...D}),[p]=(0,r.d)(),[u]=(0,r.e)(),[v]=(0,r.f)(),{workflows:m,meta:P}=S??{};return{meta:P,workflows:m,isLoading:E,createWorkflow:p,updateWorkflow:u,deleteWorkflow:v}}},14515:($,T,t)=>{t.d(T,{B:()=>R,D:()=>W,H:()=>A,R:()=>N});var r=t(92132),M=t(42455),s=t(4198),x=t(55356),D=t(38413),S=t(83997),E=t(30893),p=t(55506),u=t(46270),v=t(9005),m=t(54894),P=t(43543),I=t(25524),k=t(63891);const y=(0,k.Ay)(S.s)`
  svg path {
    fill: ${({theme:w})=>w.colors.neutral600};
  }
`,U=({name:w})=>(0,r.jsxs)(S.s,{background:"primary100",borderStyle:"dashed",borderColor:"primary600",borderWidth:"1px",gap:3,hasRadius:!0,padding:3,shadow:"tableShadow",width:(0,p.a8)(300),children:[(0,r.jsx)(y,{alignItems:"center",background:"neutral200",borderRadius:"50%",height:6,justifyContent:"center",width:6,children:(0,r.jsx)(v.A,{width:`${8/16}rem`})}),(0,r.jsx)(E.o,{fontWeight:"bold",children:w})]}),W=()=>(0,r.jsx)(P.P,{renderItem:w=>{if(w.type===I.D.STAGE)return(0,r.jsx)(U,{name:typeof w.item=="string"?w.item:null})}}),N=({children:w})=>(0,r.jsx)(M.P,{children:(0,r.jsx)(D.g,{tabIndex:-1,children:(0,r.jsx)(s.s,{children:w})})}),R=({href:w})=>{const{formatMessage:z}=(0,m.A)();return(0,r.jsx)(p.N_,{startIcon:(0,r.jsx)(u.A,{}),to:w,children:z({id:"global.back",defaultMessage:"Back"})})},A=({title:w,subtitle:z,navigationAction:J,primaryAction:q})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.x7,{name:w}),(0,r.jsx)(x.Q,{navigationAction:J,primaryAction:q,title:w,subtitle:z})]})},25346:($,T,t)=>{t.d(T,{S:()=>et,W:()=>ot,a:()=>$e,b:()=>he,c:()=>fe,d:()=>Te,e:()=>Ie,f:()=>ke,g:()=>Le,h:()=>ge,i:()=>Be,j:()=>Fe,k:()=>Ne,l:()=>Ae,r:()=>Re,s:()=>ze,u:()=>Se,v:()=>at});var r=t(21272),M=t(43543),s=t(92132),x=t(57237),D=t(44604),S=t(60888),E=t(94061),p=t(90151),u=t(68074),v=t(88353),m=t(83997),P=t(56654),I=t(48323),k=t(7537),y=t(30893),U=t(98765),W=t(55506),N=t(54894),R=t(82437),A=t(63891),w=t(39116),z=t(90625),J=t(85641),q=t(43206),we=t(33544),ce=t(5336),H=t(56936),F=t(61535),De=t(25801),i=t(25524),ee=t(35658),L=t(70653),Pe=t(56336),_e=t(88761),Oe=t(48940),C=t(12083);function Se(e,o){const n=(0,M.Q)();(0,r.useEffect)(()=>{n.injectReducer(e,o)},[n,e,o])}function Ce(e){return{type:i.j,payload:{id:e}}}function Ae({workflow:e}){return{type:i.A,payload:e}}function Te({workflows:e}){return{type:i.c,payload:e}}function xe(e){return{type:i.k,payload:{stageId:e}}}function ge(e){return{type:i.d,payload:e}}function te(e,o){return{type:i.l,payload:{stageId:e,...o}}}function We(e){return{type:i.m,payload:e}}function je(e,o){return{type:i.n,payload:{newIndex:o,oldIndex:e}}}function ue(e){return{type:i.i,payload:e}}function Re(){return{type:i.e}}function Ie(e){return{type:i.f,payload:e}}function ke(e){return{type:i.g,payload:e}}function Le(e){return{type:i.h,payload:e}}const se=(0,A.Ay)(q.A)`
  > circle {
    fill: ${({theme:e})=>e.colors.neutral150};
  }
  > path {
    fill: ${({theme:e})=>e.colors.neutral600};
  }
`,be=(0,A.Ay)(E.a)`
  border-radius: 26px;

  svg {
    height: ${({theme:e})=>e.spaces[6]};
    width: ${({theme:e})=>e.spaces[6]};

    > path {
      fill: ${({theme:e})=>e.colors.neutral600};
    }
  }

  &:hover {
    color: ${({theme:e})=>e.colors.primary600} !important;
    ${y.o} {
      color: ${({theme:e})=>e.colors.primary600} !important;
    }

    ${se} {
      > circle {
        fill: ${({theme:e})=>e.colors.primary600};
      }
      > path {
        fill: ${({theme:e})=>e.colors.neutral100};
      }
    }
  }

  &:active {
    ${y.o} {
      color: ${({theme:e})=>e.colors.primary600};
    }

    ${se} {
      > circle {
        fill: ${({theme:e})=>e.colors.primary600};
      }
      > path {
        fill: ${({theme:e})=>e.colors.neutral100};
      }
    }
  }
`,pe=({children:e,...o})=>(0,s.jsx)(be,{as:"button",background:"neutral0",border:"neutral150",paddingBottom:3,paddingLeft:4,paddingRight:4,paddingTop:3,shadow:"filterShadow",...o,children:(0,s.jsxs)(m.s,{gap:2,children:[(0,s.jsx)(se,{"aria-hidden":!0}),(0,s.jsx)(y.o,{variant:"pi",fontWeight:"bold",textColor:"neutral500",children:e})]})});pe.propTypes={children:we.node.isRequired};const G={serverState:{contentTypes:{collectionTypes:[],singleTypes:[]},roles:[],workflow:null,workflows:[]},clientState:{currentWorkflow:{data:{name:"",contentTypes:[],stages:[],permissions:void 0}},isLoading:!0}};function Be(e=G,o){return(0,_e.jM)(e,n=>{const{payload:c}=o;switch(o.type){case i.f:{n.serverState.contentTypes=c;break}case i.h:{n.clientState.isLoading=c;break}case i.g:{n.serverState.roles=c;break}case i.A:{const a=c;a&&(n.serverState.workflow=a,n.clientState.currentWorkflow.data={...a,stages:a.stages.map(d=>({...d,color:d?.color??i.b}))});break}case i.c:{n.serverState.workflows=c;break}case i.e:{n.clientState=G.clientState,n.serverState=(0,_e.mq)(G.serverState);break}case i.k:{const{stageId:a}=c,{currentWorkflow:d}=e.clientState;n.clientState.currentWorkflow.data.stages=d.data.stages?.filter(f=>(f?.id??f.__temp_key__)!==a);break}case i.d:{const{currentWorkflow:a}=e.clientState;a.data||(n.clientState.currentWorkflow.data={stages:[]});const d=me(n.clientState.currentWorkflow.data.stages);n.clientState.currentWorkflow.data.stages?.push({...c,color:c?.color??i.b,__temp_key__:d});break}case i.j:{const{currentWorkflow:a}=e.clientState,{id:d}=c,f=a.data.stages?.findIndex(h=>(h?.id??h?.__temp_key__)===d);if(f!==void 0&&f!==-1){const h=a.data.stages?.[f];n.clientState.currentWorkflow.data.stages?.splice(f+1,0,{...h,id:void 0,__temp_key__:me(n.clientState.currentWorkflow.data.stages)})}break}case i.l:{const{currentWorkflow:a}=e.clientState,{stageId:d,...f}=c;n.clientState.currentWorkflow.data.stages=a.data.stages?.map(h=>(h.id??h.__temp_key__)===d?{...h,...f}:h);break}case i.m:{const{currentWorkflow:a}=e.clientState;n.clientState.currentWorkflow.data.stages=a.data.stages?.map(d=>({...d,...c}));break}case i.n:{const{currentWorkflow:{data:{stages:a}}}=e.clientState,{newIndex:d,oldIndex:f}=c;if(a&&d>=0&&d<a.length){const h=a[f],j=[...a];j.splice(f,1),j.splice(d,0,h),n.clientState.currentWorkflow.data.stages=j}break}case i.i:{n.clientState.currentWorkflow.data={...n.clientState.currentWorkflow.data,...c};break}}})}const me=(e=[])=>{const o=e.map(n=>Number(n.id??n.__temp_key__));return Math.max(...o,-1)+1},b=e=>e[i.R]??G,Ke=(0,L.Mz)(b,({serverState:{contentTypes:e}})=>e),fe=(0,L.Mz)(b,({serverState:{roles:e}})=>e),he=(0,L.Mz)(b,({clientState:{currentWorkflow:e}})=>e.data),Ue=(0,L.Mz)(b,({serverState:{workflows:e}})=>e),$e=(0,L.Mz)(b,({serverState:e,clientState:{currentWorkflow:o}})=>!Pe(e.workflow,o.data)),Ne=(0,L.Mz)(b,({serverState:e,clientState:{currentWorkflow:o}})=>!(e.workflow?.stages??[]).every(n=>!!o.data.stages?.find(({id:c})=>c===n.id))),ze=(0,L.Mz)(b,({clientState:{isLoading:e}})=>e),Fe=(0,L.Mz)(b,({serverState:e})=>e),Ye=(0,A.Ay)(P.fe)`
  padding-left: ${({theme:e})=>e.spaces[7]};
`,He=(0,A.Ay)(m.s)`
  > * {
    flex-grow: 1;
  }
`,Ge=(0,A.Ay)(ce.Dr)`
  color: ${({theme:e})=>e.colors.danger600};
`,Ve=(0,A.Ay)(H.l9)`
  :hover,
  :focus {
    background-color: ${({theme:e})=>e.colors.neutral100};
  }

  > span {
    font-size: 0;
  }
`,Qe=(0,A.Ay)(v.K)`
  align-items: center;
  border-radius: ${({theme:e})=>e.borderRadius};
  display: flex;
  justify-content: center;

  :hover,
  :focus {
    background-color: ${({theme:e})=>e.colors.neutral100};
  }

  svg {
    height: auto;
    width: ${({theme:e})=>e.spaces[3]}};
  }
`,Ze=(0,ee.a)(),Xe=()=>(0,s.jsx)(E.a,{background:"primary100",borderStyle:"dashed",borderColor:"primary600",borderWidth:"1px",display:"block",hasRadius:!0,padding:6,shadow:"tableShadow"}),Je=({id:e,index:o,canDelete:n,canReorder:c,canUpdate:a,isOpen:d=!1,stagesCount:f})=>{const h=l=>`${l+1} of ${f}`,j=l=>{_(g({id:"dnd.grab-item",defaultMessage:"{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel."},{item:B.value,position:h(l)}))},oe=l=>{_(g({id:"dnd.drop-item",defaultMessage:"{item}, dropped. Final position in list: {position}."},{item:B.value,position:h(l)}))},V=()=>{_(g({id:"dnd.cancel-item",defaultMessage:"{item}, dropped. Re-order cancelled."},{item:B.value}))},ae=(l,K)=>{_(g({id:"dnd.reorder",defaultMessage:"{item}, moved. New position in list: {position}."},{item:B.value,position:h(l)})),O(je(K,l))},ne=()=>{ie(!0)},[Q,_]=r.useState(null),{formatMessage:g}=(0,N.A)(),{trackUsage:re}=(0,W.z1)(),O=(0,R.wA)(),nt=(0,W.hN)(),[le,rt]=r.useState(d),[lt,ie]=r.useState(!1),[B,Ee,it]=(0,F.Mt)(`stages.${o}.name`),[Z,Me,dt]=(0,F.Mt)(`stages.${o}.color`),[Y,ve,ct]=(0,F.Mt)(`stages.${o}.permissions`),_t=(0,R.d4)(fe),[{handlerId:gt,isDragging:ut,handleKeyDown:pt},mt,ft,ht,ye]=(0,M.T)(c,{index:o,item:{index:o,name:B.value},onGrabItem:j,onDropItem:oe,onMoveItem:ae,onCancel:V,type:i.D.STAGE}),Et=(0,M.V)(mt,ft),Mt=Ze.map(({hex:l,name:K})=>({value:l,label:g({id:"Settings.review-workflows.stage.color.name",defaultMessage:"{name}"},{name:K}),color:l})),{themeColorName:vt}=(0,ee.g)(Z.value)??{},de=_t?.filter(l=>l.code!=="strapi-super-admin");return r.useEffect(()=>{ye((0,De.n)(),{captureDraggingState:!1})},[ye,o]),(0,s.jsxs)(E.a,{ref:l=>Et(l),children:[Q&&(0,s.jsx)(U.s,{"aria-live":"assertive",children:Q}),ut?(0,s.jsx)(Xe,{}):(0,s.jsxs)(x.n,{size:"S",variant:"primary",onToggle:()=>{rt(!le),le||re("willEditStage")},expanded:le,shadow:"tableShadow",error:Ee.error??Me?.error??ve?.error,hasErrorMessage:!1,children:[(0,s.jsx)(S.P,{title:B.value,togglePosition:"left",action:(n||a)&&(0,s.jsxs)(m.s,{children:[(0,s.jsxs)(H.bL,{children:[(0,s.jsxs)(Ve,{size:"S",endIcon:null,paddingLeft:2,paddingRight:2,children:[(0,s.jsx)(J.A,{"aria-hidden":!0,focusable:!1}),(0,s.jsx)(U.s,{as:"span",children:g({id:"[tbdb].components.DynamicZone.more-actions",defaultMessage:"More actions"})})]}),(0,s.jsx)(H.UC,{popoverPlacement:"bottom-end",zIndex:2,children:(0,s.jsxs)(H.x8,{children:[a&&(0,s.jsx)(ce.Dr,{onClick:()=>O(Ce(e)),children:g({id:"Settings.review-workflows.stage.delete",defaultMessage:"Duplicate stage"})}),n&&(0,s.jsx)(Ge,{onClick:()=>O(xe(e)),children:g({id:"Settings.review-workflows.stage.delete",defaultMessage:"Delete"})})]})})]}),a&&(0,s.jsx)(Qe,{background:"transparent",forwardedAs:"div",hasRadius:!0,role:"button",noBorder:!0,tabIndex:0,"data-handler-id":gt,ref:ht,label:g({id:"Settings.review-workflows.stage.drag",defaultMessage:"Drag"}),onClick:l=>l.stopPropagation(),onKeyDown:pt,children:(0,s.jsx)(w.A,{})})]})}),(0,s.jsx)(D.u,{padding:6,background:"neutral0",hasRadius:!0,children:(0,s.jsxs)(p.x,{gap:4,children:[(0,s.jsx)(u.E,{col:6,children:(0,s.jsx)(k.k,{...B,id:B.name,disabled:!a,label:g({id:"Settings.review-workflows.stage.name.label",defaultMessage:"Stage name"}),error:Ee.error??!1,onChange:l=>{it.setValue(l.target.value),O(te(e,{name:l.target.value}))},required:!0})}),(0,s.jsx)(u.E,{col:6,children:(0,s.jsx)(I.Z,{disabled:!a,error:Me?.error??!1,id:Z.name,required:!0,label:g({id:"content-manager.reviewWorkflows.stage.color",defaultMessage:"Color"}),onChange:l=>{dt.setValue(l),O(te(e,{color:String(l)}))},value:Z.value.toUpperCase(),startIcon:(0,s.jsx)(m.s,{as:"span",height:2,background:Z.value,borderColor:vt==="neutral0"?"neutral150":"transparent",hasRadius:!0,shrink:0,width:2}),children:Mt.map(({value:l,label:K,color:X})=>{const{themeColorName:yt}=(0,ee.g)(X)||{};return(0,s.jsx)(I.eY,{value:l,startIcon:(0,s.jsx)(m.s,{as:"span",height:2,background:X,borderColor:yt==="neutral0"?"neutral150":"transparent",hasRadius:!0,shrink:0,width:2}),children:K},l)})})}),(0,s.jsx)(u.E,{col:6,children:de?.length===0?(0,s.jsx)(W.WT,{description:{id:"Settings.review-workflows.stage.permissions.noPermissions.description",defaultMessage:"You don\u2019t have the permission to see roles"},intlLabel:{id:"Settings.review-workflows.stage.permissions.label",defaultMessage:"Roles that can change this stage"},name:Y.name}):(0,s.jsxs)(m.s,{alignItems:"flex-end",gap:3,children:[(0,s.jsx)(He,{grow:1,children:(0,s.jsx)(P.KF,{...Y,disabled:!a,error:ve.error??!1,id:Y.name,label:g({id:"Settings.review-workflows.stage.permissions.label",defaultMessage:"Roles that can change this stage"}),onChange:l=>{const K=l.map(X=>({role:parseInt(X,10),action:"admin::review-workflows.stage.transition"}));ct.setValue(K),O(te(e,{permissions:K}))},placeholder:g({id:"Settings.review-workflows.stage.permissions.placeholder",defaultMessage:"Select a role"}),required:!0,value:(Y.value??[]).map(l=>`${l.role}`),withTags:!0,children:(0,s.jsx)(P.np,{label:g({id:"Settings.review-workflows.stage.permissions.allRoles.label",defaultMessage:"All roles"}),values:de?.map(l=>`${l.id}`),children:de?.map(l=>(0,s.jsx)(Ye,{value:`${l.id}`,children:l.name},l.id))})})}),(0,s.jsx)(v.K,{disabled:!a,icon:(0,s.jsx)(z.A,{}),label:g({id:"Settings.review-workflows.stage.permissions.apply.label",defaultMessage:"Apply to all stages"}),size:"L",variant:"secondary",onClick:()=>ne()})]})})]})})]}),(0,s.jsx)(W.TM.Root,{iconRightButton:null,isOpen:lt,onToggleDialog:()=>ie(!1),onConfirm:()=>{O(We({permissions:Y.value})),ie(!1),nt({type:"success",message:g({id:"Settings.review-workflows.page.edit.confirm.stages.permissions.copy.success",defaultMessage:"Applied roles to all other stages of the workflow"})})},variantRightButton:"default",children:(0,s.jsx)(W.TM.Body,{children:(0,s.jsx)(y.o,{textAlign:"center",variant:"omega",children:g({id:"Settings.review-workflows.page.edit.confirm.stages.permissions.copy",defaultMessage:"Roles that can change that stage will be applied to all the other stages."})})})})]})},qe=(0,A.Ay)(E.a)`
  transform: translateX(-50%);
`,et=({canDelete:e=!0,canUpdate:o=!0,stages:n=[]})=>{const{formatMessage:c}=(0,N.A)(),a=(0,R.wA)(),{trackUsage:d}=(0,W.z1)();return(0,s.jsxs)(m.s,{direction:"column",gap:6,width:"100%",children:[(0,s.jsxs)(E.a,{position:"relative",width:"100%",children:[(0,s.jsx)(qe,{background:"neutral200",height:"100%",left:"50%",position:"absolute",top:"0",width:2,zIndex:1}),(0,s.jsx)(m.s,{direction:"column",alignItems:"stretch",gap:6,zIndex:2,position:"relative",as:"ol",children:n.map((f,h)=>{const j=Number(f?.id??f.__temp_key__);return(0,s.jsx)(E.a,{as:"li",children:(0,s.jsx)(Je,{id:j,index:h,isOpen:!f.id,canDelete:n.length>1&&e,canReorder:n.length>1,canUpdate:o,stagesCount:n.length})},`stage-${j}`)})})]}),o&&(0,s.jsx)(pe,{type:"button",onClick:()=>{a(ge({name:""})),d("willCreateStage")},children:c({id:"Settings.review-workflows.stage.add",defaultMessage:"Add new stage"})})]})},tt=(0,A.Ay)(P.fe)`
  padding-left: ${({theme:e})=>e.spaces[7]};
`,st=(0,A.Ay)(y.o)`
  font-style: italic;
`,ot=({canUpdate:e=!0})=>{const{formatMessage:o,locale:n}=(0,N.A)(),c=(0,R.wA)(),a=(0,R.d4)(Ke),d=(0,R.d4)(he),f=(0,R.d4)(Ue),[h,j,oe]=(0,F.Mt)("name"),[V,ae,ne]=(0,F.Mt)("contentTypes"),Q=(0,W.QM)(n,{sensitivity:"base"});return(0,s.jsxs)(p.x,{background:"neutral0",hasRadius:!0,gap:4,padding:6,shadow:"tableShadow",children:[(0,s.jsx)(u.E,{col:6,children:(0,s.jsx)(k.k,{...h,id:h.name,disabled:!e,label:o({id:"Settings.review-workflows.workflow.name.label",defaultMessage:"Workflow Name"}),error:j.error??!1,onChange:_=>{c(ue({name:_.target.value})),oe.setValue(_.target.value)},required:!0})}),a&&(0,s.jsx)(u.E,{col:6,children:(0,s.jsx)(P.KF,{...V,customizeContent:_=>o({id:"Settings.review-workflows.workflow.contentTypes.displayValue",defaultMessage:"{count} {count, plural, one {content type} other {content types}} selected"},{count:_?.length}),disabled:!e,error:ae.error??!1,id:V.name,label:o({id:"Settings.review-workflows.workflow.contentTypes.label",defaultMessage:"Associated to"}),onChange:_=>{c(ue({contentTypes:_})),ne.setValue(_)},placeholder:o({id:"Settings.review-workflows.workflow.contentTypes.placeholder",defaultMessage:"Select"}),children:[...a.collectionTypes.length>0?[{label:o({id:"Settings.review-workflows.workflow.contentTypes.collectionTypes.label",defaultMessage:"Collection Types"}),children:[...a.collectionTypes].sort((_,g)=>Q.compare(_.info.displayName,g.info.displayName)).map(_=>({label:_.info.displayName,value:_.uid}))}]:[],...a.singleTypes.length>0?[{label:o({id:"Settings.review-workflows.workflow.contentTypes.singleTypes.label",defaultMessage:"Single Types"}),children:[...a.singleTypes].map(_=>({label:_.info.displayName,value:_.uid}))}]:[]].map(_=>{if("children"in _)return(0,s.jsx)(P.np,{label:_.label,values:_.children.map(g=>g.value.toString()),children:_.children.map(g=>{const{name:re}=f?.find(O=>(d&&O.id!==d.id||!d)&&O.contentTypes.includes(g.value))??{};return(0,s.jsx)(tt,{value:g.value,children:(0,s.jsx)(y.o,{children:o({id:"Settings.review-workflows.workflow.contentTypes.assigned.notice",defaultMessage:"{label} {name, select, undefined {} other {<i>(assigned to <em>{name}</em> workflow)</i>}}"},{label:g.label,name:re,em:(...O)=>(0,s.jsx)(y.o,{as:"em",fontWeight:"bold",children:O}),i:(...O)=>(0,s.jsx)(st,{children:O})})})},g.value)})},_.label)})})})]})};async function at({values:e,formatMessage:o}){const n=C.Ik({contentTypes:C.YO().of(C.Yj()),name:C.Yj().max(255,o({id:"Settings.review-workflows.validation.name.max-length",defaultMessage:"Name can not be longer than 255 characters"})).required(),stages:C.YO().of(C.Ik().shape({name:C.Yj().required(o({id:"Settings.review-workflows.validation.stage.name",defaultMessage:"Name is required"})).max(255,o({id:"Settings.review-workflows.validation.stage.max-length",defaultMessage:"Name can not be longer than 255 characters"})).test("unique-name",o({id:"Settings.review-workflows.validation.stage.duplicate",defaultMessage:"Stage name must be unique"}),function(c){const{options:{context:a}}=this;return a?.stages.filter(d=>d.name===c).length===1}),color:C.Yj().required(o({id:"Settings.review-workflows.validation.stage.color",defaultMessage:"Color is required"})).matches(/^#(?:[0-9a-fA-F]{3}){1,2}$/i),permissions:C.YO(C.Ik({role:C.ai().strict().typeError(o({id:"Settings.review-workflows.validation.stage.permissions.role.number",defaultMessage:"Role must be of type number"})).required(),action:C.Yj().required({id:"Settings.review-workflows.validation.stage.permissions.action.required",defaultMessage:"Action is a required argument"})})).strict()})).min(1)});try{return await n.validate(e,{abortEarly:!1,context:e}),!0}catch(c){const a={};return c instanceof C.yI&&c.inner.forEach(d=>{d.path&&Oe(a,d.path,d.message)}),a}}},35658:($,T,t)=>{t.d(T,{a:()=>x,g:()=>s});var r=t(57438),M=t(25524);function s(D){if(!D)return null;const E=Object.entries(r._.colors).filter(([,p])=>p.toUpperCase()===D.toUpperCase()).reduce((p,[u])=>(M.S?.[u]&&(p=u),p),null);return E?{themeColorName:E,name:M.S[E]}:null}function x(){return Object.entries(M.S).map(([D,S])=>({hex:r._.colors[D].toUpperCase(),name:S}))}},85071:($,T,t)=>{t.d(T,{u:()=>D});var r=t(21272),M=t(55506),s=t(54894),x=t(43543);const D=(S={},E)=>{const{locale:p}=(0,s.A)(),u=(0,M.QM)(p,{sensitivity:"base"}),{data:v,error:m,isError:P,isLoading:I,refetch:k}=(0,x.z)(S,E);return{roles:r.useMemo(()=>[...v??[]].sort((U,W)=>u.compare(U.name,W.name)),[v,u]),error:m,isError:P,isLoading:I,refetch:k}}},98898:($,T,t)=>{t.d(T,{u:()=>E});var r=t(21272),M=t(55506),s=t(43543);const x=s.n.injectEndpoints({endpoints:p=>({getComponents:p.query({query:()=>({url:"/content-manager/components",method:"GET"}),transformResponse:u=>u.data}),getContentTypes:p.query({query:()=>({url:"/content-manager/content-types",method:"GET"}),transformResponse:u=>u.data})}),overrideExisting:!1}),{useGetComponentsQuery:D,useGetContentTypesQuery:S}=x;function E(){const{_unstableFormatAPIError:p}=(0,M.wq)(),u=(0,M.hN)(),v=D(),m=S();r.useEffect(()=>{m.error&&u({type:"warning",message:p(m.error)})},[m.error,p,u]),r.useEffect(()=>{v.error&&u({type:"warning",message:p(v.error)})},[v.error,p,u]);const P=v.isLoading||m.isLoading,I=r.useMemo(()=>(m?.data??[]).filter(y=>y.kind==="collectionType"&&y.isDisplayed),[m?.data]),k=r.useMemo(()=>(m?.data??[]).filter(y=>y.kind!=="collectionType"&&y.isDisplayed),[m?.data]);return{isLoading:P,components:r.useMemo(()=>v?.data??[],[v?.data]),collectionTypes:I,singleTypes:k}}}}]);
