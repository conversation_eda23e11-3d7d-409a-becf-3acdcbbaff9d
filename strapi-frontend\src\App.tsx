import React, { useState, useEffect } from 'react';
import { Diamond, Menu, X, Plus, Check, Star, ArrowRight, Users, Zap, Shield, Globe, Mail, Phone, MapPin } from 'lucide-react';

// API Base URL
const STRAPI_URL = 'http://localhost:1337';

// Types for our data
interface Feature {
  id: number;
  attributes: {
    title: string;
    description: string;
    icon: string;
    iconColor: string;
    order: number;
    isActive: boolean;
  };
}

interface Testimonial {
  id: number;
  attributes: {
    name: string;
    role: string;
    content: string;
    rating: number;
    company?: string;
    order: number;
    isActive: boolean;
  };
}

interface HeroSection {
  id: number;
  attributes: {
    title: string;
    subtitle: string;
    description: string;
    primaryButtonText: string;
    primaryButtonUrl: string;
    secondaryButtonText: string;
    secondaryButtonUrl: string;
    heroImageUrl: string;
    heroImageAlt: string;
  };
}

interface SiteSettings {
  id: number;
  attributes: {
    siteName: string;
    topBannerText: string;
    topBannerEnabled: boolean;
    navigationLinks: Array<{label: string; url: string; active: boolean}>;
    headerCtaText: string;
    headerCtaUrl: string;
    footerDescription: string;
    contactEmail: string;
    contactPhone: string;
    contactAddress: string;
    copyrightText: string;
  };
}

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [features, setFeatures] = useState<Feature[]>([]);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [heroSection, setHeroSection] = useState<HeroSection | null>(null);
  const [siteSettings, setSiteSettings] = useState<SiteSettings | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch data from Strapi
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch features
        const featuresResponse = await fetch(`${STRAPI_URL}/api/features?populate=*&sort=order:asc`);
        const featuresData = await featuresResponse.json();
        setFeatures(featuresData.data || []);

        // Fetch testimonials
        const testimonialsResponse = await fetch(`${STRAPI_URL}/api/testimonials?populate=*&sort=order:asc`);
        const testimonialsData = await testimonialsResponse.json();
        setTestimonials(testimonialsData.data || []);

        // Fetch hero section
        const heroResponse = await fetch(`${STRAPI_URL}/api/hero-section?populate=*`);
        const heroData = await heroResponse.json();
        setHeroSection(heroData.data);

        // Fetch site settings
        const settingsResponse = await fetch(`${STRAPI_URL}/api/site-setting?populate=*`);
        const settingsData = await settingsResponse.json();
        setSiteSettings(settingsData.data);

      } catch (error) {
        console.error('Error fetching data from Strapi:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Helper function to get icon component
  const getIconComponent = (iconName: string, iconColor: string) => {
    const iconProps = { className: `h-8 w-8 text-${iconColor}-500` };

    switch (iconName) {
      case 'zap':
        return <Zap {...iconProps} />;
      case 'shield':
        return <Shield {...iconProps} />;
      case 'users':
        return <Users {...iconProps} />;
      case 'globe':
        return <Globe {...iconProps} />;
      default:
        return <Star {...iconProps} />;
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading content from Strapi...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Banner */}
      {siteSettings?.attributes.topBannerEnabled && (
        <div className="bg-gradient-to-r from-green-600 to-teal-600 text-white py-2 px-4 text-center text-sm">
          <span className="font-medium">{siteSettings.attributes.topBannerText}</span>
          <button className="ml-2 text-white hover:text-gray-200 transition-colors">
            <X size={16} className="inline" />
          </button>
        </div>
      )}

      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            {/* Logo */}
            <div className="flex items-center">
              <Diamond className="h-8 w-8 text-green-500 fill-current" />
              <span className="ml-2 text-xl font-bold text-gray-900">{siteSettings?.attributes.siteName || "Astro"}</span>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:block">
              <div className="flex items-center space-x-8">
                <a href="#" className="text-green-600 font-medium hover:text-green-700 transition-colors">
                  Home
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                  About
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                  Blog
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                  Learn More About Astro
                </a>
              </div>
            </nav>

            {/* CTA Button */}
            <div className="flex items-center space-x-4">
              <Plus className="h-6 w-6 text-gray-400 hidden md:block" />
              <button className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:scale-105">
                {siteSettings?.attributes.headerCtaText || "Learn more"}
              </button>
            </div>

            {/* Mobile menu button */}
            <button 
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <Menu className="h-6 w-6" />
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <nav className="md:hidden pb-4 border-t border-gray-200">
              <div className="flex flex-col space-y-3 pt-4">
                <a href="#" className="text-green-600 font-medium">Home</a>
                <a href="#" className="text-gray-500">About</a>
                <a href="#" className="text-gray-500">Blog</a>
                <a href="#" className="text-gray-500">Learn More About Astro</a>
              </div>
            </nav>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-12 lg:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Content */}
            <div className="space-y-8">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                {heroSection?.attributes.title || "Build & Launch"}
                <br />
                <span className="text-gray-700">{heroSection?.attributes.subtitle || "without problems"}</span>
              </h1>

              <p className="text-gray-600 text-lg leading-relaxed max-w-lg">
                {heroSection?.attributes.description || "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque efficitur nisl sodales egestas lobortis."}
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-lg font-medium transition-all duration-200 hover:scale-105 flex items-center justify-center">
                  {heroSection?.attributes.primaryButtonText || "Get Started"}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </button>

                <button className="bg-white hover:bg-gray-50 text-gray-900 px-8 py-4 rounded-lg font-medium border border-gray-300 transition-all duration-200 hover:scale-105">
                  {heroSection?.attributes.secondaryButtonText || "How It Works"}
                </button>
              </div>
            </div>

            {/* Right Column - Image */}
            <div className="relative">
              <div className="rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src={heroSection?.attributes.heroImageUrl || "https://images.pexels.com/photos/416978/pexels-photo-416978.jpeg?auto=compress&cs=tinysrgb&w=800"}
                  alt={heroSection?.attributes.heroImageAlt || "Modern conference room with large windows"}
                  className="w-full h-96 lg:h-[500px] object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our Platform?
            </h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              Discover the features that make us the preferred choice for developers and teams worldwide.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.filter(feature => feature.attributes.isActive).map((feature) => (
              <div key={feature.id} className="text-center p-6 rounded-xl hover:shadow-lg transition-shadow duration-300">
                <div className="flex justify-center mb-4">
                  {getIconComponent(feature.attributes.icon, feature.attributes.iconColor)}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.attributes.title}
                </h3>
                <p className="text-gray-600">
                  {feature.attributes.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Our Customers Say
            </h2>
            <p className="text-gray-600 text-lg">
              Don't just take our word for it - hear from our satisfied customers.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.filter(testimonial => testimonial.attributes.isActive).map((testimonial) => (
              <div key={testimonial.id} className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="flex mb-4">
                  {[...Array(testimonial.attributes.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-6 italic">
                  "{testimonial.attributes.content}"
                </p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.attributes.name}</p>
                  <p className="text-gray-500 text-sm">{testimonial.attributes.role}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-gray-600 text-lg">
              Choose the plan that's right for you and your team.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <div key={index} className={`relative p-8 rounded-xl ${plan.popular ? 'bg-green-50 border-2 border-green-500' : 'bg-gray-50 border border-gray-200'} hover:shadow-lg transition-shadow duration-300`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-500 ml-1">{plan.period}</span>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 hover:scale-105 ${plan.popular ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-white hover:bg-gray-50 text-gray-900 border border-gray-300'}`}>
                  Get Started
                </button>
              </div>
            ))}
          </div>
        </div>
      </section> */}

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-teal-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-green-100 text-lg mb-8 max-w-2xl mx-auto">
            Join thousands of developers who are already building amazing applications with our platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white hover:bg-gray-100 text-green-600 px-8 py-4 rounded-lg font-medium transition-all duration-200 hover:scale-105">
              Start Free Trial
            </button>
            <button className="bg-transparent hover:bg-white/10 text-white px-8 py-4 rounded-lg font-medium border-2 border-white transition-all duration-200 hover:scale-105">
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="flex items-center">
                <Diamond className="h-8 w-8 text-green-500 fill-current" />
                <span className="ml-2 text-xl font-bold">Astro</span>
              </div>
              <p className="text-gray-400">
                Building the future of web development with modern tools and technologies.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">f</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">t</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">in</span>
                </div>
              </div>
            </div>

            {/* Product Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">API Reference</a></li>
              </ul>
            </div>

            {/* Company Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-gray-400"><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-gray-400">+****************</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-gray-400">San Francisco, CA</span>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Astro. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;