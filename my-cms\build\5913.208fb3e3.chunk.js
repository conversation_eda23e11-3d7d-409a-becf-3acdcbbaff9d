"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[5913],{11614:(D,l,t)=>{t.d(l,{u:()=>o});var a=t(85140);function o(n={}){const{id:P="",...i}=n,{data:d,isLoading:O}=(0,a.c)({id:P,populate:"stages",...i}),[r]=(0,a.d)(),[T]=(0,a.e)(),[E]=(0,a.f)(),{workflows:s,meta:_}=d??{};return{meta:_,workflows:s,isLoading:O,createWorkflow:r,updateWorkflow:T,deleteWorkflow:E}}},25524:(D,l,t)=>{t.d(l,{A:()=>O,C:()=>U,D:()=>S,R:()=>o,S:()=>C,a:()=>K,b:()=>I,c:()=>r,d:()=>E,e:()=>n,f:()=>P,g:()=>d,h:()=>i,i:()=>f,j:()=>s,k:()=>T,l:()=>_,m:()=>e,n:()=>M});var a=t(57438);const o="settings_review-workflows",n="Settings/Review_Workflows/RESET_WORKFLOW",P="Settings/Review_Workflows/SET_CONTENT_TYPES",i="Settings/Review_Workflows/SET_IS_LOADING",d="Settings/Review_Workflows/SET_ROLES",O="Settings/Review_Workflows/SET_WORKFLOW",r="Settings/Review_Workflows/SET_WORKFLOWS",T="Settings/Review_Workflows/WORKFLOW_DELETE_STAGE",E="Settings/Review_Workflows/WORKFLOW_ADD_STAGE",s="Settings/Review_Workflows/WORKFLOW_CLONE_STAGE",_="Settings/Review_Workflows/WORKFLOW_UPDATE_STAGE",e="Settings/Review_Workflows/WORKFLOW_UPDATE_STAGES",M="Settings/Review_Workflows/WORKFLOW_UPDATE_STAGE_POSITION",f="Settings/Review_Workflows/WORKFLOW_UPDATE",C={primary600:"Blue",primary200:"Lilac",alternative600:"Violet",alternative200:"Lavender",success600:"Green",success200:"Pale Green",danger500:"Cherry",danger200:"Pink",warning600:"Orange",warning200:"Yellow",secondary600:"Teal",secondary200:"Baby Blue",neutral400:"Gray",neutral0:"White"},I=a._.colors.primary600,S={STAGE:"stage"},U="numberOfWorkflows",K="stagesPerWorkflow"},35658:(D,l,t)=>{t.d(l,{a:()=>P,g:()=>n});var a=t(57438),o=t(25524);function n(i){if(!i)return null;const O=Object.entries(a._.colors).filter(([,r])=>r.toUpperCase()===i.toUpperCase()).reduce((r,[T])=>(o.S?.[T]&&(r=T),r),null);return O?{themeColorName:O,name:o.S[O]}:null}function P(){return Object.entries(o.S).map(([i,d])=>({hex:a._.colors[i].toUpperCase(),name:d}))}},45534:(D,l,t)=>{t.d(l,{A:()=>o,S:()=>a});const a="strapi_stage",o="strapi_assignee"},85140:(D,l,t)=>{t.d(l,{a:()=>O,b:()=>r,c:()=>n,d:()=>P,e:()=>d,f:()=>i,u:()=>T});var a=t(43543);const o=a.n.injectEndpoints({endpoints:E=>({getWorkflows:E.query({query:s=>{const{id:_,...e}=s??{};return{url:`/admin/review-workflows/workflows/${_??""}`,method:"GET",config:{params:e}}},transformResponse:s=>{let _=[];return Array.isArray(s.data)?_=s.data:_=[s.data],{workflows:_,meta:"meta"in s?s.meta:void 0}},providesTags:(s,_,e)=>typeof e=="object"&&"id"in e&&e.id!==""?[{type:"ReviewWorkflow",id:e.id}]:[...s?.workflows.map(({id:M})=>({type:"ReviewWorkflow",id:M}))??[],{type:"ReviewWorkflow",id:"LIST"}]}),createWorkflow:E.mutation({query:s=>({url:"/admin/review-workflows/workflows",method:"POST",data:s}),transformResponse:s=>s.data,invalidatesTags:[{type:"ReviewWorkflow",id:"LIST"}]}),updateWorkflow:E.mutation({query:({id:s,..._})=>({url:`/admin/review-workflows/workflows/${s}`,method:"PUT",data:_}),transformResponse:s=>s.data,invalidatesTags:(s,_,e)=>[{type:"ReviewWorkflow",id:e.id}]}),deleteWorkflow:E.mutation({query:({id:s})=>({url:`/admin/review-workflows/workflows/${s}`,method:"DELETE"}),transformResponse:s=>s.data,invalidatesTags:(s,_,e)=>[{type:"ReviewWorkflow",id:e.id}]}),getStages:E.query({query:({model:s,slug:_,id:e})=>({url:`/admin/content-manager/${_}/${s}/${e}/stages`,method:"GET"}),transformResponse:s=>({meta:s.meta??{workflowCount:0},stages:s.data??[]}),providesTags:(s,_,e)=>[{type:"ReviewWorkflowStage",id:e.id}]}),updateStage:E.mutation({query:({model:s,slug:_,id:e,...M})=>({url:`/admin/content-manager/${_}/${s}/${e}/stage`,method:"PUT",data:M}),transformResponse:s=>s.data,invalidatesTags:(s,_,e)=>[{type:"ReviewWorkflowStage",id:e.id}]}),updateAssignee:E.mutation({query:({model:s,slug:_,id:e,...M})=>({url:`/admin/content-manager/${_}/${s}/${e}/assignee`,method:"PUT",data:M}),transformResponse:s=>s.data})}),overrideExisting:!1}),{useGetWorkflowsQuery:n,useCreateWorkflowMutation:P,useDeleteWorkflowMutation:i,useUpdateWorkflowMutation:d,useGetStagesQuery:O,useUpdateStageMutation:r,useUpdateAssigneeMutation:T}=o},85913:(D,l,t)=>{t.r(l),t.d(l,{REVIEW_WORKFLOW_COLUMNS_EE:()=>u,REVIEW_WORKFLOW_FILTERS:()=>y});var a=t(43543),o=t(45534),n=t(92132),P=t(76517),i=t(80782),d=t(43064),O=t(83997),r=t(48323),T=t(30893),E=t(54894),s=t(11614),_=t(35658),e=t(55506),M=t(15126),f=t(63299),C=t(67014),I=t(59080),S=t(79275),U=t(14718),K=t(21272),F=t(82437),k=t(61535),G=t(5790),j=t(12083),$=t(35223),x=t(5409),Y=t(74930),V=t(2600),z=t(48940),H=t(41286),Q=t(56336),X=t(13426),Z=t(84624),J=t(77965),b=t(54257),q=t(71210),tt=t(51187),st=t(39404),_t=t(58692),et=t(501),at=t(57646),ot=t(23120),nt=t(44414),Et=t(25962),rt=t(14664),lt=t(42588),it=t(90325),Ot=t(62785),dt=t(87443),Tt=t(41032),Pt=t(22957),Mt=t(93179),Dt=t(73055),Wt=t(15747),mt=t(85306),Rt=t(26509),At=t(32058),vt=t(81185),Lt=t(82261),gt=t(85140),ft=t(25524);const B=({value:m,onChange:v})=>{const{formatMessage:R}=(0,E.A)(),{data:L,isLoading:g}=(0,a.k)(),A=L?.users||[];return(0,n.jsx)(P.G3,{value:m,"aria-label":R({id:"content-manager.components.Filters.usersSelect.label",defaultMessage:"Search and select an user to filter"}),onChange:v,loading:g,children:A.map(W=>(0,n.jsx)(i.j,{value:W.id.toString(),children:(0,a.l)(W,R)},W.id))})},c=({value:m,onChange:v,uid:R})=>{const{formatMessage:L}=(0,E.A)(),{workflows:g,isLoading:A}=(0,s.u)({filters:{contentTypes:R}}),[W]=g??[];return(0,n.jsx)(r.Z,{"aria-label":L({id:"content-manager.components.Filters.reviewWorkflows.label",defaultMessage:"Search and select an workflow stage to filter"}),value:m,onChange:v,loading:A,customizeContent:()=>(0,n.jsxs)(O.s,{as:"span",justifyContent:"space-between",alignItems:"center",width:"100%",children:[(0,n.jsx)(T.o,{textColor:"neutral800",ellipsis:!0,children:m}),A?(0,n.jsx)(d.a,{small:!0,style:{display:"flex"}}):null]}),children:(W?.stages??[]).map(({id:p,color:w,name:h})=>{const{themeColorName:N}=(0,_.g)(w)??{};return(0,n.jsx)(r.eY,{startIcon:(0,n.jsx)(O.s,{height:2,background:w,borderColor:N==="neutral0"?"neutral150":void 0,hasRadius:!0,shrink:0,width:2}),value:h,children:h},p)})})},u=[{key:`__${o.S}_temp_key__`,name:o.S,fieldSchema:{type:"relation",relation:"oneToMany",target:"admin::review-workflow-stage"},metadatas:{label:{id:(0,a.o)("containers.ListPage.table-headers.reviewWorkflows.stage"),defaultMessage:"Review stage"},searchable:!1,sortable:!0,mainField:{name:"name",type:"string"}}},{key:`__${o.A}_temp_key__`,name:o.A,fieldSchema:{type:"relation",target:"admin::user",relation:"oneToMany"},metadatas:{label:{id:(0,a.o)("containers.ListPage.table-headers.reviewWorkflows.assignee"),defaultMessage:"Assignee"},searchable:!1,sortable:!0,mainField:{name:"firstname",type:"string"}}}],y=[{fieldSchema:{type:"relation",mainField:{name:"name",type:"string"}},metadatas:{customInput:c,label:{id:(0,a.o)("containers.ListPage.table-headers.reviewWorkflows.stage"),defaultMessage:"Review stage"}},name:"strapi_stage"},{fieldSchema:{type:"relation",mainField:{name:"id",type:"integer"}},metadatas:{customInput:B,customOperators:[{intlLabel:{id:"components.FilterOptions.FILTER_TYPES.$eq",defaultMessage:"is"},value:"$eq"},{intlLabel:{id:"components.FilterOptions.FILTER_TYPES.$ne",defaultMessage:"is not"},value:"$ne"}],label:{id:(0,a.o)("containers.ListPage.table-headers.reviewWorkflows.assignee.label"),defaultMessage:"Assignee"}},name:"strapi_assignee"}]}}]);
