"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[309],{10309:(G,L,s)=>{s.d(L,{ProtectedEditPage:()=>Z});var t=s(92132),v=s(21272),i=s(94061),m=s(85963),B=s(90151),h=s(68074),I=s(4198),U=s(55356),A=s(38413),e=s(83997),c=s(7537),T=s(5287),R=s(30893),W=s(21610),o=s(55506),K=s(46270),f=s(61535),p=s(54894),j=s(17703),Q=s(71389),x=s(12083),l=s(43543),Y=s(85071),$=s(63884),ns=s(15126),Es=s(63299),is=s(67014),rs=s(59080),ds=s(79275),es=s(14718),ls=s(82437),Ms=s(5790),Ps=s(35223),Ds=s(5409),Os=s(74930),ms=s(2600),hs=s(48940),As=s(41286),Rs=s(56336),gs=s(13426),Cs=s(84624),Ls=s(77965),vs=s(54257),Bs=s(71210),Is=s(51187),Us=s(39404),cs=s(58692),Ts=s(501),Ws=s(57646),Ks=s(23120),fs=s(44414),js=s(25962),xs=s(14664),us=s(42588),ys=s(90325),ps=s(62785),Ss=s(87443),Ns=s(41032),Fs=s(22957),zs=s(93179),Vs=s(73055),Gs=s(15747),Qs=s(85306),Ys=s(26509),$s=s(32058),Hs=s(81185),Js=s(82261),Xs=s(412),Zs=s(94710);const H=({disabled:n,role:a,values:D,errors:_,onChange:r,onBlur:g})=>{const{formatMessage:E}=(0,p.A)();return(0,t.jsx)(i.a,{background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:(0,t.jsxs)(e.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,t.jsxs)(e.s,{justifyContent:"space-between",children:[(0,t.jsxs)(i.a,{children:[(0,t.jsx)(i.a,{children:(0,t.jsx)(R.o,{fontWeight:"bold",children:a?a.name:E({id:"global.details",defaultMessage:"Details"})})}),(0,t.jsx)(i.a,{children:(0,t.jsx)(R.o,{textColor:"neutral500",variant:"pi",children:a?a.description:E({id:"Settings.roles.form.description",defaultMessage:"Name and description of the role"})})})]}),(0,t.jsx)(m.$,{disabled:!0,variant:"secondary",children:E({id:"Settings.roles.form.button.users-with-role",defaultMessage:"{number, plural, =0 {# users} one {# user} other {# users}} with this role"},{number:a.usersCount})})]}),(0,t.jsxs)(B.x,{gap:4,children:[(0,t.jsx)(h.E,{col:6,children:(0,t.jsx)(c.k,{disabled:n,name:"name",error:_.name&&E({id:_.name}),label:E({id:"global.name",defaultMessage:"Name"}),onChange:r,onBlur:g,required:!0,value:D.name||""})}),(0,t.jsx)(h.E,{col:6,children:(0,t.jsx)(T.T,{disabled:n,label:E({id:"global.description",defaultMessage:"Description"}),id:"description",error:_.name&&E({id:_.name}),onChange:r,onBlur:g,children:D.description||""})})]})]})})},J=x.Ik().shape({name:x.Yj().required(o.iW.required),description:x.Yj().optional()}),X=()=>{const n=(0,o.hN)(),{formatMessage:a}=(0,p.A)(),_=(0,j.W5)("/settings/roles/:id")?.params.id,r=v.useRef(null),{lockApp:g,unlockApp:E}=(0,o.MA)(),{trackUsage:b}=(0,o.z1)(),{_unstableFormatAPIError:S,_unstableFormatValidationErrors:N}=(0,o.wq)(),{isLoading:k,data:F}=(0,l.A)({role:_??""}),{roles:w,isLoading:z,refetch:q}=(0,Y.u)({id:_},{refetchOnMountOrArgChange:!0}),P=w[0]??{},{data:ss,isLoading:ts}=(0,l.B)({id:_},{skip:!_,refetchOnMountOrArgChange:!0}),[_s]=(0,l.G)(),[as]=(0,l.E)();if(!_)return(0,t.jsx)(j.rd,{to:"/settings/roles"});const os=async(u,C)=>{try{g();const{permissionsToSend:O,didUpdateConditions:y}=r.current?.getPermissions()??{},d=await _s({id:_,...u});if("error"in d){(0,l.x)(d.error)&&d.error.name==="ValidationError"?C.setErrors(N(d.error)):n({type:"warning",message:S(d.error)});return}if(P.code!=="strapi-super-admin"&&O){const M=await as({id:d.data.id,permissions:O});if("error"in M){(0,l.x)(M.error)&&M.error.name==="ValidationError"?C.setErrors(N(M.error)):n({type:"warning",message:S(M.error)});return}y&&b("didUpdateConditions")}r.current?.setFormAfterSubmit(),await q(),n({type:"success",message:{id:"notification.success.saved"}})}catch{n({type:"warning",message:{id:"notification.error"}})}finally{E()}},V=!z&&P.code==="strapi-super-admin";return(0,t.jsxs)(A.g,{children:[(0,t.jsx)(o.x7,{name:"Roles"}),(0,t.jsx)(f.l1,{enableReinitialize:!0,initialValues:{name:P.name??"",description:P.description??""},onSubmit:os,validationSchema:J,validateOnChange:!1,children:({handleSubmit:u,values:C,errors:O,handleChange:y,handleBlur:d,isSubmitting:M})=>(0,t.jsxs)("form",{onSubmit:u,children:[(0,t.jsx)(U.Q,{primaryAction:(0,t.jsx)(e.s,{gap:2,children:(0,t.jsx)(m.$,{type:"submit",disabled:P.code==="strapi-super-admin",loading:M,size:"L",children:a({id:"global.save",defaultMessage:"Save"})})}),title:a({id:"Settings.roles.edit.title",defaultMessage:"Edit a role"}),subtitle:a({id:"Settings.roles.create.description",defaultMessage:"Define the rights given to the role"}),navigationAction:(0,t.jsx)(W.N,{as:Q.k2,startIcon:(0,t.jsx)(K.A,{}),to:"/settings/roles",children:a({id:"global.back",defaultMessage:"Back"})})}),(0,t.jsx)(I.s,{children:(0,t.jsxs)(e.s,{direction:"column",alignItems:"stretch",gap:6,children:[(0,t.jsx)(H,{disabled:V,errors:O,values:C,onChange:y,onBlur:d,role:P}),!k&&!z&&!ts&&F?(0,t.jsx)(i.a,{shadow:"filterShadow",hasRadius:!0,children:(0,t.jsx)($.P,{isFormDisabled:V,permissions:ss,ref:r,layout:F})}):(0,t.jsx)(i.a,{background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:(0,t.jsx)(o.Bl,{})})]})})]})})]})},Z=()=>{const n=(0,l.j)(r=>r.admin_app.permissions.settings?.roles),{isLoading:a,allowedActions:{canRead:D,canUpdate:_}}=(0,o.ec)(n);return a?(0,t.jsx)(o.Bl,{}):!D&&!_?(0,t.jsx)(j.rd,{to:"/"}):(0,t.jsx)(X,{})}},85071:(G,L,s)=>{s.d(L,{u:()=>B});var t=s(21272),v=s(55506),i=s(54894),m=s(43543);const B=(h={},I)=>{const{locale:U}=(0,i.A)(),A=(0,v.QM)(U,{sensitivity:"base"}),{data:e,error:c,isError:T,isLoading:R,refetch:W}=(0,m.z)(h,I);return{roles:t.useMemo(()=>[...e??[]].sort((K,f)=>A.compare(K.name,f.name)),[e,A]),error:c,isError:T,isLoading:R,refetch:W}}}}]);
