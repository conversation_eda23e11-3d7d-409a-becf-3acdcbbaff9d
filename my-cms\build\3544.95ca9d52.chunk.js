"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3544],{63544:(t,e,i)=>{i.r(e),i.d(e,{default:()=>n});const n={"BoundRoute.title":"Pfad gebunden an","EditForm.inputSelect.description.role":"<PERSON> Rolle, die neu authentifizierten Benutzern automatisch zugewiesen wird.","EditForm.inputSelect.label.role":"Standardrolle f\xFCr authentifizierte Benutzer","EditForm.inputToggle.description.email":"Verbiete das Anlegen verschiedener Accounts mit der gleichen E-Mail-Adresse bei unterschiedlichen Anmeldemethoden.","EditForm.inputToggle.description.email-confirmation":"Wenn aktiviert (ON) erhalten neu registrierte Benutzer eine Best\xE4tigungs-E-Mail.","EditForm.inputToggle.description.email-confirmation-redirection":"Nachdem Sie die E-Mail best\xE4tigt haben, w\xE4hle wohin sie weitergeleitet wird.","EditForm.inputToggle.description.email-reset-password":"URL deiner Passwort-Zur\xFCcksetzen-Seite deiner Anwendung","EditForm.inputToggle.description.sign-up":"Wenn deaktiviert (OFF), wird der Registrationsprozess unterbunden. Niemand kann sich mehr registrieren.","EditForm.inputToggle.label.email":"Ein Account pro E-Mail-Adresse","EditForm.inputToggle.label.email-confirmation":"Aktiviere E-Mail Benachrichtigungen","EditForm.inputToggle.label.email-confirmation-redirection":"Weiterleitungs-URL","EditForm.inputToggle.label.email-reset-password":"Passwort-Zur\xFCcksetzen-Seite","EditForm.inputToggle.label.sign-up":"Registration erm\xF6glichen","Email.template.email_confirmation":"Best\xE4tigung der E-Mail-Addresse","HeaderNav.link.advancedSettings":"Erweiterte Einstellungen","HeaderNav.link.emailTemplates":"E-Mail-Templates","HeaderNav.link.providers":"Methoden","Plugin.permissions.plugins.description":"Definiere die m\xF6glichen Aktionen des {name} Plugins.","Plugins.header.description":"Nur Aktionen, die an einen Pfad gebunden sind, werden hier gelistet.","Plugins.header.title":"Berechtigungen","Policies.header.hint":"W\xE4hle eine Aktion aus und klicke auf das Zahnrad, um den an diese Aktion gebundenen Pfad anzuzeigen","Policies.header.title":"Fortgeschrittene Einstellungen","PopUpForm.Email.email_templates.inputDescription":"{link} f\xFCr mehr Informationen","PopUpForm.Email.link.documentation":"Lies die Dokumentation","PopUpForm.Email.options.from.email.label":"E-Mail-Adresse des Absenders","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Name des Absenders","PopUpForm.Email.options.from.name.placeholder":"Kai Doe","PopUpForm.Email.options.message.label":"Nachricht","PopUpForm.Email.options.object.label":"Betreff","PopUpForm.Email.options.object.placeholder":"Bitte best\xE4tige deine E-Mail-Adresse f\xFCr %APP_NAME%","PopUpForm.Email.options.response_email.label":"Antwort E-Mail-Adresse","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"Wenn deaktiviert, kann diese Methode nicht verwendet werden.","PopUpForm.Providers.enabled.label":"Aktivieren","PopUpForm.Providers.key.label":"Client ID","PopUpForm.Providers.key.placeholder":"TEXT","PopUpForm.Providers.redirectURL.front-end.label":"Die URL zur Weiterleitung zu deiner Frontend-App","PopUpForm.Providers.redirectURL.label":"Die Weiterleitungs-URL f\xFCr die App-Einstellungen von {provider}","PopUpForm.Providers.secret.label":"Client Secret","PopUpForm.Providers.secret.placeholder":"TEXT","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"E-Mail-Templates bearbeiten","PopUpForm.header.edit.providers":"Anbieter bearbeiten","Settings.roles.deleted":"Rolle gel\xF6scht","Settings.roles.edited":"Rolle bearbeitet","Settings.section-label":"Nutzer- & Berechtigungen-Plugin","notification.success.submit":"Einstellungen aktualisiert","plugin.description.long":"Besch\xFCtze deine API mit einem vollst\xE4ndigen Authentifikationsprozess basierend auf JWT. Zudem bietet dieses Plugin eine ACL-Strategie, die erlaubt, die Berechtigungen f\xFCr Benutzergruppen festzulegen.","plugin.description.short":"Besch\xFCtze deine API mit einem vollst\xE4ndigen Authentifikationsprozess basierend auf JWT.","plugin.name":"Nutzer- & Berechtigungen-Plugin","popUpWarning.button.cancel":"Abbrechen","popUpWarning.button.confirm":"Best\xE4tigen","popUpWarning.title":"Bitte best\xE4tigen","popUpWarning.warning.cancel":"Willst du wirklich alle deine \xC4nderungen verwerfen?"}}}]);
