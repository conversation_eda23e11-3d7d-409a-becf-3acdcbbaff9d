(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3902],{2547:(f,T,t)=>{class e{constructor(g,E){if(E=u(E),g instanceof e)return g.loose===!!E.loose&&g.includePrerelease===!!E.includePrerelease?g:new e(g.raw,E);if(g instanceof c)return this.raw=g.value,this.set=[[g]],this.format(),this;if(this.options=E,this.loose=!!E.loose,this.includePrerelease=!!E.includePrerelease,this.raw=g.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(S=>this.parseRange(S.trim())).filter(S=>S.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const S=this.set[0];if(this.set=this.set.filter($=>!W($[0])),this.set.length===0)this.set=[S];else if(this.set.length>1){for(const $ of this.set)if($.length===1&&D($[0])){this.set=[$];break}}}this.format()}format(){return this.range=this.set.map(g=>g.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange(g){const S=((this.options.includePrerelease&&x)|(this.options.loose&&C))+":"+g,$=i.get(S);if($)return $;const j=this.options.loose,L=j?a[r.HYPHENRANGELOOSE]:a[r.HYPHENRANGE];g=g.replace(L,se(this.options.includePrerelease)),s("hyphen replace",g),g=g.replace(a[r.COMPARATORTRIM],l),s("comparator trim",g),g=g.replace(a[r.TILDETRIM],d),s("tilde trim",g),g=g.replace(a[r.CARETTRIM],p),s("caret trim",g);let z=g.split(" ").map(X=>Z(X,this.options)).join(" ").split(/\s+/).map(X=>Y(X,this.options));j&&(z=z.filter(X=>(s("loose invalid filter",X,this.options),!!X.match(a[r.COMPARATORLOOSE])))),s("range list",z);const N=new Map,U=z.map(X=>new c(X,this.options));for(const X of U){if(W(X))return[X];N.set(X.value,X)}N.size>1&&N.has("")&&N.delete("");const Q=[...N.values()];return i.set(S,Q),Q}intersects(g,E){if(!(g instanceof e))throw new TypeError("a Range is required");return this.set.some(S=>I(S,E)&&g.set.some($=>I($,E)&&S.every(j=>$.every(L=>j.intersects(L,E)))))}test(g){if(!g)return!1;if(typeof g=="string")try{g=new n(g,this.options)}catch{return!1}for(let E=0;E<this.set.length;E++)if(he(this.set[E],g,this.options))return!0;return!1}}f.exports=e;const h=t(71969),i=new h({max:1e3}),u=t(1231),c=t(99260),s=t(47180),n=t(85968),{safeRe:a,t:r,comparatorTrimReplace:l,tildeTrimReplace:d,caretTrimReplace:p}=t(88618),{FLAG_INCLUDE_PRERELEASE:x,FLAG_LOOSE:C}=t(97718),W=m=>m.value==="<0.0.0-0",D=m=>m.value==="",I=(m,g)=>{let E=!0;const S=m.slice();let $=S.pop();for(;E&&S.length;)E=S.every(j=>$.intersects(j,g)),$=S.pop();return E},Z=(m,g)=>(s("comp",m,g),m=y(m,g),s("caret",m),m=M(m,g),s("tildes",m),m=R(m,g),s("xrange",m),m=q(m,g),s("stars",m),m),H=m=>!m||m.toLowerCase()==="x"||m==="*",M=(m,g)=>m.trim().split(/\s+/).map(E=>o(E,g)).join(" "),o=(m,g)=>{const E=g.loose?a[r.TILDELOOSE]:a[r.TILDE];return m.replace(E,(S,$,j,L,z)=>{s("tilde",m,S,$,j,L,z);let N;return H($)?N="":H(j)?N=`>=${$}.0.0 <${+$+1}.0.0-0`:H(L)?N=`>=${$}.${j}.0 <${$}.${+j+1}.0-0`:z?(s("replaceTilde pr",z),N=`>=${$}.${j}.${L}-${z} <${$}.${+j+1}.0-0`):N=`>=${$}.${j}.${L} <${$}.${+j+1}.0-0`,s("tilde return",N),N})},y=(m,g)=>m.trim().split(/\s+/).map(E=>v(E,g)).join(" "),v=(m,g)=>{s("caret",m,g);const E=g.loose?a[r.CARETLOOSE]:a[r.CARET],S=g.includePrerelease?"-0":"";return m.replace(E,($,j,L,z,N)=>{s("caret",m,$,j,L,z,N);let U;return H(j)?U="":H(L)?U=`>=${j}.0.0${S} <${+j+1}.0.0-0`:H(z)?j==="0"?U=`>=${j}.${L}.0${S} <${j}.${+L+1}.0-0`:U=`>=${j}.${L}.0${S} <${+j+1}.0.0-0`:N?(s("replaceCaret pr",N),j==="0"?L==="0"?U=`>=${j}.${L}.${z}-${N} <${j}.${L}.${+z+1}-0`:U=`>=${j}.${L}.${z}-${N} <${j}.${+L+1}.0-0`:U=`>=${j}.${L}.${z}-${N} <${+j+1}.0.0-0`):(s("no pr"),j==="0"?L==="0"?U=`>=${j}.${L}.${z}${S} <${j}.${L}.${+z+1}-0`:U=`>=${j}.${L}.${z}${S} <${j}.${+L+1}.0-0`:U=`>=${j}.${L}.${z} <${+j+1}.0.0-0`),s("caret return",U),U})},R=(m,g)=>(s("replaceXRanges",m,g),m.split(/\s+/).map(E=>P(E,g)).join(" ")),P=(m,g)=>{m=m.trim();const E=g.loose?a[r.XRANGELOOSE]:a[r.XRANGE];return m.replace(E,(S,$,j,L,z,N)=>{s("xRange",m,S,$,j,L,z,N);const U=H(j),Q=U||H(L),X=Q||H(z),oe=X;return $==="="&&oe&&($=""),N=g.includePrerelease?"-0":"",U?$===">"||$==="<"?S="<0.0.0-0":S="*":$&&oe?(Q&&(L=0),z=0,$===">"?($=">=",Q?(j=+j+1,L=0,z=0):(L=+L+1,z=0)):$==="<="&&($="<",Q?j=+j+1:L=+L+1),$==="<"&&(N="-0"),S=`${$+j}.${L}.${z}${N}`):Q?S=`>=${j}.0.0${N} <${+j+1}.0.0-0`:X&&(S=`>=${j}.${L}.0${N} <${j}.${+L+1}.0-0`),s("xRange return",S),S})},q=(m,g)=>(s("replaceStars",m,g),m.trim().replace(a[r.STAR],"")),Y=(m,g)=>(s("replaceGTE0",m,g),m.trim().replace(a[g.includePrerelease?r.GTE0PRE:r.GTE0],"")),se=m=>(g,E,S,$,j,L,z,N,U,Q,X,oe,ne)=>(H(S)?E="":H($)?E=`>=${S}.0.0${m?"-0":""}`:H(j)?E=`>=${S}.${$}.0${m?"-0":""}`:L?E=`>=${E}`:E=`>=${E}${m?"-0":""}`,H(U)?N="":H(Q)?N=`<${+U+1}.0.0-0`:H(X)?N=`<${U}.${+Q+1}.0-0`:oe?N=`<=${U}.${Q}.${X}-${oe}`:m?N=`<${U}.${Q}.${+X+1}-0`:N=`<=${N}`,`${E} ${N}`.trim()),he=(m,g,E)=>{for(let S=0;S<m.length;S++)if(!m[S].test(g))return!1;if(g.prerelease.length&&!E.includePrerelease){for(let S=0;S<m.length;S++)if(s(m[S].semver),m[S].semver!==c.ANY&&m[S].semver.prerelease.length>0){const $=m[S].semver;if($.major===g.major&&$.minor===g.minor&&$.patch===g.patch)return!0}return!1}return!0}},2972:(f,T,t)=>{const e=t(39100),h=(i,u,c)=>e(i,u,c)<=0;f.exports=h},6515:(f,T,t)=>{"use strict";f.exports=e,e.Node=c,e.create=e;function e(s){var n=this;if(n instanceof e||(n=new e),n.tail=null,n.head=null,n.length=0,s&&typeof s.forEach=="function")s.forEach(function(l){n.push(l)});else if(arguments.length>0)for(var a=0,r=arguments.length;a<r;a++)n.push(arguments[a]);return n}e.prototype.removeNode=function(s){if(s.list!==this)throw new Error("removing node which does not belong to this list");var n=s.next,a=s.prev;return n&&(n.prev=a),a&&(a.next=n),s===this.head&&(this.head=n),s===this.tail&&(this.tail=a),s.list.length--,s.next=null,s.prev=null,s.list=null,n},e.prototype.unshiftNode=function(s){if(s!==this.head){s.list&&s.list.removeNode(s);var n=this.head;s.list=this,s.next=n,n&&(n.prev=s),this.head=s,this.tail||(this.tail=s),this.length++}},e.prototype.pushNode=function(s){if(s!==this.tail){s.list&&s.list.removeNode(s);var n=this.tail;s.list=this,s.prev=n,n&&(n.next=s),this.tail=s,this.head||(this.head=s),this.length++}},e.prototype.push=function(){for(var s=0,n=arguments.length;s<n;s++)i(this,arguments[s]);return this.length},e.prototype.unshift=function(){for(var s=0,n=arguments.length;s<n;s++)u(this,arguments[s]);return this.length},e.prototype.pop=function(){if(this.tail){var s=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,s}},e.prototype.shift=function(){if(this.head){var s=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,s}},e.prototype.forEach=function(s,n){n=n||this;for(var a=this.head,r=0;a!==null;r++)s.call(n,a.value,r,this),a=a.next},e.prototype.forEachReverse=function(s,n){n=n||this;for(var a=this.tail,r=this.length-1;a!==null;r--)s.call(n,a.value,r,this),a=a.prev},e.prototype.get=function(s){for(var n=0,a=this.head;a!==null&&n<s;n++)a=a.next;if(n===s&&a!==null)return a.value},e.prototype.getReverse=function(s){for(var n=0,a=this.tail;a!==null&&n<s;n++)a=a.prev;if(n===s&&a!==null)return a.value},e.prototype.map=function(s,n){n=n||this;for(var a=new e,r=this.head;r!==null;)a.push(s.call(n,r.value,this)),r=r.next;return a},e.prototype.mapReverse=function(s,n){n=n||this;for(var a=new e,r=this.tail;r!==null;)a.push(s.call(n,r.value,this)),r=r.prev;return a},e.prototype.reduce=function(s,n){var a,r=this.head;if(arguments.length>1)a=n;else if(this.head)r=this.head.next,a=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var l=0;r!==null;l++)a=s(a,r.value,l),r=r.next;return a},e.prototype.reduceReverse=function(s,n){var a,r=this.tail;if(arguments.length>1)a=n;else if(this.tail)r=this.tail.prev,a=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var l=this.length-1;r!==null;l--)a=s(a,r.value,l),r=r.prev;return a},e.prototype.toArray=function(){for(var s=new Array(this.length),n=0,a=this.head;a!==null;n++)s[n]=a.value,a=a.next;return s},e.prototype.toArrayReverse=function(){for(var s=new Array(this.length),n=0,a=this.tail;a!==null;n++)s[n]=a.value,a=a.prev;return s},e.prototype.slice=function(s,n){n=n||this.length,n<0&&(n+=this.length),s=s||0,s<0&&(s+=this.length);var a=new e;if(n<s||n<0)return a;s<0&&(s=0),n>this.length&&(n=this.length);for(var r=0,l=this.head;l!==null&&r<s;r++)l=l.next;for(;l!==null&&r<n;r++,l=l.next)a.push(l.value);return a},e.prototype.sliceReverse=function(s,n){n=n||this.length,n<0&&(n+=this.length),s=s||0,s<0&&(s+=this.length);var a=new e;if(n<s||n<0)return a;s<0&&(s=0),n>this.length&&(n=this.length);for(var r=this.length,l=this.tail;l!==null&&r>n;r--)l=l.prev;for(;l!==null&&r>s;r--,l=l.prev)a.push(l.value);return a},e.prototype.splice=function(s,n,...a){s>this.length&&(s=this.length-1),s<0&&(s=this.length+s);for(var r=0,l=this.head;l!==null&&r<s;r++)l=l.next;for(var d=[],r=0;l&&r<n;r++)d.push(l.value),l=this.removeNode(l);l===null&&(l=this.tail),l!==this.head&&l!==this.tail&&(l=l.prev);for(var r=0;r<a.length;r++)l=h(this,l,a[r]);return d},e.prototype.reverse=function(){for(var s=this.head,n=this.tail,a=s;a!==null;a=a.prev){var r=a.prev;a.prev=a.next,a.next=r}return this.head=n,this.tail=s,this};function h(s,n,a){var r=n===s.head?new c(a,null,n,s):new c(a,n,n.next,s);return r.next===null&&(s.tail=r),r.prev===null&&(s.head=r),s.length++,r}function i(s,n){s.tail=new c(n,s.tail,null,s),s.head||(s.head=s.tail),s.length++}function u(s,n){s.head=new c(n,null,s.head,s),s.tail||(s.tail=s.head),s.length++}function c(s,n,a,r){if(!(this instanceof c))return new c(s,n,a,r);this.list=r,this.value=s,n?(n.next=this,this.prev=n):this.prev=null,a?(a.prev=this,this.next=a):this.next=null}try{t(83479)(e)}catch{}},8051:(f,T,t)=>{const e=t(41833),h=(i,u)=>i.sort((c,s)=>e(c,s,u));f.exports=h},12419:(f,T,t)=>{const e=t(2547),h=(i,u)=>new e(i,u).set.map(c=>c.map(s=>s.value).join(" ").trim().split(" "));f.exports=h},14149:(f,T,t)=>{const e=t(39100),h=(i,u,c)=>e(i,u,c)===0;f.exports=h},16454:(f,T,t)=>{const e=t(39100),h=(i,u,c)=>e(u,i,c);f.exports=h},26434:(f,T,t)=>{const e=t(2547),h=(i,u,c)=>{try{u=new e(u,c)}catch{return!1}return u.test(i)};f.exports=h},27009:(f,T,t)=>{const e=t(26434),h=t(39100);f.exports=(i,u,c)=>{const s=[];let n=null,a=null;const r=i.sort((x,C)=>h(x,C,c));for(const x of r)e(x,u,c)?(a=x,n||(n=x)):(a&&s.push([n,a]),a=null,n=null);n&&s.push([n,null]);const l=[];for(const[x,C]of s)x===C?l.push(x):!C&&x===r[0]?l.push("*"):C?x===r[0]?l.push(`<=${C}`):l.push(`${x} - ${C}`):l.push(`>=${x}`);const d=l.join(" || "),p=typeof u.raw=="string"?u.raw:String(u);return d.length<p.length?d:u}},28953:(f,T,t)=>{const e=t(41833),h=(i,u)=>i.sort((c,s)=>e(s,c,u));f.exports=h},35225:(f,T,t)=>{const e=t(88618),h=t(97718),i=t(85968),u=t(46087),c=t(1660),s=t(79077),n=t(64530),a=t(78203),r=t(96924),l=t(55182),d=t(93546),p=t(60185),x=t(45349),C=t(39100),W=t(16454),D=t(41607),I=t(41833),Z=t(8051),H=t(28953),M=t(79656),o=t(55151),y=t(14149),v=t(37075),R=t(52869),P=t(2972),q=t(96763),Y=t(73182),se=t(99260),he=t(2547),m=t(26434),g=t(12419),E=t(98536),S=t(79762),$=t(50777),j=t(35902),L=t(38079),z=t(99367),N=t(61010),U=t(73328),Q=t(27009),X=t(62508);f.exports={parse:c,valid:s,clean:n,inc:a,diff:r,major:l,minor:d,patch:p,prerelease:x,compare:C,rcompare:W,compareLoose:D,compareBuild:I,sort:Z,rsort:H,gt:M,lt:o,eq:y,neq:v,gte:R,lte:P,cmp:q,coerce:Y,Comparator:se,Range:he,satisfies:m,toComparators:g,maxSatisfying:E,minSatisfying:S,minVersion:$,validRange:j,outside:L,gtr:z,ltr:N,intersects:U,simplifyRange:Q,subset:X,SemVer:i,re:e.re,src:e.src,tokens:e.t,SEMVER_SPEC_VERSION:h.SEMVER_SPEC_VERSION,RELEASE_TYPES:h.RELEASE_TYPES,compareIdentifiers:u.compareIdentifiers,rcompareIdentifiers:u.rcompareIdentifiers}},35902:(f,T,t)=>{const e=t(2547),h=(i,u)=>{try{return new e(i,u).range||"*"}catch{return null}};f.exports=h},37075:(f,T,t)=>{const e=t(39100),h=(i,u,c)=>e(i,u,c)!==0;f.exports=h},38079:(f,T,t)=>{const e=t(85968),h=t(99260),{ANY:i}=h,u=t(2547),c=t(26434),s=t(79656),n=t(55151),a=t(2972),r=t(52869),l=(d,p,x,C)=>{d=new e(d,C),p=new u(p,C);let W,D,I,Z,H;switch(x){case">":W=s,D=a,I=n,Z=">",H=">=";break;case"<":W=n,D=r,I=s,Z="<",H="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(c(d,p,C))return!1;for(let M=0;M<p.set.length;++M){const o=p.set[M];let y=null,v=null;if(o.forEach(R=>{R.semver===i&&(R=new h(">=0.0.0")),y=y||R,v=v||R,W(R.semver,y.semver,C)?y=R:I(R.semver,v.semver,C)&&(v=R)}),y.operator===Z||y.operator===H||(!v.operator||v.operator===Z)&&D(d,v.semver))return!1;if(v.operator===H&&I(d,v.semver))return!1}return!0};f.exports=l},40799:(f,T,t)=>{"use strict";t.d(T,{A:()=>i});var e=t(92132);const h=u=>(0,e.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 24 24",...u,children:(0,e.jsx)("path",{fill:"#161614",d:"M12 0C5.373 0 0 5.501 0 12.288c0 5.43 3.438 10.035 8.206 11.66.6.114.82-.266.82-.59 0-.294-.01-1.262-.016-2.289-3.338.744-4.043-1.45-4.043-1.45-.546-1.42-1.332-1.797-1.332-1.797-1.089-.763.082-.747.082-.747 1.205.086 1.84 1.266 1.84 1.266 1.07 1.878 2.807 1.335 3.491 1.021.108-.794.42-1.336.762-1.643-2.665-.31-5.467-1.364-5.467-6.073 0-1.341.469-2.437 1.236-3.298-.124-.31-.535-1.56.117-3.252 0 0 1.007-.33 3.3 1.26A11.25 11.25 0 0 1 12 5.942c1.02.005 2.047.141 3.006.414 2.29-1.59 3.297-1.26 3.297-1.26.653 1.693.242 2.943.118 3.252.77.86 1.235 1.957 1.235 3.298 0 4.72-2.808 5.76-5.48 6.063.43.382.814 1.13.814 2.276 0 1.644-.014 2.967-.014 3.372 0 .327.216.71.825.59C20.566 22.32 24 17.715 24 12.288 24 5.501 18.627 0 12 0Z"})}),i=h},41607:(f,T,t)=>{const e=t(39100),h=(i,u)=>e(i,u,!0);f.exports=h},41833:(f,T,t)=>{const e=t(85968),h=(i,u,c)=>{const s=new e(i,c),n=new e(u,c);return s.compare(n)||s.compareBuild(n)};f.exports=h},43902:(f,T,t)=>{"use strict";t.r(T),t.d(T,{MarketplacePage:()=>Me,ProtectedMarketplacePage:()=>Qe});var e=t(92132),h=t(21272),i=t(94061),u=t(12493),c=t(52993),s=t(85963),n=t(83997),a=t(56654),r=t(30893),l=t(79739),d=t(58805),p=t(48653),x=t(43064),C=t(81035),W=t(90151),D=t(68074),I=t(55356),Z=t(42455),H=t(38413),M=t(48323),o=t(11273),y=t(4198),v=t(12081),R=t(12408),P=t(7297),q=t(24093),Y=t(55506),se=t(28604),he=t(68802),m=t(85166),g=t(14595),E=t(54514),S=t(90625),$=t(40799),j=t(42386),L=t(93714),z=t(36481);const N=A=>(0,e.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 24 25",...A,children:(0,e.jsx)("path",{fill:"#212134",fillRule:"evenodd",d:"M13.571 18.272H10.43v-8.47H2.487a.2.2 0 0 1-.14-.343L11.858.058a.2.2 0 0 1 .282 0l9.513 9.4a.2.2 0 0 1-.14.343H13.57v8.47ZM2.2 21.095a.2.2 0 0 0-.2.2v2.424c0 .*********.2h19.6a.2.2 0 0 0 .2-.2v-2.424a.2.2 0 0 0-.2-.2H2.2Z",clipRule:"evenodd"})}),U=N,Q=A=>(0,e.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1rem",height:"1rem",fill:"none",viewBox:"0 0 32 32",...A,children:[(0,e.jsx)("path",{fill:"#AC73E6",d:"M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4Z"}),(0,e.jsx)("path",{fill:"#fff",fillRule:"evenodd",d:"M15.027 13.839c-3.19-.836-6.305-1.064-10.18-.608-1.215.152-1.063 1.975.076 2.203.304.836.456 2.355.912 3.267.987 2.279 5.622 1.975 7.369.835 1.14-.683 1.443-2.279 1.9-3.494.227-.684 1.595-.684 1.822 0 .38 1.215.76 2.81 1.9 3.494 1.747 1.14 6.381 1.444 7.369-.835.456-.912.607-2.431.911-3.267 1.14-.228 1.216-2.051.076-2.203-3.874-.456-6.989-.228-10.18.608-.455.075-1.519.075-1.975 0Z",clipRule:"evenodd"})]}),X=Q;var oe=t(14718),ne=t(54894),Oe=t(82437),ye=t(43543),Ie=t(66248),ue=t(63891),je=t(49654),Ne=t(21835),pe=t(35225),Pe=t(5409),Se=t(74930),et=t(15126),tt=t(63299),st=t(67014),nt=t(59080),at=t(79275),rt=t(61535),it=t(5790),ot=t(12083),lt=t(35223),ct=t(2600),ht=t(48940),ut=t(41286),dt=t(56336),ft=t(13426),gt=t(84624),pt=t(77965),mt=t(54257),vt=t(71210),xt=t(51187),yt=t(39404),jt=t(58692),Pt=t(501),St=t(57646),$t=t(23120),Et=t(44414),At=t(25962),Ct=t(14664),Rt=t(42588),Mt=t(90325),Tt=t(62785),Lt=t(87443),Ot=t(41032),It=t(22957),Nt=t(93179),wt=t(73055),Dt=t(15747),Vt=t(85306),Bt=t(26509),Gt=t(32058),Ht=t(81185),zt=t(82261),Ft=t(55151),Wt=t(79077);const we=({handleSelectClear:A,handleSelectChange:O,npmPackageType:V,possibleCategories:F,possibleCollections:B,query:b})=>{const[w,K]=h.useState(!1),te=h.useRef(null),{formatMessage:J}=(0,ne.A)(),k=()=>K(G=>!G),_=(G,ee)=>{const le={[ee]:(b[ee]??[]).filter(ce=>ce!==G)};O(le)};return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(i.a,{paddingTop:1,paddingBottom:1,children:[(0,e.jsx)(De,{variant:"tertiary",ref:te,startIcon:(0,e.jsx)(se.A,{}),onClick:k,size:"S",children:J({id:"app.utils.filters",defaultMessage:"Filters"})}),w&&(0,e.jsx)(u.AM,{source:te,onDismiss:k,padding:3,spacing:4,children:(0,e.jsxs)(Ve,{direction:"column",alignItems:"stretch",gap:1,children:[(0,e.jsx)($e,{message:J({id:"admin.pages.MarketPlacePage.filters.collections",defaultMessage:"Collections"}),value:b?.collections||[],onChange:G=>{O({collections:G})},onClear:()=>A("collections"),possibleFilters:B,customizeContent:G=>J({id:"admin.pages.MarketPlacePage.filters.collectionsSelected",defaultMessage:"{count, plural, =0 {No collections} one {# collection} other {# collections}} selected"},{count:G?.length??0})}),V==="plugin"&&(0,e.jsx)($e,{message:J({id:"admin.pages.MarketPlacePage.filters.categories",defaultMessage:"Categories"}),value:b?.categories||[],onChange:G=>{O({categories:G})},onClear:()=>A("categories"),possibleFilters:F,customizeContent:G=>J({id:"admin.pages.MarketPlacePage.filters.categoriesSelected",defaultMessage:"{count, plural, =0 {No categories} one {# category} other {# categories}} selected"},{count:G?.length??0})})]})})]}),b.collections?.map(G=>(0,e.jsx)(i.a,{padding:1,children:(0,e.jsx)(c.v,{icon:(0,e.jsx)(he.A,{}),onClick:()=>_(G,"collections"),children:G})},G)),V==="plugin"&&b.categories?.map(G=>(0,e.jsx)(i.a,{padding:1,children:(0,e.jsx)(c.v,{icon:(0,e.jsx)(he.A,{}),onClick:()=>_(G,"categories"),children:G})},G))]})},De=(0,ue.Ay)(s.$)`
  height: ${({theme:A})=>A.sizes.input.S};
`,Ve=(0,ue.Ay)(n.s)`
  /* Hide the label, every input needs a label. */
  label {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
`,$e=({message:A,value:O,onChange:V,possibleFilters:F,onClear:B,customizeContent:b})=>(0,e.jsx)(a.KF,{"data-testid":`${A}-button`,label:A,placeholder:A,size:"M",onChange:V,onClear:B,value:O,customizeContent:b,children:Object.entries(F).map(([w,K])=>(0,e.jsx)(a.fe,{"data-testid":`${w}-${K}`,value:w,children:`${w} (${K})`},w))}),Be=(0,ue.Ay)(r.o)`
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
`,Ge=({npmPackage:A,isInstalled:O,useYarn:V,isInDevelopmentMode:F,npmPackageType:B,strapiAppVersion:b})=>{const{attributes:w}=A,{formatMessage:K}=(0,ne.A)(),{trackUsage:te}=(0,Y.z1)(),J=V?`yarn add ${w.npmPackageName}`:`npm install ${w.npmPackageName}`,k=K({id:"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi",defaultMessage:"Made by Strapi"}),_=`https://market.strapi.io/${Ne.plural(B)}/${w.slug}`,G=pe.validRange(w.strapiVersion),ee=pe.satisfies(b??"",G??"");return(0,e.jsxs)(n.s,{direction:"column",justifyContent:"space-between",paddingTop:4,paddingRight:4,paddingBottom:4,paddingLeft:4,hasRadius:!0,background:"neutral0",shadow:"tableShadow",height:"100%",alignItems:"normal","data-testid":"npm-package-card",children:[(0,e.jsxs)(i.a,{children:[(0,e.jsxs)(n.s,{direction:"row",justifyContent:"space-between",alignItems:"flex-start",children:[(0,e.jsx)(i.a,{as:"img",src:w.logo.url,alt:`${w.name} logo`,hasRadius:!0,width:11,height:11}),(0,e.jsx)(Fe,{githubStars:w.githubStars,npmDownloads:w.npmDownloads,npmPackageType:B})]}),(0,e.jsx)(i.a,{paddingTop:4,children:(0,e.jsx)(r.o,{as:"h3",variant:"delta",children:(0,e.jsxs)(n.s,{alignItems:"center",children:[w.name,w.validated&&!w.madeByStrapi&&(0,e.jsx)(l.m,{description:K({id:"admin.pages.MarketPlacePage.plugin.tooltip.verified",defaultMessage:"Plugin verified by Strapi"}),children:(0,e.jsx)(n.s,{children:(0,e.jsx)(d.I,{as:m.A,marginLeft:2,color:"success600"})})}),w.madeByStrapi&&(0,e.jsx)(l.m,{description:k,children:(0,e.jsx)(n.s,{children:(0,e.jsx)(i.a,{as:"img",src:ye.S,alt:k,marginLeft:1,width:6,height:"auto"})})})]})})}),(0,e.jsx)(i.a,{paddingTop:2,children:(0,e.jsx)(Be,{as:"p",variant:"omega",textColor:"neutral600",children:w.description})})]}),(0,e.jsxs)(n.s,{gap:2,style:{alignSelf:"flex-end"},paddingTop:6,children:[(0,e.jsx)(je.z,{size:"S",href:_,isExternal:!0,endIcon:(0,e.jsx)(g.A,{}),"aria-label":K({id:"admin.pages.MarketPlacePage.plugin.info.label",defaultMessage:"Learn more about {pluginName}"},{pluginName:w.name}),variant:"tertiary",onClick:()=>te("didPluginLearnMore"),children:K({id:"admin.pages.MarketPlacePage.plugin.info.text",defaultMessage:"More"})}),(0,e.jsx)(He,{isInstalled:O,isInDevelopmentMode:F,isCompatible:ee,commandToCopy:J,strapiAppVersion:b,strapiPeerDepVersion:w.strapiVersion,pluginName:w.name})]})]})},He=({isInstalled:A,isInDevelopmentMode:O,isCompatible:V,commandToCopy:F,strapiAppVersion:B,strapiPeerDepVersion:b,pluginName:w})=>{const K=(0,Y.hN)(),{formatMessage:te}=(0,ne.A)(),{trackUsage:J}=(0,Y.z1)(),{copy:k}=(0,Y.iD)(),_=async()=>{await k(F)&&(J("willInstallPlugin"),K({type:"success",message:{id:"admin.pages.MarketPlacePage.plugin.copy.success"}}))};return A?(0,e.jsxs)(i.a,{paddingLeft:4,children:[(0,e.jsx)(d.I,{as:E.A,marginRight:2,width:12,height:12,color:"success600"}),(0,e.jsx)(r.o,{variant:"omega",textColor:"success600",fontWeight:"bold",children:te({id:"admin.pages.MarketPlacePage.plugin.installed",defaultMessage:"Installed"})})]}):O&&V!==!1?(0,e.jsx)(ze,{strapiAppVersion:B,strapiPeerDepVersion:b,handleCopy:_,pluginName:w}):null},ze=({strapiPeerDepVersion:A,strapiAppVersion:O,handleCopy:V,pluginName:F})=>{const{formatMessage:B}=(0,ne.A)(),b=pe.validRange(A),w=pe.satisfies(O??"",b??""),K=B({id:"admin.pages.MarketPlacePage.plugin.copy",defaultMessage:"Copy install command"});return O&&(!b||!w)?(0,e.jsx)(l.m,{"data-testid":`tooltip-${F}`,description:b?B({id:"admin.pages.MarketPlacePage.plugin.version",defaultMessage:'Update your Strapi version: "{strapiAppVersion}" to: "{versionRange}"'},{strapiAppVersion:O,versionRange:b}):B({id:"admin.pages.MarketPlacePage.plugin.version.null",defaultMessage:'Unable to verify compatibility with your Strapi version: "{strapiAppVersion}"'},{strapiAppVersion:O}),children:(0,e.jsx)(s.$,{size:"S",startIcon:(0,e.jsx)(S.A,{}),variant:"secondary",onClick:V,disabled:!w,children:K})}):(0,e.jsx)(s.$,{size:"S",startIcon:(0,e.jsx)(S.A,{}),variant:"secondary",onClick:V,children:K})},Fe=({githubStars:A=0,npmDownloads:O=0,npmPackageType:V})=>{const{formatMessage:F}=(0,ne.A)();return(0,e.jsxs)(n.s,{gap:1,children:[!!A&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(d.I,{as:$.A,height:(0,Y.a8)(12),width:(0,Y.a8)(12),"aria-hidden":!0}),(0,e.jsx)(d.I,{as:j.A,height:(0,Y.a8)(12),width:(0,Y.a8)(12),color:"warning500","aria-hidden":!0}),(0,e.jsx)("p",{"aria-label":F({id:`admin.pages.MarketPlacePage.${V}.githubStars`,defaultMessage:"This {package} was starred {starsCount} on GitHub"},{starsCount:A,package:V}),children:(0,e.jsx)(r.o,{variant:"pi",textColor:"neutral800",children:A})}),(0,e.jsx)(We,{unsetMargin:!1})]}),(0,e.jsx)(d.I,{as:L.A,height:(0,Y.a8)(12),width:(0,Y.a8)(12),"aria-hidden":!0}),(0,e.jsx)("p",{"aria-label":F({id:`admin.pages.MarketPlacePage.${V}.downloads`,defaultMessage:"This {package} has {downloadsCount} weekly downloads"},{downloadsCount:O,package:V}),children:(0,e.jsx)(r.o,{variant:"pi",textColor:"neutral800",children:O})})]})},We=(0,ue.Ay)(p.c)`
  width: ${(0,Y.a8)(12)};
  transform: rotate(90deg);
`,Ee=({status:A,npmPackages:O=[],installedPackageNames:V=[],useYarn:F,isInDevelopmentMode:B,npmPackageType:b,strapiAppVersion:w,debouncedSearch:K})=>{const{formatMessage:te}=(0,ne.A)();if(A==="error")return(0,e.jsx)(n.s,{paddingTop:8,children:(0,e.jsx)(Y.hH,{})});if(A==="loading")return(0,e.jsx)(n.s,{justifyContent:"center",paddingTop:8,children:(0,e.jsx)(x.a,{children:"Loading content..."})});const J=te({id:"admin.pages.MarketPlacePage.search.empty",defaultMessage:'No result for "{target}"'},{target:K});return O.length===0?(0,e.jsxs)(i.a,{position:"relative",children:[(0,e.jsx)(C.J,{children:Array(12).fill(null).map((k,_)=>(0,e.jsx)(Ye,{height:"234px",hasRadius:!0},_))}),(0,e.jsx)(i.a,{position:"absolute",top:11,width:"100%",children:(0,e.jsxs)(n.s,{alignItems:"center",justifyContent:"center",direction:"column",children:[(0,e.jsx)(d.I,{as:z.A,color:void 0,width:"160px",height:"88px"}),(0,e.jsx)(i.a,{paddingTop:6,children:(0,e.jsx)(r.o,{variant:"delta",as:"p",textColor:"neutral600",children:J})})]})})]}):(0,e.jsx)(W.x,{gap:4,children:O.map(k=>(0,e.jsx)(D.E,{col:4,s:6,xs:12,style:{height:"100%"},children:(0,e.jsx)(Ge,{npmPackage:k,isInstalled:V.includes(k.attributes.npmPackageName),useYarn:F,isInDevelopmentMode:B,npmPackageType:b,strapiAppVersion:w})},k.id))})},Ye=(0,ue.Ay)(i.a)`
  background: ${({theme:A})=>`linear-gradient(180deg, rgba(234, 234, 239, 0) 0%, ${A.colors.neutral150} 100%)`};
  opacity: 0.33;
`,Ae=({isOnline:A,npmPackageType:O="plugin"})=>{const{formatMessage:V}=(0,ne.A)(),{trackUsage:F}=(0,Y.z1)(),B=O==="provider"?"didSubmitProvider":"didSubmitPlugin";return(0,e.jsx)(I.Q,{title:V({id:"global.marketplace",defaultMessage:"Marketplace"}),subtitle:V({id:"admin.pages.MarketPlacePage.subtitle",defaultMessage:"Get more out of Strapi"}),primaryAction:A&&(0,e.jsx)(je.z,{startIcon:(0,e.jsx)(U,{}),variant:"tertiary",href:`https://market.strapi.io/submit-${O}`,onClick:()=>F(B),isExternal:!0,children:V({id:`admin.pages.MarketPlacePage.submit.${O}.link`,defaultMessage:`Submit ${O}`})})})},be=()=>{const{formatMessage:A}=(0,ne.A)();return(0,e.jsx)(Z.P,{children:(0,e.jsxs)(H.g,{children:[(0,e.jsx)(Ae,{}),(0,e.jsxs)(n.s,{width:"100%",direction:"column",alignItems:"center",justifyContent:"center",paddingTop:(0,Y.a8)(120),children:[(0,e.jsx)(i.a,{paddingBottom:2,children:(0,e.jsx)(r.o,{textColor:"neutral700",variant:"alpha",children:A({id:"admin.pages.MarketPlacePage.offline.title",defaultMessage:"You are offline"})})}),(0,e.jsx)(i.a,{paddingBottom:6,children:(0,e.jsx)(r.o,{textColor:"neutral700",variant:"epsilon",children:A({id:"admin.pages.MarketPlacePage.offline.subtitle",defaultMessage:"You need to be connected to the Internet to access Strapi Market."})})}),(0,e.jsxs)("svg",{width:"88",height:"88",viewBox:"0 0 88 88",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,e.jsx)("rect",{x:".5",y:".5",width:"87",height:"87",rx:"43.5",fill:"#F0F0FF"}),(0,e.jsx)("path",{d:"M34 39.3h-4c-2.6 0-4.7 1-6.6 2.8a9 9 0 0 0-2.7 6.6 9 9 0 0 0 2.7 6.6A9 9 0 0 0 30 58h22.8L34 39.3Zm-11-11 3-3 39 39-3 3-4.7-4.6H30a13.8 13.8 0 0 1-14-14c0-3.8 1.3-7 4-9.7 2.6-2.7 5.7-4.2 9.5-4.3L23 28.2Zm38.2 11.1c3 .2 5.5 1.5 7.6 3.7A11 11 0 0 1 72 51c0 4-1.6 7.2-5 9.5l-3.3-3.4a6.5 6.5 0 0 0 3.6-6.1c0-1.9-.7-3.5-2-5-1.5-1.3-3.1-2-5-2h-3.5v-1.2c0-3.6-1.2-6.6-3.7-9a13 13 0 0 0-15-2.3L34.6 28a17 17 0 0 1 20.3 1.5c3.5 2.7 5.5 6 6.3 10Z",fill:"#4945FF"}),(0,e.jsx)("rect",{x:".5",y:".5",width:"87",height:"87",rx:"43.5",stroke:"#D9D8FF"})]})]})]})})},Ce={"name:asc":{selected:{id:"admin.pages.MarketPlacePage.sort.alphabetical.selected",defaultMessage:"Sort by alphabetical order"},option:{id:"admin.pages.MarketPlacePage.sort.alphabetical",defaultMessage:"Alphabetical order"}},"submissionDate:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.newest.selected",defaultMessage:"Sort by newest"},option:{id:"admin.pages.MarketPlacePage.sort.newest",defaultMessage:"Newest"}},"githubStars:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.githubStars.selected",defaultMessage:"Sort by GitHub stars"},option:{id:"admin.pages.MarketPlacePage.sort.githubStars",defaultMessage:"Number of GitHub stars"}},"npmDownloads:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.npmDownloads.selected",defaultMessage:"Sort by npm downloads"},option:{id:"admin.pages.MarketPlacePage.sort.npmDownloads",defaultMessage:"Number of downloads"}}},Ue=({sortQuery:A,handleSelectChange:O})=>{const{formatMessage:V}=(0,ne.A)();return(0,e.jsx)(Xe,{children:(0,e.jsx)(M.Z,{size:"S",id:"sort-by-select",value:A,customizeContent:()=>V(Ce[A].selected),onChange:F=>{O({sort:F})},label:V({id:"admin.pages.MarketPlacePage.sort.label",defaultMessage:"Sort by"}),children:Object.entries(Ce).map(([F,B])=>(0,e.jsx)(M.eY,{value:F,children:V(B.option)},F))})})},Xe=(0,ue.Ay)(i.a)`
  font-weight: ${({theme:A})=>A.fontWeights.semiBold};

  span {
    font-size: ${({theme:A})=>A.fontSizes[1]};
  }

  /* Hide the label, every input needs a label. */
  label {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
`,Re="https://market-api.strapi.io";function Ze({npmPackageType:A,debouncedSearch:O,query:V,tabQuery:F}){const{notifyStatus:B}=(0,o.W)(),{formatMessage:b}=(0,ne.A)(),w=(0,Y.hN)(),K=b({id:"global.marketplace",defaultMessage:"Marketplace"}),te=()=>{B(b({id:"app.utils.notify.data-loaded",defaultMessage:"The {target} has loaded"},{target:K}))},J={page:V?.page||1,pageSize:V?.pageSize||24},k={...F.plugin,pagination:J,search:O},{data:_,status:G}=(0,Se.useQuery)(["marketplace","plugins",k],async()=>{try{const fe=Pe.stringify(k),ie=await fetch(`${Re}/plugins?${fe}`);if(!ie.ok)throw new Error("Failed to fetch marketplace plugins.");return await ie.json()}catch{}return null},{onSuccess(){te()},onError(){w({type:"warning",message:{id:"notification.error",defaultMessage:"An error occured"}})}}),ee={...F.provider,pagination:J,search:O},{data:le,status:ce}=(0,Se.useQuery)(["marketplace","providers",ee],async()=>{const fe=Pe.stringify(ee),ie=await fetch(`${Re}/providers?${fe}`);if(!ie.ok)throw new Error("Failed to fetch marketplace providers.");return await ie.json()},{onSuccess(){te()},onError(){w({type:"warning",message:{id:"notification.error",defaultMessage:"An error occured"}})}}),de=A==="plugin"?_:le,me=de?.meta.collections??{},ve=_?.meta.categories??{},{pagination:xe}=de?.meta??{};return{pluginsResponse:_,providersResponse:le,pluginsStatus:G,providersStatus:ce,possibleCollections:me,possibleCategories:ve,pagination:xe}}const Ke=()=>{const A=typeof navigator<"u"&&typeof navigator.onLine=="boolean"?navigator.onLine:!0,[O,V]=h.useState(A),F=()=>V(!0),B=()=>V(!1);return h.useEffect(()=>(window.addEventListener("online",F),window.addEventListener("offline",B),()=>{window.removeEventListener("online",F),window.removeEventListener("offline",B)}),[]),O},Me=()=>{const A=h.useRef(null),{formatMessage:O}=(0,ne.A)(),{trackUsage:V}=(0,Y.z1)(),F=(0,Y.hN)(),[{query:B},b]=(0,Y.sq)(),w=(0,ye.r)(B?.search,500)||"",{autoReload:K,dependencies:te,useYarn:J,strapiVersion:k}=(0,Y.Xe)(),_=Ke(),G=B?.npmPackageType||"plugin",[ee,le]=h.useState({plugin:G==="plugin"?{...B}:{},provider:G==="provider"?{...B}:{}});(0,Y.L4)(),h.useEffect(()=>{V("didGoToMarketplace")},[V]),h.useEffect(()=>{K||F({type:"info",message:{id:"admin.pages.MarketPlacePage.production",defaultMessage:"Manage plugins from the development environment"}})},[F,K]);const{pluginsResponse:ce,providersResponse:de,pluginsStatus:me,providersStatus:ve,possibleCollections:xe,possibleCategories:fe,pagination:ie}=Ze({npmPackageType:G,debouncedSearch:w,query:B,tabQuery:ee}),ge=["plugin","provider"].indexOf(G);if(h.useEffect(()=>{A.current&&A.current._handlers.setSelectedTabIndex(ge)},[ge]),!_)return(0,e.jsx)(be,{});const Je=ae=>{const re=ae===0?"plugin":"provider",_e=ee[re]&&Object.keys(ee[re]).length;b(_e?{...ee[re],search:B?.search||"",npmPackageType:re,page:1}:{npmPackageType:re,collections:[],categories:[],sort:"name:asc",page:1,search:B?.search||""})},Te=ae=>{b({...ae,page:1}),le(re=>({...re,[G]:{...re[G],...ae}}))},ke=ae=>{b({[ae]:[],page:void 0},"remove"),le(re=>({...re,[G]:{}}))},qe=({sort:ae})=>Te({sort:ae}),Le=Object.keys(te??{});return(0,e.jsx)(Z.P,{children:(0,e.jsxs)(H.g,{children:[(0,e.jsx)(oe.m,{title:O({id:"admin.pages.MarketPlacePage.helmet",defaultMessage:"Marketplace - Plugins"})}),(0,e.jsx)(Ae,{isOnline:_,npmPackageType:G}),(0,e.jsxs)(y.s,{children:[(0,e.jsxs)(v.f,{label:O({id:"admin.pages.MarketPlacePage.tab-group.label",defaultMessage:"Plugins and Providers for Strapi"}),id:"tabs",variant:"simple",initialSelectedTabIndex:ge,onTabChange:Je,ref:A,children:[(0,e.jsxs)(n.s,{justifyContent:"space-between",paddingBottom:4,children:[(0,e.jsxs)(R.t,{children:[(0,e.jsxs)(R.o,{children:[O({id:"admin.pages.MarketPlacePage.plugins",defaultMessage:"Plugins"})," ",ce?`(${ce.meta.pagination.total})`:"..."]}),(0,e.jsxs)(R.o,{children:[O({id:"admin.pages.MarketPlacePage.providers",defaultMessage:"Providers"})," ",de?`(${de.meta.pagination.total})`:"..."]})]}),(0,e.jsx)(i.a,{width:"25%",children:(0,e.jsx)(P.S,{name:"searchbar",onClear:()=>b({search:"",page:1}),value:B?.search,onChange:ae=>b({search:ae.target.value,page:1}),clearLabel:O({id:"admin.pages.MarketPlacePage.search.clear",defaultMessage:"Clear the search"}),placeholder:O({id:"admin.pages.MarketPlacePage.search.placeholder",defaultMessage:"Search"}),children:O({id:"admin.pages.MarketPlacePage.search.placeholder",defaultMessage:"Search"})})})]}),(0,e.jsxs)(n.s,{paddingBottom:4,gap:2,children:[(0,e.jsx)(Ue,{sortQuery:B?.sort||"name:asc",handleSelectChange:qe}),(0,e.jsx)(we,{npmPackageType:G,possibleCollections:xe,possibleCategories:fe,query:B||{},handleSelectChange:Te,handleSelectClear:ke})]}),(0,e.jsxs)(q.T,{children:[(0,e.jsx)(q.K,{children:(0,e.jsx)(Ee,{npmPackages:ce?.data,status:me,installedPackageNames:Le,useYarn:J,isInDevelopmentMode:K,npmPackageType:"plugin",strapiAppVersion:k,debouncedSearch:w})}),(0,e.jsx)(q.K,{children:(0,e.jsx)(Ee,{npmPackages:de?.data,status:ve,installedPackageNames:Le,useYarn:J,isInDevelopmentMode:K,npmPackageType:"provider",debouncedSearch:w})})]})]}),ie?(0,e.jsx)(i.a,{paddingTop:4,children:(0,e.jsxs)(n.s,{alignItems:"flex-end",justifyContent:"space-between",children:[(0,e.jsx)(Y._u,{options:["12","24","50","100"],defaultValue:"24"}),(0,e.jsx)(Y.W7,{pagination:ie})]})}):null,(0,e.jsx)(i.a,{paddingTop:8,children:(0,e.jsx)("a",{href:"https://strapi.canny.io/plugin-requests",target:"_blank",rel:"noopener noreferrer nofollow",style:{textDecoration:"none"},onClick:()=>V("didMissMarketplacePlugin"),children:(0,e.jsx)(Y.bQ,{title:O({id:"admin.pages.MarketPlacePage.missingPlugin.title",defaultMessage:"Documentation"}),subtitle:O({id:"admin.pages.MarketPlacePage.missingPlugin.description",defaultMessage:"Tell us what plugin you are looking for and we'll let our community plugin developers know in case they are in search for inspiration!"}),icon:(0,e.jsx)(X,{}),iconBackground:"alternative100",endAction:(0,e.jsx)(d.I,{as:g.A,color:"neutral600",width:3,height:3,marginLeft:2})})})})]})]})})},Qe=()=>{const A=(0,Oe.d4)(Ie.s);return(0,e.jsx)(Y.kz,{permissions:A.marketplace.main,children:(0,e.jsx)(Me,{})})}},45349:(f,T,t)=>{const e=t(1660),h=(i,u)=>{const c=e(i,u);return c&&c.prerelease.length?c.prerelease:null};f.exports=h},50777:(f,T,t)=>{const e=t(85968),h=t(2547),i=t(79656),u=(c,s)=>{c=new h(c,s);let n=new e("0.0.0");if(c.test(n)||(n=new e("0.0.0-0"),c.test(n)))return n;n=null;for(let a=0;a<c.set.length;++a){const r=c.set[a];let l=null;r.forEach(d=>{const p=new e(d.semver.version);switch(d.operator){case">":p.prerelease.length===0?p.patch++:p.prerelease.push(0),p.raw=p.format();case"":case">=":(!l||i(p,l))&&(l=p);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${d.operator}`)}}),l&&(!n||i(n,l))&&(n=l)}return n&&c.test(n)?n:null};f.exports=u},52869:(f,T,t)=>{const e=t(39100),h=(i,u,c)=>e(i,u,c)>=0;f.exports=h},55182:(f,T,t)=>{const e=t(85968),h=(i,u)=>new e(i,u).major;f.exports=h},60185:(f,T,t)=>{const e=t(85968),h=(i,u)=>new e(i,u).patch;f.exports=h},61010:(f,T,t)=>{const e=t(38079),h=(i,u,c)=>e(i,u,"<",c);f.exports=h},62508:(f,T,t)=>{const e=t(2547),h=t(99260),{ANY:i}=h,u=t(26434),c=t(39100),s=(p,x,C={})=>{if(p===x)return!0;p=new e(p,C),x=new e(x,C);let W=!1;e:for(const D of p.set){for(const I of x.set){const Z=r(D,I,C);if(W=W||Z!==null,Z)continue e}if(W)return!1}return!0},n=[new h(">=0.0.0-0")],a=[new h(">=0.0.0")],r=(p,x,C)=>{if(p===x)return!0;if(p.length===1&&p[0].semver===i){if(x.length===1&&x[0].semver===i)return!0;C.includePrerelease?p=n:p=a}if(x.length===1&&x[0].semver===i){if(C.includePrerelease)return!0;x=a}const W=new Set;let D,I;for(const P of p)P.operator===">"||P.operator===">="?D=l(D,P,C):P.operator==="<"||P.operator==="<="?I=d(I,P,C):W.add(P.semver);if(W.size>1)return null;let Z;if(D&&I){if(Z=c(D.semver,I.semver,C),Z>0)return null;if(Z===0&&(D.operator!==">="||I.operator!=="<="))return null}for(const P of W){if(D&&!u(P,String(D),C)||I&&!u(P,String(I),C))return null;for(const q of x)if(!u(P,String(q),C))return!1;return!0}let H,M,o,y,v=I&&!C.includePrerelease&&I.semver.prerelease.length?I.semver:!1,R=D&&!C.includePrerelease&&D.semver.prerelease.length?D.semver:!1;v&&v.prerelease.length===1&&I.operator==="<"&&v.prerelease[0]===0&&(v=!1);for(const P of x){if(y=y||P.operator===">"||P.operator===">=",o=o||P.operator==="<"||P.operator==="<=",D){if(R&&P.semver.prerelease&&P.semver.prerelease.length&&P.semver.major===R.major&&P.semver.minor===R.minor&&P.semver.patch===R.patch&&(R=!1),P.operator===">"||P.operator===">="){if(H=l(D,P,C),H===P&&H!==D)return!1}else if(D.operator===">="&&!u(D.semver,String(P),C))return!1}if(I){if(v&&P.semver.prerelease&&P.semver.prerelease.length&&P.semver.major===v.major&&P.semver.minor===v.minor&&P.semver.patch===v.patch&&(v=!1),P.operator==="<"||P.operator==="<="){if(M=d(I,P,C),M===P&&M!==I)return!1}else if(I.operator==="<="&&!u(I.semver,String(P),C))return!1}if(!P.operator&&(I||D)&&Z!==0)return!1}return!(D&&o&&!I&&Z!==0||I&&y&&!D&&Z!==0||R||v)},l=(p,x,C)=>{if(!p)return x;const W=c(p.semver,x.semver,C);return W>0?p:W<0||x.operator===">"&&p.operator===">="?x:p},d=(p,x,C)=>{if(!p)return x;const W=c(p.semver,x.semver,C);return W<0?p:W>0||x.operator==="<"&&p.operator==="<="?x:p};f.exports=s},64530:(f,T,t)=>{const e=t(1660),h=(i,u)=>{const c=e(i.trim().replace(/^[=v]+/,""),u);return c?c.version:null};f.exports=h},71969:(f,T,t)=>{"use strict";const e=t(6515),h=Symbol("max"),i=Symbol("length"),u=Symbol("lengthCalculator"),c=Symbol("allowStale"),s=Symbol("maxAge"),n=Symbol("dispose"),a=Symbol("noDisposeOnSet"),r=Symbol("lruList"),l=Symbol("cache"),d=Symbol("updateAgeOnGet"),p=()=>1;class x{constructor(o){if(typeof o=="number"&&(o={max:o}),o||(o={}),o.max&&(typeof o.max!="number"||o.max<0))throw new TypeError("max must be a non-negative number");const y=this[h]=o.max||1/0,v=o.length||p;if(this[u]=typeof v!="function"?p:v,this[c]=o.stale||!1,o.maxAge&&typeof o.maxAge!="number")throw new TypeError("maxAge must be a number");this[s]=o.maxAge||0,this[n]=o.dispose,this[a]=o.noDisposeOnSet||!1,this[d]=o.updateAgeOnGet||!1,this.reset()}set max(o){if(typeof o!="number"||o<0)throw new TypeError("max must be a non-negative number");this[h]=o||1/0,D(this)}get max(){return this[h]}set allowStale(o){this[c]=!!o}get allowStale(){return this[c]}set maxAge(o){if(typeof o!="number")throw new TypeError("maxAge must be a non-negative number");this[s]=o,D(this)}get maxAge(){return this[s]}set lengthCalculator(o){typeof o!="function"&&(o=p),o!==this[u]&&(this[u]=o,this[i]=0,this[r].forEach(y=>{y.length=this[u](y.value,y.key),this[i]+=y.length})),D(this)}get lengthCalculator(){return this[u]}get length(){return this[i]}get itemCount(){return this[r].length}rforEach(o,y){y=y||this;for(let v=this[r].tail;v!==null;){const R=v.prev;H(this,o,v,y),v=R}}forEach(o,y){y=y||this;for(let v=this[r].head;v!==null;){const R=v.next;H(this,o,v,y),v=R}}keys(){return this[r].toArray().map(o=>o.key)}values(){return this[r].toArray().map(o=>o.value)}reset(){this[n]&&this[r]&&this[r].length&&this[r].forEach(o=>this[n](o.key,o.value)),this[l]=new Map,this[r]=new e,this[i]=0}dump(){return this[r].map(o=>W(this,o)?!1:{k:o.key,v:o.value,e:o.now+(o.maxAge||0)}).toArray().filter(o=>o)}dumpLru(){return this[r]}set(o,y,v){if(v=v||this[s],v&&typeof v!="number")throw new TypeError("maxAge must be a number");const R=v?Date.now():0,P=this[u](y,o);if(this[l].has(o)){if(P>this[h])return I(this,this[l].get(o)),!1;const se=this[l].get(o).value;return this[n]&&(this[a]||this[n](o,se.value)),se.now=R,se.maxAge=v,se.value=y,this[i]+=P-se.length,se.length=P,this.get(o),D(this),!0}const q=new Z(o,y,P,R,v);return q.length>this[h]?(this[n]&&this[n](o,y),!1):(this[i]+=q.length,this[r].unshift(q),this[l].set(o,this[r].head),D(this),!0)}has(o){if(!this[l].has(o))return!1;const y=this[l].get(o).value;return!W(this,y)}get(o){return C(this,o,!0)}peek(o){return C(this,o,!1)}pop(){const o=this[r].tail;return o?(I(this,o),o.value):null}del(o){I(this,this[l].get(o))}load(o){this.reset();const y=Date.now();for(let v=o.length-1;v>=0;v--){const R=o[v],P=R.e||0;if(P===0)this.set(R.k,R.v);else{const q=P-y;q>0&&this.set(R.k,R.v,q)}}}prune(){this[l].forEach((o,y)=>C(this,y,!1))}}const C=(M,o,y)=>{const v=M[l].get(o);if(v){const R=v.value;if(W(M,R)){if(I(M,v),!M[c])return}else y&&(M[d]&&(v.value.now=Date.now()),M[r].unshiftNode(v));return R.value}},W=(M,o)=>{if(!o||!o.maxAge&&!M[s])return!1;const y=Date.now()-o.now;return o.maxAge?y>o.maxAge:M[s]&&y>M[s]},D=M=>{if(M[i]>M[h])for(let o=M[r].tail;M[i]>M[h]&&o!==null;){const y=o.prev;I(M,o),o=y}},I=(M,o)=>{if(o){const y=o.value;M[n]&&M[n](y.key,y.value),M[i]-=y.length,M[l].delete(y.key),M[r].removeNode(o)}};class Z{constructor(o,y,v,R,P){this.key=o,this.value=y,this.length=v,this.now=R,this.maxAge=P||0}}const H=(M,o,y,v)=>{let R=y.value;W(M,R)&&(I(M,y),M[c]||(R=void 0)),R&&o.call(v,R.value,R.key,M)};f.exports=x},73182:(f,T,t)=>{const e=t(85968),h=t(1660),{safeRe:i,t:u}=t(88618),c=(s,n)=>{if(s instanceof e)return s;if(typeof s=="number"&&(s=String(s)),typeof s!="string")return null;n=n||{};let a=null;if(!n.rtl)a=s.match(i[u.COERCE]);else{let r;for(;(r=i[u.COERCERTL].exec(s))&&(!a||a.index+a[0].length!==s.length);)(!a||r.index+r[0].length!==a.index+a[0].length)&&(a=r),i[u.COERCERTL].lastIndex=r.index+r[1].length+r[2].length;i[u.COERCERTL].lastIndex=-1}return a===null?null:h(`${a[2]}.${a[3]||"0"}.${a[4]||"0"}`,n)};f.exports=c},73328:(f,T,t)=>{const e=t(2547),h=(i,u,c)=>(i=new e(i,c),u=new e(u,c),i.intersects(u,c));f.exports=h},78203:(f,T,t)=>{const e=t(85968),h=(i,u,c,s,n)=>{typeof c=="string"&&(n=s,s=c,c=void 0);try{return new e(i instanceof e?i.version:i,c).inc(u,s,n).version}catch{return null}};f.exports=h},79656:(f,T,t)=>{const e=t(39100),h=(i,u,c)=>e(i,u,c)>0;f.exports=h},79762:(f,T,t)=>{const e=t(85968),h=t(2547),i=(u,c,s)=>{let n=null,a=null,r=null;try{r=new h(c,s)}catch{return null}return u.forEach(l=>{r.test(l)&&(!n||a.compare(l)===1)&&(n=l,a=new e(n,s))}),n};f.exports=i},81035:(f,T,t)=>{"use strict";t.d(T,{J:()=>h});var e=t(63891);const h=e.Ay.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-gap: ${({theme:i})=>i.spaces[4]};
`},83479:f=>{"use strict";f.exports=function(T){T.prototype[Symbol.iterator]=function*(){for(let t=this.head;t;t=t.next)yield t.value}}},93546:(f,T,t)=>{const e=t(85968),h=(i,u)=>new e(i,u).minor;f.exports=h},96763:(f,T,t)=>{const e=t(14149),h=t(37075),i=t(79656),u=t(52869),c=t(55151),s=t(2972),n=(a,r,l,d)=>{switch(r){case"===":return typeof a=="object"&&(a=a.version),typeof l=="object"&&(l=l.version),a===l;case"!==":return typeof a=="object"&&(a=a.version),typeof l=="object"&&(l=l.version),a!==l;case"":case"=":case"==":return e(a,l,d);case"!=":return h(a,l,d);case">":return i(a,l,d);case">=":return u(a,l,d);case"<":return c(a,l,d);case"<=":return s(a,l,d);default:throw new TypeError(`Invalid operator: ${r}`)}};f.exports=n},96924:(f,T,t)=>{const e=t(1660),h=(i,u)=>{const c=e(i,null,!0),s=e(u,null,!0),n=c.compare(s);if(n===0)return null;const a=n>0,r=a?c:s,l=a?s:c,d=!!r.prerelease.length;if(!!l.prerelease.length&&!d)return!l.patch&&!l.minor?"major":r.patch?"patch":r.minor?"minor":"major";const x=d?"pre":"";return c.major!==s.major?x+"major":c.minor!==s.minor?x+"minor":c.patch!==s.patch?x+"patch":"prerelease"};f.exports=h},98536:(f,T,t)=>{const e=t(85968),h=t(2547),i=(u,c,s)=>{let n=null,a=null,r=null;try{r=new h(c,s)}catch{return null}return u.forEach(l=>{r.test(l)&&(!n||a.compare(l)===-1)&&(n=l,a=new e(n,s))}),n};f.exports=i},99260:(f,T,t)=>{const e=Symbol("SemVer ANY");class h{static get ANY(){return e}constructor(d,p){if(p=i(p),d instanceof h){if(d.loose===!!p.loose)return d;d=d.value}d=d.trim().split(/\s+/).join(" "),n("comparator",d,p),this.options=p,this.loose=!!p.loose,this.parse(d),this.semver===e?this.value="":this.value=this.operator+this.semver.version,n("comp",this)}parse(d){const p=this.options.loose?u[c.COMPARATORLOOSE]:u[c.COMPARATOR],x=d.match(p);if(!x)throw new TypeError(`Invalid comparator: ${d}`);this.operator=x[1]!==void 0?x[1]:"",this.operator==="="&&(this.operator=""),x[2]?this.semver=new a(x[2],this.options.loose):this.semver=e}toString(){return this.value}test(d){if(n("Comparator.test",d,this.options.loose),this.semver===e||d===e)return!0;if(typeof d=="string")try{d=new a(d,this.options)}catch{return!1}return s(d,this.operator,this.semver,this.options)}intersects(d,p){if(!(d instanceof h))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new r(d.value,p).test(this.value):d.operator===""?d.value===""?!0:new r(this.value,p).test(d.semver):(p=i(p),p.includePrerelease&&(this.value==="<0.0.0-0"||d.value==="<0.0.0-0")||!p.includePrerelease&&(this.value.startsWith("<0.0.0")||d.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&d.operator.startsWith(">")||this.operator.startsWith("<")&&d.operator.startsWith("<")||this.semver.version===d.semver.version&&this.operator.includes("=")&&d.operator.includes("=")||s(this.semver,"<",d.semver,p)&&this.operator.startsWith(">")&&d.operator.startsWith("<")||s(this.semver,">",d.semver,p)&&this.operator.startsWith("<")&&d.operator.startsWith(">")))}}f.exports=h;const i=t(1231),{safeRe:u,t:c}=t(88618),s=t(96763),n=t(47180),a=t(85968),r=t(2547)},99367:(f,T,t)=>{const e=t(38079),h=(i,u,c)=>e(i,u,">",c);f.exports=h}}]);
