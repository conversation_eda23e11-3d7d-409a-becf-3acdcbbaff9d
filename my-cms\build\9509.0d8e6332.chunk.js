"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[9509],{39509:(s,e,a)=>{a.r(e),a.d(e,{default:()=>t});const t={"content-manager-edit-view.add-to-release.select-label":"Select a release","content-manager-edit-view.add-to-release.select-placeholder":"Select","content-manager-edit-view.add-to-release.action-type-label":"What do you want to do with this entry?","content-manager-edit-view.add-to-release.cancel-button":"Cancel","content-manager-edit-view.add-to-release.continue-button":"Continue","content-manager-edit-view.add-to-release":"Add to release","content-manager-edit-view.add-to-release.notification.success":"Entry added to release","content-manager-edit-view.add-to-release.no-releases-message":"No available releases. Open the list of releases and create a new one from there.","content-manager-edit-view.add-to-release.redirect-button":"Open the list of releases","content-manager-edit-view.list-releases.title":"{isPublish, select, true {Will be published in} other {Will be unpublished in}}","content-manager-edit-view.remove-from-release":"Remove from release","content-manager-edit-view.scheduled.date":"{date} at {time} ({offset})","content-manager-edit-view.edit-release":"Edit release","content-releases.content-manager-edit-view.edit-entry":"Edit entry","content-manager-edit-view.remove-from-release.notification.success":"Entry removed from release","content-manager-edit-view.release-action-menu":"Release action options","content-manager-list-view.add-to-release":"Add to release","content-manager-list-view.add-to-release.cancel-button":"Cancel","content-manager-list-view.add-to-release.continue-button":"Continue","content-manager-list-view.add-to-release.select-label":"Select a release","content-manager-list-view.add-to-release.select-placeholder":"Select","content-manager-list-view.add-to-release.action-type-label":"What do you want to do with these entries?","content-manager-list-view.add-to-release.notification.success.title":"Successfully added to release.","content-manager-list-view.add-to-release.notification.success.message":"{entriesAlreadyInRelease} out of {totalEntries} entries were already in the release.","content-manager.notification.entry-error":"Failed to get entry data","content-manager.list-view.releases-number":"{number} {number, plural, one {release} other {releases}}","plugin.name":"Releases","pages.Releases.title":"Releases","pages.Releases.header-subtitle":"Create and manage content updates","pages.Releases.max-limit-reached.title":"You have reached the {number} pending {number, plural, one {release} other {releases}} limit.","pages.Releases.max-limit-reached.message":"Upgrade to manage an unlimited number of releases.","pages.Releases.max-limit-reached.action":"Explore plans","pages.PurchaseRelease.subTitle":"Manage content updates and releases.","pages.PurchaseRelease.not-available":"Releases is only available as part of a paid plan. Upgrade to create and manage releases.","header.actions.add-release":"New Release","header.actions.refresh":"Refresh","header.actions.publish":"Publish","header.actions.open-release-actions":"Release edit and delete menu","header.actions.edit":"Edit","header.actions.delete":"Delete","header.actions.created":"Created","header.actions.created.description":"{hasCreatedByUser, select, true { by {createdBy}} other { by deleted user}}","modal.release-created-notification-success":"Release created","modal.release-updated-notification-success":"Release updated","modal.title":"{isCreatingRelease, select, true {New release} other {Edit release}}","modal.form.input.label.release-name":"Name","modal.form.input.label.schedule-release":"Schedule release","modal.form.input.label.date":"Date","modal.form.input.label.time":"Time","modal.form.input.label.timezone":"Timezone","modal.form.input.clearLabel":"Clear","modal.form.button.submit":"{isCreatingRelease, select, true {Continue} other {Save}}","pages.Details.header-subtitle":"{number, plural, =0 {No entries} one {# entry} other {# entries}}","pages.Releases.tab-group.label":"Releases list","pages.Releases.tab.pending":"Pending ({count})","pages.Releases.tab.done":"Done","page.Releases.tab.emptyEntries":"No releases","pages.Details.tab.emptyEntries":"This release is empty. Open the Content Manager, select an entry and add it to the release.","page.ReleaseDetails.table.header.label.name":"name","page.ReleaseDetails.table.header.label.locale":"locale","page.ReleaseDetails.table.header.label.content-type":"content-type","page.ReleaseDetails.table.header.label.action":"action","content-releases.page.ReleaseDetails.table.header.label.status":"status","page.ReleaseDetails.table.action-published":"This entry was <b>{isPublish, select, true {published} other {unpublished}}</b>.","pages.ReleaseDetails.publish-notification-success":"Release was published successfully.","dialog.confirmation-message":"Are you sure you want to delete this release?","page.Details.button.openContentManager":"Open the Content Manager","pages.Releases.notification.error.title":"Your request could not be processed.","pages.Releases.notification.error.message":"Please try again or open another release.","pages.Releases.not-scheduled":"Not scheduled","pages.ReleaseDetails.groupBy.label":"Group by {groupBy}","pages.ReleaseDetails.groupBy.aria-label":"Group by","pages.ReleaseDetails.entry-validation.already-published":"Already published","pages.ReleaseDetails.entry-validation.ready-to-publish":"Ready to publish","pages.ReleaseDetails.entry-validation.already-unpublished":"Already unpublished","pages.ReleaseDetails.entry-validation.ready-to-unpublish":"Ready to unpublish","pages.ReleaseDetails.groupBy.option.content-type":"Content-Types","pages.ReleaseDetails.groupBy.option.locales":"Locales","pages.ReleaseDetails.groupBy.option.actions":"Actions","pages.ReleaseDetails.header-subtitle.scheduled":"Scheduled for {date} at {time} ({offset})"}}}]);
