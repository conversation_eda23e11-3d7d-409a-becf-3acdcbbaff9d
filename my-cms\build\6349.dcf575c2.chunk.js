"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[6349],{6349:(g,t,s)=>{s.d(t,{PurchaseContentReleases:()=>P});var e=s(92132),_=s(94061),n=s(53563),l=s(42455),i=s(55356),r=s(38413),d=s(49654),E=s(3047),o=s(14595),M=s(54894);const P=()=>{const{formatMessage:a}=(0,M.A)();return(0,e.jsx)(l.P,{children:(0,e.jsxs)(r.g,{children:[(0,e.jsx)(i.Q,{title:a({id:"content-releases.pages.Releases.title",defaultMessage:"Releases"}),subtitle:a({id:"content-releases.pages.PurchaseRelease.subTitle",defaultMessage:"Manage content updates and releases."})}),(0,e.jsx)(_.a,{paddingLeft:10,paddingRight:10,children:(0,e.jsx)(n.p,{icon:(0,e.jsx)(E.A,{width:"10rem"}),content:a({id:"content-releases.pages.PurchaseRelease.not-available",defaultMessage:"Releases is only available as part of a paid plan. Upgrade to create and manage releases."}),action:(0,e.jsx)(d.z,{variant:"default",endIcon:(0,e.jsx)(o.A,{}),href:"https://strapi.io/pricing-self-hosted?utm_campaign=Growth-Experiments&utm_source=In-Product&utm_medium=Releases",isExternal:!0,target:"_blank",children:a({id:"global.learn-more",defaultMessage:"Learn more"})})})})]})})}}}]);
