"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3153],{43153:(t,E,_)=>{_.r(E),_.d(E,{MagicLinkEE:()=>o});var M=_(92132),s=_(54894),D=_(43543),P=_(82803),n=_(55506),d=_(15126),l=_(63299),v=_(67014),C=_(59080),L=_(79275),A=_(14718),U=_(21272),B=_(82437),K=_(61535),R=_(5790),I=_(12083),T=_(35223),W=_(5409),h=_(74930),m=_(2600),i=_(48940),g=_(41286),r=_(56336),f=_(13426),y=_(84624),j=_(77965),S=_(54257),$=_(71210),x=_(51187),N=_(39404),F=_(58692),z=_(501),G=_(57646),V=_(23120),H=_(44414),J=_(25962),Q=_(14664),X=_(42588),Y=_(90325),Z=_(62785),c=_(87443),u=_(41032),k=_(22957),p=_(93179),w=_(73055),b=_(15747),q=_(85306),e=_(26509),__=_(32058),E_=_(81185),M_=_(82261),D_=_(85071);const o=({registrationToken:O})=>{const{formatMessage:a}=(0,s.A)();return O?(0,M.jsx)(P.a,{target:`${window.location.origin}${(0,D.K)()}/auth/register?registrationToken=${O}`,children:a({id:"app.components.Users.MagicLink.connect",defaultMessage:"Copy and share this link to give access to this user"})}):(0,M.jsx)(P.a,{target:`${window.location.origin}${(0,D.K)()}/auth/login`,children:a({id:"app.components.Users.MagicLink.connect.sso",defaultMessage:"Send this link to the user, the first login can be made via a SSO provider."})})}}}]);
