"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[3292],{13292:($,W,s)=>{s.d(W,{ProtectedEditView:()=>J,w:()=>V});var e=s(92132),D=s(21272),B=s(94061),K=s(85963),u=s(90151),O=s(68074),p=s(4198),M=s(55356),t=s(38413),T=s(83997),E=s(30893),a=s(55506),G=s(54514),Q=s(61535),y=s(54894),j=s(17703),h=s(12083),I=s(43543),x=s(34819),v=s(99831),m=s(81590),as=s(15126),rs=s(63299),os=s(67014),_s=s(59080),is=s(79275),Es=s(14718),ds=s(82437),ls=s(5790),Ts=s(35223),Ms=s(5409),Ps=s(74930),Ds=s(2600),Os=s(48940),ms=s(41286),fs=s(56336),cs=s(13426),gs=s(84624),hs=s(77965),vs=s(54257),As=s(71210),Cs=s(51187),Ls=s(39404),ps=s(58692),Is=s(501),Rs=s(57646),Us=s(23120),Ws=s(44414),Bs=s(25962),Ks=s(14664),us=s(42588),ys=s(90325),js=s(62785),xs=s(87443),ks=s(41032),Ss=s(22957),Vs=s(93179),Fs=s(73055),zs=s(15747),Ns=s(85306),$s=s(26509),Gs=s(32058),Qs=s(81185),Ys=s(82261);const Y=h.Ik().shape({name:h.Yj().max(100).required(a.iW.required),description:h.Yj().nullable(),lifespan:h.ai().integer().min(0).nullable().defined(a.iW.required),permissions:h.Yj().required(a.iW.required)}),V=()=>{(0,a.L4)();const{formatMessage:o}=(0,y.A)(),{lockApp:_,unlockApp:A}=(0,a.MA)(),d=(0,a.hN)(),f=(0,j.W6)(),{state:C}=(0,j.zy)(),[r,c]=D.useState(C&&"accessKey"in C.transferToken?{...C.transferToken}:null),{trackUsage:P}=(0,a.z1)(),{setCurrentStep:Z}=(0,a.Cx)(),w=(0,I.j)(i=>i.admin_app.permissions.settings?.["transfer-tokens"]),{allowedActions:{canCreate:b,canUpdate:q,canRegenerate:ss}}=(0,a.ec)(w),R=(0,j.W5)("/settings/transfer-tokens/:id")?.params?.id,l=R==="create",{_unstableFormatAPIError:U,_unstableFormatValidationErrors:F}=(0,a.wq)();D.useEffect(()=>{P(l?"didAddTokenFromList":"didEditTokenFromList",{tokenType:v.T})},[l,P]);const{data:k,error:S}=(0,x.u)(R,{skip:l||r!==null||!R});D.useEffect(()=>{S&&d({type:"warning",message:U(S)})},[S,U,d]),D.useEffect(()=>{k&&c(k)},[k]);const[es]=(0,x.a)(),[ts]=(0,x.b)(),ns=async(i,g)=>{P(l?"willCreateToken":"willEditToken",{tokenType:v.T}),_();const L=i.permissions.split("-");if((n=>n.length===1?n[0]==="push"||n[0]==="pull":n[0]==="push"&&n[1]==="pull")(L))try{if(l){const n=await es({...i,lifespan:i?.lifespan||null,permissions:L});if("error"in n){(0,I.x)(n.error)&&n.error.name==="ValidationError"?g.setErrors(F(n.error)):d({type:"warning",message:U(n.error)});return}c(n.data),d({type:"success",message:o({id:"notification.success.transfertokencreated",defaultMessage:"Transfer Token successfully created"})}),P("didCreateToken",{type:r?.permissions,tokenType:v.T}),f.push(`/settings/transfer-tokens/${n.data.id}`,{transferToken:n.data}),Z("transferTokens.success")}else{const n=await ts({id:R,name:i.name,description:i.description,permissions:L});if("error"in n){(0,I.x)(n.error)&&n.error.name==="ValidationError"?g.setErrors(F(n.error)):d({type:"warning",message:U(n.error)});return}c(n.data),d({type:"success",message:o({id:"notification.success.transfertokenedited",defaultMessage:"Transfer Token successfully edited"})}),P("didEditToken",{type:r?.permissions,tokenType:v.T})}}catch{d({type:"warning",message:{id:"notification.error",defaultMessage:"Something went wrong"}})}finally{A()}},z=q&&!l||b&&l;return!l&&!r?(0,e.jsx)(X,{}):(0,e.jsxs)(t.g,{children:[(0,e.jsx)(a.x7,{name:"Transfer Tokens"}),(0,e.jsx)(Q.l1,{validationSchema:Y,validateOnChange:!1,initialValues:{name:r?.name||"",description:r?.description||"",lifespan:r?.lifespan||null,permissions:r?.permissions.join("-")??""},enableReinitialize:!0,onSubmit:(i,g)=>ns(i,g),children:({errors:i,handleChange:g,isSubmitting:L,values:N})=>(0,e.jsxs)(a.lV,{children:[(0,e.jsx)(m.F,{backUrl:"/settings/transfer-tokens",title:{id:"Settings.transferTokens.createPage.title",defaultMessage:"TokenCreate Transfer Token"},token:r,setToken:c,canEditInputs:z,canRegenerate:ss,isSubmitting:L,regenerateUrl:"/admin/transfer/tokens/"}),(0,e.jsx)(p.s,{children:(0,e.jsxs)(T.s,{direction:"column",alignItems:"stretch",gap:6,children:[r&&Boolean(r?.name)&&"accessKey"in r&&(0,e.jsx)(m.c,{token:r.accessKey,tokenType:v.T}),(0,e.jsx)(H,{errors:i,onChange:g,canEditInputs:z,isCreating:l,values:N,transferToken:r})]})})]})})]})},J=()=>{const o=(0,I.j)(_=>_.admin_app.permissions.settings?.["transfer-tokens"].read);return(0,e.jsx)(a.kz,{permissions:o,children:(0,e.jsx)(V,{})})},H=({errors:o={},onChange:_,canEditInputs:A,isCreating:d,values:f,transferToken:C={}})=>{const{formatMessage:r}=(0,y.A)(),c=[{value:"push",label:{id:"Settings.transferTokens.types.push",defaultMessage:"Push"}},{value:"pull",label:{id:"Settings.transferTokens.types.pull",defaultMessage:"Pull"}},{value:"push-pull",label:{id:"Settings.transferTokens.types.push-pull",defaultMessage:"Full Access"}}];return(0,e.jsx)(B.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,e.jsxs)(T.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,e.jsx)(E.o,{variant:"delta",as:"h2",children:r({id:"global.details",defaultMessage:"Details"})}),(0,e.jsxs)(u.x,{gap:5,children:[(0,e.jsx)(O.E,{col:6,xs:12,children:(0,e.jsx)(m.T,{error:o.name,value:f.name,canEditInputs:A,onChange:_})},"name"),(0,e.jsx)(O.E,{col:6,xs:12,children:(0,e.jsx)(m.a,{error:o.description,value:f.description,canEditInputs:A,onChange:_})},"description"),(0,e.jsx)(O.E,{col:6,xs:12,children:(0,e.jsx)(m.L,{isCreating:d,error:o.lifespan,value:f.lifespan,onChange:_,token:C})},"lifespan"),(0,e.jsx)(O.E,{col:6,xs:12,children:(0,e.jsx)(m.b,{name:"permissions",value:f.permissions,error:o.permissions,label:{id:"Settings.tokens.form.type",defaultMessage:"Token type"},onChange:P=>{_({target:{name:"permissions",value:P}})},options:c,canEditInputs:A})},"permissions")]})]})})},X=({transferTokenName:o})=>{const{formatMessage:_}=(0,y.A)();return(0,a.L4)(),(0,e.jsxs)(t.g,{"aria-busy":"true",children:[(0,e.jsx)(a.x7,{name:"Transfer Tokens"}),(0,e.jsx)(M.Q,{primaryAction:(0,e.jsx)(K.$,{disabled:!0,startIcon:(0,e.jsx)(G.A,{}),type:"button",size:"L",children:_({id:"global.save",defaultMessage:"Save"})}),title:o||_({id:"Settings.transferTokens.createPage.title",defaultMessage:"Create Transfer Token"})}),(0,e.jsx)(p.s,{children:(0,e.jsx)(a.Bl,{})})]})}},34819:($,W,s)=>{s.d(W,{a:()=>u,b:()=>p,c:()=>B,d:()=>O,u:()=>K});var e=s(43543);const D=e.n.injectEndpoints({endpoints:M=>({getTransferTokens:M.query({query:()=>({url:"/admin/transfer/tokens",method:"GET"}),transformResponse:t=>t.data,providesTags:(t,T)=>[...t?.map(({id:E})=>({type:"TransferToken",id:E}))??[],{type:"TransferToken",id:"LIST"}]}),getTransferToken:M.query({query:t=>`/admin/transfer/tokens/${t}`,transformResponse:t=>t.data,providesTags:(t,T,E)=>[{type:"TransferToken",id:E}]}),createTransferToken:M.mutation({query:t=>({url:"/admin/transfer/tokens",method:"POST",data:t}),transformResponse:t=>t.data,invalidatesTags:[{type:"TransferToken",id:"LIST"}]}),deleteTransferToken:M.mutation({query:t=>({url:`/admin/transfer/tokens/${t}`,method:"DELETE"}),transformResponse:t=>t.data,invalidatesTags:(t,T,E)=>[{type:"TransferToken",id:E}]}),updateTransferToken:M.mutation({query:({id:t,...T})=>({url:`/admin/transfer/tokens/${t}`,method:"PUT",data:T}),transformResponse:t=>t.data,invalidatesTags:(t,T,{id:E})=>[{type:"TransferToken",id:E}]})}),overrideExisting:!1}),{useGetTransferTokensQuery:B,useGetTransferTokenQuery:K,useCreateTransferTokenMutation:u,useDeleteTransferTokenMutation:O,useUpdateTransferTokenMutation:p}=D}}]);
