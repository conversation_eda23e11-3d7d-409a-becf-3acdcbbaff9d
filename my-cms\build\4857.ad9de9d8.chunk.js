"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4857],{54857:(l,e,i)=>{i.r(e),i.d(e,{default:()=>t});const t={"Settings.email.plugin.button.test-email":"Send test e-mail","Settings.email.plugin.label.defaultFrom":"Standard afsender e-mail","Settings.email.plugin.label.defaultReplyTo":"Standard svar e-mail","Settings.email.plugin.label.provider":"E-mail provider","Settings.email.plugin.label.testAddress":"Modtager e-mail","Settings.email.plugin.notification.config.error":"Fejlede i at hente e-mail konfiguration","Settings.email.plugin.notification.data.loaded":"E-mail indstillinger er blevet hentet","Settings.email.plugin.notification.test.error":"Fejlede ved udsendelse af test mail til {to}","Settings.email.plugin.notification.test.success":"E-mail test lykkedes, tjek indbakken hos {to}","Settings.email.plugin.placeholder.defaultFrom":"f.eks. Strapi No-Reply <<EMAIL>>","Settings.email.plugin.placeholder.defaultReplyTo":"f.eks. Strapi <<EMAIL>>","Settings.email.plugin.placeholder.testAddress":"f.eks. <EMAIL>","Settings.email.plugin.subTitle":"Test indstillingerne for Email plugin","Settings.email.plugin.text.configuration":"Pluginnet er konfigureret igennem {file} filen, tjek {link} for dokumentation.","Settings.email.plugin.title":"Konfiguration","Settings.email.plugin.title.config":"Konfiguration","Settings.email.plugin.title.test":"Test e-mail modtagelse","SettingsNav.link.settings":"Indstillinger","SettingsNav.section-label":"E-mail plugin","components.Input.error.validation.email":"Dette er en ugyldig e-mail"}}}]);
