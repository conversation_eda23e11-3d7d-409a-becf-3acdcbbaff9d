"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[7808],{97808:(g,u,C)=>{C.r(u),C.d(u,{Analytics:()=>e,Documentation:()=>t,Email:()=>n,Password:()=>o,Provider:()=>B,ResetPasswordToken:()=>a,Role:()=>i,Username:()=>s,Users:()=>r,anErrorOccurred:()=>D,clearLabel:()=>c,default:()=>m,or:()=>p,skipToContent:()=>A,submit:()=>l});const e="\uD1B5\uACC4",t="\uB3C4\uD050\uBA58\uD14C\uC774\uC158",n="\uC774\uBA54\uC77C",o="\uBE44\uBC00\uBC88\uD638",B="\uD504\uB85C\uBC14\uC774\uB354(provider)",a="\uBE44\uBC00\uBC88\uD638 \uD1A0\uD070 \uC7AC\uC124\uC815",i="\uC5ED\uD560",s="\uC0AC\uC6A9\uC790 \uC774\uB984(Username)",r="\uC0AC\uC6A9\uC790",D="\uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4. \uC7A0\uC2DC \uD6C4\uC5D0 \uB2E4\uC2DC \uC2DC\uB3C4\uD574\uC8FC\uC138\uC694.",c="\uCD08\uAE30\uD654",p="\uB610\uB294",A="\uCF58\uD150\uCE20\uB85C \uAC74\uB108\uB6F0\uAE30",l="\uB4F1\uB85D",m={Analytics:e,"Auth.components.Oops.text":"\uACC4\uC815\uC774 \uC815\uC9C0\uB418\uC5C8\uC2B5\uB2C8\uB2E4.","Auth.components.Oops.text.admin":"\uC624\uB958\uAC00 \uC788\uB294 \uACBD\uC6B0 \uAD00\uB9AC\uC790\uC5D0\uAC8C \uBB38\uC758\uD574\uC8FC\uC138\uC694.","Auth.components.Oops.title":"\uC557...","Auth.form.button.forgot-password":"\uBA54\uC77C \uBCF4\uB0B4\uAE30","Auth.form.button.go-home":"\uD648\uC73C\uB85C","Auth.form.button.login":"\uB85C\uADF8\uC778","Auth.form.button.login.providers.error":"\uC120\uD0DD\uD55C \uD504\uB85C\uBC14\uC774\uB354\uB85C \uB85C\uADF8\uC778\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","Auth.form.button.login.strapi":"Strapi \uACC4\uC815\uC73C\uB85C \uB85C\uADF8\uC778","Auth.form.button.password-recovery":"\uBE44\uBC00\uBC88\uD638 \uBCF5\uC6D0","Auth.form.button.register":"\uB4F1\uB85D","Auth.form.confirmPassword.label":"\uBE44\uBC00\uBC88\uD638 \uD655\uC778","Auth.form.currentPassword.label":"\uAE30\uC874 \uBE44\uBC00\uBC88\uD638","Auth.form.email.label":"\uC774\uBA54\uC77C","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"\uAD00\uB9AC\uC790\uC5D0 \uC758\uD574 \uC811\uADFC\uC774 \uC81C\uD55C\uB41C \uACC4\uC815\uC785\uB2C8\uB2E4.","Auth.form.error.code.provide":"\uC720\uD6A8\uD558\uC9C0 \uC54A\uC740 \uCF54\uB4DC\uC785\uB2C8\uB2E4.","Auth.form.error.confirmed":"\uC774\uBA54\uC77C \uC778\uC99D\uC774 \uD544\uC694\uD569\uB2C8\uB2E4.","Auth.form.error.email.invalid":"\uC720\uD6A8\uD558\uC9C0 \uC54A\uC740 \uC774\uBA54\uC77C\uC785\uB2C8\uB2E4.","Auth.form.error.email.provide":"\uC774\uBA54\uC77C\uC744 \uC785\uB825\uD574 \uC8FC\uC138\uC694.","Auth.form.error.email.taken":"\uC774\uBBF8 \uC0AC\uC6A9 \uC911\uC778 \uC774\uBA54\uC77C\uC785\uB2C8\uB2E4.","Auth.form.error.invalid":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uC720\uD6A8\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.","Auth.form.error.params.provide":"\uC720\uD6A8\uD558\uC9C0 \uC54A\uC740 \uD30C\uB77C\uBBF8\uD130\uC785\uB2C8\uB2E4.","Auth.form.error.password.format":"\uBE44\uBC00\uBC88\uD638\uC5D0 `$` \uBB38\uC790\uB97C \uC138 \uBC88 \uC774\uC0C1 \uD3EC\uD568 \uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","Auth.form.error.password.local":"\uBE44\uBC00\uBC88\uD638\uB97C \uC124\uC815\uD558\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4. \uB2E4\uB978 \uBC29\uBC95\uC73C\uB85C \uB85C\uADF8\uC778 \uD558\uC138\uC694.","Auth.form.error.password.matching":"\uBE44\uBC00\uBC88\uD638\uAC00 \uC77C\uCE58\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.","Auth.form.error.password.provide":"\uBE44\uBC00\uBC88\uD638\uB97C \uC785\uB825\uD574 \uC8FC\uC138\uC694.","Auth.form.error.ratelimit":"\uC694\uCCAD\uC774 \uB108\uBB34 \uB9CE\uC2B5\uB2C8\uB2E4. \uC7A0\uC2DC \uD6C4 \uB2E4\uC2DC \uC2DC\uB3C4\uD574\uC8FC\uC138\uC694.","Auth.form.error.user.not-exist":"\uC774\uBA54\uC77C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","Auth.form.error.username.taken":"\uC774\uBBF8 \uC0AC\uC6A9 \uC911\uC778 \uC544\uC774\uB514\uC785\uB2C8\uB2E4.","Auth.form.firstname.label":"\uC774\uB984","Auth.form.firstname.placeholder":"e.g. Kai","Auth.form.forgot-password.email.label":"\uBA54\uC77C \uC8FC\uC18C\uB97C \uC785\uB825\uD558\uC138\uC694.","Auth.form.forgot-password.email.label.success":"\uBA54\uC77C\uC744 \uBCF4\uB0C8\uC2B5\uB2C8\uB2E4.","Auth.form.lastname.label":"\uC131","Auth.form.lastname.placeholder":"e.g. Doe","Auth.form.password.hide-password":"\uBE44\uBC00\uBC88\uD638 \uC228\uAE30\uAE30","Auth.form.password.hint":"\uC554\uD638\uB294 \uB300\uBB38\uC790 1\uC790, \uC18C\uBB38\uC790 1\uC790, \uC22B\uC790 1\uC790\uB97C \uD3EC\uD568\uD55C 8\uC790 \uC774\uC0C1\uC758 \uBB38\uC790\uB97C \uC785\uB825\uD574\uC8FC\uC138\uC694.","Auth.form.password.show-password":"\uBE44\uBC00\uBC88\uD638 \uD45C\uC2DC","Auth.form.register.news.label":"\uC0C8 \uAE30\uB2A5\uACFC \uD5A5\uD6C4 \uAC1C\uC120 \uC0AC\uD56D\uC5D0 \uB300\uD55C \uCD5C\uC2E0 \uC815\uBCF4\uB97C \uACC4\uC18D \uC81C\uACF5\uD574\uC8FC\uC138\uC694 (\uC120\uD0DD\uC2DC {terms}\uACFC {policy}\uC5D0 \uB3D9\uC758\uD558\uB294 \uAC78\uB85C \uAC04\uC8FC\uB429\uB2C8\uB2E4).","Auth.form.register.subtitle":"\uC778\uC99D \uC815\uBCF4\uB294 \uAD00\uB9AC\uC790 \uD328\uB110\uC5D0\uC11C \uC790\uC2E0\uC744 \uC778\uC99D\uD558\uB294 \uB370\uB9CC \uC0AC\uC6A9\uB429\uB2C8\uB2E4. \uC800\uC7A5\uB41C \uBAA8\uB4E0 \uB370\uC774\uD130\uB294 \uC0AC\uC6A9\uC790\uC758 \uB370\uC774\uD130\uBCA0\uC774\uC2A4\uC5D0 \uC800\uC7A5\uB429\uB2C8\uB2E4.","Auth.form.rememberMe.label":"\uB85C\uADF8\uC778 \uC0C1\uD0DC \uC800\uC7A5","Auth.form.username.label":"\uC544\uC774\uB514","Auth.form.username.placeholder":"KaiDoe","Auth.form.welcome.subtitle":"Strapi \uACC4\uC815\uC73C\uB85C \uB85C\uADF8\uC778\uD558\uC138\uC694.","Auth.form.welcome.title":"\uC548\uB155\uD558\uC138\uC694!","Auth.link.forgot-password":"\uBE44\uBC00\uBC88\uD638 \uC7AC\uC124\uC815","Auth.link.ready":"\uB85C\uADF8\uC778 \uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?","Auth.link.signin":"\uB85C\uADF8\uC778","Auth.link.signin.account":"\uC774\uBBF8 \uACC4\uC815\uC774 \uC788\uC73C\uC2E0\uAC00\uC694?","Auth.login.sso.divider":"Or login with","Auth.login.sso.loading":"\uD504\uB85C\uBC14\uC774\uB354\uB97C \uBD88\uB7EC\uC624\uB294 \uC911...","Auth.login.sso.subtitle":"SSO\uB97C \uD1B5\uD574 \uB85C\uADF8\uC778\uD569\uB2C8\uB2E4.","Auth.privacy-policy-agreement.policy":"\uAC1C\uC778\uC815\uBCF4 \uBCF4\uD638\uC815\uCC45","Auth.privacy-policy-agreement.terms":"\uC57D\uAD00","Content Manager":"\uCF58\uD150\uCE20 \uAD00\uB9AC","Content Type Builder":"\uCF58\uD150\uCE20 \uD0C0\uC785 \uBE4C\uB354",Documentation:t,Email:n,"Files Upload":"\uD30C\uC77C \uC5C5\uB85C\uB4DC","HomePage.helmet.title":"\uD648\uD398\uC774\uC9C0","HomePage.roadmap":"\uB85C\uB4DC\uB9F5 \uBCF4\uAE30","HomePage.welcome.congrats":"\uCD95\uD558\uD569\uB2C8\uB2E4!","HomePage.welcome.congrats.content":"\uCCAB\uBC88\uC9F8 \uAD00\uB9AC\uC790\uB85C \uB85C\uADF8\uC778\uD558\uC168\uC2B5\uB2C8\uB2E4. Strapi\uC758 \uAC15\uB825\uD55C \uAE30\uB2A5\uC744 \uD655\uC778\uD558\uC2DC\uB824\uBA74,","HomePage.welcome.congrats.content.bold":"we recommend you to create your first Collection-Type.","Media Library":"\uBBF8\uB514\uC5B4 \uB77C\uC774\uBE0C\uB7EC\uB9AC","New entry":"\uC0C8 \uD56D\uBAA9",Password:o,Provider:B,ResetPasswordToken:a,Role:i,"Roles & Permissions":"\uC5ED\uD560(Roles) & \uAD8C\uD55C(Permissions)","Roles.ListPage.notification.delete-all-not-allowed":"Some roles could not be deleted since they are associated with users","Roles.ListPage.notification.delete-not-allowed":"A role cannot be deleted if associated with users","Roles.RoleRow.select-all":"Select {name} for bulk actions","Roles.components.List.empty.withSearch":"There is no role corresponding to the search ({search})...","Settings.PageTitle":"Settings - {name}","Settings.apiTokens.addFirstToken":"\uCCAB API Token\uC744 \uB9CC\uB4E4\uC5B4\uBCF4\uC138\uC694.","Settings.apiTokens.addNewToken":"\uC0C8 API Token \uB9CC\uB4E4\uAE30","Settings.tokens.copy.editMessage":"\uBCF4\uC548\uC0C1\uC758 \uC774\uC720\uB85C \uD1A0\uD070\uC740 \uD55C \uBC88\uB9CC \uBCFC \uC218 \uC788\uC2B5\uB2C8\uB2E4.","Settings.tokens.copy.editTitle":"\uC774 \uD1A0\uD070\uC740 \uB354 \uC774\uC0C1 \uC561\uC138\uC2A4\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","Settings.tokens.copy.lastWarning":"\uC774 \uD1A0\uD070\uC744 \uBCF5\uC0AC\uD574\uB450\uC138\uC694. \uB2E4\uC2DC \uBCFC \uC218 \uC5C6\uC2B5\uB2C8\uB2E4!","Settings.apiTokens.create":"\uD56D\uBAA9 \uCD94\uAC00","Settings.apiTokens.description":"API \uC0AC\uC6A9\uC744 \uC704\uD574 \uC0DD\uC131\uB41C \uD1A0\uD070 \uBAA9\uB85D\uC785\uB2C8\uB2E4.","Settings.apiTokens.emptyStateLayout":"\uC544\uC9C1 \uCF58\uD150\uCE20\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.","Settings.tokens.notification.copied":"\uD1A0\uD070\uC774 \uD074\uB9BD\uBCF4\uB4DC\uC5D0 \uBCF5\uC0AC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.","Settings.apiTokens.title":"API \uD1A0\uD070","Settings.tokens.types.full-access":"\uC804\uCCB4 \uC561\uC138\uC2A4","Settings.tokens.types.read-only":"\uC77D\uAE30 \uC804\uC6A9","Settings.application.description":"\uD504\uB85C\uC81D\uD2B8 \uC138\uBD80 \uC815\uBCF4","Settings.application.edition-title":"\uD604\uC7AC \uD50C\uB79C","Settings.application.link-pricing":"\uBAA8\uB4E0 \uAC00\uACA9 \uC815\uCC45 \uBCF4\uAE30","Settings.application.link-upgrade":"\uC5B4\uB4DC\uBBFC \uD328\uB110 \uC5C5\uADF8\uB808\uC774\uB4DC","Settings.application.node-version":"node version","Settings.application.strapi-version":"strapi version","Settings.application.title":"\uC5B4\uD50C\uB9AC\uCF00\uC774\uC158","Settings.error":"\uC5D0\uB7EC","Settings.global":"\uAE00\uB85C\uBC8C \uC124\uC815","Settings.permissions":"\uC5B4\uB4DC\uBBFC \uD328\uB110","Settings.permissions.category":"{category} \uC0AC\uC6A9 \uAD8C\uD55C \uC124\uC815","Settings.permissions.category.plugins":"{category} \uD50C\uB7EC\uADF8\uC778 \uC0AC\uC6A9 \uAD8C\uD55C \uC124\uC815","Settings.permissions.conditions.anytime":"Anytime","Settings.permissions.conditions.apply":"Apply","Settings.permissions.conditions.can":"Can","Settings.permissions.conditions.conditions":"Define conditions","Settings.permissions.conditions.links":"Links","Settings.permissions.conditions.no-actions":"You first need to select actions (create, read, update, ...) before defining conditions on them.","Settings.permissions.conditions.none-selected":"Anytime","Settings.permissions.conditions.or":"OR","Settings.permissions.conditions.when":"When","Settings.permissions.select-all-by-permission":"Select all {label} permissions","Settings.permissions.select-by-permission":"Select {label} permission","Settings.permissions.users.create":"\uC0C8 \uC0AC\uC6A9\uC790 \uCD94\uAC00","Settings.permissions.users.email":"\uC774\uBA54\uC77C","Settings.permissions.users.firstname":"\uC774\uB984","Settings.permissions.users.lastname":"\uC131","Settings.permissions.users.form.sso":"Connect with SSO","Settings.permissions.users.form.sso.description":"When enabled (ON), users can login via SSO","Settings.permissions.users.listview.header.subtitle":"{number, plural, =0 {# \uBA85} one {# \uBA85} other {# \uBA85}}\uC758 \uC0AC\uC6A9\uC790\uB97C \uCC3E\uC558\uC2B5\uB2C8\uB2E4.","Settings.permissions.users.tabs.label":"Tabs Permissions","Settings.profile.form.notify.data.loaded":"\uC0AC\uC6A9\uC790 \uD504\uB85C\uD544 \uC815\uBCF4\uB97C \uBD88\uB7EC\uC654\uC2B5\uB2C8\uB2E4.","Settings.profile.form.section.experience.clear.select":"Clear the interface language selected","Settings.profile.form.section.experience.interfaceLanguage":"\uC778\uD130\uD398\uC774\uC2A4 \uC5B8\uC5B4","Settings.profile.form.section.experience.interfaceLanguage.hint":"\uC120\uD0DD\uD55C \uC5B8\uC5B4\uB85C \uC778\uD130\uD398\uC774\uC2A4\uC758 \uC5B8\uC5B4\uAC00 \uBCC0\uACBD\uB429\uB2C8\uB2E4.","Settings.profile.form.section.experience.interfaceLanguageHelp":"\uC120\uD0DD\uD558\uBA74 \uC774 \uACC4\uC815\uC5D0\uC11C\uB9CC \uC778\uD130\uD398\uC774\uC2A4 \uC5B8\uC5B4\uAC00 \uBCC0\uACBD\uB429\uB2C8\uB2E4. \uD300\uC5D0\uC11C \uB2E4\uB978 \uC5B8\uC5B4\uB97C \uC0AC\uC6A9\uD560 \uC218 \uC788\uB3C4\uB85D \uD558\uB824\uBA74 \uC774 {documentation}\uB97C \uCC38\uC870\uD574\uC8FC\uC138\uC694.","Settings.profile.form.section.experience.title":"\uC0AC\uC6A9\uC790 \uACBD\uD5D8","Settings.profile.form.section.helmet.title":"\uC0AC\uC6A9\uC790 \uD504\uB85C\uD544","Settings.profile.form.section.profile.page.title":"Profile page","Settings.roles.create.description":"\uC5ED\uD560\uC5D0 \uBD80\uC5EC\uB41C \uAD8C\uD55C\uC744 \uC815\uC758\uD569\uB2C8\uB2E4.","Settings.roles.create.title":"Create a role","Settings.roles.created":"Role created","Settings.roles.edit.title":"\uC5ED\uD560 \uC218\uC815","Settings.roles.form.button.users-with-role":"{number, plural, =0 {# \uBA85} one {# \uBA85} other {# \uBA85}}\uC758 \uC0AC\uC6A9\uC790\uAC00 \uC774 \uC5ED\uD560\uC744 \uAC00\uC9C0\uACE0 \uC788\uC2B5\uB2C8\uB2E4.","Settings.roles.form.created":"Created","Settings.roles.form.description":"\uC5ED\uD560\uC5D0 \uB300\uD55C \uC774\uB984\uACFC \uC124\uBA85","Settings.roles.form.permission.property-label":"{label} permissions","Settings.roles.form.permissions.attributesPermissions":"Fields permissions","Settings.roles.form.permissions.create":"\uC0DD\uC131","Settings.roles.form.permissions.delete":"\uC0AD\uC81C","Settings.roles.form.permissions.publish":"\uBC1C\uD589","Settings.roles.form.permissions.read":"\uC870\uD68C","Settings.roles.form.permissions.update":"\uC218\uC815","Settings.roles.list.button.add":"\uC0C8 \uC5ED\uD560 \uCD94\uAC00","Settings.roles.list.description":"\uC5ED\uD560 \uBAA9\uB85D","Settings.roles.title.singular":"\uC5ED\uD560","Settings.sso.description":"Single Sign-On \uAE30\uB2A5\uC5D0 \uB300\uD55C \uC124\uC815\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.","Settings.sso.form.defaultRole.description":"\uC0C8 \uC0AC\uC6A9\uC790\uB294 \uC120\uD0DD\uD55C \uC5ED\uD560\uC5D0 \uC5F0\uACB0\uB429\uB2C8\uB2E4.","Settings.sso.form.defaultRole.description-not-allowed":"\uC5B4\uB4DC\uBBFC \uC5ED\uD560\uC744 \uBCF4\uB824\uBA74 \uAD8C\uD55C\uC774 \uD544\uC694\uD569\uB2C8\uB2E4.","Settings.sso.form.defaultRole.label":"\uAE30\uBCF8 \uC5ED\uD560","Settings.sso.form.registration.description":"\uACC4\uC815\uC774 \uC5C6\uC73C\uBA74 SSO \uB85C\uADF8\uC778 \uC2DC \uC0C8 \uC0AC\uC6A9\uC790\uB97C \uC0DD\uC131\uD569\uB2C8\uB2E4.","Settings.sso.form.registration.label":"\uC790\uB3D9 \uD68C\uC6D0\uAC00\uC785","Settings.sso.title":"Single Sign-On","Settings.webhooks.create":"\uC6F9\uD6C5 \uB9CC\uB4E4\uAE30","Settings.webhooks.create.header":"\uC0C8 \uD5E4\uB354 \uB9CC\uB4E4\uAE30","Settings.webhooks.created":"\uC6F9\uD6C5\uC774 \uC0DD\uC131\uB418\uC5C8\uC2B5\uB2C8\uB2E4.","Settings.webhooks.event.publish-tooltip":"\uC774 \uC774\uBCA4\uD2B8\uB294 \uCD08\uC548/\uBC1C\uD589 \uC2DC\uC2A4\uD15C\uC774 \uD65C\uC131\uD654\uB41C \uCF58\uD150\uCE20\uC5D0 \uB300\uD574\uC11C\uB9CC \uC874\uC7AC\uD569\uB2C8\uB2E4.","Settings.webhooks.events.create":"\uC0DD\uC131","Settings.webhooks.events.update":"\uC218\uC815","Settings.webhooks.form.events":"\uC774\uBCA4\uD2B8","Settings.webhooks.form.headers":"\uD5E4\uB354","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"Remove header row {number}","Settings.webhooks.key":"Key","Settings.webhooks.list.button.add":"\uC0C8 \uC6F9\uD6C5 \uB9CC\uB4E4\uAE30","Settings.webhooks.list.description":"POST \uBCC0\uACBD \uC54C\uB9BC\uC744 \uAC00\uC838\uC635\uB2C8\uB2E4.","Settings.webhooks.list.empty.description":"\uCCAB \uC6F9\uD6C5\uC744 \uB9CC\uB4E4\uC5B4\uBCF4\uC138\uC694.","Settings.webhooks.list.empty.link":"\uC124\uBA85\uC11C \uBCF4\uAE30","Settings.webhooks.list.empty.title":"\uC544\uC9C1 \uC6F9\uD6C5\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","Settings.webhooks.list.th.actions":"actions","Settings.webhooks.list.th.status":"status","Settings.webhooks.singular":"\uC6F9\uD6C5","Settings.webhooks.title":"\uC6F9\uD6C5","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# \uAC1C\uC758 \uC5D0\uC14B\uC774} other {# \uAC1C\uC758 \uC5D0\uC14B\uC774}} \uC120\uD0DD\uB428","Settings.webhooks.trigger":"Trigger","Settings.webhooks.trigger.cancel":"Cancel trigger","Settings.webhooks.trigger.pending":"Pending\u2026","Settings.webhooks.trigger.save":"Please save to trigger","Settings.webhooks.trigger.success":"Success!","Settings.webhooks.trigger.success.label":"Trigger succeeded","Settings.webhooks.trigger.test":"Test-trigger","Settings.webhooks.trigger.title":"Save before Trigger","Settings.webhooks.value":"Value",Username:s,Users:r,"Users & Permissions":"\uC0AC\uC6A9\uC790 & \uAD8C\uD55C(Permissions)","Users.components.List.empty":"\uC0AC\uC6A9\uC790\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.","Users.components.List.empty.withFilters":"\uC801\uC6A9\uB41C \uD544\uD130\uC640 \uC77C\uCE58\uD558\uB294 \uC0AC\uC6A9\uC790\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.","Users.components.List.empty.withSearch":"({search}) \uAC80\uC0C9 \uACB0\uACFC\uC640 \uC77C\uCE58\uD558\uB294 \uC0AC\uC6A9\uC790\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.","admin.pages.MarketPlacePage.helmet":"\uB9C8\uCF13\uD50C\uB808\uC774\uC2A4 - \uD50C\uB7EC\uADF8\uC778","admin.pages.MarketPlacePage.submit.plugin.link":"\uD50C\uB7EC\uADF8\uC778 \uC81C\uCD9C","admin.pages.MarketPlacePage.subtitle":"Strapi\uC5D0\uC11C \uB354 \uB9CE\uC740 \uAC83\uC744 \uD574\uBCF4\uC138\uC694.",anErrorOccurred:D,"app.component.CopyToClipboard.label":"\uD074\uB9BD\uBCF4\uB4DC \uBCF5\uC0AC","app.component.search.label":"Search for {target}","app.component.table.duplicate":"Duplicate {target}","app.component.table.edit":"Edit {target}","app.component.table.select.one-entry":"Select {target}","app.components.BlockLink.blog":"\uBE14\uB85C\uADF8","app.components.BlockLink.blog.content":"Strapi\uC640 \uC0DD\uD0DC\uACC4\uC5D0 \uB300\uD55C \uCD5C\uC2E0 \uB274\uC2A4\uB97C \uC77D\uC5B4\uBCF4\uC138\uC694.","app.components.BlockLink.code":"\uCF54\uB4DC \uC0D8\uD50C","app.components.BlockLink.code.content":"\uC2E4\uC81C \uD504\uB85C\uC81D\uD2B8\uB97C \uD14C\uC2A4\uD2B8\uD558\uC5EC \uD559\uC2B5\uD569\uB2C8\uB2E4.","app.components.BlockLink.documentation.content":"\uD544\uC218 \uAC1C\uB150, \uAC00\uC774\uB4DC \uBC0F \uC9C0\uCE68\uC744 \uC0B4\uD3B4\uBCF4\uC138\uC694.","app.components.BlockLink.tutorial":"\uD29C\uD1A0\uB9AC\uC5BC","app.components.BlockLink.tutorial.content":"\uB2E8\uACC4\uBCC4 \uC9C0\uCE68\uC5D0 \uB530\uB77C Strapi\uB97C \uC0AC\uC6A9\uD558\uACE0 \uCEE4\uC2A4\uD130\uB9C8\uC774\uC9D5 \uD574\uBCF4\uC138\uC694.","app.components.Button.cancel":"\uCDE8\uC18C","app.components.Button.confirm":"\uD655\uC778","app.components.Button.reset":"\uB9AC\uC14B","app.components.ComingSoonPage.comingSoon":"Coming soon","app.components.ConfirmDialog.title":"\uD655\uC778","app.components.DownloadInfo.download":"\uB2E4\uC6B4\uB85C\uB4DC \uC911...","app.components.DownloadInfo.text":"\uC870\uAE08\uB9CC \uAE30\uB2E4\uB824 \uC8FC\uC138\uC694.","app.components.EmptyAttributes.title":"\uC544\uC9C1 \uC0DD\uC131\uB41C \uD544\uB4DC\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.","app.components.EmptyStateLayout.content-document":"\uC544\uC9C1 \uCF58\uD150\uCE20\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.","app.components.EmptyStateLayout.content-permissions":"\uD574\uB2F9 \uCF58\uD150\uCE20\uC5D0 \uC561\uC138\uC2A4\uD560 \uC218 \uC788\uB294 \uAD8C\uD55C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4","app.components.HomePage.button.blog":"\uBE14\uB85C\uADF8 \uBCF4\uAE30","app.components.HomePage.community":"\uCEE4\uBBA4\uB2C8\uD2F0\uB97C \uCC3E\uC544\uBCF4\uC138\uC694!","app.components.HomePage.community.content":"\uB2E4\uC591\uD55C \uCC44\uB110\uC5D0\uC11C Strapi \uD300\uC6D0, \uCF58\uD2B8\uB9AC\uBDF0\uD130 \uBC0F \uAC1C\uBC1C\uC790\uB4E4\uACFC \uD1A0\uB860\uD574\uBCF4\uC138\uC694.","app.components.HomePage.create":"\uCCAB \uCF58\uD150\uCE20 \uD0C0\uC785 \uB9CC\uB4E4\uAE30","app.components.HomePage.roadmap":"\uB85C\uB4DC\uB9F5 \uBCF4\uAE30","app.components.HomePage.welcome":"\uD658\uC601\uD569\uB2C8\uB2E4 \u{1F44B}","app.components.HomePage.welcome.again":"\uBC18\uAC11\uC2B5\uB2C8\uB2E4 \u{1F44B}","app.components.HomePage.welcomeBlock.content":"\uCD95\uD558\uB4DC\uB9BD\uB2C8\uB2E4! \uCCAB \uBC88\uC9F8 \uAD00\uB9AC\uC790\uB85C \uB85C\uADF8\uC778\uD558\uC168\uC2B5\uB2C8\uB2E4. Strapi\uAC00 \uC81C\uACF5\uD558\uB294 \uAC15\uB825\uD55C \uAE30\uB2A5\uC744 \uC54C\uC544\uBCF4\uB824\uBA74 \uCCAB \uBC88\uC9F8 \uCF58\uD150\uCE20 \uC720\uD615\uC744 \uB9CC\uB4E4\uC5B4\uBCF4\uC138\uC694!","app.components.HomePage.welcomeBlock.content.again":"Strapi\uC5D0 \uB300\uD55C \uCD5C\uC2E0 \uB274\uC2A4\uB97C \uC790\uC720\uB86D\uAC8C \uC77D\uC5B4\uBCF4\uC138\uC694. \uC800\uD76C\uB294 \uC5EC\uB7EC\uBD84\uC758 \uD53C\uB4DC\uBC31\uC744 \uBC14\uD0D5\uC73C\uB85C \uC81C\uD488\uC744 \uAC1C\uC120\uD558\uAE30 \uC704\uD574 \uCD5C\uC120\uC744 \uB2E4\uD558\uACE0 \uC788\uC2B5\uB2C8\uB2E4.","app.components.HomePage.welcomeBlock.content.issues":"\uC774\uC288","app.components.HomePage.welcomeBlock.content.raise":", ","app.components.ImgPreview.hint":"\uD30C\uC77C\uC744 \uB04C\uC5B4 \uB193\uAC70\uB098 {browse} \uD558\uC138\uC694.","app.components.ImgPreview.hint.browse":"\uC120\uD0DD","app.components.InputFile.newFile":"\uD30C\uC77C \uCD94\uAC00","app.components.InputFileDetails.open":"\uC0C8 \uD0ED\uC73C\uB85C \uC5F4\uAE30","app.components.InputFileDetails.originalName":"\uC6D0\uB798 \uD30C\uC77C \uC774\uB984:","app.components.InputFileDetails.remove":"\uD30C\uC77C \uC0AD\uC81C","app.components.InputFileDetails.size":"\uD06C\uAE30:","app.components.InstallPluginPage.Download.description":"\uD50C\uB7EC\uADF8\uC778\uC744 \uB2E4\uC6B4\uB85C\uB4DC\uD558\uC5EC \uC124\uCE58\uD558\uB294 \uB370 \uBA87 \uCD08 \uC815\uB3C4 \uAC78\uB9B4 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","app.components.InstallPluginPage.Download.title":"\uB2E4\uC6B4\uB85C\uB4DC \uC911...","app.components.InstallPluginPage.description":"\uBE60\uB974\uACE0 \uAC04\uB2E8\uD558\uAC8C \uAE30\uB2A5\uC744 \uD655\uC7A5\uD574 \uBCF4\uC138\uC694.","app.components.LeftMenu.collapse":"Collapse the navbar","app.components.LeftMenu.expand":"Expand the navbar","app.components.LeftMenu.logout":"\uB85C\uADF8\uC544\uC6C3","app.components.LeftMenu.navbrand.title":"Strapi \uB300\uC2DC\uBCF4\uB4DC","app.components.LeftMenu.navbrand.workplace":"\uC791\uC5C5 \uACF5\uAC04","app.components.LeftMenuFooter.help":"\uB3C4\uC6C0\uB9D0","app.components.LeftMenuFooter.poweredBy":"Powered by ","app.components.LeftMenuLinkContainer.collectionTypes":"\uCF5C\uB809\uC158 \uD0C0\uC785","app.components.LeftMenuLinkContainer.configuration":"\uD658\uACBD\uC124\uC815","app.components.LeftMenuLinkContainer.general":"\uC77C\uBC18","app.components.LeftMenuLinkContainer.noPluginsInstalled":"\uC124\uCE58\uB41C \uD50C\uB7EC\uADF8\uC778\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","app.components.LeftMenuLinkContainer.plugins":"\uD50C\uB7EC\uADF8\uC778","app.components.LeftMenuLinkContainer.singleTypes":"\uC2F1\uAE00 \uD0C0\uC785","app.components.ListPluginsPage.deletePlugin.description":"\uD50C\uB7EC\uADF8\uC778\uC744 \uC81C\uAC70\uD558\uB294 \uB370 \uBA87 \uCD08 \uC815\uB3C4 \uAC78\uB9B4 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","app.components.ListPluginsPage.deletePlugin.title":"\uC81C\uAC70\uD558\uB294 \uC911","app.components.ListPluginsPage.description":"\uC774 \uD504\uB85C\uC81D\uD2B8\uC5D0 \uC124\uCE58\uB41C \uD50C\uB7EC\uADF8\uC778 \uBAA9\uB85D\uC785\uB2C8\uB2E4.","app.components.ListPluginsPage.helmet.title":"\uD50C\uB7EC\uADF8\uC778 \uBAA9\uB85D","app.components.Logout.logout":"\uB85C\uADF8\uC544\uC6C3","app.components.Logout.profile":"\uD504\uB85C\uD544","app.components.MarketplaceBanner":"Discover plugins built by the community, and many more awesome things to kickstart your project, on Strapi Awesome.","app.components.MarketplaceBanner.image.alt":"a strapi rocket logo","app.components.MarketplaceBanner.link":"\uC9C0\uAE08 \uD655\uC778\uD574\uBCF4\uAE30","app.components.NotFoundPage.back":"\uD648\uC73C\uB85C \uB3CC\uC544\uAC00\uAE30","app.components.NotFoundPage.description":"\uCC3E\uC744 \uC218 \uC5C6\uB294 \uD398\uC774\uC9C0\uC785\uB2C8\uB2E4.","app.components.Official":"\uACF5\uC2DD","app.components.Onboarding.help.button":"\uB3C4\uC6C0\uB9D0","app.components.Onboarding.label.completed":"% \uC644\uB8CC","app.components.Onboarding.title":"\uB3D9\uC601\uC0C1 \uC2DC\uCCAD\uD558\uAE30","app.components.PluginCard.Button.label.download":"\uB2E4\uC6B4\uB85C\uB4DC","app.components.PluginCard.Button.label.install":"\uC124\uCE58\uB428","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"autoReload \uAE30\uB2A5\uC744 \uC0AC\uC6A9\uD558\uC9C0 \uC54A\uB3C4\uB85D \uC124\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. `yarn develop`\uB85C \uC571\uC744 \uC2DC\uC791\uD558\uC138\uC694.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"\uC54C\uACA0\uC2B5\uB2C8\uB2E4!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"\uBCF4\uC548\uC0C1\uC758 \uC774\uC720\uB85C \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uD50C\uB7EC\uADF8\uC778\uC744 \uB2E4\uC6B4\uB85C\uB4DC\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","app.components.PluginCard.PopUpWarning.install.impossible.title":"\uB2E4\uC6B4\uB85C\uB4DC \uBD88\uAC00","app.components.PluginCard.compatible":"\uC774 \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC5D0 \uD638\uD658\uB429\uB2C8\uB2E4.","app.components.PluginCard.compatibleCommunity":"\uC158\uBBA4\uB2C8\uD2F0\uC5D0 \uD638\uD658\uB429\uB2C8\uB2E4.","app.components.PluginCard.more-details":"[\uB354\uBCF4\uAE30]","app.components.ToggleCheckbox.off-label":"False","app.components.ToggleCheckbox.on-label":"True","app.components.Users.MagicLink.connect":"\uC774 \uB9C1\uD06C\uB97C \uAC00\uC785\uD560 \uC0AC\uC6A9\uC790\uC5D0\uAC8C \uBCF4\uB0B4\uC8FC\uC138\uC694.","app.components.Users.MagicLink.connect.sso":"\uC774 \uB9C1\uD06C\uB97C \uAC00\uC785\uD560 \uC0AC\uC6A9\uC790\uC5D0\uAC8C \uBCF4\uB0B4\uC8FC\uC138\uC694. SSO \uD504\uB85C\uBC14\uC774\uB354\uB97C \uD1B5\uD574 \uCC98\uC74C \uB85C\uADF8\uC778\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","app.components.Users.ModalCreateBody.block-title.details":"\uC0C1\uC138 \uC815\uBCF4","app.components.Users.ModalCreateBody.block-title.roles":"\uC0AC\uC6A9\uC790 \uC5ED\uD560","app.components.Users.ModalCreateBody.block-title.roles.description":"\uC0AC\uC6A9\uC790\uB294 \uD558\uB098 \uC774\uC0C1\uC758 \uC5ED\uD560\uC744 \uAC00\uC9C8 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","app.components.Users.SortPicker.button-label":"\uC815\uB82C \uAE30\uC900","app.components.Users.SortPicker.sortby.email_asc":"\uC774\uBA54\uC77C (A to Z)","app.components.Users.SortPicker.sortby.email_desc":"\uC774\uBA54\uC77C (Z to A)","app.components.Users.SortPicker.sortby.firstname_asc":"\uC774\uB984 (A to Z)","app.components.Users.SortPicker.sortby.firstname_desc":"\uC774\uB984 (Z to A)","app.components.Users.SortPicker.sortby.lastname_asc":"\uC131 (A to Z)","app.components.Users.SortPicker.sortby.lastname_desc":"\uC131 (Z to A)","app.components.Users.SortPicker.sortby.username_asc":"Username (A to Z)","app.components.Users.SortPicker.sortby.username_desc":"Username (Z to A)","app.components.listPlugins.button":"\uC0C8\uB85C\uC6B4 \uD50C\uB7EC\uADF8\uC778 \uCD94\uAC00\uD558\uAE30","app.components.listPlugins.title.none":"\uC124\uCE58\uB41C \uD50C\uB7EC\uADF8\uC778\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","app.components.listPluginsPage.deletePlugin.error":"\uD50C\uB7EC\uADF8\uC778\uC744 \uC81C\uAC70\uD558\uB294\uB370 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","app.containers.App.notification.error.init":"API \uC694\uCCAD \uC911\uC5D0 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"\uB9C1\uD06C\uB97C \uBC1B\uC9C0 \uBABB\uD588\uB2E4\uBA74 \uAD00\uB9AC\uC790\uC5D0\uAC8C \uBB38\uC758\uD574\uC8FC\uC138\uC694.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"\uBE44\uBC00\uBC88\uD638 \uBCF5\uAD6C \uB9C1\uD06C\uB97C \uBC1B\uB294 \uB370 \uBA87 \uBD84 \uC815\uB3C4 \uAC78\uB9B4 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","app.containers.AuthPage.ForgotPasswordSuccess.title":"Email sent","app.containers.Users.EditPage.form.active.label":"\uD65C\uC131","app.containers.Users.EditPage.header.label":"{name} \uC218\uC815","app.containers.Users.EditPage.header.label-loading":"\uC0AC\uC6A9\uC790 \uC218\uC815","app.containers.Users.EditPage.roles-bloc-title":"Attributed roles","app.containers.Users.ModalForm.footer.button-success":"\uC0AC\uC6A9\uC790 \uC0DD\uC131","app.links.configure-view":"\uBCF4\uAE30 \uC124\uC815","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"\uD544\uD130 \uCD94\uAC00","app.utils.close-label":"\uB2EB\uAE30","app.utils.defaultMessage":" ","app.utils.duplicate":"\uBCF5\uC0AC","app.utils.edit":"\uC218\uC815","app.utils.errors.file-too-big.message":"\uD30C\uC77C\uC774 \uB108\uBB34 \uD07D\uB2C8\uB2E4","app.utils.filter-value":"\uD544\uD130 \uAC12","app.utils.filters":"\uD544\uD130","app.utils.notify.data-loaded":"{target}\uC744 \uBD88\uB7EC\uC654\uC2B5\uB2C8\uB2E4.","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"\uBC1C\uD589","app.utils.select-all":"\uC804\uCCB4 \uC120\uD0DD","app.utils.select-field":"\uD544\uB4DC \uC120\uD0DD","app.utils.select-filter":"\uD544\uD130 \uC120\uD0DD","app.utils.unpublish":"\uBC1C\uD589 \uCDE8\uC18C",clearLabel:c,"coming.soon":"\uC8FC","component.Input.error.validation.integer":"\uAC12\uC740 \uC815\uC218\uC5EC\uC57C \uD569\uB2C8\uB2E4.","components.AutoReloadBlocker.description":"\uB2E4\uC74C \uBA85\uB839\uC5B4 \uC911 \uD558\uB098\uB97C \uC0AC\uC6A9\uD558\uC5EC Strapi\uB97C \uC2E4\uD589\uD569\uB2C8\uB2E4:","components.AutoReloadBlocker.header":"\uC774 \uD50C\uB7EC\uADF8\uC778\uC740 \uB9AC\uB85C\uB4DC \uAE30\uB2A5\uC774 \uD544\uC694\uD569\uB2C8\uB2E4.","components.ErrorBoundary.title":"\uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","components.FilterOptions.FILTER_TYPES.$contains":"\uD3EC\uD568","components.FilterOptions.FILTER_TYPES.$containsi":"\uD3EC\uD568(\uB300\uC18C\uBB38\uC790 \uAD6C\uBD84 \uC548 \uD568)","components.FilterOptions.FILTER_TYPES.$endsWith":"\uB85C \uB05D\uB098\uB2E4","components.FilterOptions.FILTER_TYPES.$endsWithi":"\uB2E4\uC74C\uC73C\uB85C \uB05D\uB0A8(case insensitive)","components.FilterOptions.FILTER_TYPES.$eq":"\uAC19\uC74C","components.FilterOptions.FILTER_TYPES.$eqi":"\uAC19\uC74C(\uB300\uC18C\uBB38\uC790 \uAD6C\uBD84 \uC548 \uD568)","components.FilterOptions.FILTER_TYPES.$gt":"is greater than","components.FilterOptions.FILTER_TYPES.$gte":"is greater than or equal to","components.FilterOptions.FILTER_TYPES.$lt":"is lower than","components.FilterOptions.FILTER_TYPES.$lte":"is lower than or equal to","components.FilterOptions.FILTER_TYPES.$ne":"\uAC19\uC9C0 \uC54A\uC74C","components.FilterOptions.FILTER_TYPES.$nei":"\uAC19\uC9C0 \uC54A\uC74C(case insensitive)","components.FilterOptions.FILTER_TYPES.$notContains":"\uD3EC\uD568\uB418\uC5B4 \uC788\uC9C0 \uC54A\uB2E4","components.FilterOptions.FILTER_TYPES.$notContainsi":"\uD3EC\uD568\uD558\uC9C0 \uC54A\uC74C(\uB300\uC18C\uBB38\uC790 \uAD6C\uBD84 \uC548 \uD568)","components.FilterOptions.FILTER_TYPES.$notNull":"is not null","components.FilterOptions.FILTER_TYPES.$null":"is null","components.FilterOptions.FILTER_TYPES.$startsWith":"\uB85C \uC2DC\uC791","components.FilterOptions.FILTER_TYPES.$startsWithi":"\uB85C \uC2DC\uC791(\uB300\uC18C\uBB38\uC790 \uAD6C\uBD84 \uC548 \uD568)","components.Input.error.attribute.key.taken":"\uC774\uBBF8 \uC0AC\uC6A9\uC911\uC778 \uD0A4\uC785\uB2C8\uB2E4.","components.Input.error.attribute.sameKeyAndName":"\uAC19\uC740 \uAC12\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","components.Input.error.attribute.taken":"\uC774\uBBF8 \uC0AC\uC6A9\uC911\uC778 \uC774\uB984\uC785\uB2C8\uB2E4.","components.Input.error.contain.lowercase":"\uBE44\uBC00\uBC88\uD638\uB294 \uC18C\uBB38\uC790 \uD558\uB098\uB97C \uBC18\uB4DC\uC2DC \uD3EC\uD568\uD574\uC57C \uD569\uB2C8\uB2E4.","components.Input.error.contain.number":"\uBE44\uBC00\uBC88\uD638\uB294 \uC22B\uC790 \uD558\uB098\uB97C \uBC18\uB4DC\uC2DC \uD3EC\uD568\uD574\uC57C \uD569\uB2C8\uB2E4.","components.Input.error.contain.uppercase":"\uBE44\uBC00\uBC88\uD638\uB294 \uB300\uBB38\uC790 \uD558\uB098\uB97C \uBC18\uB4DC\uC2DC \uD3EC\uD568\uD574\uC57C \uD569\uB2C8\uB2E4.","components.Input.error.contentTypeName.taken":"\uC774\uBBF8 \uC0AC\uC6A9\uC911\uC778 \uC774\uB984\uC785\uB2C8\uB2E4.","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"\uBE44\uBC00\uBC88\uD638\uAC00 \uC77C\uCE58\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.","components.Input.error.validation.email":"\uC62C\uBC14\uB978 \uC774\uBA54\uC77C \uC8FC\uC18C\uAC00 \uC544\uB2D9\uB2C8\uB2E4.","components.Input.error.validation.json":"JSON \uD615\uC2DD\uC774 \uC544\uB2D9\uB2C8\uB2E4.","components.Input.error.validation.max":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB108\uBB34 \uD07D\uB2C8\uB2E4 {max}.","components.Input.error.validation.maxLength":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB108\uBB34 \uAE41\uB2C8\uB2E4 {max}.","components.Input.error.validation.min":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB108\uBB34 \uC791\uC2B5\uB2C8\uB2E4 {min}.","components.Input.error.validation.minLength":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB108\uBB34 \uC9E7\uC2B5\uB2C8\uB2E4 {min}.","components.Input.error.validation.minSupMax":"\uC774 \uBCF4\uB2E4 \uB354 \uD074 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","components.Input.error.validation.regex":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uD615\uC2DD\uC5D0 \uB9DE\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.","components.Input.error.validation.required":"\uB0B4\uC6A9\uC744 \uC785\uB825\uD574 \uC8FC\uC138\uC694.","components.Input.error.validation.unique":"\uC774 \uAC12\uC740 \uC774\uBBF8 \uC0AC\uC6A9\uC911\uC785\uB2C8\uB2E4.","components.InputSelect.option.placeholder":"\uC120\uD0DD\uD574 \uC8FC\uC138\uC694.","components.ListRow.empty":"\uB370\uC774\uD130\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.","components.NotAllowedInput.text":"\uC774 \uD544\uB4DC\uB97C \uBCFC \uC218 \uC788\uB294 \uAD8C\uD55C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","components.OverlayBlocker.description":"\uC774 \uAE30\uB2A5\uC740 \uC11C\uBC84\uB97C \uC7AC\uC2DC\uC791\uD574\uC57C \uD569\uB2C8\uB2E4. \uC11C\uBC84\uAC00 \uC2DC\uC791\uB420 \uB54C\uAE4C\uC9C0 \uC7A0\uC2DC\uB9CC \uAE30\uB2E4\uB824\uC8FC\uC138\uC694.","components.OverlayBlocker.description.serverError":"\uC11C\uBC84\uAC00 \uC7AC\uC2DC\uC791\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4. \uD130\uBBF8\uB110\uC5D0\uC11C \uB85C\uADF8\uB97C \uD655\uC778\uD558\uC2ED\uC2DC\uC624.","components.OverlayBlocker.title":"\uC7AC\uC2DC\uC791\uD558\uACE0 \uC788\uC2B5\uB2C8\uB2E4...","components.OverlayBlocker.title.serverError":"\uC7AC\uC2DC\uC791 \uC2DC\uAC04\uC774 \uC608\uC0C1\uBCF4\uB2E4 \uC624\uB798 \uAC78\uB9AC\uACE0 \uC788\uC2B5\uB2C8\uB2E4.","components.PageFooter.select":"\uD56D\uBAA9 \uC218 / \uD398\uC774\uC9C0","components.ProductionBlocker.description":"\uC774 \uD50C\uB7EC\uADF8\uC778\uC740 \uC548\uC804\uC744 \uC704\uD574 \uB2E4\uB978 \uD658\uACBD\uC5D0\uC11C \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","components.ProductionBlocker.header":"\uC774 \uD50C\uB7EC\uADF8\uC778\uC740 \uAC1C\uBC1C \uBAA8\uB4DC\uC5D0\uC11C\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","components.Search.placeholder":"\uAC80\uC0C9...","components.TableHeader.sort":"Sort on {label}","components.Wysiwyg.ToggleMode.markdown-mode":"\uB9C8\uD06C\uB2E4\uC6B4 \uBAA8\uB4DC","components.Wysiwyg.ToggleMode.preview-mode":"\uBBF8\uB9AC\uBCF4\uAE30 \uBAA8\uB4DC","components.Wysiwyg.collapse":"\uBCD1\uD569","components.Wysiwyg.selectOptions.H1":"\uC81C\uBAA9 H1","components.Wysiwyg.selectOptions.H2":"\uC81C\uBAA9 H2","components.Wysiwyg.selectOptions.H3":"\uC81C\uBAA9 H3","components.Wysiwyg.selectOptions.H4":"\uC81C\uBAA9 H4","components.Wysiwyg.selectOptions.H5":"\uC81C\uBAA9 H5","components.Wysiwyg.selectOptions.H6":"\uC81C\uBAA9 H6","components.Wysiwyg.selectOptions.title":"\uC81C\uBAA9","components.WysiwygBottomControls.charactersIndicators":"\uBB38\uC790 \uD45C\uC2DC\uAE30","components.WysiwygBottomControls.fullscreen":"\uC804\uCCB4\uD654\uBA74","components.WysiwygBottomControls.uploadFiles":"\uD30C\uC77C\uC744 \uB04C\uC5B4 \uB193\uC73C\uC138\uC694. \uD639\uC740 \uD074\uB9BD\uBCF4\uB4DC\uC5D0\uC11C \uBD99\uD600\uB123\uAC70\uB098 {browse} \uD558\uC138\uC694.","components.WysiwygBottomControls.uploadFiles.browse":"\uC120\uD0DD","components.pagination.go-to":"{page} \uD398\uC774\uC9C0\uB85C","components.pagination.go-to-next":"\uB2E4\uC74C \uD398\uC774\uC9C0","components.pagination.go-to-previous":"\uC774\uC804 \uD398\uC774\uC9C0","components.pagination.remaining-links":"And {number} other links","components.popUpWarning.button.cancel":"\uC544\uB2C8\uC694, \uCDE8\uC18C\uD569\uB2C8\uB2E4.","components.popUpWarning.button.confirm":"\uB124, \uD655\uC778\uD588\uC2B5\uB2C8\uB2E4.","components.popUpWarning.message":"\uC0AD\uC81C\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?","components.popUpWarning.title":"\uD655\uC778","content-manager.App.schemas.data-loaded":"\uC2A4\uD0A4\uB9C8\uB97C \uBD88\uB7EC\uC654\uC2B5\uB2C8\uB2E4.","content-manager.ListViewTable.relation-loaded":"\uB9B4\uB808\uC774\uC158\uC744 \uBD88\uB7EC\uC654\uC2B5\uB2C8\uB2E4.","content-manager.EditRelations.title":"\uAD00\uACC4 \uB370\uC774\uD130","content-manager.HeaderLayout.button.label-add-entry":"\uC0C8 \uD56D\uBAA9 \uCD94\uAC00","content-manager.api.id":"API ID","content-manager.components.AddFilterCTA.add":"\uD544\uD130","content-manager.components.AddFilterCTA.hide":"\uD544\uD130","content-manager.components.DragHandle-label":"\uB4DC\uB798\uADF8","content-manager.components.DraggableAttr.edit":"\uD074\uB9AD\uD558\uC5EC \uC218\uC815","content-manager.components.DraggableCard.delete.field":"{item} \uC0AD\uC81C","content-manager.components.DraggableCard.edit.field":"{item} \uC218\uC815","content-manager.components.DraggableCard.move.field":"{item} \uC774\uB3D9","content-manager.components.ListViewTable.row-line":"item line {number}","content-manager.components.DynamicZone.ComponentPicker-label":"Pick one component","content-manager.components.DynamicZone.add-component":"Add a component to {componentName}","content-manager.components.DynamicZone.delete-label":"{name} \uC0AD\uC81C","content-manager.components.DynamicZone.error-message":"The component contains error(s)","content-manager.components.DynamicZone.missing-components":"{number, plural, =0 {# \uAC1C} one {is # \uAC1C} other {are # \uAC1C}}\uC758 \uB204\uB77D\uB41C \uCEF4\uD3EC\uB10C\uD2B8\uAC00 \uC788\uC2B5\uB2C8\uB2E4.","content-manager.components.DynamicZone.move-down-label":"\uCEF4\uD3EC\uB10C\uD2B8 \uC544\uB798\uB85C \uC774\uB3D9","content-manager.components.DynamicZone.move-up-label":"\uCEF4\uD3EC\uB10C\uD2B8 \uC704\uB85C \uC774\uB3D9","content-manager.components.DynamicZone.pick-compo":"Pick one component","content-manager.components.DynamicZone.required":"\uCEF4\uD3EC\uB10C\uD2B8\uB294 \uD544\uC218 \uD56D\uBAA9\uC785\uB2C8\uB2E4.","content-manager.components.EmptyAttributesBlock.button":"\uC124\uC815 \uD398\uC774\uC9C0 \uC774\uB3D9","content-manager.components.EmptyAttributesBlock.description":"\uC124\uC815\uC744 \uBCC0\uACBD\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","content-manager.components.FieldItem.linkToComponentLayout":"\uCEF4\uD3EC\uB10C\uD2B8 \uB808\uC774\uC544\uC6C3 \uC124\uC815","content-manager.components.FieldSelect.label":"\uD544\uB4DC \uCD94\uAC00","content-manager.components.FilterOptions.button.apply":"\uC801\uC6A9","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"\uC801\uC6A9","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"\uBAA8\uB450 \uC7AC\uC124\uC815","content-manager.components.FiltersPickWrapper.PluginHeader.description":"\uD544\uD130\uB9C1 \uC870\uAC74\uC744 \uC124\uC815\uD558\uC138\uC694.","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"\uD544\uD130","content-manager.components.FiltersPickWrapper.hide":"\uC228\uAE40","content-manager.components.LeftMenu.Search.label":"\uCF58\uD150\uCE20 \uD0C0\uC785 \uAC80\uC0C9","content-manager.components.LeftMenu.collection-types":"\uCF5C\uB809\uC158 \uD0C0\uC785","content-manager.components.LeftMenu.single-types":"\uC2F1\uAE00 \uD0C0\uC785","content-manager.components.LimitSelect.itemsPerPage":"\uD56D\uBAA9 \uC218 / \uD398\uC774\uC9C0","content-manager.components.NotAllowedInput.text":"\uC774 \uD544\uB4DC\uB97C \uBCFC \uC218 \uC788\uB294 \uAD8C\uD55C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.components.RepeatableComponent.error-message":"The component(s) contain error(s)","content-manager.components.Search.placeholder":"\uAC80\uC0C9 \uC911\uC785\uB2C8\uB2E4...","content-manager.components.Select.draft-info-title":"\uC0C1\uD0DC: \uCD08\uC548","content-manager.components.Select.publish-info-title":"\uC0C1\uD0DC: \uBC1C\uD589\uB428","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"\uD3B8\uC9D1 \uBCF4\uAE30 \uD654\uBA74\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"\uBAA9\uB85D \uBCF4\uAE30 \uD654\uBA74\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"\uBCF4\uAE30 \uC124\uC815 - {name}","content-manager.components.TableDelete.delete":"\uBAA8\uB450 \uC0AD\uC81C","content-manager.components.TableDelete.deleteSelected":"\uC120\uD0DD\uD56D\uBAA9 \uC0AD\uC81C","content-manager.components.TableDelete.label":"{number, plural, one {# \uAC1C} other {# \uAC1C}}\uC758 \uD56D\uBAA9\uC774 \uC120\uD0DD\uB428","content-manager.components.TableEmpty.withFilters":"\uD544\uD130 \uC870\uAC74\uC5D0 \uB9DE\uB294 {contentType} \uBAA9\uB85D\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.components.TableEmpty.withSearch":'"{search}" \uAC80\uC0C9. {contentType} \uBAA9\uB85D\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.',"content-manager.components.TableEmpty.withoutFilter":"{contentType} \uBAA9\uB85D\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.components.empty-repeatable":"\uD56D\uBAA9\uC774 \uC5C6\uC2B5\uB2C8\uB2E4. \uD56D\uBAA9\uC744 \uCD94\uAC00\uD558\uB824\uBA74 \uC544\uB798 \uBC84\uD2BC\uC744 \uD074\uB9AD\uD574\uC8FC\uC138\uC694.","content-manager.components.notification.info.maximum-requirement":"\uC774\uBBF8 \uCD5C\uB300 \uD544\uB4DC \uC218\uC5D0 \uB3C4\uB2EC\uD588\uC2B5\uB2C8\uB2E4.","content-manager.components.notification.info.minimum-requirement":"\uCD5C\uC18C \uC694\uAD6C \uC0AC\uD56D\uACFC \uC77C\uCE58\uD558\uB3C4\uB85D \uD544\uB4DC\uAC00 \uCD94\uAC00\uB418\uC5C8\uC2B5\uB2C8\uB2E4.","content-manager.components.repeatable.reorder.error":"\uCEF4\uD3EC\uB10C\uD2B8 \uD544\uB4DC\uB97C \uC7AC\uC815\uB82C\uD558\uB294 \uC911\uC5D0 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4. \uB2E4\uC2DC \uC2DC\uB3C4\uD558\uC2ED\uC2DC\uC624.","content-manager.components.reset-entry":"Reset entry","content-manager.components.uid.apply":"\uC801\uC6A9","content-manager.components.uid.available":"\uC0AC\uC6A9 \uAC00\uB2A5","content-manager.components.uid.regenerate":"\uC7AC\uC0DD\uC131","content-manager.components.uid.suggested":"\uC81C\uC548\uB428","content-manager.components.uid.unavailable":"\uC0AC\uC6A9 \uBD88\uAC00","content-manager.containers.Edit.Link.Layout":"\uB808\uC774\uC544\uC6C3 \uC124\uC815","content-manager.containers.Edit.Link.Model":"\uCF5C\uB809\uC158 \uD0C0\uC785 \uC218\uC815","content-manager.containers.Edit.addAnItem":"\uCD94\uAC00\uD560 \uD56D\uBAA9...","content-manager.containers.Edit.clickToJump":"\uD574\uB2F9 \uD56D\uBAA9\uC73C\uB85C \uC774\uB3D9\uD558\uB824\uBA74 \uD074\uB9AD","content-manager.containers.Edit.delete":"\uC0AD\uC81C","content-manager.containers.Edit.delete-entry":"\uC774 \uD56D\uBAA9 \uC0AD\uC81C","content-manager.containers.Edit.editing":"\uC218\uC815 \uC911...","content-manager.containers.Edit.information":"\uC815\uBCF4","content-manager.containers.Edit.information.by":"\uD3B8\uC9D1\uC790","content-manager.containers.Edit.information.draftVersion":"\uCD08\uC548 \uBC84\uC804","content-manager.containers.Edit.information.editing":"\uC218\uC815\uC911 -","content-manager.containers.Edit.information.lastUpdate":"\uCD5C\uADFC \uC5C5\uB370\uC774\uD2B8","content-manager.containers.Edit.information.publishedVersion":"\uBC1C\uD589 \uBC84\uC804","content-manager.containers.Edit.pluginHeader.title.new":"\uD56D\uBAA9 \uC0DD\uC131","content-manager.containers.Edit.reset":"\uCD08\uAE30\uD654","content-manager.containers.Edit.returnList":"\uBAA9\uB85D","content-manager.containers.Edit.seeDetails":"\uC138\uBD80 \uC0AC\uD56D","content-manager.containers.Edit.submit":"\uC800\uC7A5","content-manager.containers.EditSettingsView.modal-form.edit-field":"\uD544\uB4DC \uC218\uC815","content-manager.containers.EditView.add.new-entry":"\uD56D\uBAA9 \uCD94\uAC00","content-manager.containers.EditView.notification.errors":"\uC798\uBABB \uC785\uB825\uB41C \uD544\uB4DC\uAC00 \uC874\uC7AC\uD569\uB2C8\uB2E4.","content-manager.containers.Home.introduction":"\uD56D\uBAA9\uC744 \uC218\uC815\uD558\uB824\uBA74 \uC67C\uD3B8 \uB9C1\uD06C\uB97C \uD074\uB9AD\uD558\uC138\uC694. \uC774 \uD50C\uB7EC\uADF8\uC778\uC740 \uC124\uC815\uC744 \uD3B8\uC9D1\uD560 \uC218 \uC788\uB294 \uBC29\uBC95\uC744 \uAC1C\uBC1C \uC911\uC785\uB2C8\uB2E4.","content-manager.containers.Home.pluginHeaderDescription":"\uC27D\uACE0 \uAC15\uB825\uD55C UI\uB97C \uD1B5\uD574 \uD56D\uBAA9\uB4E4\uC744 \uAD00\uB9AC \uD558\uC138\uC694.","content-manager.containers.Home.pluginHeaderTitle":"\uCF58\uD150\uCE20 \uAD00\uB9AC","content-manager.containers.List.draft":"\uCD08\uC548","content-manager.containers.List.errorFetchRecords":"\uC5D0\uB7EC","content-manager.containers.List.published":"\uBC1C\uD589\uB428","content-manager.containers.ListPage.displayedFields":"\uD45C\uC2DC \uD544\uB4DC","content-manager.containers.ListPage.items":"{number, plural, =0 {items} one {item} other {items}}","content-manager.containers.ListPage.table-headers.publishedAt":"\uC0C1\uD0DC","content-manager.containers.ListSettingsView.modal-form.edit-label":"{fieldName} \uC218\uC815","content-manager.containers.SettingPage.add.field":"\uB2E4\uB978 \uD544\uB4DC \uCD94\uAC00","content-manager.containers.SettingPage.attributes":"\uC18D\uC131","content-manager.containers.SettingPage.attributes.description":"\uC18D\uC131\uC758 \uC21C\uC11C\uB97C \uC9C0\uC815\uD569\uB2C8\uB2E4","content-manager.containers.SettingPage.editSettings.description":"\uB808\uC774\uC544\uC6C3\uC744 \uAD6C\uC131\uD558\uB824\uBA74 \uD544\uB4DC\uB97C \uB4DC\uB798\uADF8 & \uB4DC\uB86D\uD558\uC138\uC694.","content-manager.containers.SettingPage.editSettings.entry.title":"\uD56D\uBAA9 \uC81C\uBAA9","content-manager.containers.SettingPage.editSettings.entry.title.description":"\uC81C\uBAA9\uC73C\uB85C \uBCF4\uC5EC\uC904 \uD544\uB4DC\uB97C \uC120\uD0DD\uD558\uC138\uC694.","content-manager.containers.SettingPage.editSettings.relation-field.description":"\uD3B8\uC9D1 \uBC0F \uBAA9\uB85D \uBCF4\uAE30 \uD654\uBA74\uC5D0 \uBAA8\uB450 \uD45C\uC2DC\uB418\uB294 \uD544\uB4DC\uB97C \uC124\uC815\uD569\uB2C8\uB2E4.","content-manager.containers.SettingPage.editSettings.title":"\uD654\uBA74 \uC218\uC815 (\uC124\uC815)","content-manager.containers.SettingPage.layout":"\uB808\uC774\uC544\uC6C3","content-manager.containers.SettingPage.listSettings.description":"\uC774 \uCEEC\uB809\uC158 \uD0C0\uC785\uC5D0 \uB300\uD55C \uC635\uC158\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.","content-manager.containers.SettingPage.listSettings.title":"\uBAA9\uB85D (\uC124\uC815)","content-manager.containers.SettingPage.pluginHeaderDescription":"\uC774 \uCEEC\uB809\uC158 \uD0C0\uC785\uC5D0 \uB300\uD55C \uD2B9\uC815 \uC124\uC815\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.","content-manager.containers.SettingPage.settings":"\uC124\uC815","content-manager.containers.SettingPage.view":"\uBCF4\uAE30","content-manager.containers.SettingViewModel.pluginHeader.title":"\uCF58\uD150\uCE20 \uB9E4\uB2C8\uC800 - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"\uD2B9\uC815 \uC124\uC815\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.","content-manager.containers.SettingsPage.Block.contentType.title":"\uCF5C\uB809\uC158 \uD0C0\uC785","content-manager.containers.SettingsPage.Block.generalSettings.description":"\uCF5C\uB809\uC158 \uD0C0\uC785\uC5D0 \uB300\uD55C \uAE30\uBCF8 \uC635\uC158\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.","content-manager.containers.SettingsPage.Block.generalSettings.title":"\uC77C\uBC18","content-manager.containers.SettingsPage.pluginHeaderDescription":"\uBAA8\uB4E0 \uCF58\uD150\uCE20 \uD0C0\uC785 \uBC0F \uADF8\uB8F9\uC5D0 \uB300\uD55C \uC124\uC815\uC744 \uAD6C\uC131\uD569\uB2C8\uB2E4.","content-manager.containers.SettingsView.list.subtitle":"\uCF58\uD150\uCE20 \uD0C0\uC785 \uBC0F \uADF8\uB8F9\uC758 \uB808\uC774\uC544\uC6C3\uACFC \uD45C\uC2DC\uB97C \uAD6C\uC131\uD569\uB2C8\uB2E4.","content-manager.containers.SettingsView.list.title":"\uD45C\uC2DC \uC124\uC815","content-manager.edit-settings-view.link-to-ctb.components":"\uCEF4\uD3EC\uB10C\uD2B8 \uC218\uC815","content-manager.edit-settings-view.link-to-ctb.content-types":"\uCF58\uD150\uCE20 \uD0C0\uC785 \uC218\uC815","content-manager.emptyAttributes.button":"\uCF5C\uB809\uC158 \uD0C0\uC785 \uBE4C\uB354\uB85C \uC774\uB3D9","content-manager.emptyAttributes.description":"\uCF5C\uB809\uC158 \uD0C0\uC785\uC5D0 \uCCAB \uD544\uB4DC\uB97C \uCD94\uAC00\uD574\uBCF4\uC138\uC694.","content-manager.emptyAttributes.title":"\uC544\uC9C1 \uD544\uB4DC\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.error.attribute.key.taken":"\uC774\uBBF8 \uC0AC\uC6A9\uC911\uC778 \uD0A4\uC785\uB2C8\uB2E4.","content-manager.error.attribute.sameKeyAndName":"\uAC19\uC740 \uAC12\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.error.attribute.taken":"\uC774\uBBF8 \uC0AC\uC6A9\uC911\uC778 \uC774\uB984\uC785\uB2C8\uB2E4.","content-manager.error.contentTypeName.taken":"\uC774\uBBF8 \uC0AC\uC6A9\uC911\uC778 \uC774\uB984\uC785\uB2C8\uB2E4.","content-manager.error.model.fetch":"\uBAA8\uB378 \uC124\uC815\uC744 \uAC00\uC838\uC624\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.error.record.create":"\uB370\uC774\uD130\uB97C \uC0DD\uC131\uD558\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.error.record.delete":"\uB370\uC774\uD130\uB97C \uC0AD\uC81C\uD558\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.error.record.fetch":"\uB370\uC774\uD130\uB97C \uAC00\uC838\uC624\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.error.record.update":"\uB370\uC774\uD130\uB97C \uC5C5\uB370\uC774\uD2B8\uD558\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.error.records.count":"\uB370\uC774\uD130 \uC218\uB97C \uAC00\uC838\uC624\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.error.records.fetch":"\uB370\uC774\uD130\uB97C \uAC00\uC838\uC624\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.error.schema.generation":"\uC2A4\uD0A4\uB9C8\uB97C \uC0DD\uC131\uD558\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.error.validation.json":"JSON \uD615\uC2DD\uC774 \uC544\uB2D9\uB2C8\uB2E4.","content-manager.error.validation.max":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB108\uBB34 \uD07D\uB2C8\uB2E4.","content-manager.error.validation.maxLength":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB108\uBB34 \uAE41\uB2C8\uB2E4.","content-manager.error.validation.min":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB108\uBB34 \uC791\uC2B5\uB2C8\uB2E4.","content-manager.error.validation.minLength":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB108\uBB34 \uC9E7\uC2B5\uB2C8\uB2E4.","content-manager.error.validation.minSupMax":"\uC774 \uBCF4\uB2E4 \uB354 \uD074 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.error.validation.regex":"\uC785\uB825\uD55C \uB0B4\uC6A9\uC774 \uB9DE\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.","content-manager.error.validation.required":"\uB0B4\uC6A9\uC744 \uC785\uB825\uD574 \uC8FC\uC138\uC694.","content-manager.form.Input.bulkActions":"\uB300\uADDC\uBAA8 \uC561\uC158 \uD65C\uC131\uD654","content-manager.form.Input.defaultSort":"\uAE30\uBCF8 \uC815\uB82C \uC18D\uC131","content-manager.form.Input.description":"\uC124\uBA85","content-manager.form.Input.description.placeholder":"Display name in the profile","content-manager.form.Input.editable":"\uD544\uB4DC \uC218\uC815\uAC00\uB2A5 \uC5EC\uBD80","content-manager.form.Input.filters":"\uD544\uB354 \uD65C\uC131\uD654","content-manager.form.Input.label":"\uB77C\uBCA8","content-manager.form.Input.label.inputDescription":"\uC774 \uAC12\uC740 \uD14C\uC774\uBE14 \uBA38\uB9AC\uC5D0 \uD45C\uC2DC\uB41C \uB77C\uBCA8\uC744 \uB36E\uC5B4\uC50C\uC6C1\uB2C8\uB2E4.","content-manager.form.Input.pageEntries":"\uD398\uC774\uC9C0 \uB2F9 \uC694\uC18C","content-manager.form.Input.pageEntries.inputDescription":"\uCC38\uACE0: \uCF58\uD150\uCE20 \uD0C0\uC785 \uC124\uC815 \uD398\uC774\uC9C0\uC5D0\uC11C \uC774 \uAC12\uC744 \uC7AC\uC815\uC758(override)\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","content-manager.form.Input.placeholder":"Placeholder","content-manager.form.Input.placeholder.placeholder":"My awesome value","content-manager.form.Input.search":"\uAC80\uC0C9 \uD65C\uC131\uD654","content-manager.form.Input.search.field":"\uC774 \uD544\uB4DC\uC5D0 \uAC80\uC0C9 \uD65C\uC131\uD654","content-manager.form.Input.sort.field":"\uC774 \uD544\uB4DC\uC5D0 \uC815\uB82C \uD65C\uC131\uD654","content-manager.form.Input.sort.order":"\uAE30\uBCF8 \uC815\uB82C \uC21C\uC11C","content-manager.form.Input.wysiwyg":"WYSIWYG\uB85C \uBCF4\uAE30","content-manager.global.displayedFields":"\uD45C\uC2DC \uD544\uB4DC","content-manager.groups":"\uADF8\uB8F9","content-manager.groups.numbered":"\uADF8\uB8F9 ({number}\uAC1C)","content-manager.header.name":"\uCF58\uD150\uCE20","content-manager.link-to-ctb":"\uBAA8\uB378 \uC218\uC815","content-manager.models":"\uCF5C\uB809\uC158 \uD0C0\uC785","content-manager.models.numbered":"\uCF5C\uB809\uC158 \uD0C0\uC785 ({number})","content-manager.notification.error.displayedFields":"\uD45C\uC2DC\uB420 \uD544\uB4DC\uAC00 \uCD5C\uC18C \uD558\uB098 \uC774\uC0C1 \uD544\uC694\uD569\uB2C8\uB2E4.","content-manager.notification.error.relationship.fetch":"\uB370\uC774\uD130 \uAD00\uACC4\uB97C \uAC00\uC838\uC624\uB294 \uB3C4\uC911 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.notification.info.SettingPage.disableSort":"\uC815\uB82C\uC774 \uD65C\uC131\uD654\uB41C \uD55C \uAC1C\uC758 \uC18D\uC131\uC774 \uD544\uC694\uD569\uB2C8\uB2E4.","content-manager.notification.info.minimumFields":"\uD45C\uC2DC\uB420 \uD544\uB4DC\uAC00 \uCD5C\uC18C \uD558\uB098 \uC774\uC0C1 \uD544\uC694\uD569\uB2C8\uB2E4.","content-manager.notification.upload.error":"\uD30C\uC77C \uC5C5\uB85C\uB4DC \uC911\uC5D0 \uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","content-manager.pageNotFound":"\uD398\uC774\uC9C0\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.pages.ListView.header-subtitle":"{number, plural, =0 {# \uAC1C} one {# \uAC1C} other {# \uAC1C}} \uD56D\uBAA9\uC744 \uCC3E\uC558\uC2B5\uB2C8\uB2E4.","content-manager.pages.NoContentType.button":"\uCCAB \uCF58\uD150\uCE20 \uD0C0\uC785 \uC0DD\uC131\uD558\uAE30","content-manager.pages.NoContentType.text":"\uC544\uC9C1 \uCF58\uD150\uCE20\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4. \uCCAB \uCF58\uD150\uCE20 \uD0C0\uC785\uC744 \uC0DD\uC131\uD574\uBCF4\uC138\uC694.","content-manager.permissions.not-allowed.create":"\uBB38\uC11C\uB97C \uC0DD\uC131\uD560 \uC218 \uC788\uB294 \uAD8C\uD55C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.permissions.not-allowed.update":"\uC774 \uBB38\uC11C\uB97C \uBCFC \uC218 \uC788\uB294 \uAD8C\uD55C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","content-manager.plugin.description.long":"\uB370\uC774\uD130\uB97C \uC27D\uAC8C \uD655\uC778 \uD558\uACE0 \uC218\uC815, \uC0AD\uC81C \uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","content-manager.plugin.description.short":"\uB370\uC774\uD130\uB97C \uC27D\uAC8C \uD655\uC778 \uD558\uACE0 \uC218\uC815, \uC0AD\uC81C \uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","content-manager.popover.display-relations.label":"Display relations","content-manager.success.record.delete":"\uC0AD\uC81C","content-manager.success.record.publish":"\uBC1C\uD589\uB428","content-manager.success.record.save":"\uC800\uC7A5","content-manager.success.record.unpublish":"\uBC1C\uD589\uC774 \uCDE8\uC18C\uB428","content-manager.utils.data-loaded":"The {number, plural, =1 {\uAC1C} other {\uAC1C}}\uC758 \uD56D\uBAA9\uC744 \uBD88\uB7EC\uC654\uC2B5\uB2C8\uB2E4.","content-manager.popUpWarning.warning.publish-question":"\uC815\uB9D0 \uBC1C\uD589\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"\uB124, \uBC1C\uD589\uD569\uB2C8\uB2E4.","form.button.done":"\uD655\uC778","global.prompt.unsaved":"\uC774 \uD398\uC774\uC9C0\uB97C \uB5A0\uB098\uC2DC\uACA0\uC2B5\uB2C8\uAE4C? \uBAA8\uB4E0 \uBCC0\uACBD \uC0AC\uD56D\uC774 \uC5C6\uC5B4\uC9D1\uB2C8\uB2E4.","notification.contentType.relations.conflict":"\uCF58\uD150\uCE20 \uD0C0\uC785\uC5D0 \uCDA9\uB3CC\uD558\uB294 \uB9B4\uB808\uC774\uC158(conflict relation)\uC774 \uC788\uC2B5\uB2C8\uB2E4.","notification.default.title":"\uC815\uBCF4 \uC54C\uB9BC:","notification.error":"\uC5D0\uB7EC\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.","notification.error.layout":"\uB808\uC774\uC544\uC6C3\uC744 \uAC00\uC838\uC62C \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.","notification.form.error.fields":"\uC798\uBABB \uC785\uB825\uB41C \uD544\uB4DC\uAC00 \uC874\uC7AC\uD569\uB2C8\uB2E4.","notification.form.success.fields":"\uBCC0\uACBD \uC0AC\uD56D\uC774 \uC800\uC7A5\uB418\uC5C8\uC2B5\uB2C8\uB2E4.","notification.link-copied":"\uB9C1\uD06C\uAC00 \uD074\uB9BD\uBCF4\uB4DC\uC5D0 \uBCF5\uC0AC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.","notification.permission.not-allowed-read":"\uC774 \uBB38\uC11C\uB97C \uBCFC \uC218 \uC788\uB294 \uAD8C\uD55C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.","notification.success.delete":"\uD56D\uBAA9\uC774 \uC0AD\uC81C\uB418\uC5C8\uC2B5\uB2C8\uB2E4.","notification.success.saved":"\uC800\uC7A5\uB418\uC5C8\uC2B5\uB2C8\uB2E4.","notification.success.title":"\uC131\uACF5 \uC54C\uB9BC:","notification.version.update.message":"Strapi \uC0C8 \uBC84\uC804\uC774 \uCD9C\uC2DC\uB418\uC5C8\uC2B5\uB2C8\uB2E4!","notification.warning.title":"\uACBD\uACE0 \uC54C\uB9BC:",or:p,"request.error.model.unknown":"\uBAA8\uB378\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.",skipToContent:A,submit:l}}}]);
