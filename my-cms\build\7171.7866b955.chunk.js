"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[7171],{17171:(r,e,i)=>{i.r(e),i.d(e,{configurations:()=>a,default:()=>n,from:()=>t});const a="Kurulumlar",t="kimden",n={"ComponentIconPicker.search.placeholder":"Bir ikon ara","attribute.boolean":"Mant\u0131ksal","attribute.component":"Bile\u015Fen","attribute.component.description":"Tekrarlayabilece\u011Fin alanlar grubu","attribute.customField":"\xD6zel alan","attribute.date":"Tarih","attribute.dynamiczone":"Dinamik b\xF6lge","attribute.dynamiczone.description":"\u0130\xE7eri\u011Fi d\xFCzenlerken bile\u015Fenleri dinamik olarak se\xE7","attribute.email":"E-posta","attribute.enumeration":"Enumeration","attribute.json":"JSON","attribute.media":"Medya","attribute.null":" ","attribute.number":"Say\u0131","attribute.number.description":"Say\u0131lar (tamsay\u0131, kayan, ondal\u0131kl\u0131)","attribute.password":"Parola","attribute.relation":"\u0130li\u015Fki","attribute.richtext":"Zengin metin","attribute.richtext.description":"Bi\xE7imlendirme se\xE7enekleri olan bir zengin metin d\xFCzenleyici","attribute.text":"Yaz\u0131","attribute.time":"Zaman","attribute.timestamp":"Zaman damgas\u0131","attribute.uid":"UID","attribute.uid.description":"Benzersiz kimlik numaras\u0131","button.attributes.add.another":"Ba\u015Fka bir alan ekle","button.component.add":"Bir bile\u015Fen ekle","button.component.create":"Yeni bile\u015Fen olu\u015Ftur","button.model.create":"Yeni koleksiyon tipi olu\u015Ftur","button.single-types.create":"Yeni tekil tip olu\u015Ftur","component.repeatable":"(tekrarlayabilen)","components.SelectComponents.displayed-value":"{number} bile\u015Fen se\xE7ildi","components.componentSelect.no-component-available":"T\xFCm bile\u015Fenleri ekledin","components.componentSelect.no-component-available.with-search":"Araman\u0131zla e\u015Fle\u015Fen bile\u015Fen bulunamad\u0131","components.componentSelect.value-component":"{number} bile\u015Fen se\xE7ildi (aramak istedi\u011Fin bile\u015Feni yaz)","components.componentSelect.value-components":"{number} bile\u015Fen se\xE7ildi",configurations:a,"contentType.apiId-plural.description":"\xC7o\u011Ful API Kimli\u011Fi","contentType.apiId-plural.label":"API Kimli\u011Fi (\xC7o\u011Ful)","contentType.apiId-singular.description":"UID API yollar\u0131 ve veritaban\u0131 tablolar\u0131n\u0131 olu\u015Fturmak i\xE7in kullan\u0131l\u0131r","contentType.apiId-singular.label":"API Kimli\u011Fi (Tekil)","contentType.collectionName.description":"\u0130\xE7erik tipin ve tablo ad\u0131n farkl\u0131 oldu\u011Fu durumlarda kullan\u0131\u015Fl\u0131d\u0131r","contentType.collectionName.label":"Koleksiyon ad\u0131","contentType.displayName.label":"G\xF6r\xFCnt\xFClenme ad\u0131","contentType.draftAndPublish.description":"Yay\u0131nlamadan \xF6nce girdilerin taslak halini yaz","contentType.draftAndPublish.label":"Taslak/Yay\u0131n sistemi","contentType.kind.change.warning":"\u0130\xE7erik tipinin \xE7e\u015Fidini de\u011Fi\u015Ftirdiniz. API s\u0131f\u0131rlanacak (routes, controllers, services).","error.attributeName.reserved-name":"Bu isim, di\u011Fer \xF6zellikleri bozabilece\u011Finden, i\xE7erik tipi i\xE7in kullan\u0131lamaz","error.contentType.pluralName-used":"Bu de\u011Fer tekil olanla ayn\u0131 olamaz","error.contentType.singularName-used":"Bu de\u011Fer \xE7o\u011Ful olanla ayn\u0131 olamaz","error.contentTypeName.reserved-name":"Bu isim, di\u011Fer \xF6zellikleri bozabilece\u011Finden, projede kullan\u0131lamaz","error.validation.enum-duplicate":"M\xFCkerrer de\u011Ferlere izin verilmez (yaln\u0131zca alfan\xFCmerik karakterler hesaba kat\u0131lm\u0131\u015Ft\u0131r).","error.validation.enum-empty-string":"Kelime bo\u015F olamaz","error.validation.enum-regex":"En az bir de\u011Fer ge\xE7ersiz. De\u011Ferlerin i\xE7inde say\u0131lardan \xF6nce en az bir alfabetik karakter olmal\u0131d\u0131r.","error.validation.minSupMax":"\xDCst olamaz","error.validation.positive":"Pozitif say\u0131 olmal\u0131d\u0131r","error.validation.regex":"Regex ifadesi ge\xE7ersiz","error.validation.relation.targetAttribute-taken":"Bu isim hedefte yer al\u0131yor","form.attribute.component.option.add":"Bir bile\u015Fen ekle","form.attribute.component.option.create":"Yeni bir bile\u015Fen olu\u015Ftur","form.attribute.component.option.create.description":"Bile\u015Fen tipler ve bile\u015Fenler aras\u0131nda payla\u015F\u0131l\u0131r ve her yerden eri\u015Filebilir.","form.attribute.component.option.repeatable":"Tekrarlanabilir bile\u015Fen","form.attribute.component.option.repeatable.description":"\xC7ok kay\u0131tl\u0131 (diziler) i\xE7erikler, meta etiketleri, v.b. i\xE7in ideal","form.attribute.component.option.reuse-existing":"Mevcut bir bile\u015Feni kullan","form.attribute.component.option.reuse-existing.description":"Verilerini i\xE7erik tipleri aras\u0131nda tutarl\u0131 k\u0131lmak i\xE7in daha \xF6nceden olu\u015Fturulmu\u015F bir bile\u015Feni kullan.","form.attribute.component.option.single":"Tekil bile\u015Fen","form.attribute.component.option.single.description":"Gruplama alanlar\u0131 (adres, temel bilgiler, v.b. i\xE7in ideal","form.attribute.item.customColumnName":"\xD6zel kolon isimleri","form.attribute.item.customColumnName.description":"Bu veritaban\u0131 s\xFCtun isimleri servis yan\u0131tlar\u0131 i\xE7in daha kapsaml\u0131 bir bi\xE7imde yeniden adland\u0131rmak i\xE7in kullan\u0131\u015Fl\u0131d\u0131r","form.attribute.item.date.type.date":"tarih (\xF6r: 01/01/{currentYear})","form.attribute.item.date.type.datetime":"tarih saat (\xF6r: 01/01/{currentYear} 00:00)","form.attribute.item.date.type.time":"saat (\xF6r: 00:00)","form.attribute.item.defineRelation.fieldName":"Alan ad\u0131","form.attribute.item.enumeration.graphql":"GraphQL i\xE7in isim ge\xE7ersiz k\u0131l","form.attribute.item.enumeration.graphql.description":"GraphQL i\xE7in varsay\u0131lan olu\u015Fturulan ad\u0131 ge\xE7ersiz k\u0131lman\u0131za izin verir.","form.attribute.item.enumeration.placeholder":`\xD6rn:
sabah
\xF6\u011Flen
ak\u015Fam`,"form.attribute.item.enumeration.rules":"Values (one line per value)","form.attribute.item.maximum":"En y\xFCksek de\u011Fer","form.attribute.item.maximumLength":"En y\xFCksek uzunluk","form.attribute.item.minimum":"En d\xFC\u015F\xFCk de\u011Fer","form.attribute.item.minimumLength":"En d\xFC\u015F\xFCk uzunluk","form.attribute.item.number.type":"Say\u0131 bi\xE7imi","form.attribute.item.number.type.decimal":"ondal\u0131k (ex: 2.22)","form.attribute.item.number.type.float":"float (ex: 3.33333333)","form.attribute.item.number.type.integer":"tamsay\u0131 (ex: 10)","form.attribute.item.privateField":"Gizli alan","form.attribute.item.privateField.description":"Bu alan API yan\u0131t\u0131nda g\xF6r\xFCnmez","form.attribute.item.requiredField":"Zorunlu alan","form.attribute.item.requiredField.description":"Bu alan bo\u015Fsa kay\u0131t olu\u015Fturamazs\u0131n\u0131z","form.attribute.item.text.regex":"RegExp ifadesi","form.attribute.item.text.regex.description":"RegExp ifadesi","form.attribute.item.uniqueField":"Benzersiz alan","form.attribute.item.uniqueField.description":"Ayn\u0131 i\xE7eri\u011Fe sahip bir kay\u0131t varsa kay\u0131t olu\u015Fturamazs\u0131n\u0131z.","form.attribute.settings.default":"Varsay\u0131lan de\u011Fer","form.button.add-components-to-dynamiczone":"B\xF6lgeye bile\u015Fenleri ekle","form.button.add-field":"Ba\u015Fka bir alan ekle","form.button.add-first-field-to-created-component":"\u0130lk alan\u0131 bile\u015Fene ekle","form.button.add.field.to.collectionType":"Bu koleksiyon tipine ba\u015Fka bir alan ekle","form.button.add.field.to.component":"Bu bile\u015Fene ba\u015Fka bir alan ekle","form.button.add.field.to.contentType":"Bu i\xE7erik tipine ba\u015Fka bir alan ekle","form.button.add.field.to.singleType":"Bu tekil tipe ba\u015Fka bir alan ekle","form.button.cancel":"\u0130ptal","form.button.collection-type.description":"\xC7oklu kay\u0131tlar (makaleler, \xFCr\xFCnler, yorumlar, v.b.) i\xE7in ideal","form.button.collection-type.name":"Koleksiyon Tipi","form.button.configure-component":"Bile\u015Feni ayarla","form.button.configure-view":"G\xF6r\xFCn\xFCm\xFC ayarla","form.button.select-component":"Bir bile\u015Fen se\xE7","form.button.single-type.description":"Tekil kay\u0131tlar (hakk\u0131m\u0131zda, ana sayfa, v.b.) i\xE7in ideal","form.button.single-type.name":"Tekil Tip",from:t,"listView.headerLayout.description":"\u0130\xE7eri\u011Finin veri mimarisini kur","menu.section.components.name":"Bile\u015Fenler","menu.section.models.name":"Koleksiyon Tipleri","menu.section.single-types.name":"Tekil Tipler","modalForm.attribute.form.base.name.description":"Nitelik ad\u0131nda bo\u015Fluk olamaz","modalForm.attribute.form.base.name.placeholder":"\xF6r. slug, seoUrl, canonicalUrl","modalForm.attribute.target-field":"\u0130li\u015Ftirilmi\u015F alan","modalForm.attributes.select-component":"Bir bile\u015Fen se\xE7","modalForm.attributes.select-components":"Bile\u015Fenleri se\xE7","modalForm.collectionType.header-create":"Bir koleksiyon tipi olu\u015Ftur","modalForm.component.header-create":"Bir bile\u015Fen olu\u015Ftur","modalForm.components.create-component.category.label":"Kategori se\xE7 ya da yeni bir tane olu\u015Fturmak i\xE7in isim gir","modalForm.components.icon.label":"\u0130kon","modalForm.custom-fields.advanced.settings.extended":"Geli\u015Fmi\u015F ayarlar","modalForm.editCategory.base.name.description":"Kategori ad\u0131nda bo\u015Fluk olamaz","modalForm.empty.button":"\xD6zel alanlar ekle","modalForm.empty.heading":"Burada hen\xFCz bir \u015Fey yok.","modalForm.empty.sub-heading":"Geni\u015F yelpazedeki uzant\u0131lar ile arad\u0131\u011F\u0131n\u0131 bul.","modalForm.header-edit":"D\xFCzenle: {name}","modalForm.header.categories":"Kategoriler","modalForm.header.back":"Geri","modalForm.singleType.header-create":"Bir tekil tip olu\u015Ftur","modalForm.sub-header.addComponentToDynamicZone":"Dinamik b\xF6lgeye yeni bile\u015Fen ekle","modalForm.sub-header.attribute.create":"Yeni {type} alan\u0131 ekle","modalForm.sub-header.attribute.create.step":"Yeni bile\u015Fen ekle ({step}/2)","modalForm.sub-header.attribute.edit":"D\xFCzenle: {name}","modalForm.sub-header.chooseAttribute.collectionType":"Koleksiyon tipin i\xE7in bir alan se\xE7","modalForm.sub-header.chooseAttribute.component":"Bile\u015Fenin i\xE7in bir alan se\xE7","modalForm.sub-header.chooseAttribute.singleType":"Tekil tipin i\xE7in bir alan se\xE7","modalForm.tabs.custom":"\xD6zel","modalForm.tabs.custom.howToLink":"Nas\u0131l \xF6zel alan eklenir","modalForm.tabs.default":"Varsay\u0131lan","modalForm.tabs.label":"Varsay\u0131lan ve \xD6zel tip sekmeleri","modelPage.attribute.relationWith":"\u0130li\u015Fkili","notification.error.dynamiczone-min.validation":"Bir i\xE7erik tipini kaydetmek i\xE7in dinamik b\xF6lgede en az bir bile\u015Fen olmas\u0131 gereklidir","notification.info.autoreaload-disable":"Bu eklentinin kullan\u0131labilmesi i\xE7in otomatik yeniden y\xFCkleme (autoReload) \xF6zelli\u011Fi gereklidir. Sunucunu `strapi develop` ile ba\u015Flat","notification.info.creating.notSaved":"Yeni bir koleksiyon tipi ya da bile\u015Fen olu\u015Fturmadan \xF6nce yapt\u0131klar\u0131n\u0131 kaydet","plugin.description.long":"APInin veri yap\u0131s\u0131n\u0131 modelle. Sadece bir iki dakikada yeni alanlar ve ili\u015Fkiler olu\u015Ftur. Projendeki dosyalar otomatik olarak olu\u015Fturulur ve g\xFCncellenir.","plugin.description.short":"APInin veri yap\u0131s\u0131n\u0131 modelle.","plugin.name":"\u0130\xE7erik Tipi Kurucusu","popUpForm.navContainer.advanced":"Geli\u015Fmi\u015F Ayarlar","popUpForm.navContainer.base":"Temel ayarlar","popUpWarning.bodyMessage.cancel-modifications":"De\u011Fi\u015Fikliklerini iptal etmek istedi\u011Finden emin misin?","popUpWarning.bodyMessage.cancel-modifications.with-components":"De\u011Fi\u015Fikliklerini iptal etmek istedi\u011Finden emin misin? Baz\u0131 bile\u015Fenler olu\u015Fturuldu ya da de\u011Fi\u015Ftirildi.","popUpWarning.bodyMessage.category.delete":"Bu kategoriyi silmek istedi\u011Finden emin misin? T\xFCm bile\u015Fenler de silinecek.","popUpWarning.bodyMessage.component.delete":"Bu bile\u015Feni silmek istedi\u011Finden emin misin?","popUpWarning.bodyMessage.contentType.delete":"Bu \u0130\xE7erik T\xFCr\xFCn\xFC silmek istedi\u011Finizden emin misiniz?","popUpWarning.draft-publish.button.confirm":"Evet, devred\u0131\u015F\u0131 b\u0131rak","popUpWarning.draft-publish.message":"Taslak/Yay\u0131nla sistemini devred\u0131\u015F\u0131 b\u0131rak\u0131rsan taslaklar\u0131n silinecek.","popUpWarning.draft-publish.second-message":"Devred\u0131\u015F\u0131 b\u0131rakmak istedi\u011Finden emin misin?","prompt.unsaved":"\xC7\u0131kmak istedi\u011Finden emin misin? T\xFCm de\u011Fi\u015Fikliklerin kaybolacak.","relation.attributeName.placeholder":"\xD6rnek: yazar, katagori, etiket","relation.manyToMany":"bir\xE7ok ki\u015Fiye ait ve ait","relation.manyToOne":"Bir\xE7ok var","relation.manyWay":"\xE7ok y\xF6nl\xFC","relation.oneToMany":"Bir\xE7o\u011Funa ait","relation.oneToOne":"biri var","relation.oneWay":"tek y\xF6nl\xFC","table.button.no-fields":"Yeni alan ekle","table.content.create-first-content-type":"\u0130lk Koleksiyon-Tipini olu\u015Ftur","table.content.no-fields.collection-type":"Bu Koleksiyon-Tipine ile alan\u0131n\u0131 ekle","table.content.no-fields.component":"Bu bile\u015Fene ilk alan\u0131n\u0131 ekle"}}}]);
