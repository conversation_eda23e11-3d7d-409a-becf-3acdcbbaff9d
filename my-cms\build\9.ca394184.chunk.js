"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[9],{9:(J,I,t)=>{t.d(I,{ProtectedListPage:()=>Et});var s=t(92132),m=t(94061),U=t(76517),v=t(80782),u=t(90151),B=t(88353),R=t(4171),y=t(42455),c=t(74773),O=t(4198),K=t(55356),W=t(43064),A=t(38413),C=t(61485),j=t(99248),X=t(6239),p=t(83997),Z=t(25641),b=t(90361),V=t(33363),h=t(30893),_=t(55506),L=t(54894),f=t(43543),k=t(62482),x=t(21272),w=t(37679),q=t(60043),tt=t(62490),st=t(83925),S=t(33544),mt=t(15126),gt=t(63299),Mt=t(67014),Dt=t(59080),Pt=t(79275),Ot=t(14718),Lt=t(82437),ut=t(61535),ct=t(5790),At=t(12083),Ct=t(35223),pt=t(5409),ht=t(74930),ft=t(2600),Tt=t(48940),It=t(41286),Ut=t(56336),vt=t(13426),Bt=t(84624),Rt=t(77965),yt=t(54257),Kt=t(71210),Wt=t(51187),jt=t(39404),xt=t(58692),St=t(501),Ft=t(57646),Nt=t(23120),$t=t(44414),Vt=t(25962),zt=t(14664),Gt=t(42588),Qt=t(90325),Yt=t(62785),Ht=t(87443),Jt=t(41032),Xt=t(22957),Zt=t(93179),bt=t(73055),kt=t(15747),wt=t(85306),qt=t(26509),ts=t(32058),ss=t(81185),es=t(82261);const et=f.n.injectEndpoints({endpoints:e=>({getAuditLogs:e.query({query:i=>({url:"/admin/audit-logs",config:{params:i}})}),getAuditLog:e.query({query:i=>`/admin/audit-logs/${i}`})}),overrideExisting:!1}),{useGetAuditLogsQuery:at,useGetAuditLogQuery:nt}=et,z=()=>{const{formatDate:e}=(0,L.A)();return r=>{const a=(0,tt.A)(r),d=e(a,{dateStyle:"long"}),n=e(a,{timeStyle:"medium",hourCycle:"h24"});return`${d}, ${n}`}},G={"entry.create":"Create entry{model, select, undefined {} other { ({model})}}","entry.update":"Update entry{model, select, undefined {} other { ({model})}}","entry.delete":"Delete entry{model, select, undefined {} other { ({model})}}","entry.publish":"Publish entry{model, select, undefined {} other { ({model})}}","entry.unpublish":"Unpublish entry{model, select, undefined {} other { ({model})}}","media.create":"Create media","media.update":"Update media","media.delete":"Delete media","media-folder.create":"Create media folder","media-folder.update":"Update media folder","media-folder.delete":"Delete media folder","user.create":"Create user","user.update":"Update user","user.delete":"Delete user","admin.auth.success":"Admin login","admin.logout":"Admin logout","content-type.create":"Create content type","content-type.update":"Update content type","content-type.delete":"Delete content type","component.create":"Create component","component.update":"Update component","component.delete":"Delete component","role.create":"Create role","role.update":"Update role","role.delete":"Delete role","permission.create":"Create permission","permission.update":"Update permission","permission.delete":"Delete permission"},F=e=>G[e]||e,it=({handleClose:e,logId:i})=>{const r=(0,_.hN)(),{_unstableFormatAPIError:a}=(0,_.wq)(),{data:d,error:n,isLoading:o}=nt(i);x.useEffect(()=>{n&&(r({type:"warning",message:a(n)}),e())},[n,a,e,r]);const l=z(),E=d&&"date"in d?l(d.date):"";return(0,s.jsxs)(C.k,{onClose:e,labelledBy:"title",children:[(0,s.jsx)(j.r,{children:(0,s.jsx)(w.B,{label:E,id:"title",children:(0,s.jsx)(q.m,{isCurrent:!0,children:E})})}),(0,s.jsx)(X.c,{children:(0,s.jsx)(ot,{isLoading:o,data:d,formattedDate:E})})]})},ot=({isLoading:e,data:i,formattedDate:r})=>{const{formatMessage:a}=(0,L.A)();if(e)return(0,s.jsx)(p.s,{padding:7,justifyContent:"center",alignItems:"center",children:(0,s.jsx)(W.a,{children:"Loading content..."})});const{action:d,user:n,payload:o}=i;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.a,{marginBottom:3,children:(0,s.jsx)(h.o,{variant:"delta",id:"title",children:a({id:"Settings.permissions.auditLogs.details",defaultMessage:"Log Details"})})}),(0,s.jsxs)(u.x,{gap:4,gridCols:2,paddingTop:4,paddingBottom:4,paddingLeft:6,paddingRight:6,marginBottom:4,background:"neutral100",hasRadius:!0,children:[(0,s.jsx)(T,{actionLabel:a({id:"Settings.permissions.auditLogs.action",defaultMessage:"Action"}),actionName:a({id:`Settings.permissions.auditLogs.${d}`,defaultMessage:F(d)},{model:o?.model})}),(0,s.jsx)(T,{actionLabel:a({id:"Settings.permissions.auditLogs.date",defaultMessage:"Date"}),actionName:r}),(0,s.jsx)(T,{actionLabel:a({id:"Settings.permissions.auditLogs.user",defaultMessage:"User"}),actionName:n?.displayName||"-"}),(0,s.jsx)(T,{actionLabel:a({id:"Settings.permissions.auditLogs.userId",defaultMessage:"User ID"}),actionName:n?.id.toString()||"-"})]}),(0,s.jsx)(R.j,{value:JSON.stringify(o,null,2),disabled:!0,label:a({id:"Settings.permissions.auditLogs.payload",defaultMessage:"Payload"})})]})},T=({actionLabel:e,actionName:i})=>(0,s.jsxs)(p.s,{direction:"column",alignItems:"baseline",gap:1,children:[(0,s.jsx)(h.o,{textColor:"neutral600",variant:"sigma",children:e}),(0,s.jsx)(h.o,{textColor:"neutral600",children:i})]}),dt=({pagination:e}={pagination:{page:1,pageCount:0,pageSize:50,total:0}})=>(0,s.jsx)(m.a,{paddingTop:4,children:(0,s.jsxs)(p.s,{alignItems:"flex-end",justifyContent:"space-between",children:[(0,s.jsx)(_._u,{}),(0,s.jsx)(_.W7,{pagination:e})]})}),N=({headers:e,rows:i,onOpenModal:r})=>{const{formatMessage:a}=(0,L.A)(),d=z(),n=({type:o,value:l,model:E})=>o==="date"?d(l):o==="action"?a({id:`Settings.permissions.auditLogs.${l}`,defaultMessage:F(l)},{model:E}):l||"-";return(0,s.jsx)(Z.N,{children:i.map(o=>(0,s.jsxs)(b.Tr,{...(0,_.qM)({fn:()=>r(o.id)}),children:[e?.map(({key:l,name:E,cellFormatter:g})=>{const M=o[E];return(0,s.jsx)(V.Td,{children:(0,s.jsx)(h.o,{textColor:"neutral800",children:n({type:l,value:g?g(M):M,model:o.payload?.model})})},l)}),(0,s.jsx)(V.Td,{..._.dG,children:(0,s.jsx)(p.s,{justifyContent:"end",children:(0,s.jsx)(B.K,{onClick:()=>r(o.id),"aria-label":a({id:"app.component.table.view",defaultMessage:"{target} details"},{target:`${o.action} action`}),noBorder:!0,icon:(0,s.jsx)(st.A,{})})})})]},o.id))})};N.defaultProps={rows:[]},N.propTypes={headers:S.array.isRequired,rows:S.array,onOpenModal:S.func.isRequired};const rt=({canReadAuditLogs:e,canReadUsers:i})=>{const r=(0,_.hN)(),{_unstableFormatAPIError:a}=(0,_.wq)(),[{query:d}]=(0,_.sq)(),{data:n,error:o,isError:l,isLoading:E}=(0,f.k)({},{skip:!i,refetchOnMountOrArgChange:!0});x.useEffect(()=>{o&&r({type:"warning",message:a(o)})},[o,r,a]);const{data:g,isLoading:M,isError:$,error:D}=at(d,{refetchOnMountOrArgChange:!0,skip:!e});return x.useEffect(()=>{D&&r({type:"warning",message:a(D)})},[D,r,a]),{auditLogs:g,users:n?.users??[],isLoading:E||M,hasError:$||l}},Q=({value:e,options:i,onChange:r}={value:void 0})=>{const{formatMessage:a}=(0,L.A)(),d=a({id:"Settings.permissions.auditLogs.filter.aria-label",defaultMessage:"Search and select an option to filter"});return(0,s.jsx)(U.G3,{"aria-label":d,value:e,onChange:r,children:i?.map(({label:n,customValue:o})=>(0,s.jsx)(v.j,{value:o,children:n},o))})},Y=[{intlLabel:{id:"components.FilterOptions.FILTER_TYPES.$eq",defaultMessage:"is"},value:"$eq"},{intlLabel:{id:"components.FilterOptions.FILTER_TYPES.$ne",defaultMessage:"is not"},value:"$ne"}],_t=({formatMessage:e,users:i,canReadUsers:r})=>{const a=[{name:"action",metadatas:{customOperators:Y,label:e({id:"Settings.permissions.auditLogs.action",defaultMessage:"Action"}),customInput:Q,options:Object.keys(G).map(d=>({label:e({id:`Settings.permissions.auditLogs.${d}`,defaultMessage:F(d)},{model:void 0}),customValue:d}))},fieldSchema:{type:"enumeration"}},{name:"date",metadatas:{label:e({id:"Settings.permissions.auditLogs.date",defaultMessage:"Date"})},fieldSchema:{type:"datetime"}}];if(r&&i){const d=n=>n.username?n.username:n.firstname&&n.lastname?e({id:"Settings.permissions.auditLogs.user.fullname",defaultMessage:"{firstname} {lastname}"},{firstname:n.firstname,lastname:n.lastname}):n.email;return[...a,{name:"user",metadatas:{customOperators:Y,label:e({id:"Settings.permissions.auditLogs.user",defaultMessage:"User"}),options:i.map(n=>({label:d(n),customValue:n.id.toString()})),customInput:Q},fieldSchema:{type:"relation",mainField:{name:"id"}}}]}return a},lt=()=>{const{formatMessage:e}=(0,L.A)(),i=(0,f.j)(P=>P.admin_app.permissions.settings),{allowedActions:{canRead:r,canReadUsers:a},isLoading:d}=(0,_.ec)({...i?.auditLogs,readUsers:i?.users.read||[]}),[{query:n},o]=(0,_.sq)(),{auditLogs:l,users:E,isLoading:g,hasError:M}=rt({canReadAuditLogs:r,canReadUsers:a});(0,_.L4)();const $=_t({formatMessage:e,users:E,canReadUsers:a}),D=[{name:"action",key:"action",metadatas:{label:e({id:"Settings.permissions.auditLogs.action",defaultMessage:"Action"}),sortable:!0}},{name:"date",key:"date",metadatas:{label:e({id:"Settings.permissions.auditLogs.date",defaultMessage:"Date"}),sortable:!0}},{key:"user",name:"user",metadatas:{label:e({id:"Settings.permissions.auditLogs.user",defaultMessage:"User"}),sortable:!1},cellFormatter:P=>P?P.displayName:""}];if(M)return(0,s.jsx)(y.P,{children:(0,s.jsx)(O.s,{children:(0,s.jsx)(m.a,{paddingTop:8,children:(0,s.jsx)(_.hH,{})})})});const H=g||d;return(0,s.jsxs)(A.g,{"aria-busy":H,children:[(0,s.jsx)(_.x7,{name:e({id:"global.auditLogs",defaultMessage:"Audit Logs"})}),(0,s.jsx)(K.Q,{title:e({id:"global.auditLogs",defaultMessage:"Audit Logs"}),subtitle:e({id:"Settings.permissions.auditLogs.listview.header.subtitle",defaultMessage:"Logs of all the activities that happened in your environment"})}),(0,s.jsx)(c.B,{startActions:(0,s.jsx)(k.F,{displayedFilters:$})}),(0,s.jsxs)(O.s,{children:[(0,s.jsx)(_.Ee,{contentType:"Audit logs",headers:D,rows:l?.results||[],withBulkActions:!0,isLoading:H,children:(0,s.jsx)(N,{headers:D,rows:l?.results||[],onOpenModal:P=>o({id:`${P}`})})}),l?.pagination&&(0,s.jsx)(dt,{pagination:l.pagination})]}),n?.id&&(0,s.jsx)(it,{handleClose:()=>o({id:null},"remove"),logId:n.id})]})},Et=()=>{const e=(0,f.j)(i=>i.admin_app.permissions.settings?.auditLogs?.main);return(0,s.jsx)(_.kz,{permissions:e,children:(0,s.jsx)(lt,{})})}},62482:(J,I,t)=>{t.d(I,{F:()=>y});var s=t(92132),m=t(21272),U=t(94061),v=t(85963),u=t(55506),B=t(28604),R=t(54894);const y=({displayedFilters:c})=>{const[O,K]=m.useState(!1),{formatMessage:W}=(0,R.A)(),A=m.useRef(null),C=()=>{K(j=>!j)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(U.a,{paddingTop:1,paddingBottom:1,children:[(0,s.jsx)(v.$,{variant:"tertiary",ref:A,startIcon:(0,s.jsx)(B.A,{}),onClick:C,size:"S",children:W({id:"app.utils.filters",defaultMessage:"Filters"})}),O&&(0,s.jsx)(u.LC,{displayedFilters:c,isVisible:O,onToggle:C,source:A})]}),(0,s.jsx)(u.cZ,{filtersSchema:c})]})}}}]);
