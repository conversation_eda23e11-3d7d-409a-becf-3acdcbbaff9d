"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[1888],{91888:(n,e,t)=>{t.r(e),t.d(e,{default:()=>o,from:()=>r});const r="de",o={"attribute.boolean":"Bool\xE9en","attribute.date":"Date","attribute.email":"Email","attribute.enumeration":"\xC9num\xE9ration","attribute.json":"JSON","attribute.media":"M\xE9dia","attribute.password":"Mot de passe","attribute.relation":"Relation","attribute.richtext":"Texte enrichi","attribute.text":"Texte","button.attributes.add.another":"Ajouter un autre champ","button.component.create":"Cr\xE9er un composant","button.model.create":"Cr\xE9er un type de collection","button.single-types.create":"Cr\xE9er un single type","contentType.draftAndPublish.description":"R\xE9digez une version brouillon de chaque entr\xE9e avant de la publier","contentType.draftAndPublish.label":"Syst\xE8me brouillon/publier","contentType.kind.change.warning":"Vous venez de changer le type de ce mod\xE8le: L'API va red\xE9marrer (Les routes, controllers, et les services seront \xE9cras\xE9s).","form.attribute.item.customColumnName":"Nom de colonne personalis\xE9e","form.attribute.item.customColumnName.description":"Pratique pour renommer la colonne de la db dans un format plus comprehensible pour les responses de l'API","form.attribute.item.defineRelation.fieldName":"Nom du Champ","form.attribute.item.enumeration.graphql":"Surchage du nom pour GraphQL","form.attribute.item.enumeration.graphql.description":"Vous permet de remplacer le nom g\xE9n\xE9r\xE9 par d\xE9faut pour GraphQL","form.attribute.item.enumeration.placeholder":`Ex:
matin
midi
soir`,"form.attribute.item.enumeration.rules":"Valeurs (les s\xE9parer par une nouvelle ligne)","form.attribute.item.maximum":"Valeur maximum","form.attribute.item.maximumLength":"Taille maximum","form.attribute.item.minimum":"Valeur minimun","form.attribute.item.minimumLength":"Taille minimun","form.attribute.item.number.type":"Format nombre","form.attribute.item.number.type.decimal":"d\xE9cimal approximatif (ex: 2,22)","form.attribute.item.number.type.float":"d\xE9cimal (ex: 3,33333)","form.attribute.item.number.type.integer":"entier (ex: 10)","form.attribute.item.requiredField":"Champ obligatoire","form.attribute.item.requiredField.description":"Vous ne pourrez pas cr\xE9er une entr\xE9e si ce champ est vide","form.attribute.item.uniqueField":"Champ unique","form.attribute.item.uniqueField.description":"Vous ne pourrez pas cr\xE9er une entr\xE9e s'il existe un champ similaire","form.attribute.settings.default":"Valeur par d\xE9fault","form.button.add.field.to.collectionType":"Ajouter un nouveau champ \xE0 cette collection","form.button.add.field.to.component":"Ajouter un nouveau champ \xE0 ce composant","form.button.add.field.to.contentType":"Ajouter un nouveau champ \xE0 cette content type","form.button.add.field.to.singleType":"Ajouter un nouveau champ \xE0 ce single type","form.button.add-field":"Ajouter un autre champ","form.button.cancel":"Annuler","form.button.configure-view":"Configurer la vue",from:r,"modalForm.attribute.form.base.name.placeholder":"ex : slug, urlSeo, urlCanonique","modalForm.attribute.target-field":"Champ associ\xE9","modalForm.header.back":"Dos","modalForm.singleType.header-create":"Cr\xE9er un single type","modalForm.sub-header.chooseAttribute.collectionType":"Selectionnez un champ pour votre collection","modalForm.sub-header.chooseAttribute.component":"Selectionnez un champ pour votre composant","modalForm.sub-header.chooseAttribute.singleType":"Selectionnez un champ pour votre single type","modelPage.attribute.relationWith":"Relation avec","plugin.description.long":"Mod\xE9lisez la structure de donn\xE9es de votre API. Cr\xE9er des nouveaux champs et relations en un instant. Les fichiers se cr\xE9ent et se mettent \xE0 jour automatiquement.","plugin.description.short":"Mod\xE9lisez la structure de donn\xE9es de votre API.","plugin.name":"Content-Type Builder","popUpForm.navContainer.advanced":"R\xE9glages avanc\xE9s","popUpForm.navContainer.base":"R\xE9glages de base","popUpWarning.bodyMessage.contentType.delete":"\xCAtes-vous s\xFBr de vouloir supprimer cette Collection ? Cela le supprimera aussi de vos types de contenu.","popUpWarning.draft-publish.button.confirm":"Oui, d\xE9sactiver","popUpWarning.draft-publish.message":"Si vous d\xE9sactivez le syst\xE8me Brouillon/Publier, vos brouillons seront supprim\xE9s.","popUpWarning.draft-publish.second-message":"\xCAtes-vous s\xFBr de vouloir le d\xE9sactiver ?","relation.attributeName.placeholder":"Ex : auteur, cat\xE9gorie, tag","relation.manyToMany":"a et appartient \xE0 plusieurs","relation.manyToOne":"a plusieurs","relation.manyWay":"a plusieurs","relation.oneToMany":"appartient \xE0 plusieurs","relation.oneToOne":"a et appartient \xE0 un","relation.oneWay":"a un"}}}]);
