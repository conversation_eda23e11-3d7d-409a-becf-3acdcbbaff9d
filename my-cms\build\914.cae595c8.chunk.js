"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[914,3292],{13292:(F,R,s)=>{s.d(R,{ProtectedEditView:()=>Ks,w:()=>$});var _=s(92132),D=s(21272),B=s(94061),W=s(85963),f=s(90151),l=s(68074),U=s(4198),O=s(55356),t=s(38413),M=s(83997),d=s(30893),a=s(55506),z=s(54514),N=s(61535),y=s(54894),j=s(17703),v=s(12083),h=s(43543),x=s(34819),A=s(99831),T=s(81590),H=s(15126),X=s(63299),Z=s(67014),w=s(59080),b=s(79275),q=s(14718),ss=s(82437),_s=s(5790),ts=s(35223),ns=s(5409),as=s(74930),Es=s(2600),os=s(48940),rs=s(41286),es=s(56336),ds=s(13426),Ms=s(84624),Ps=s(77965),is=s(54257),Ds=s(71210),Os=s(51187),ls=s(39404),Ts=s(58692),ms=s(501),vs=s(57646),As=s(23120),Cs=s(44414),Ls=s(25962),Is=s(14664),Rs=s(42588),Bs=s(90325),Ws=s(62785),fs=s(87443),Us=s(41032),G=s(22957),Q=s(93179),zs=s(73055),Ns=s(15747),Gs=s(85306),Qs=s(26509),$s=s(32058),Ys=s(81185),Js=s(82261);const hs=v.Ik().shape({name:v.Yj().max(100).required(a.iW.required),description:v.Yj().nullable(),lifespan:v.ai().integer().min(0).nullable().defined(a.iW.required),permissions:v.Yj().required(a.iW.required)}),$=()=>{(0,a.L4)();const{formatMessage:o}=(0,y.A)(),{lockApp:r,unlockApp:K}=(0,a.MA)(),P=(0,a.hN)(),C=(0,j.W6)(),{state:g}=(0,j.zy)(),[E,L]=D.useState(g&&"accessKey"in g.transferToken?{...g.transferToken}:null),{trackUsage:m}=(0,a.z1)(),{setCurrentStep:ys}=(0,a.Cx)(),js=(0,h.j)(e=>e.admin_app.permissions.settings?.["transfer-tokens"]),{allowedActions:{canCreate:xs,canUpdate:ps,canRegenerate:us}}=(0,a.ec)(js),p=(0,j.W5)("/settings/transfer-tokens/:id")?.params?.id,i=p==="create",{_unstableFormatAPIError:u,_unstableFormatValidationErrors:Y}=(0,a.wq)();D.useEffect(()=>{m(i?"didAddTokenFromList":"didEditTokenFromList",{tokenType:A.T})},[i,m]);const{data:S,error:V}=(0,x.u)(p,{skip:i||E!==null||!p});D.useEffect(()=>{V&&P({type:"warning",message:u(V)})},[V,u,P]),D.useEffect(()=>{S&&L(S)},[S]);const[Ss]=(0,x.a)(),[Vs]=(0,x.b)(),Fs=async(e,I)=>{m(i?"willCreateToken":"willEditToken",{tokenType:A.T}),r();const c=e.permissions.split("-");if((n=>n.length===1?n[0]==="push"||n[0]==="pull":n[0]==="push"&&n[1]==="pull")(c))try{if(i){const n=await Ss({...e,lifespan:e?.lifespan||null,permissions:c});if("error"in n){(0,h.x)(n.error)&&n.error.name==="ValidationError"?I.setErrors(Y(n.error)):P({type:"warning",message:u(n.error)});return}L(n.data),P({type:"success",message:o({id:"notification.success.transfertokencreated",defaultMessage:"Transfer Token successfully created"})}),m("didCreateToken",{type:E?.permissions,tokenType:A.T}),C.push(`/settings/transfer-tokens/${n.data.id}`,{transferToken:n.data}),ys("transferTokens.success")}else{const n=await Vs({id:p,name:e.name,description:e.description,permissions:c});if("error"in n){(0,h.x)(n.error)&&n.error.name==="ValidationError"?I.setErrors(Y(n.error)):P({type:"warning",message:u(n.error)});return}L(n.data),P({type:"success",message:o({id:"notification.success.transfertokenedited",defaultMessage:"Transfer Token successfully edited"})}),m("didEditToken",{type:E?.permissions,tokenType:A.T})}}catch{P({type:"warning",message:{id:"notification.error",defaultMessage:"Something went wrong"}})}finally{K()}},J=ps&&!i||xs&&i;return!i&&!E?(0,_.jsx)(cs,{}):(0,_.jsxs)(t.g,{children:[(0,_.jsx)(a.x7,{name:"Transfer Tokens"}),(0,_.jsx)(N.l1,{validationSchema:hs,validateOnChange:!1,initialValues:{name:E?.name||"",description:E?.description||"",lifespan:E?.lifespan||null,permissions:E?.permissions.join("-")??""},enableReinitialize:!0,onSubmit:(e,I)=>Fs(e,I),children:({errors:e,handleChange:I,isSubmitting:c,values:k})=>(0,_.jsxs)(a.lV,{children:[(0,_.jsx)(T.F,{backUrl:"/settings/transfer-tokens",title:{id:"Settings.transferTokens.createPage.title",defaultMessage:"TokenCreate Transfer Token"},token:E,setToken:L,canEditInputs:J,canRegenerate:us,isSubmitting:c,regenerateUrl:"/admin/transfer/tokens/"}),(0,_.jsx)(U.s,{children:(0,_.jsxs)(M.s,{direction:"column",alignItems:"stretch",gap:6,children:[E&&Boolean(E?.name)&&"accessKey"in E&&(0,_.jsx)(T.c,{token:E.accessKey,tokenType:A.T}),(0,_.jsx)(gs,{errors:e,onChange:I,canEditInputs:J,isCreating:i,values:k,transferToken:E})]})})]})})]})},Ks=()=>{const o=(0,h.j)(r=>r.admin_app.permissions.settings?.["transfer-tokens"].read);return(0,_.jsx)(a.kz,{permissions:o,children:(0,_.jsx)($,{})})},gs=({errors:o={},onChange:r,canEditInputs:K,isCreating:P,values:C,transferToken:g={}})=>{const{formatMessage:E}=(0,y.A)(),L=[{value:"push",label:{id:"Settings.transferTokens.types.push",defaultMessage:"Push"}},{value:"pull",label:{id:"Settings.transferTokens.types.pull",defaultMessage:"Pull"}},{value:"push-pull",label:{id:"Settings.transferTokens.types.push-pull",defaultMessage:"Full Access"}}];return(0,_.jsx)(B.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,_.jsxs)(M.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,_.jsx)(d.o,{variant:"delta",as:"h2",children:E({id:"global.details",defaultMessage:"Details"})}),(0,_.jsxs)(f.x,{gap:5,children:[(0,_.jsx)(l.E,{col:6,xs:12,children:(0,_.jsx)(T.T,{error:o.name,value:C.name,canEditInputs:K,onChange:r})},"name"),(0,_.jsx)(l.E,{col:6,xs:12,children:(0,_.jsx)(T.a,{error:o.description,value:C.description,canEditInputs:K,onChange:r})},"description"),(0,_.jsx)(l.E,{col:6,xs:12,children:(0,_.jsx)(T.L,{isCreating:P,error:o.lifespan,value:C.lifespan,onChange:r,token:g})},"lifespan"),(0,_.jsx)(l.E,{col:6,xs:12,children:(0,_.jsx)(T.b,{name:"permissions",value:C.permissions,error:o.permissions,label:{id:"Settings.tokens.form.type",defaultMessage:"Token type"},onChange:m=>{r({target:{name:"permissions",value:m}})},options:L,canEditInputs:K})},"permissions")]})]})})},cs=({transferTokenName:o})=>{const{formatMessage:r}=(0,y.A)();return(0,a.L4)(),(0,_.jsxs)(t.g,{"aria-busy":"true",children:[(0,_.jsx)(a.x7,{name:"Transfer Tokens"}),(0,_.jsx)(O.Q,{primaryAction:(0,_.jsx)(W.$,{disabled:!0,startIcon:(0,_.jsx)(z.A,{}),type:"button",size:"L",children:r({id:"global.save",defaultMessage:"Save"})}),title:o||r({id:"Settings.transferTokens.createPage.title",defaultMessage:"Create Transfer Token"})}),(0,_.jsx)(U.s,{children:(0,_.jsx)(a.Bl,{})})]})}},34819:(F,R,s)=>{s.d(R,{a:()=>f,b:()=>U,c:()=>B,d:()=>l,u:()=>W});var _=s(43543);const D=_.n.injectEndpoints({endpoints:O=>({getTransferTokens:O.query({query:()=>({url:"/admin/transfer/tokens",method:"GET"}),transformResponse:t=>t.data,providesTags:(t,M)=>[...t?.map(({id:d})=>({type:"TransferToken",id:d}))??[],{type:"TransferToken",id:"LIST"}]}),getTransferToken:O.query({query:t=>`/admin/transfer/tokens/${t}`,transformResponse:t=>t.data,providesTags:(t,M,d)=>[{type:"TransferToken",id:d}]}),createTransferToken:O.mutation({query:t=>({url:"/admin/transfer/tokens",method:"POST",data:t}),transformResponse:t=>t.data,invalidatesTags:[{type:"TransferToken",id:"LIST"}]}),deleteTransferToken:O.mutation({query:t=>({url:`/admin/transfer/tokens/${t}`,method:"DELETE"}),transformResponse:t=>t.data,invalidatesTags:(t,M,d)=>[{type:"TransferToken",id:d}]}),updateTransferToken:O.mutation({query:({id:t,...M})=>({url:`/admin/transfer/tokens/${t}`,method:"PUT",data:M}),transformResponse:t=>t.data,invalidatesTags:(t,M,{id:d})=>[{type:"TransferToken",id:d}]})}),overrideExisting:!1}),{useGetTransferTokensQuery:B,useGetTransferTokenQuery:W,useCreateTransferTokenMutation:f,useDeleteTransferTokenMutation:l,useUpdateTransferTokenMutation:U}=D},90914:(F,R,s)=>{s.d(R,{ProtectedCreateView:()=>G});var _=s(92132),D=s(55506),B=s(82437),W=s(66248),f=s(13292),l=s(21272),U=s(55151),O=s(79077),t=s(43543),M=s(15126),d=s(63299),a=s(67014),z=s(59080),N=s(79275),y=s(14718),j=s(61535),v=s(5790),h=s(12083),x=s(35223),A=s(5409),T=s(74930),H=s(2600),X=s(48940),Z=s(41286),w=s(56336),b=s(13426),q=s(84624),ss=s(77965),_s=s(54257),ts=s(71210),ns=s(51187),as=s(39404),Es=s(58692),os=s(501),rs=s(57646),es=s(23120),ds=s(44414),Ms=s(25962),Ps=s(14664),is=s(42588),Ds=s(90325),Os=s(62785),ls=s(87443),Ts=s(41032),ms=s(22957),vs=s(93179),As=s(73055),Cs=s(15747),Ls=s(85306),Is=s(26509),Rs=s(32058),Bs=s(81185),Ws=s(82261),fs=s(34819),Us=s(81590);const G=()=>{const Q=(0,B.d4)(W.s);return(0,_.jsx)(D.kz,{permissions:Q.settings?.["transfer-tokens"].create,children:(0,_.jsx)(f.w,{})})}}}]);
