"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[4407],{54407:(a,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i={"BoundRoute.title":"Spojit adresu s","EditForm.inputSelect.description.role":"P\u0159ipoj\xED nov\u011B autentifikovan\xE9ho u\u017Eivatele ke svolen\xE9 roli.","EditForm.inputSelect.label.role":"V\xFDchoz\xED role pro autentifikovan\xE9ho u\u017Eivatele","EditForm.inputToggle.description.email":"Zabr\xE1nit u\u017Eivateli vytv\xE1\u0159et r\u016Fzn\xE9 \xFA\u010Dty se stejn\xFDm e-mailem a jin\xFDmi poskytovateli autentifikace.","EditForm.inputToggle.description.email-confirmation":"<PERSON>kud je tato funk<PERSON> povolena (ON), nov\u011B registrovan\xED u\u017Eivatel\xE9 dostanou potvrzuj\xEDc\xED e-mail.","EditForm.inputToggle.description.email-confirmation-redirection":"Po potvrzen\xED e-mailu, zvolte kam budete p\u0159esm\u011Brov\xE1ni.","EditForm.inputToggle.description.email-reset-password":"Adresa str\xE1nky obnoven\xED hesla va\u0161\xED aplikace","EditForm.inputToggle.description.sign-up":"Pokud je tato mo\u017Enost zak\xE1z\xE1na (OFF), nen\xED mo\u017Eno proj\xEDt registrac\xED. Nikdo se ji\u017E nem\u016F\u017Ee p\u0159ipojit, bez ohledu jak\xE9ho pou\u017Eije poskytovatele.","EditForm.inputToggle.label.email":"Jeden \xFA\u010Det na e-mail","EditForm.inputToggle.label.email-confirmation":"Povolit potvrzen\xED z e-mailu","EditForm.inputToggle.label.email-confirmation-redirection":"Adresa pro p\u0159esm\u011Brov\xE1n\xED","EditForm.inputToggle.label.email-reset-password":"Str\xE1nka pro obnoven\xED hesla","EditForm.inputToggle.label.sign-up":"Povolit registrace","HeaderNav.link.advancedSettings":"Pokro\u010Dil\xE1 nastaven\xED","HeaderNav.link.emailTemplates":"E-mailov\xE9 \u0161ablony","HeaderNav.link.providers":"Poskytovatel\xE9","Plugin.permissions.plugins.description":"Nastavit v\u0161echny akce pro z\xE1suvn\xFD modul {name}.","Plugins.header.description":"Pouze akce spojen\xE9 s adresou jsou vyps\xE1ny n\xED\u017Ee.","Plugins.header.title":"Povolen\xED","Policies.header.hint":"Vyberte akce aplikace, nebo akce z\xE1suvn\xE9ho modulu a klikn\u011Bte na ikonku ozuben\xE9ho kole\u010Dka pro zobrazen\xED adresy s nimi spojenou.","Policies.header.title":"Pokro\u010Dil\xE1 nastaven\xED","PopUpForm.Email.email_templates.inputDescription":"Pokud si nejste jisti jak pou\u017E\xEDvat prom\u011Bnn\xE9, {link}","PopUpForm.Email.options.from.email.label":"Odesilatel\u016Fv e-mail","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Jm\xE9no odesilatele","PopUpForm.Email.options.from.name.placeholder":"Jan Nov\xE1k","PopUpForm.Email.options.message.label":"Zpr\xE1va","PopUpForm.Email.options.object.label":"P\u0159edm\u011Bt","PopUpForm.Email.options.response_email.label":"Response e-mail","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"If disabled, users won't be able to use this provider.","PopUpForm.Providers.enabled.label":"Povolit","PopUpForm.Providers.key.label":"Client ID","PopUpForm.Providers.key.placeholder":"TEXT","PopUpForm.Providers.redirectURL.front-end.label":"Adresa pro p\u0159esm\u011Brov\xE1n\xED na va\u0161i front-end aplikaci","PopUpForm.Providers.secret.label":"Client Secret","PopUpForm.Providers.secret.placeholder":"TEXT","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Upravit e-mailov\xE9 \u0161ablony","notification.success.submit":"Nastaven\xED bylo aktualizov\xE1n\xED","plugin.description.long":"Chra\u0148te sv\xE9 API pomoc\xED kompletn\xEDho autentifika\u010Dn\xEDho procesu, zalo\u017Een\xE9ho na JWT. Tento z\xE1suvn\xFD modul obsahuje ACL strategii, kter\xE1 v\xE1m umo\u017En\xED spravovat opr\xE1vn\u011Bn\xED mezi skupinami u\u017Eivatel\u016F.","plugin.description.short":"Chra\u0148te sv\xE9 API pomoc\xED kompletn\xEDho autentifika\u010Dn\xEDho procesu, zalo\u017Een\xE9ho na JWT","plugin.name":"Role a opr\xE1vn\u011Bn\xED"}}}]);
