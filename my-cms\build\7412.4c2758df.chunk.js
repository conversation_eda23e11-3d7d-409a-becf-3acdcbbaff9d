"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[7412],{77412:(k,e,n)=>{n.r(e),n.d(e,{Analytics:()=>o,Documentation:()=>t,Email:()=>a,Password:()=>i,Provider:()=>s,ResetPasswordToken:()=>r,Role:()=>u,Username:()=>p,Users:()=>c,anErrorOccurred:()=>l,clearLabel:()=>m,default:()=>y,or:()=>d,skipToContent:()=>g,submit:()=>w});const o="Analityka",t="Dokumentacja",a="E-mail",i="Has\u0142o",s="Dostawca",r="Token odzyskiwania has\u0142a",u="Rola",p="Nazwa u\u017Cytkownika",c="U\u017Cytkownicy",l="Ups! Co\u015B posz\u0142o nie tak. Spr\xF3buj ponownie.",m="Wyczy\u015B\u0107",d="LUB",g="Przeskocz do zawarto\u015Bci",w="Wy\u015Blij",y={Analytics:o,"Auth.components.Oops.text":"Twoje konto zosta\u0142o zablokowane","Auth.components.Oops.text.admin":"Je\u015Bli to b\u0142\u0105d, skontaktuj si\u0119 z administratorem.","Auth.components.Oops.title":"Ups...","Auth.form.button.forgot-password":"Wy\u015Blij e-mail","Auth.form.button.go-home":"PRZEJD\u0179 DO STRONY G\u0141\xD3WNEJ","Auth.form.button.login":"Zaloguj si\u0119","Auth.form.button.login.providers.error":"Nie mo\u017Cemy po\u0142\u0105czy\u0107 Ci\u0119 za po\u015Brednictwem wybranego dostawcy.","Auth.form.button.login.strapi":"Zaloguj si\u0119 przez Strapi","Auth.form.button.password-recovery":"Odzyskiwanie has\u0142a","Auth.form.button.register":"Zaczynajmy","Auth.form.confirmPassword.label":"Potwierd\u017A has\u0142o","Auth.form.currentPassword.label":"Obecne has\u0142o","Auth.form.email.label":"E-mail","Auth.form.email.placeholder":"np. <EMAIL>","Auth.form.error.blocked":"Twoje konto zosta\u0142o zablokowane przez administratora.","Auth.form.error.code.provide":"Podano nieprawid\u0142owy kod.","Auth.form.error.confirmed":"Adres e-mail Twojego konta nie zosta\u0142 potwierdzony.","Auth.form.error.email.invalid":"Ten e-mail jest nieprawid\u0142owy.","Auth.form.error.email.provide":"Podaj swoj\u0105 nazw\u0119 u\u017Cytkownika lub adres e-mail.","Auth.form.error.email.taken":"Adres e-mail jest ju\u017C zaj\u0119ty.","Auth.form.error.invalid":"Identyfikator lub has\u0142o jest nieprawid\u0142owe.","Auth.form.error.params.provide":"Podano nieprawid\u0142owe parametry.","Auth.form.error.password.format":"Twoje has\u0142o nie mo\u017Ce zawiera\u0107 symbolu `$` wi\u0119cej ni\u017C trzy razy.","Auth.form.error.password.local":"Ten u\u017Cytkownik nigdy nie ustawi\u0142 has\u0142a lokalnego, zaloguj si\u0119 za po\u015Brednictwem dostawcy u\u017Cytego podczas tworzenia konta.","Auth.form.error.password.matching":"Has\u0142a r\xF3\u017Cni\u0105 si\u0119 od siebie.","Auth.form.error.password.provide":"Prosz\u0119 poda\u0107 swoje has\u0142o.","Auth.form.error.ratelimit":"Zbyt wiele pr\xF3b, spr\xF3buj ponownie za minut\u0119.","Auth.form.error.user.not-exist":"Ten adres e-mail nie istnieje.","Auth.form.error.username.taken":"Nazwa u\u017Cytkownika jest ju\u017C zaj\u0119ta.","Auth.form.firstname.label":"Imi\u0119","Auth.form.firstname.placeholder":"Jan","Auth.form.forgot-password.email.label":"Wpisz sw\xF3j e-mail","Auth.form.forgot-password.email.label.success":"E-mail pomy\u015Blnie wys\u0142any do","Auth.form.lastname.label":"Nazwisko","Auth.form.lastname.placeholder":"Kowalski","Auth.form.password.hide-password":"Ukryj has\u0142o","Auth.form.password.hint":"Minimum 8 znak\xF3w, 1 z du\u017Cej litery, 1 z ma\u0142ej litery i 1 cyfra","Auth.form.password.show-password":"Poka\u017C has\u0142o","Auth.form.register.news.label":"Informuj mnie na bie\u017C\u0105co o nowych funkcjach i nadchodz\u0105cych ulepszeniach (robi\u0105c to, akceptujesz postanowienia zawarte w niniejszych dokumentach - {terms} i {policy}).","Auth.form.register.subtitle":"Dane logowania uzywane s\u0105 tylko do uwierzytelniania w Strapi. Wszystkie zapisane dane b\u0119d\u0105 przechowywane w twojej bazie danych.","Auth.form.rememberMe.label":"Zapami\u0119taj mnie","Auth.form.username.label":"Nazwa u\u017Cytkownika","Auth.form.username.placeholder":"Jan Kowalski","Auth.form.welcome.subtitle":"Zaloguj si\u0119 do swojego konta","Auth.form.welcome.title":"Witaj w Strapi!","Auth.link.forgot-password":"Zapomnia\u0142e\u015B has\u0142a?","Auth.link.ready":"Chcesz si\u0119 zalogowa\u0107?","Auth.link.signin":"Zaloguj","Auth.link.signin.account":"Posiadasz ju\u017C konto?","Auth.login.sso.divider":"Lub zaloguj si\u0119 za pomoc\u0105","Auth.login.sso.loading":"\u0141adowanie dostawc\xF3w...","Auth.login.sso.subtitle":"Zaloguj si\u0119 do twojego konta za pomoc\u0105 SSO","Auth.privacy-policy-agreement.policy":"polityka prywatno\u015Bci","Auth.privacy-policy-agreement.terms":"warunki","Auth.reset-password.title":"Zresetuj has\u0142o","Content Manager":"Mened\u017Cer tre\u015Bci","Content Type Builder":"Kreator typ\xF3w tre\u015Bci",Documentation:t,Email:a,"Files Upload":"Przesy\u0142anie plik\xF3w","HomePage.helmet.title":"Strona g\u0142\xF3wna","HomePage.roadmap":"Zobacz nasz\u0105 roadmap\u0119","HomePage.welcome.congrats":"Gratulacje!","HomePage.welcome.congrats.content":"Jeste\u015B zalogowany jako pierwszy administrator. Aby odkry\u0107 pot\u0119\u017Cne funkcje oferowane przez Strapi,","HomePage.welcome.congrats.content.bold":"zach\u0119camy do utworzenia pierwszego typu kolekcji.","Media Library":"Biblioteka medi\xF3w","New entry":"Nowy wpis",Password:i,Provider:s,ResetPasswordToken:r,Role:u,"Roles & Permissions":"Role i Uprawnienia","Roles.ListPage.notification.delete-all-not-allowed":"Niekt\xF3rych r\xF3l nie mo\u017Cna usun\u0105\u0107, poniewa\u017C s\u0105 powi\u0105zane z u\u017Cytkownikami","Roles.ListPage.notification.delete-not-allowed":"Nie mo\u017Cna usun\u0105\u0107 roli, je\u015Bli jest powi\u0105zana z u\u017Cytkownikami","Roles.RoleRow.select-all":"Wybierz {name} do grupowych czynno\u015Bci","Roles.RoleRow.user-count":"{number, plural, =0 {#  } one {# } other {# }}","Roles.components.List.empty.withSearch":"Brak roli odpowiadaj\u0105cej wyszukiwaniu ({search}) ...","Settings.PageTitle":"Ustawienia - {name}","Settings.apiTokens.addFirstToken":"Dodaj pierwszy API Token","Settings.apiTokens.addNewToken":"Dodaj nowy API Token","Settings.tokens.copy.editMessage":"Dla bezpiecze\u0144stwa, mo\u017Cesz tylko raz zobaczy\u0107 sw\xF3j token.","Settings.tokens.copy.editTitle":"Ten token nie jest ju\u017C dost\u0119pny.","Settings.tokens.copy.lastWarning":"Pami\u0119taj \u017Ceby skopiowa\u0107 token, nie b\u0119dziesz w stanie kolejny raz go zobaczy\u0107!","Settings.apiTokens.create":"Stw\xF3rz nowy API Token","Settings.apiTokens.description":"Lista wygenerowanych token\xF3w pozwalaj\u0105cych korzysta\u0107 z API","Settings.apiTokens.emptyStateLayout":"Brak zawarto\u015Bci...","Settings.tokens.notification.copied":"Token skopiowany do schowka","Settings.apiTokens.title":"API Tokeny","Settings.tokens.types.full-access":"Full access","Settings.tokens.types.read-only":"Read-only","Settings.application.description":"Globalne informacje dotycz\u0105ce panelu administratora","Settings.application.edition-title":"obecny plan","Settings.application.get-help":"Uzyskaj pomoc","Settings.application.link-pricing":"Zobacz cennik","Settings.application.link-upgrade":"Aktualizuj panel admina","Settings.application.node-version":"wersja node","Settings.application.strapi-version":"wersja strapi","Settings.application.strapiVersion":"wersja strapi","Settings.application.title":"Og\xF3lne","Settings.error":"B\u0142\u0105d","Settings.global":"Ustawienia Globalne","Settings.permissions":"Panel administracyjny","Settings.permissions.category":"Ustawienia uprawnie\u0144 dla {category}","Settings.permissions.category.plugins":"Ustawienia uprawnie\u0144 dla pluginu {category} ","Settings.permissions.conditions.anytime":"W dowolnym momencie","Settings.permissions.conditions.apply":"Zastosuj","Settings.permissions.conditions.can":"Mo\u017Ce","Settings.permissions.conditions.conditions":"Definiowa\u0107 warunki","Settings.permissions.conditions.links":"Linki","Settings.permissions.conditions.no-actions":"Najpierw musisz wybra\u0107 akcje (tworzenie, odczytywanie, aktualizowanie, ...) przed zdefiniowaniem dla nich warunk\xF3w.","Settings.permissions.conditions.none-selected":"W dowolnym momencie","Settings.permissions.conditions.or":"LUB","Settings.permissions.conditions.when":"Kiedy","Settings.permissions.select-all-by-permission":"Wybierz wszystkie {label} uprawnienia","Settings.permissions.select-by-permission":"Wybierz {label} uprawnienie","Settings.permissions.users.create":"Utw\xF3rz nowego u\u017Cytkownika","Settings.permissions.users.email":"E-mail","Settings.permissions.users.firstname":"Imi\u0119","Settings.permissions.users.lastname":"Nazwisko","Settings.permissions.users.form.sso":"Po\u0142\u0105cz z logowaniem jednokrotnym (SSO)","Settings.permissions.users.form.sso.description":"Po w\u0142\u0105czeniu (ON) u\u017Cytkownicy mog\u0105 logowa\u0107 si\u0119 za pomoc\u0105 logowania jednokrotnego (SSO)","Settings.permissions.users.listview.header.subtitle":"Wszyscy u\u017Cytkownicy posiadaj\u0105cy dost\u0119p do panelu admina","Settings.permissions.users.tabs.label":"Uprawnienia","Settings.profile.form.notify.data.loaded":"Dane twojego profilu zosta\u0142y za\u0142adowane","Settings.profile.form.section.experience.clear.select":"Usu\u0144 wyb\xF3r j\u0119zyka interfejsu","Settings.profile.form.section.experience.here":"tutaj","Settings.profile.form.section.experience.interfaceLanguage":"J\u0119zyk aplikacji","Settings.profile.form.section.experience.interfaceLanguage.hint":"Wy\u015Bwietla aplikacj\u0119 w wybranym j\u0119zyku.","Settings.profile.form.section.experience.interfaceLanguageHelp":"Zmiany preferencji b\u0119d\u0105 mia\u0142y zastosowanie tylko do tego profilu. Wi\u0119cej informacji {tutaj}.","Settings.profile.form.section.experience.mode.label":"Motyw aplikacji","Settings.profile.form.section.experience.mode.hint":"Wy\u015Bwietla aplikacj\u0119 w wybranym motywie.","Settings.profile.form.section.experience.mode.option-label":"{name} mode","Settings.profile.form.section.experience.title":"Korzystanie","Settings.profile.form.section.helmet.title":"Profil u\u017Cytkownika","Settings.profile.form.section.profile.page.title":"Strona profilu","Settings.roles.create.description":"Zdefiniuj uprawnienia nadane roli","Settings.roles.create.title":"Utw\xF3rz rol\u0119","Settings.roles.created":"Utworzono rol\u0119","Settings.roles.edit.title":"Edytuj rol\u0119","Settings.roles.form.button.users-with-role":"U\u017Cytkownicy z t\u0105 rol\u0105","Settings.roles.form.created":"Utworzono","Settings.roles.form.description":"Nazwa i opis roli","Settings.roles.form.permission.property-label":"Uprawnienia dla {label}","Settings.roles.form.permissions.attributesPermissions":"Uprawnienia do p\xF3l","Settings.roles.form.permissions.create":"Tworzenie","Settings.roles.form.permissions.delete":"Usuwanie","Settings.roles.form.permissions.publish":"Publikowanie","Settings.roles.form.permissions.read":"Odczyt","Settings.roles.form.permissions.update":"Aktualizowanie","Settings.roles.list.button.add":"Dodaj now\u0105 rol\u0119","Settings.roles.list.description":"Lista r\xF3l","Settings.roles.title.singular":"rola","Settings.sso.description":"Skonfiguruj ustawienia funkcji logowania jednokrotnego (SSO).","Settings.sso.form.defaultRole.description":"Po\u0142\u0105czy to nowego uwierzytelnionego u\u017Cytkownika do wybranej roli","Settings.sso.form.defaultRole.description-not-allowed":"Musisz mie\u0107 uprawnienia do odczytu r\xF3l administratora","Settings.sso.form.defaultRole.label":"Domy\u015Blna rola","Settings.sso.form.registration.description":"Utw\xF3rz nowego u\u017Cytkownika przy logowaniu SSO, je\u015Bli konto nie istnieje","Settings.sso.form.registration.label":"Automatyczna rejestracja","Settings.sso.title":"Jednokrotne logowanie (SSO)","Settings.webhooks.create":"Utw\xF3rz webhook","Settings.webhooks.create.header":"Utw\xF3rz nowy nag\u0142\xF3wek","Settings.webhooks.created":"Utworzono webhook","Settings.webhooks.event.publish-tooltip":"To zdarzenie istnieje tylko dla tre\u015Bci z w\u0142\u0105czonym systemem wersji roboczej/publikacji","Settings.webhooks.events.create":"Utw\xF3rz","Settings.webhooks.events.update":"Edytuj","Settings.webhooks.form.events":"Zdarzenia","Settings.webhooks.form.headers":"Nag\u0142\xF3wki","Settings.webhooks.form.url":"URL","Settings.webhooks.headers.remove":"Usu\u0144 rz\u0105d {number}","Settings.webhooks.key":"Klucz","Settings.webhooks.list.button.add":"Dodaj nowy webhook","Settings.webhooks.list.description":"Otrzymaj powiadomienia o zmianach POST.","Settings.webhooks.list.empty.description":"Nie ma jeszcze \u017Cadnych webhook\xF3w","Settings.webhooks.list.empty.link":"Zobacz nasz\u0105 dokumentacj\u0119","Settings.webhooks.list.empty.title":"Nie ma jeszcze \u017Cadnych webhook\xF3w","Settings.webhooks.list.th.actions":"akcje","Settings.webhooks.list.th.status":"stan","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooki","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# } other {# }} wybrano","Settings.webhooks.trigger":"Uruchom","Settings.webhooks.trigger.cancel":"Anuluj wyzwalacz","Settings.webhooks.trigger.pending":"W oczekiwaniu...","Settings.webhooks.trigger.save":"Zapisz, aby uruchomi\u0107","Settings.webhooks.trigger.success":"Powodzenie!","Settings.webhooks.trigger.success.label":"Uruchomiono poprawnie","Settings.webhooks.trigger.test":"Testowy-wyzwalacz","Settings.webhooks.trigger.title":"Zapisz przed wyzwoleniem","Settings.webhooks.value":"Warto\u015B\u0107","Usecase.back-end":"Back-end developer","Usecase.button.skip":"Pomi\u0144 to pytanie","Usecase.content-creator":"Content Creator","Usecase.front-end":"Front-end developer","Usecase.full-stack":"Full-stack developer","Usecase.input.work-type":"Jaki rodzaj pracy wykonujesz?","Usecase.notification.success.project-created":"Projekt zosta\u0142 utworzony","Usecase.other":"Inny","Usecase.title":"Opowiedz nam troch\u0119 o sobie",Username:p,Users:c,"Users & Permissions":"U\u017Cytkownicy i Uprawnienia","Users.components.List.empty":"Brak u\u017Cytkownik\xF3w...","Users.components.List.empty.withFilters":"Brak u\u017Cytkownik\xF3w z zastosowanymi filtrami...","Users.components.List.empty.withSearch":"Brak u\u017Cytkownik\xF3w odpowiadaj\u0105cych wyszukiwaniu ({search})...","admin.pages.MarketPlacePage.helmet":"Sklep - Pluginy","admin.pages.MarketPlacePage.offline.title":"Jeste\u015B offline","admin.pages.MarketPlacePage.offline.subtitle":"Musisz by\u0107 po\u0142\u0105czony z internetem \u017Ceby skorzysta\u0107 ze sklepu Strapi.","admin.pages.MarketPlacePage.plugin.copy":"Skopiuj instalacj\u0119","admin.pages.MarketPlacePage.plugin.copy.success":"Polecenie instalacji gotowe do u\u017Cycia w twoim terminalu","admin.pages.MarketPlacePage.plugin.info":"Dowiedz si\u0119 wi\u0119cej","admin.pages.MarketPlacePage.plugin.info.label":"Dowiedz si\u0119 wi\u0119cej o {pluginName}","admin.pages.MarketPlacePage.plugin.info.text":"Informacje","admin.pages.MarketPlacePage.plugin.installed":"Zainstalowano","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"Stworzony przez Strapi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"Zweryfikowany przez Strapi","admin.pages.MarketPlacePage.search.clear":"Wyczy\u015B\u0107 wyszukiwark\u0119","admin.pages.MarketPlacePage.search.empty":'Brak wynik\xF3w dla "{target}"',"admin.pages.MarketPlacePage.search.placeholder":"Szukaj pluginu","admin.pages.MarketPlacePage.submit.plugin.link":"Wy\u015Blij sw\xF3j plugin","admin.pages.MarketPlacePage.subtitle":"Wykorzystaj Strapi lepiej","admin.pages.MarketPlacePage.missingPlugin.title":"Brakuje pluginu?","admin.pages.MarketPlacePage.missingPlugin.description":"Powiedz nam jakiego pluginu szukasz, a my damy zna\u0107 o tym naszym developerom w razie gdyby szukali inspiracji!",anErrorOccurred:l,"app.component.CopyToClipboard.label":"Skopiuj do schowka","app.component.search.label":"Szukaj {target}","app.component.table.duplicate":"Duplikuj {target}","app.component.table.edit":"Edytuj {target}","app.component.table.select.one-entry":"Wybierz {target}","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Czytaj najnowsze wiadomo\u015Bci na temat Strapi.","app.components.BlockLink.code":"Przyk\u0142ady","app.components.BlockLink.code.content":"Ucz si\u0119 poprzez testowanie prawdziwych projekt\xF3w tworzonych przez nasz\u0105 spo\u0142eczno\u015B\u0107.","app.components.BlockLink.documentation.content":"Odkryj kluczowe poj\u0119cia, wskaz\xF3wki i instrukcje.","app.components.BlockLink.tutorial":"Tutoriale","app.components.BlockLink.tutorial.content":"Pod\u0105\u017Caj za instrukcjami krok po kroku \u017Ceby u\u017Cy\u0107 i dostosowa\u0107 Strapi.","app.components.Button.cancel":"Anuluj","app.components.Button.confirm":"Potwierd\u017A","app.components.Button.reset":"Resetuj","app.components.ComingSoonPage.comingSoon":"Wkr\xF3tce","app.components.ConfirmDialog.title":"Potwierdzenie","app.components.DownloadInfo.download":"Pobieranie w toku...","app.components.DownloadInfo.text":"Mo\u017Ce to chwil\u0119 potrwa\u0107. Dzi\u0119kujemy za cierpliwo\u015B\u0107.","app.components.EmptyAttributes.title":"Nie ma jeszcze \u017Cadnych p\xF3l","app.components.EmptyStateLayout.content-document":"Brak zawarto\u015Bci","app.components.EmptyStateLayout.content-permissions":"Nie masz dost\u0119pu do tej zawarto\u015Bci","app.components.GuidedTour.CM.create.content":"<p>Tw\xF3rz i zarz\u0105dzaj zawarto\u015Bci\u0105 tutaj w Mened\u017Cerze Tre\u015Bci.</p><p>Przyk\u0142ad: We\u017Amy pod uwag\u0119 przyk\u0142ad z blogiem, mo\u017Cesz napisa\u0107 aktyku\u0142, zapisa\u0107 i opublikowa\u0107 go kiedy tylko chcesz.</p><p>\u{1F4A1} Szybka wskaz\xF3wka - Nie zapomnij klikn\u0105\u0107 opublikuj, w tre\u015Bci kt\xF3r\u0105 tworzysz.</p>","app.components.GuidedTour.CM.create.title":"\u26A1\uFE0F Stw\xF3rz tre\u015B\u0107","app.components.GuidedTour.CM.success.content":"<p>Super, zosta\u0142 ostatni krok!</p><b>\u{1F680} Zobacz materia\u0142y w praktyce</b>","app.components.GuidedTour.CM.success.cta.title":"Przetestuj API","app.components.GuidedTour.CM.success.title":"Krok 2: Uko\u0144czony \u2705","app.components.GuidedTour.CTB.create.content":"<p>Kolekcje pomagaj\u0105 zarz\u0105dza\u0107 wieloma pozycjami, a pojedyncze typy s\u0105 odpowiednie do zarz\u0105dzania tylko jednym wpisem.</p> <p>Przyk\u0142ad: Wyobra\u017A sobie stron\u0119 z blogiem. Tam artyku\u0142y by\u0142yby kolekcjami, a strona g\u0142\xF3wna by\u0142aby pojedynczym typem.</p>","app.components.GuidedTour.CTB.create.cta.title":"Stw\xF3rz kolekcj\u0119","app.components.GuidedTour.CTB.create.title":"\u{1F9E0} Stw\xF3rz swoj\u0105 pierwsz\u0105 kolekcj\u0119","app.components.GuidedTour.CTB.success.content":"<p>Niez\u0142a robota!</p><b>\u26A1\uFE0F Czym chcia\u0142by\u015B si\u0119 podzieli\u0107 ze \u015Bwiatem?</b>","app.components.GuidedTour.CTB.success.title":"Krok 1: Uko\u0144czony \u2705","app.components.GuidedTour.apiTokens.create.content":"<p>Wygeneruj token aby otrzyma\u0107 dost\u0119p do tre\u015Bci, kt\xF3r\u0105 stworzy\u0142e\u015B.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"Wygeneruj API Token","app.components.GuidedTour.apiTokens.create.title":"\u{1F680} Zobacz materia\u0142y w praktyce","app.components.GuidedTour.apiTokens.success.content":"<p>Przetestuj tre\u015B\u0107 wykonuj\u0105c \u017C\u0105danie HTTP:</p><ul><li><p>Pod URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Przy u\u017Cyciu nag\u0142\xF3wka: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Zerknij na <documentationLink>dokumentacj\u0119</documentationLink> by pozna\u0107 wi\u0119cej sposob\xF3w na interakcj\u0119 z tre\u015Bci\u0105.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Powr\xF3t na stron\u0119 g\u0142\xF3wn\u0105","app.components.GuidedTour.apiTokens.success.title":"Krok 3: Uko\u0144czony \u2705","app.components.GuidedTour.create-content":"Stw\xF3rz zawarto\u015B\u0107","app.components.GuidedTour.home.CM.title":"\u26A1\uFE0F Czym chcia\u0142by\u015B si\u0119 podzieli\u0107 ze \u015Bwiatem?","app.components.GuidedTour.home.CTB.cta.title":"Przejd\u017A do kreatora typ\xF3w tre\u015Bci","app.components.GuidedTour.home.CTB.title":"\u{1F9E0} Stw\xF3rz struktur\u0119 tre\u015Bci","app.components.GuidedTour.home.apiTokens.cta.title":"Przetestuj API","app.components.GuidedTour.skip":"Pomi\u0144","app.components.GuidedTour.title":"3 kroki \u017Ceby zacz\u0105\u0107","app.components.HomePage.button.blog":"Zobacz wi\u0119cej na blogu","app.components.HomePage.community":"Do\u0142\u0105cz do spo\u0142eczno\u015Bci","app.components.HomePage.community.content":"Porozmawiaj z cz\u0142onkami zespo\u0142u, wsp\xF3\u0142tw\xF3rcami i programistami na r\xF3\u017Cnych kana\u0142ach.","app.components.HomePage.create":"Utw\xF3rz sw\xF3j pierwszy typ zawarto\u015Bci","app.components.HomePage.roadmap":"Zobacz nasz\u0105 roadmap\u0119","app.components.HomePage.welcome":"Witaj na pok\u0142adzie \u{1F44B}!","app.components.HomePage.welcome.again":"Witaj \u{1F44B}","app.components.HomePage.welcomeBlock.content":"Cieszymy si\u0119, \u017Ce jeste\u015B cz\u0119\u015Bci\u0105 spo\u0142eczno\u015Bci. Nieustannie poszukujemy opinii, wi\u0119c zach\u0119camy do wysy\u0142ania nam wiadomo\u015Bci","app.components.HomePage.welcomeBlock.content.again":"Mamy nadziej\u0119, \u017Ce robisz post\u0119py w swoim projekcie! Zach\u0119camy do zapoznania si\u0119 z najnowszymi wiadomo\u015Bciami o Strapi. Dok\u0142adamy wszelkich stara\u0144, aby ulepszy\u0107 produkt w oparciu o Wasze opinie.","app.components.HomePage.welcomeBlock.content.issues":"problemy.","app.components.HomePage.welcomeBlock.content.raise":" lub wskaza\u0107 ","app.components.ImgPreview.hint":"Przeci\u0105gnij i upu\u015B\u0107 plik w tym obszarze lub {browse}, aby przes\u0142a\u0107 plik","app.components.ImgPreview.hint.browse":"przegl\u0105daj","app.components.InputFile.newFile":"Dodaj nowy plik","app.components.InputFileDetails.open":"Otw\xF3rz w nowej karcie","app.components.InputFileDetails.originalName":"Oryginalna nazwa:","app.components.InputFileDetails.remove":"Usu\u0144 ten plik","app.components.InputFileDetails.size":"Rozmiar:","app.components.InstallPluginPage.Download.description":"Pobranie i zainstalowanie pluginu mo\u017Ce zaj\u0105\u0107 kilka sekund.","app.components.InstallPluginPage.Download.title":"Pobieranie...","app.components.InstallPluginPage.description":"Rozszerz swoj\u0105 aplikacj\u0119 bez wysi\u0142ku.","app.components.LeftMenu.collapse":"Zwi\u0144 nawigacj\u0119","app.components.LeftMenu.expand":"Rozszerz nawigacj\u0119","app.components.LeftMenu.logout":"Wyloguj","app.components.LeftMenu.navbrand.title":"Strapi Dashboard","app.components.LeftMenu.navbrand.workplace":"Workplace","app.components.LeftMenuFooter.help":"Wsparcie","app.components.LeftMenuFooter.poweredBy":"Obs\u0142ugiwane przez ","app.components.LeftMenuLinkContainer.collectionTypes":"Typy kolekcji","app.components.LeftMenuLinkContainer.configuration":"Ustawienia","app.components.LeftMenuLinkContainer.general":"Og\xF3lne","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Nie zainstalowano jeszcze \u017Cadnych plugin\xF3w","app.components.LeftMenuLinkContainer.plugins":"Pluginy","app.components.LeftMenuLinkContainer.singleTypes":"Pojedyncze typy","app.components.ListPluginsPage.deletePlugin.description":"Odinstalowanie pluginu mo\u017Ce zaj\u0105\u0107 kilka sekund.","app.components.ListPluginsPage.deletePlugin.title":"Odinstalowywanie","app.components.ListPluginsPage.description":"Lista zainstalowanych plugin\xF3w.","app.components.ListPluginsPage.helmet.title":"Lista plugin\xF3w","app.components.Logout.logout":"Wyloguj","app.components.Logout.profile":"Profil","app.components.MarketplaceBanner":"Odkryj pluginy tworzone przez spo\u0142eczno\u015B\u0107 oraz wiele wi\u0119cej rzeczy \u017Ceby odpali\u0107 projekt, u\u017Cywaj\u0105c Strapi.","app.components.MarketplaceBanner.image.alt":"strapi logo","app.components.MarketplaceBanner.link":"Sprawd\u017A","app.components.NotFoundPage.back":"Powr\xF3t na stron\u0119 g\u0142\xF3wn\u0105","app.components.NotFoundPage.description":"Nie znaleziono","app.components.Official":"Oficjalne","app.components.Onboarding.help.button":"Pomoc","app.components.Onboarding.label.completed":"% uko\u0144czono","app.components.Onboarding.title":"Odpal filmy szkoleniowe","app.components.PluginCard.Button.label.download":"Pobierz","app.components.PluginCard.Button.label.install":"Ju\u017C zainstalowane","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"Funkcja autoReload musi by\u0107 w\u0142\u0105czona. Uruchom aplikacj\u0119 za pomoc\u0105 polecenia `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Rozumiem!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Ze wzgl\u0119d\xF3w bezpiecze\u0144stwa plugin mo\u017Cna pobra\u0107 tylko w \u015Brodowisku programistycznym.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Pobieranie jest niemo\u017Cliwe","app.components.PluginCard.compatible":"Zgodny z Twoj\u0105 aplikacj\u0105","app.components.PluginCard.compatibleCommunity":"Zgodny ze spo\u0142eczno\u015Bci\u0105","app.components.PluginCard.more-details":"Wi\u0119cej szczeg\xF3\u0142\xF3w","app.components.ToggleCheckbox.off-label":"Nie","app.components.ToggleCheckbox.on-label":"Tak","app.components.Users.MagicLink.connect":"Wy\u015Blij ten link do u\u017Cytkownika, aby m\xF3g\u0142 si\u0119 po\u0142\u0105czy\u0107.","app.components.Users.MagicLink.connect.sso":"Wy\u015Blij ten link do u\u017Cytkownika, pierwsze logowanie mo\u017Cna wykona\u0107 za po\u015Brednictwem dostawcy SSO","app.components.Users.ModalCreateBody.block-title.details":"Szczeg\xF3\u0142y","app.components.Users.ModalCreateBody.block-title.roles":"Role u\u017Cytkownika","app.components.Users.ModalCreateBody.block-title.roles.description":"Tw\xF3j u\u017Cytkownik mo\u017Ce mie\u0107 jedn\u0105 lub kilka r\xF3l","app.components.Users.SortPicker.button-label":"Sortuj wed\u0142ug","app.components.Users.SortPicker.sortby.email_asc":"E-mail (od A do Z)","app.components.Users.SortPicker.sortby.email_desc":"E-mail (od Z do A)","app.components.Users.SortPicker.sortby.firstname_asc":"Imi\u0119 (od A do Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Imi\u0119 (od Z do A)","app.components.Users.SortPicker.sortby.lastname_asc":"Nazwisko (od A do Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Nazwisko (od Z do A)","app.components.Users.SortPicker.sortby.username_asc":"Nazwa u\u017Cytkownika (od A do Z)","app.components.Users.SortPicker.sortby.username_desc":"Nazwa u\u017Cytkownika (od Z do A)","app.components.listPlugins.button":"Dodaj nowy plugin","app.components.listPlugins.title.none":"Brak zainstalowanych plugin\xF3w","app.components.listPluginsPage.deletePlugin.error":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas odinstalowywania pluginu","app.containers.App.notification.error.init":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas \u017C\u0105dania interfejsu API","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Je\u015Bli nie otrzymasz tego \u0142\u0105cza, skontaktuj si\u0119 z administratorem.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"Do otrzymania linku do odzyskiwania has\u0142a mo\u017Ce min\u0105\u0107 kilka minut.","app.containers.AuthPage.ForgotPasswordSuccess.title":"E-mail wys\u0142any","app.containers.Users.EditPage.form.active.label":"Aktywne","app.containers.Users.EditPage.header.label":"Edytuj {name}","app.containers.Users.EditPage.header.label-loading":"Edytuj u\u017Cytkownika","app.containers.Users.EditPage.roles-bloc-title":"Role przypisane","app.containers.Users.ModalForm.footer.button-success":"Stw\xF3rz u\u017Cytkownika","app.links.configure-view":"Skonfiguruj widok","app.page.not.found":"Ups! Nie mo\u017Cemy znale\u017A\u0107 strony, kt\xF3rej szukasz...","app.static.links.cheatsheet":"\u015Aci\u0105ga","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Dodaj filtr","app.utils.close-label":"Zamknij","app.utils.defaultMessage":" ","app.utils.duplicate":"Duplikuj","app.utils.edit":"Edytuj","app.utils.errors.file-too-big.message":"Plik jest za du\u017Cy","app.utils.filter-value":"Filtr","app.utils.filters":"Filtry","app.utils.notify.data-loaded":"{target} zosta\u0142 za\u0142adowany","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Opublikuj","app.utils.select-all":"Zaznacz wszystko","app.utils.select-field":"Zaznacz pole","app.utils.select-filter":"Zaznacz filtr","app.utils.unpublish":"Cofnij publikacj\u0119",clearLabel:m,"coming.soon":"Ta zawarto\u015B\u0107 jest aktualnie w trakcie budowy i wr\xF3ci za jaki\u015B czas!","component.Input.error.validation.integer":"Warto\u015B\u0107 ta musi by\u0107 liczb\u0105 ca\u0142kowit\u0105","components.AutoReloadBlocker.description":"Uruchom Strapi za pomoc\u0105 jednego z nast\u0119puj\u0105cych polece\u0144:","components.AutoReloadBlocker.header":"Ten plugin wymaga funkcji prze\u0142adowania.","components.ErrorBoundary.title":"Co\u015B posz\u0142o nie tak...","components.FilterOptions.FILTER_TYPES.$contains":"zawiera","components.FilterOptions.FILTER_TYPES.$containsi":"zawiera (wielko\u015B\u0107 liter nie ma znaczenia)","components.FilterOptions.FILTER_TYPES.$endsWith":"ko\u0144czy si\u0119","components.FilterOptions.FILTER_TYPES.$endsWithi":"ko\u0144czy si\u0119 (wielko\u015B\u0107 liter nie ma znaczenia)","components.FilterOptions.FILTER_TYPES.$eq":"ko\u0144czy si\u0119 na","components.FilterOptions.FILTER_TYPES.$eqi":"ko\u0144czy si\u0119 na (wielko\u015B\u0107 liter nie ma znaczenia)","components.FilterOptions.FILTER_TYPES.$gt":"jest wi\u0119ksza ni\u017C","components.FilterOptions.FILTER_TYPES.$gte":"jest wi\u0119ksze lub r\xF3wne","components.FilterOptions.FILTER_TYPES.$lt":"jest mniejsze ni\u017C","components.FilterOptions.FILTER_TYPES.$lte":"jest mniejsze lub r\xF3wne","components.FilterOptions.FILTER_TYPES.$ne":"nie jest","components.FilterOptions.FILTER_TYPES.$nei":"nie jest (wielko\u015B\u0107 liter nie ma znaczenia)","components.FilterOptions.FILTER_TYPES.$notContains":"nie zawiera","components.FilterOptions.FILTER_TYPES.$notContainsi":"nie zawiera (wielko\u015B\u0107 liter nie ma znaczenia)","components.FilterOptions.FILTER_TYPES.$notNull":"nie jest null","components.FilterOptions.FILTER_TYPES.$null":"jest null","components.FilterOptions.FILTER_TYPES.$startsWith":"zaczyna si\u0119 na","components.FilterOptions.FILTER_TYPES.$startsWithi":"zaczyna si\u0119 na (wielko\u015B\u0107 liter nie ma znaczenia)","components.Input.error.attribute.key.taken":"Ta warto\u015B\u0107 ju\u017C istnieje","components.Input.error.attribute.sameKeyAndName":"Nie mog\u0105 by\u0107 r\xF3wne","components.Input.error.attribute.taken":"Ta nazwa pola ju\u017C istnieje","components.Input.error.contain.lowercase":"Has\u0142o musi zawiera\u0107 co najmniej jedn\u0105 ma\u0142\u0105 liter\u0119","components.Input.error.contain.number":"Has\u0142o musi zawiera\u0107 co najmniej jedn\u0105 cyfr\u0119","components.Input.error.contain.uppercase":"Has\u0142o musi zawiera\u0107 co najmniej jedn\u0105 wielk\u0105 liter\u0119","components.Input.error.contentTypeName.taken":"Ta nazwa ju\u017C istnieje","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"Has\u0142a nie pasuj\u0105 do siebie","components.Input.error.validation.email":"To nie jest e-mail","components.Input.error.validation.json":"To nie pasuje do formatu JSON","components.Input.error.validation.lowercase":"Warto\u015B\u0107 musi by\u0107 zapisana ma\u0142ymi literami","components.Input.error.validation.max":"Warto\u015B\u0107 jest za wysoka {max}.","components.Input.error.validation.maxLength":"Warto\u015B\u0107 jest za d\u0142uga {max}.","components.Input.error.validation.min":"Warto\u015B\u0107 jest za ma\u0142a {min}.","components.Input.error.validation.minLength":"Warto\u015B\u0107 jest za kr\xF3tka {min}.","components.Input.error.validation.minSupMax":"Warto\u015B\u0107 nie mo\u017Ce by\u0107 wi\u0119ksza","components.Input.error.validation.regex":"Warto\u015B\u0107 nie jest zgodna z wyra\u017Ceniem regularnym.","components.Input.error.validation.required":"To pole jest wymagane.","components.Input.error.validation.unique":"Ta warto\u015B\u0107 jest ju\u017C u\u017Cywana.","components.InputSelect.option.placeholder":"Wybierz tutaj","components.ListRow.empty":"Brak danych do wy\u015Bwietlenia.","components.NotAllowedInput.text":"Brak uprawnie\u0144 do wy\u015Bwietlania tego pola","components.OverlayBlocker.description":"U\u017Cywasz funkcji, kt\xF3ra wymaga ponownego uruchomienia serwera. Poczekaj, a\u017C serwer si\u0119 uruchomi.","components.OverlayBlocker.description.serverError":"Serwer powinien si\u0119 ju\u017C zrestartowa\u0107, sprawd\u017A swoje logi w terminalu.","components.OverlayBlocker.title":"Czekam na ponowne uruchomienie...","components.OverlayBlocker.title.serverError":"Ponowne uruchomienie trwa d\u0142u\u017Cej ni\u017C oczekiwano","components.PageFooter.select":"wpis\xF3w na stronie","components.ProductionBlocker.description":"Ze wzgl\u0119d\xF3w bezpiecze\u0144stwa musimy wy\u0142\u0105czy\u0107 ten plugin w innych \u015Brodowiskach.","components.ProductionBlocker.header":"Ten plugin jest dost\u0119pna tylko w trybie deweloperskim.","components.Search.placeholder":"Szukaj...","components.TableHeader.sort":"Sortuj {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Tryb Markdown","components.Wysiwyg.ToggleMode.preview-mode":"Tryb podgl\u0105du","components.Wysiwyg.collapse":"Zwi\u0144","components.Wysiwyg.selectOptions.H1":"Tytu\u0142 H1","components.Wysiwyg.selectOptions.H2":"Tytu\u0142 H2","components.Wysiwyg.selectOptions.H3":"Tytu\u0142 H3","components.Wysiwyg.selectOptions.H4":"Tytu\u0142 H4","components.Wysiwyg.selectOptions.H5":"Tytu\u0142 H5","components.Wysiwyg.selectOptions.H6":"Tytu\u0142 H6","components.Wysiwyg.selectOptions.title":"Dodaj tytu\u0142","components.WysiwygBottomControls.charactersIndicators":"znak\xF3w","components.WysiwygBottomControls.fullscreen":"Rozszerz","components.WysiwygBottomControls.uploadFiles":"Przeci\u0105gnij i upu\u015B\u0107 pliki, wklej ze schowka lub {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"wybierz je","components.pagination.go-to":"Id\u017A do {page}","components.pagination.go-to-next":"Id\u017A do nast\u0119pnej strony","components.pagination.go-to-previous":"Id\u017A do poprzedniej strony","components.pagination.remaining-links":"Dodaj {number} inne linki","components.popUpWarning.button.cancel":"Nie, anuluj","components.popUpWarning.button.confirm":"Tak, potwierd\u017A","components.popUpWarning.message":"Czy na pewno chcesz to usun\u0105\u0107?","components.popUpWarning.title":"Prosz\u0119 potwierdzi\u0107","content-manager.App.schemas.data-loaded":"Schematy zosta\u0142y poprawnie za\u0142adowane","content-manager.ListViewTable.relation-loaded":"Relacje zosta\u0142y za\u0142adowane","content-manager.ListViewTable.relation-loading":"Trwa \u0142adowanie relacji","content-manager.ListViewTable.relation-more":"Ta relacja zwiera wi\u0119cej warto\u015Bci nie\u017C wy\u015Bwietlana","content-manager.EditRelations.title":"Relacje","content-manager.HeaderLayout.button.label-add-entry":"Dodaj nowy wpis","content-manager.api.id":"API ID","content-manager.components.AddFilterCTA.add":"Filtry","content-manager.components.AddFilterCTA.hide":"Filtry","content-manager.components.DragHandle-label":"Przenie\u015B","content-manager.components.DraggableAttr.edit":"Kliknij by edytowa\u0107","content-manager.components.DraggableCard.delete.field":"Usu\u0144 {item}","content-manager.components.DraggableCard.edit.field":"Edytuj {item}","content-manager.components.DraggableCard.move.field":"Przenie\u015B {item}","content-manager.components.ListViewTable.row-line":"rz\u0105d {number}","content-manager.components.DynamicZone.ComponentPicker-label":"Wybierz komponent","content-manager.components.DynamicZone.add-component":"Dodaj komponent do {componentName}","content-manager.components.DynamicZone.delete-label":"Usu\u0144 {name}","content-manager.components.DynamicZone.error-message":"Komponent zawiera b\u0142\u0105d/b\u0142\u0119dy","content-manager.components.DynamicZone.missing-components":"Brakuje {number, plural, =0 {# komponent\xF3w} one {# komponentu} other {# komponent\xF3w}}","content-manager.components.DynamicZone.move-down-label":"Przesu\u0144 ni\u017Cej","content-manager.components.DynamicZone.move-up-label":"Przesu\u0144 wy\u017Cej","content-manager.components.DynamicZone.pick-compo":"Wybierz jeden komponent","content-manager.components.DynamicZone.required":"Komponent jest wymagany","content-manager.components.EmptyAttributesBlock.button":"Przejd\u017A do ustawie\u0144","content-manager.components.EmptyAttributesBlock.description":"Mo\u017Cesz zmieni\u0107 ustawienia","content-manager.components.FieldItem.linkToComponentLayout":"Ustaw uk\u0142ad komponentu","content-manager.components.FieldSelect.label":"Dodaj pole","content-manager.components.FilterOptions.button.apply":"Zastosuj","content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply":"Zastosuj","content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Wyczy\u015B\u0107 wszystko","content-manager.components.FiltersPickWrapper.PluginHeader.description":"Ustawianie warunk\xF3w filtrowania element\xF3w.","content-manager.components.FiltersPickWrapper.PluginHeader.title.filter":"Filtry","content-manager.components.FiltersPickWrapper.hide":"Ukryj","content-manager.components.LeftMenu.Search.label":"Szukaj","content-manager.components.LeftMenu.collection-types":"Typy kolekcji","content-manager.components.LeftMenu.single-types":"Pojedynczy typ","content-manager.components.LimitSelect.itemsPerPage":"Element\xF3w na stron\u0119","content-manager.components.NotAllowedInput.text":"Brak uprawnie\u0144 do zobaczenia tego pola","content-manager.components.RepeatableComponent.error-message":"Komponent zawiera b\u0142\u0105d/b\u0142\u0119dy","content-manager.components.Search.placeholder":"Szukaj elementu...","content-manager.components.Select.draft-info-title":"Stan: Szkic","content-manager.components.Select.publish-info-title":"Stan: Opublikowany","content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Dostosuj wygl\u0105d widoku edycji.","content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings":"Zdefiniuj ustawienia widoku listy.","content-manager.components.SettingsViewWrapper.pluginHeader.title":"Skonfiguruj widok - {name}","content-manager.components.TableDelete.delete":"Usu\u0144","content-manager.components.TableDelete.deleteSelected":"Usu\u0144 zaznaczone","content-manager.components.TableDelete.label":"{number, plural, one {# wpis zaznaczony} other {# wpisy zaznaczone}}","content-manager.components.TableEmpty.withFilters":"Nie istniej\u0105 elementy {contentType} zgodne z zastosowanymi filtrami...","content-manager.components.TableEmpty.withSearch":"Nie istniej\u0105 elementy {contentType} zgodne z wyszukiwan\u0105 fraz\u0105 ({search})...","content-manager.components.TableEmpty.withoutFilter":"Nie istniej\u0105 jeszcze elementy zwi\u0105zane z {contentType}... Stw\xF3rz pierwszy jak najszybciej!","content-manager.components.empty-repeatable":"Jeszcze nie ma wpisu. Kliknij przycisk poni\u017Cej, aby go doda\u0107.","content-manager.components.notification.info.maximum-requirement":"Osi\u0105gi\u0119to maksymaln\u0105 liczb\u0119 p\xF3l","content-manager.components.notification.info.minimum-requirement":"Dodano pole spe\u0142niaj\u0105ce minimalne wymagania","content-manager.components.repeatable.reorder.error":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas zmiany pozycji komponentu, spr\xF3buj raz jeszcze","content-manager.components.reset-entry":"Zresetuj wpis","content-manager.components.uid.apply":"zastostuj","content-manager.components.uid.available":"Dost\u0119pny","content-manager.components.uid.regenerate":"Odn\xF3w","content-manager.components.uid.suggested":"zasugerowany","content-manager.components.uid.unavailable":"Niedost\u0119pny","content-manager.containers.Edit.Link.Layout":"Skonfiguruj uk\u0142ad","content-manager.containers.Edit.Link.Model":"Edytuj typ kolekcji","content-manager.containers.Edit.addAnItem":"Dodaj element...","content-manager.containers.Edit.clickToJump":"Kliknij aby przej\u015B\u0107 do elementu","content-manager.containers.Edit.delete":"Usu\u0144","content-manager.containers.Edit.delete-entry":"Usu\u0144 ten wpis","content-manager.containers.Edit.editing":"Edytowanie...","content-manager.containers.Edit.information":"Informacje","content-manager.containers.Edit.information.by":"Przez","content-manager.containers.Edit.information.created":"Stworzony","content-manager.containers.Edit.information.draftVersion":"wersja szkicu","content-manager.containers.Edit.information.editing":"Edytowanie","content-manager.containers.Edit.information.lastUpdate":"Ostatnia aktualizacja","content-manager.containers.Edit.information.publishedVersion":"wersja publikacji","content-manager.containers.Edit.pluginHeader.title.new":"Nowy wpis","content-manager.containers.Edit.reset":"Wyczy\u015B\u0107","content-manager.containers.Edit.returnList":"Wr\xF3\u0107 do listy","content-manager.containers.Edit.seeDetails":"Szczeg\xF3\u0142y","content-manager.containers.Edit.submit":"Prze\u015Blij","content-manager.containers.EditSettingsView.modal-form.edit-field":"Edytuj pole","content-manager.containers.EditView.add.new-entry":"Dodaj wpis","content-manager.containers.EditView.notification.errors":"Formularz zawiera b\u0142\u0119dy","content-manager.containers.Home.introduction":"Aby edytowa\u0107 wpisy przejd\u017A do odpowiedniego linku w menu po lewej. Ten plugin nie ma odpowiedniego sposobu na edytowanie ustawie\u0144 i nadal jest w trakcie rozwijania.","content-manager.containers.Home.pluginHeaderDescription":"Zarz\u0105dzaj swoimi danymi za pomoc\u0105 pot\u0119\u017Cnego i pi\u0119knego interfejsu.","content-manager.containers.Home.pluginHeaderTitle":"Tre\u015Bci","content-manager.containers.List.draft":"Szkic","content-manager.containers.List.errorFetchRecords":"B\u0142\u0105d","content-manager.containers.List.published":"Opublikowany","content-manager.containers.ListPage.displayedFields":"Wy\u015Bwietlone atrybuty","content-manager.containers.ListPage.items":"{number, plural, =0 {items} one {item} other {items}}","content-manager.containers.ListPage.table-headers.publishedAt":"Stan","content-manager.containers.ListSettingsView.modal-form.edit-label":"Edytuj etykiet\u0119","content-manager.containers.SettingPage.add.field":"Wstaw inne pole","content-manager.containers.SettingPage.attributes":"Pola atrybut\xF3w","content-manager.containers.SettingPage.attributes.description":"Zdefiniuj kolejno\u015B\u0107 atrybut\xF3w","content-manager.containers.SettingPage.editSettings.description":"Przeci\u0105gnij i upu\u015B pola by zbudowa\u0107 uk\u0142ad","content-manager.containers.SettingPage.editSettings.entry.title":"Tytu\u0142 wpisu","content-manager.containers.SettingPage.editSettings.entry.title.description":"Ustaw wy\u015Bwietlane pole swojego wpisu","content-manager.containers.SettingPage.editSettings.relation-field.description":"Ustaw wy\u015Bwietlane pole w obydwu widokach listy","content-manager.containers.SettingPage.editSettings.title":"Edycja (ustawienia)","content-manager.containers.SettingPage.layout":"Uk\u0142ad","content-manager.containers.SettingPage.listSettings.description":"Skonfiguruj opcje dla tego modelu","content-manager.containers.SettingPage.listSettings.title":"Lista (ustawienia)","content-manager.containers.SettingPage.pluginHeaderDescription":"Skonfiguruj konkretne ustawienia tego modelu","content-manager.containers.SettingPage.settings":"Ustawienia","content-manager.containers.SettingPage.view":"Widok","content-manager.containers.SettingViewModel.pluginHeader.title":"Mened\u017Cer tre\u015Bci  - {name}","content-manager.containers.SettingsPage.Block.contentType.description":"Skonfiguruj konkretne ustawienia","content-manager.containers.SettingsPage.Block.contentType.title":"Typy Kolekcji","content-manager.containers.SettingsPage.Block.generalSettings.description":"Skonfiguruj domy\u015Blne opcje dla twoich typ\xF3w kolekcji","content-manager.containers.SettingsPage.Block.generalSettings.title":"Og\xF3lne","content-manager.containers.SettingsPage.pluginHeaderDescription":"Skonfiguruj domy\u015Blne opcje wszystkich twoich modeli","content-manager.containers.SettingsView.list.subtitle":"Skonfiguruj uk\u0142ad i wy\u015Bwietlanie modeli i grup","content-manager.containers.SettingsView.list.title":"Wy\u015Bwietl ustawienia","content-manager.edit-settings-view.link-to-ctb.components":"Edytuj komponent","content-manager.edit-settings-view.link-to-ctb.content-types":"Edytuj","content-manager.emptyAttributes.button":"Przejd\u017A do konstruktora modeli","content-manager.emptyAttributes.description":"Dodaj swoje pierwszy atrybut do modelu","content-manager.emptyAttributes.title":"Nie ma jeszcze \u017Cadnych atrybut\xF3w","content-manager.error.attribute.key.taken":"Ta warto\u015B\u0107 ju\u017C istnieje","content-manager.error.attribute.sameKeyAndName":"Nie mog\u0105 by\u0107 takie same","content-manager.error.attribute.taken":"Atrybut o tej nazwie ju\u017C istnieje","content-manager.error.contentTypeName.taken":"Ta nazwa ju\u017C istnieje","content-manager.error.model.fetch":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas pobierania konfiguracji model\xF3w.","content-manager.error.record.create":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas tworzenia rekordu.","content-manager.error.record.delete":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas usuwania rekordu.","content-manager.error.record.fetch":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas pobierania rekordu.","content-manager.error.record.update":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas zmiany rekordu.","content-manager.error.records.count":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas liczenia rekord\xF3w.","content-manager.error.records.fetch":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas pobierania rekord\xF3w.","content-manager.error.schema.generation":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas generowania schematu.","content-manager.error.validation.json":"To nie jest JSON","content-manager.error.validation.max":"Warto\u015B\u0107 jest za wysoka.","content-manager.error.validation.maxLength":"Warto\u015B\u0107 jest za d\u0142uga.","content-manager.error.validation.min":"Warto\u015B\u0107 jest za niska.","content-manager.error.validation.minLength":"Warto\u015B\u0107 jest za kr\xF3tka.","content-manager.error.validation.minSupMax":"Nie mo\u017Ce by\u0107 wi\u0119ksza","content-manager.error.validation.regex":"Warto\u015B\u0107 nie jest zgodna z wymaganym wzorcem.","content-manager.error.validation.required":"Wpisanie warto\u015Bci dla tego atrybutu jest wymagane.","content-manager.form.Input.bulkActions":"W\u0142\u0105cz akcje masowe","content-manager.form.Input.defaultSort":"Domy\u015Blny atrybut sortowania","content-manager.form.Input.description":"Opis","content-manager.form.Input.description.placeholder":"Nazwa wy\u015Bwietlana","content-manager.form.Input.editable":"Edytowalne pole","content-manager.form.Input.filters":"W\u0142\u0105cz filtry","content-manager.form.Input.label":"Etykieta","content-manager.form.Input.label.inputDescription":"Ta warto\u015B\u0107 nadpisuje etykiet\u0119 wy\u015Bwietlan\u0105 w nag\u0142\xF3wku tabeli","content-manager.form.Input.pageEntries":"Wpisy na stron\u0119","content-manager.form.Input.pageEntries.inputDescription":"Uwaga: Mo\u017Cesz zmieni\u0107 t\u0119 warto\u015B\u0107 na stronie ustawie\u0144 modeli.","content-manager.form.Input.placeholder":"Placeholder","content-manager.form.Input.placeholder.placeholder":"Moja warto\u015B\u0107","content-manager.form.Input.search":"W\u0142\u0105cz wyszukiwanie","content-manager.form.Input.search.field":"W\u0142\u0105cz wyszukiwanie po tym polu","content-manager.form.Input.sort.field":"W\u0142\u0105cz sortowanie po tym polu","content-manager.form.Input.sort.order":"Domy\u015Blne sortowanie","content-manager.form.Input.wysiwyg":"Wy\u015Bwietl jako edytor WYSIWYG","content-manager.global.displayedFields":"Wy\u015Bwietlane pola","content-manager.groups":"Grupy","content-manager.groups.numbered":"Grupy ({number})","content-manager.header.name":"Zawarto\u015B\u0107","content-manager.link-to-ctb":"Edytuj model","content-manager.models":"Typy Kolekcji","content-manager.models.numbered":"Modele ({number})","content-manager.notification.error.displayedFields":"Co najmniej jedno pole musi by\u0107 wy\u015Bwietlane","content-manager.notification.error.relationship.fetch":"Wyst\u0105pi\u0142 b\u0142\u0105d podczas pobierania relacji.","content-manager.notification.info.SettingPage.disableSort":"Co najmniej jeden atrybut musi mie\u0107 w\u0142\u0105czon\u0105 mo\u017Cliwo\u015B\u0107 sortowania","content-manager.notification.info.minimumFields":"Musisz wy\u015Bwietli\u0107 przynajmniej jedno pole","content-manager.notification.upload.error":"Wyst\u0105pi\u0142 b\u0142ad podczas przesy\u0142ania plik\xF3w","content-manager.pageNotFound":"Strona nie znaleziona","content-manager.pages.ListView.header-subtitle":"{number, plural, =0 {#} one {# } other {# }} znaleziono","content-manager.pages.NoContentType.button":"Stw\xF3rz pierszy Content-Type","content-manager.pages.NoContentType.text":"Nie masz jeszcze \u017Cadnej zawarto\u015Bci. Polecamy stworzy\u0107 pierwszy Content-Type.","content-manager.permissions.not-allowed.create":"Brak uprawnie\u0144 do stworzenia dokumentu","content-manager.permissions.not-allowed.update":"Brak uprawnie\u0144 do odczytu dokumentu","content-manager.plugin.description.long":"Szybki spos\xF3b na przegl\u0105danie, zmian\u0119 i usuwanie element\xF3w z twojej bazy danych.","content-manager.plugin.description.short":"Szybki spos\xF3b na przegl\u0105danie, zmian\u0119 i usuwanie element\xF3w z twojej bazy danych.","content-manager.popover.display-relations.label":"Wy\u015Bwietl powi\u0105zania","content-manager.success.record.delete":"Usuni\u0119to","content-manager.success.record.publish":"Opublikowano","content-manager.success.record.save":"Zapisano","content-manager.success.record.unpublish":"Cofni\u0119to publikacj\u0119","content-manager.utils.data-loaded":"Uda\u0142o si\u0119 za\u0142adowa\u0107 wpis/wpisy.","content-manager.apiError.This attribute must be unique":"{field} musi by\u0107 unikalne","content-manager.popUpWarning.warning.publish-question":"Czy nadal chcesz to opublikowa\u0107?","content-manager.popUpwarning.warning.has-draft-relations.button-confirm":"Tak, opublikuj","form.button.continue":"Dalej","form.button.done":"Gotowe","global.actions":"Akcje","global.back":"Powr\xF3t","global.change-password":"Zmie\u0144 has\u0142o","global.content-manager":"Mened\u017Cer tre\u015Bci","global.continue":"Dalej","global.delete":"Usu\u0144","global.delete-target":"Usu\u0144 {target}","global.description":"Opis","global.details":"Szczeg\xF3\u0142y","global.disabled":"Wy\u0142\u0105czony","global.documentation":"Dokumentacja","global.enabled":"W\u0142\u0105czony","global.finish":"Zapisz","global.marketplace":"Sklep","global.name":"Nazwa","global.none":"None","global.password":"Has\u0142o","global.plugins":"Pluginy","global.profile":"Profil","global.prompt.unsaved":"Czy na pewno chcesz opu\u015Bci\u0107 t\u0119 stron\u0119? Wszystkie twoje modyfikacje zostan\u0105 utracone","global.reset-password":"Zresetuj has\u0142o","global.roles":"Role","global.save":"Zapisz","global.see-more":"Zobacz wi\u0119cej","global.select":"Wybierz","global.select-all-entries":"Wybierz wszystkie wpisy","global.settings":"Ustawienia","global.type":"Typ","global.users":"U\u017Cytkownicy","notification.contentType.relations.conflict":"Typ tre\u015Bci ma sprzeczne relacje","notification.default.title":"Informacja:","notification.error":"Wyst\u0105pi\u0142 b\u0142ad","notification.error.layout":"Nie uda\u0142o si\u0119 pobra\u0107 uk\u0142adu","notification.form.error.fields":"Ten formularz zawiera b\u0142\u0119dy","notification.form.success.fields":"Zapisano zmiany","notification.link-copied":"Link zosta\u0142 skopiowany do schowka","notification.permission.not-allowed-read":"Nie masz uprawnie\u0144, by zobaczy\u0107 ten dokument","notification.success.delete":"Pozycja zosta\u0142a usuni\u0119ta","notification.success.saved":"Zapisano","notification.success.title":"Uda\u0142o si\u0119:","notification.version.update.message":"Dost\u0119pna jest nowa wersja Strapi!","notification.warning.title":"Ostrze\u017Cenie:",or:d,"request.error.model.unknown":"Ten model nie istnieje",skipToContent:g,submit:w}}}]);
