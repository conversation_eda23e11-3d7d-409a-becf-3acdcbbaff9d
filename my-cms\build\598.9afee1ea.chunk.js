"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[598],{10598:(x,m,s)=>{s.d(m,{ProtectedListView:()=>rs});var t=s(92132),i=s(21272),R=s(4198),U=s(55356),C=s(25815),I=s(38413),o=s(55506),r=s(5194),a=s(5409),l=s(54894),e=s(17703),W=s(43543),K=s(34819),g=s(99831),B=s(22185),u=s(15126),z=s(63299),F=s(67014),V=s(59080),G=s(79275),Q=s(14718),p=s(82437),Y=s(61535),J=s(5790),H=s(12083),X=s(35223),n=s(74930),d=s(2600),T=s(48940),D=s(41286),P=s(56336),M=s(13426),E=s(84624),L=s(77965),O=s(54257),y=s(71210),Z=s(51187),w=s(39404),b=s(58692),q=s(501),ss=s(57646),_=s(23120),ts=s(44414),S=s(25962),Os=s(14664),ms=s(42588),As=s(90325),fs=s(62785),cs=s(87443),Cs=s(41032),gs=s(22957),Ls=s(93179),hs=s(73055),vs=s(15747),Rs=s(85306),Us=s(26509),Is=s(32058),Ws=s(81185),Bs=s(82261);const _s=[{name:"name",key:"name",metadatas:{label:{id:"Settings.tokens.ListView.headers.name",defaultMessage:"Name"},sortable:!0}},{name:"description",key:"description",metadatas:{label:{id:"Settings.tokens.ListView.headers.description",defaultMessage:"Description"},sortable:!1}},{name:"createdAt",key:"createdAt",metadatas:{label:{id:"Settings.tokens.ListView.headers.createdAt",defaultMessage:"Created at"},sortable:!1}},{name:"lastUsedAt",key:"lastUsedAt",metadatas:{label:{id:"Settings.tokens.ListView.headers.lastUsedAt",defaultMessage:"Last used"},sortable:!1}}],os=()=>{(0,o.L4)();const{formatMessage:A}=(0,l.A)(),h=(0,o.hN)(),ds=(0,W.j)(c=>c.admin_app.permissions.settings?.["transfer-tokens"]),{isLoading:Es,allowedActions:{canCreate:k,canDelete:is,canUpdate:ls,canRead:v}}=(0,o.ec)(ds),{push:as}=(0,e.W6)(),{trackUsage:j}=(0,o.z1)(),{_unstableFormatAPIError:N}=(0,o.wq)();i.useEffect(()=>{as({search:a.stringify({sort:"name:ASC"},{encode:!1})})},[as]),(0,W.b)(()=>{j("willAccessTokenList",{tokenType:g.T})});const Ms=_s.map(c=>({...c,metadatas:{...c.metadatas,label:A(c.metadatas.label)}})),{data:f=[],isLoading:Ts,error:$}=(0,K.c)(void 0,{skip:!v});i.useEffect(()=>{f&&j("didAccessTokenList",{number:f.length,tokenType:g.T})},[j,f]),i.useEffect(()=>{$&&h({type:"warning",message:N($)})},[$,N,h]);const[Ds]=(0,K.d)(),Ps=async c=>{try{const ns=await Ds(c);"error"in ns&&h({type:"warning",message:N(ns.error)})}catch{h({type:"warning",message:{id:"notification.error",defaultMessage:"An error occured"}})}},es=Ts||Es;return(0,t.jsxs)(I.g,{"aria-busy":es,children:[(0,t.jsx)(o.x7,{name:"Transfer Tokens"}),(0,t.jsx)(U.Q,{title:A({id:"Settings.transferTokens.title",defaultMessage:"Transfer Tokens"}),subtitle:A({id:"Settings.transferTokens.description",defaultMessage:'"List of generated transfer tokens"'}),primaryAction:k?(0,t.jsx)(C.z,{"data-testid":"create-transfer-token-button",startIcon:(0,t.jsx)(r.A,{}),size:"S",onClick:()=>j("willAddTokenFromList",{tokenType:g.T}),to:"/settings/transfer-tokens/create",children:A({id:"Settings.transferTokens.create",defaultMessage:"Create new Transfer Token"})}):void 0}),(0,t.jsxs)(R.s,{children:[!v&&(0,t.jsx)(o.UW,{}),v&&f.length>0&&(0,t.jsx)(B.T,{permissions:{canRead:v,canDelete:is,canUpdate:ls},headers:Ms,contentType:"trasfer-tokens",isLoading:es,onConfirmDelete:Ps,tokens:f,tokenType:g.T}),v&&k&&f.length===0&&(0,t.jsx)(o.R1,{content:{id:"Settings.transferTokens.addFirstToken",defaultMessage:"Add your first Transfer Token"},action:(0,t.jsx)(C.z,{variant:"secondary",startIcon:(0,t.jsx)(r.A,{}),to:"/settings/transfer-tokens/create",children:A({id:"Settings.transferTokens.addNewToken",defaultMessage:"Add new Transfer Token"})})}),v&&!k&&f.length===0&&(0,t.jsx)(o.R1,{content:{id:"Settings.transferTokens.emptyStateLayout",defaultMessage:"You don\u2019t have any content yet..."}})]})]})},rs=()=>{const A=(0,W.j)(h=>h.admin_app.permissions.settings?.["transfer-tokens"].main);return(0,t.jsx)(o.kz,{permissions:A,children:(0,t.jsx)(os,{})})}},22185:(x,m,s)=>{s.d(m,{T:()=>V});var t=s(92132),i=s(21272),R=s(94061),U=s(88353),C=s(83997),I=s(25641),o=s(90361),r=s(33363),a=s(30893),l=s(21610),e=s(55506),W=s(83925),K=s(41909),g=s(50612),B=s(54894),u=s(17703),z=s(71389),F=s(63891);const V=({permissions:n,headers:d=[],contentType:T,isLoading:D=!1,tokens:P=[],onConfirmDelete:M,tokenType:E})=>{const{canDelete:L,canUpdate:O,canRead:y}=n;return(0,t.jsx)(e.Ee,{headers:d,contentType:T,rows:P,withBulkActions:L||O||y,isLoading:D,onConfirmDelete:M,children:(0,t.jsx)(G,{tokenType:E,permissions:n,onConfirmDelete:M})})},G=({tokenType:n,permissions:d,rows:T=[],withBulkActions:D,onConfirmDelete:P})=>{const{canDelete:M,canUpdate:E,canRead:L}=d,[{query:O}]=(0,e.sq)(),{formatMessage:y}=(0,B.A)(),[,Z]=O&&O.sort?O.sort.split(":"):[void 0,"ASC"],{push:w,location:{pathname:b}}=(0,u.W6)(),{trackUsage:q}=(0,e.z1)(),ss=[...T].sort((_,ts)=>{const S=_.name.localeCompare(ts.name);return Z==="DESC"?-S:S});return(0,t.jsx)(I.N,{children:ss.map(_=>(0,t.jsxs)(o.Tr,{...(0,e.qM)({fn(){q("willEditTokenFromList",{tokenType:n}),w(`${b}/${_.id}`)},condition:E}),children:[(0,t.jsx)(r.Td,{maxWidth:(0,e.a8)(250),children:(0,t.jsx)(a.o,{textColor:"neutral800",fontWeight:"bold",ellipsis:!0,children:_.name})}),(0,t.jsx)(r.Td,{maxWidth:(0,e.a8)(250),children:(0,t.jsx)(a.o,{textColor:"neutral800",ellipsis:!0,children:_.description})}),(0,t.jsx)(r.Td,{children:(0,t.jsx)(a.o,{textColor:"neutral800",children:(0,t.jsx)(e.sR,{timestamp:new Date(_.createdAt)})})}),(0,t.jsx)(r.Td,{children:_.lastUsedAt&&(0,t.jsx)(a.o,{textColor:"neutral800",children:(0,t.jsx)(e.sR,{timestamp:new Date(_.lastUsedAt),customIntervals:[{unit:"hours",threshold:1,text:y({id:"Settings.apiTokens.lastHour",defaultMessage:"last hour"})}]})})}),D&&(0,t.jsx)(r.Td,{children:(0,t.jsxs)(C.s,{justifyContent:"end",children:[E&&(0,t.jsx)(X,{tokenName:_.name,tokenId:_.id}),!E&&L&&(0,t.jsx)(H,{tokenName:_.name,tokenId:_.id}),M&&(0,t.jsx)(J,{tokenName:_.name,onClickDelete:()=>P?.(_.id),tokenType:n})]})})]},_.id))})},Q={edit:{id:"app.component.table.edit",defaultMessage:"Edit {target}"},read:{id:"app.component.table.read",defaultMessage:"Read {target}"}},p=({tokenName:n,tokenId:d,buttonType:T="edit",children:D})=>{const{formatMessage:P}=(0,B.A)(),{location:{pathname:M}}=(0,u.W6)();return(0,t.jsx)(Y,{forwardedAs:z.k2,to:`${M}/${d}`,title:P(Q[T],{target:n}),children:D})},Y=(0,F.Ay)(l.N)`
  svg {
    path {
      fill: ${({theme:n})=>n.colors.neutral500};
    }
  }

  &:hover,
  &:focus {
    svg {
      path {
        fill: ${({theme:n})=>n.colors.neutral800};
      }
    }
  }
`,J=({tokenName:n,onClickDelete:d,tokenType:T})=>{const{formatMessage:D}=(0,B.A)(),{trackUsage:P}=(0,e.z1)(),[M,E]=i.useState(!1),L=()=>{E(!1),P("willDeleteToken",{tokenType:T}),d()};return(0,t.jsxs)(R.a,{paddingLeft:1,onClick:O=>O.stopPropagation(),children:[(0,t.jsx)(U.K,{onClick:()=>{E(!0)},label:D({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:`${n}`}),name:"delete",borderWidth:0,icon:(0,t.jsx)(g.A,{})}),(0,t.jsx)(e.TM,{onToggleDialog:()=>E(!1),onConfirm:L,isOpen:M})]})},H=({tokenName:n,tokenId:d})=>(0,t.jsx)(p,{tokenName:n,tokenId:d,buttonType:"read",children:(0,t.jsx)(W.A,{})}),X=({tokenName:n,tokenId:d})=>(0,t.jsx)(p,{tokenName:n,tokenId:d,children:(0,t.jsx)(K.A,{width:12})})},34819:(x,m,s)=>{s.d(m,{a:()=>C,b:()=>o,c:()=>R,d:()=>I,u:()=>U});var t=s(43543);const i=t.n.injectEndpoints({endpoints:r=>({getTransferTokens:r.query({query:()=>({url:"/admin/transfer/tokens",method:"GET"}),transformResponse:a=>a.data,providesTags:(a,l)=>[...a?.map(({id:e})=>({type:"TransferToken",id:e}))??[],{type:"TransferToken",id:"LIST"}]}),getTransferToken:r.query({query:a=>`/admin/transfer/tokens/${a}`,transformResponse:a=>a.data,providesTags:(a,l,e)=>[{type:"TransferToken",id:e}]}),createTransferToken:r.mutation({query:a=>({url:"/admin/transfer/tokens",method:"POST",data:a}),transformResponse:a=>a.data,invalidatesTags:[{type:"TransferToken",id:"LIST"}]}),deleteTransferToken:r.mutation({query:a=>({url:`/admin/transfer/tokens/${a}`,method:"DELETE"}),transformResponse:a=>a.data,invalidatesTags:(a,l,e)=>[{type:"TransferToken",id:e}]}),updateTransferToken:r.mutation({query:({id:a,...l})=>({url:`/admin/transfer/tokens/${a}`,method:"PUT",data:l}),transformResponse:a=>a.data,invalidatesTags:(a,l,{id:e})=>[{type:"TransferToken",id:e}]})}),overrideExisting:!1}),{useGetTransferTokensQuery:R,useGetTransferTokenQuery:U,useCreateTransferTokenMutation:C,useDeleteTransferTokenMutation:I,useUpdateTransferTokenMutation:o}=i},99831:(x,m,s)=>{s.d(m,{A:()=>t,T:()=>i});const t="api-token",i="transfer-token"}}]);
