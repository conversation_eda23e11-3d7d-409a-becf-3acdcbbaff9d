"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[2466,6153],{28026:(X,L,e)=>{e.d(L,{u:()=>O});var s=e(43543);const m=s.n.injectEndpoints({endpoints:M=>({getWebhooks:M.query({query:o=>({url:`/admin/webhooks/${o?.id??""}`,method:"GET"}),transformResponse:o=>Array.isArray(o.data)?o.data:[o.data],providesTags:(o,_,D)=>typeof D=="object"&&"id"in D?[{type:"Webhook",id:D.id}]:[...o?.map(({id:y})=>({type:"Webhook",id:y}))??[],{type:"Webhook",id:"LIST"}]}),createWebhook:M.mutation({query:o=>({url:"/admin/webhooks",method:"POST",data:o}),transformResponse:o=>o.data,invalidatesTags:[{type:"Webhook",id:"LIST"}]}),updateWebhook:M.mutation({query:({id:o,..._})=>({url:`/admin/webhooks/${o}`,method:"PUT",data:_}),transformResponse:o=>o.data,invalidatesTags:(o,_,{id:D})=>[{type:"Webhook",id:D}]}),triggerWebhook:M.mutation({query:o=>({url:`/admin/webhooks/${o}/trigger`,method:"POST"}),transformResponse:o=>o.data}),deleteManyWebhooks:M.mutation({query:o=>({url:"/admin/webhooks/batch-delete",method:"POST",data:o}),transformResponse:o=>o.data,invalidatesTags:(o,_,{ids:D})=>D.map(y=>({type:"Webhook",id:y}))})}),overrideExisting:!1}),{useGetWebhooksQuery:K,useCreateWebhookMutation:T,useUpdateWebhookMutation:U,useTriggerWebhookMutation:S,useDeleteManyWebhooksMutation:b}=m,O=(M=void 0,o)=>{const{data:_,isLoading:D,error:y}=K(M,o),[k,{error:j}]=T(),[H,{error:x}]=U(),[F]=S(),[z]=b();return{webhooks:_,isLoading:D,error:y||j||x,createWebhook:k,updateWebhook:H,triggerWebhook:F,deleteManyWebhooks:z}}},46153:(X,L,e)=>{e.r(L),e.d(L,{E:()=>oe,a:()=>V,b:()=>Se});var s=e(92132),m=e(21272),K=e(50215),T=e(94061),U=e(85963),S=e(4181),b=e(76517),O=e(80782),M=e(8361),o=e(90151),_=e(68074),D=e(4198),y=e(55356),k=e(43064),j=e(38413),H=e(46462),x=e(81621),F=e(66809),z=e(379),J=e(84843),u=e(83997),ie=e(18629),G=e(7537),W=e(30893),de=e(98765),R=e(55506),le=e(71389),Z=e(17703),N=e(43543),w=e(98898),_e=e(66248),Ee=e(21610),he=e(46270),q=e(54514),ee=e(68802),ce=e(84795),ge=e(98052),Me=e(5194),C=e(61535),p=e(54894),I=e(12083),Pe=e(89032),Q=e(63891),me=e(28026);const[ue,se]=(0,Pe.q)("WebhookEvent"),De=({children:t})=>{const{formatMessage:n}=(0,p.A)(),{collectionTypes:a,isLoading:E}=(0,w.u)(),r=m.useMemo(()=>a.some(i=>i.options?.draftAndPublish===!0),[a]),h=n({id:"Settings.webhooks.form.events",defaultMessage:"Events"});return(0,s.jsx)(ue,{isDraftAndPublish:r,children:(0,s.jsxs)(u.s,{direction:"column",alignItems:"stretch",gap:1,children:[(0,s.jsx)(M.d,{"aria-hidden":!0,children:h}),E&&(0,s.jsx)(k.a,{children:n({id:"Settings.webhooks.events.isLoading",defaultMessage:"Events loading"})}),(0,s.jsx)(Oe,{"aria-label":h,children:t})]})})},Oe=(0,Q.Ay)(H.X)`
  tbody tr:nth-child(odd) {
    background: ${({theme:t})=>t.colors.neutral100};
  }

  thead th span {
    color: ${({theme:t})=>t.colors.neutral500};
  }

  td,
  th {
    padding-block-start: ${({theme:t})=>t.spaces[3]};
    padding-block-end: ${({theme:t})=>t.spaces[3]};
    width: 10%;
    vertical-align: middle;
    text-align: center;
  }

  tbody tr td:first-child {
    /**
     * Add padding to the start of the first column to avoid the checkbox appearing
     * too close to the edge of the table
     */
    padding-inline-start: ${({theme:t})=>t.spaces[2]};
  }
`,ve=t=>{const n=[{id:"Settings.webhooks.events.create",defaultMessage:"Create"},{id:"Settings.webhooks.events.update",defaultMessage:"Update"},{id:"app.utils.delete",defaultMessage:"Delete"}];return t&&(n.push({id:"app.utils.publish",defaultMessage:"Publish"}),n.push({id:"app.utils.unpublish",defaultMessage:"Unpublish"})),n},fe=({getHeaders:t=ve})=>{const{isDraftAndPublish:n}=se("Headers"),{formatMessage:a}=(0,p.A)(),E=t(n);return(0,s.jsx)(F.r,{children:(0,s.jsxs)(J.N,{children:[(0,s.jsx)(x.e,{children:(0,s.jsx)(de.s,{children:a({id:"Settings.webhooks.event.select",defaultMessage:"Select event"})})}),E.map(r=>["app.utils.publish","app.utils.unpublish"].includes(r?.id??"")?(0,s.jsx)(x.e,{title:a({id:"Settings.webhooks.event.publish-tooltip",defaultMessage:"This event only exists for content with draft & publish enabled"}),children:(0,s.jsx)(W.o,{variant:"sigma",textColor:"neutral600",children:a(r)})},r.id):(0,s.jsx)(x.e,{children:(0,s.jsx)(W.o,{variant:"sigma",textColor:"neutral600",children:a(r)})},r.id))]})})},Ae=({providedEvents:t})=>{const{isDraftAndPublish:n}=se("Body"),a=t||Ce(n),{values:E,handleChange:r}=(0,C.j7)(),h="events",i=E.events,P=[],v=i.reduce((d,l)=>{const g=l.split(".")[0];return d[g]||(d[g]=[]),d[g].push(l),d},{}),c=({target:{name:d,value:l}})=>{const g=new Set(i);l?g.add(d):g.delete(d),r({target:{name:h,value:Array.from(g)}})},f=({target:{name:d,value:l}})=>{const g=new Set(i);l?a[d].forEach(B=>{P.includes(B)||g.add(B)}):a[d].forEach(B=>g.delete(B)),r({target:{name:h,value:Array.from(g)}})};return(0,s.jsx)(z.f,{children:Object.entries(a).map(([d,l])=>(0,s.jsx)(te,{disabledEvents:P,name:d,events:l,inputValue:v[d],handleSelect:c,handleSelectAll:f},d))})},Ce=t=>{const n=["entry.create","entry.update","entry.delete"];return t&&n.push("entry.publish","entry.unpublish"),{entry:n,media:["media.create","media.update","media.delete"]}},te=({disabledEvents:t=[],name:n,events:a=[],inputValue:E=[],handleSelect:r,handleSelectAll:h})=>{const{formatMessage:i}=(0,p.A)(),P=a.filter(l=>!t.includes(l)),v=E.length>0,c=E.length===P.length,f=({target:{name:l}})=>{h({target:{name:l,value:!c}})},d=5;return(0,s.jsxs)(J.N,{children:[(0,s.jsx)(x.N,{children:(0,s.jsx)(S.S,{indeterminate:v&&!c,"aria-label":i({id:"global.select-all-entries",defaultMessage:"Select all entries"}),name:n,onChange:f,value:c,children:Te(n)})}),a.map(l=>(0,s.jsx)(x.N,{children:(0,s.jsx)(K.J,{disabled:t.includes(l),"aria-label":l,name:l,value:E.includes(l),onValueChange:g=>r({target:{name:l,value:g}})})},l)),a.length<d&&(0,s.jsx)(x.N,{colSpan:d-a.length})]})},Te=t=>t.replace(/-/g," ").split(" ").map(n=>n.charAt(0).toUpperCase()+n.slice(1).toLowerCase()).join(" "),V={Root:De,Headers:fe,Body:Ae,Row:te},ne=()=>(0,s.jsxs)(V.Root,{children:[(0,s.jsx)(V.Headers,{}),(0,s.jsx)(V.Body,{})]}),ae=()=>{const{formatMessage:t}=(0,p.A)(),{values:n,errors:a}=(0,C.j7)();return(0,s.jsxs)(u.s,{direction:"column",alignItems:"stretch",gap:1,children:[(0,s.jsx)(M.d,{children:t({id:"Settings.webhooks.form.headers",defaultMessage:"Headers"})}),(0,s.jsx)(T.a,{padding:8,background:"neutral100",hasRadius:!0,children:(0,s.jsx)(C.ED,{validateOnChange:!1,name:"headers",render:({push:E,remove:r})=>(0,s.jsxs)(o.x,{gap:4,children:[n.headers.map((h,i)=>{const P=a.headers?.[i],v=typeof P=="object"?P.key:void 0,c=typeof P=="object"?P.value:void 0;return(0,s.jsxs)(m.Fragment,{children:[(0,s.jsx)(_.E,{col:6,children:(0,s.jsx)(C.D0,{as:Ie,name:`headers.${i}.key`,"aria-label":`row ${i+1} key`,label:t({id:"Settings.webhooks.key",defaultMessage:"Key"}),error:v})}),(0,s.jsx)(_.E,{col:6,children:(0,s.jsxs)(u.s,{alignItems:"flex-end",children:[(0,s.jsx)(T.a,{style:{flex:1},children:(0,s.jsx)(C.D0,{as:G.k,name:`headers.${i}.value`,"aria-label":`row ${i+1} value`,label:t({id:"Settings.webhooks.value",defaultMessage:"Value"}),error:c})}),(0,s.jsx)(u.s,{paddingLeft:2,style:{alignSelf:"center"},paddingTop:c?0:5,children:(0,s.jsx)(R.yX,{disabled:n.headers.length===1,onClick:()=>r(i),label:t({id:"Settings.webhooks.headers.remove",defaultMessage:"Remove header row {number}"},{number:i+1})})})]})})]},`${i}.${h.key}`)}),(0,s.jsx)(_.E,{col:12,children:(0,s.jsx)(ie.Q,{type:"button",onClick:()=>{E({key:"",value:""})},startIcon:(0,s.jsx)(Me.A,{}),children:t({id:"Settings.webhooks.create.header",defaultMessage:"Create new header"})})})]})})})]})},Ie=({name:t,onChange:n,value:a,...E})=>{const{values:{headers:r}}=(0,C.j7)(),[h,i]=m.useState([...ye]);m.useEffect(()=>{const c=ye.filter(f=>!r?.some(d=>d.key!==a&&d.key===f));i(c)},[r,a]);const P=c=>{n({target:{name:t,value:c}})},v=c=>{i(f=>[...f,c]),P(c)};return(0,s.jsx)(b.nP,{...E,onClear:()=>P(""),onChange:P,onCreateOption:v,placeholder:"",value:a,children:h.map(c=>(0,s.jsx)(O.j,{value:c,children:c},c))})},ye=["A-IM","Accept","Accept-Charset","Accept-Encoding","Accept-Language","Accept-Datetime","Access-Control-Request-Method","Access-Control-Request-Headers","Authorization","Cache-Control","Connection","Content-Length","Content-Type","Cookie","Date","Expect","Forwarded","From","Host","If-Match","If-Modified-Since","If-None-Match","If-Range","If-Unmodified-Since","Max-Forwards","Origin","Pragma","Proxy-Authorization","Range","Referer","TE","User-Agent","Upgrade","Via","Warning"],xe=({isPending:t,onCancel:n,response:a})=>{const{statusCode:E,message:r}=a??{},{formatMessage:h}=(0,p.A)();return(0,s.jsx)(T.a,{background:"neutral0",padding:5,shadow:"filterShadow",hasRadius:!0,children:(0,s.jsxs)(o.x,{gap:4,style:{alignItems:"center"},children:[(0,s.jsx)(_.E,{col:3,children:(0,s.jsx)(W.o,{children:h({id:"Settings.webhooks.trigger.test",defaultMessage:"Test-trigger"})})}),(0,s.jsx)(_.E,{col:3,children:(0,s.jsx)(Re,{isPending:t,statusCode:E})}),(0,s.jsx)(_.E,{col:6,children:t?(0,s.jsx)(u.s,{justifyContent:"flex-end",children:(0,s.jsx)("button",{onClick:n,type:"button",children:(0,s.jsxs)(u.s,{gap:2,alignItems:"center",children:[(0,s.jsx)(W.o,{textColor:"neutral400",children:h({id:"Settings.webhooks.trigger.cancel",defaultMessage:"cancel"})}),(0,s.jsx)(Y,{as:ee.A,color:"neutral400"})]})})}):(0,s.jsx)(pe,{statusCode:E,message:r})})]})})},Y=Q.Ay.svg(({theme:t,color:n})=>`
  width: ${12/16}rem;
  height: ${12/16}rem;


  ${n?(0,Q.AH)`
          path {
            fill: ${t.colors[n]};
          }
        `:""}
`),Re=({isPending:t,statusCode:n})=>{const{formatMessage:a}=(0,p.A)();return t||!n?(0,s.jsxs)(u.s,{gap:2,alignItems:"center",children:[(0,s.jsx)(Y,{as:ce.A}),(0,s.jsx)(W.o,{children:a({id:"Settings.webhooks.trigger.pending",defaultMessage:"pending"})})]}):n>=200&&n<300?(0,s.jsxs)(u.s,{gap:2,alignItems:"center",children:[(0,s.jsx)(Y,{as:q.A,color:"success700"}),(0,s.jsx)(W.o,{children:a({id:"Settings.webhooks.trigger.success",defaultMessage:"success"})})]}):n>=300?(0,s.jsxs)(u.s,{gap:2,alignItems:"center",children:[(0,s.jsx)(Y,{as:ee.A,color:"danger700"}),(0,s.jsxs)(W.o,{children:[a({id:"Settings.error",defaultMessage:"error"})," ",n]})]}):null},pe=({statusCode:t,message:n})=>{const{formatMessage:a}=(0,p.A)();return t?t>=200&&t<300?(0,s.jsx)(u.s,{justifyContent:"flex-end",children:(0,s.jsx)(W.o,{textColor:"neutral600",ellipsis:!0,children:a({id:"Settings.webhooks.trigger.success.label",defaultMessage:"Trigger succeeded"})})}):t>=300?(0,s.jsx)(u.s,{justifyContent:"flex-end",children:(0,s.jsx)(u.s,{maxWidth:(0,R.a8)(250),justifyContent:"flex-end",title:n,children:(0,s.jsx)(W.o,{ellipsis:!0,textColor:"neutral600",children:n})})}):null:null},Le=({handleSubmit:t,triggerWebhook:n,isCreating:a,isTriggering:E,triggerResponse:r,data:h})=>{const{formatMessage:i}=(0,p.A)(),[P,v]=m.useState(!1),c=(0,N.p)(ne,async()=>(await e.e(7063).then(e.bind(e,27063))).EventsTableEE),f=l=>Object.keys(l).length?Object.entries(l).map(([g,B])=>({key:g,value:B})):[{key:"",value:""}],d=(0,C.Wx)({initialValues:{name:h?.name||"",url:h?.url||"",headers:f(h?.headers||{}),events:h?.events||[]},async onSubmit(l,g){await t(l,g),g.resetForm({values:l})},validationSchema:Ke({formatMessage:i}),validateOnChange:!1,validateOnBlur:!1});return c?(0,s.jsx)(C.QI,{value:d,children:(0,s.jsxs)(R.lV,{children:[(0,s.jsx)(y.Q,{primaryAction:(0,s.jsxs)(u.s,{gap:2,children:[(0,s.jsx)(U.$,{onClick:()=>{n(),v(!0)},variant:"tertiary",startIcon:(0,s.jsx)(ge.A,{}),disabled:a||E,size:"L",children:i({id:"Settings.webhooks.trigger",defaultMessage:"Trigger"})}),(0,s.jsx)(U.$,{startIcon:(0,s.jsx)(q.A,{}),type:"submit",size:"L",disabled:!d.dirty,loading:d.isSubmitting,children:i({id:"global.save",defaultMessage:"Save"})})]}),title:a?i({id:"Settings.webhooks.create",defaultMessage:"Create a webhook"}):h?.name,navigationAction:(0,s.jsx)(Ee.N,{as:le.k2,startIcon:(0,s.jsx)(he.A,{}),to:"/settings/webhooks",children:i({id:"global.back",defaultMessage:"Back"})})}),(0,s.jsx)(D.s,{children:(0,s.jsxs)(u.s,{direction:"column",alignItems:"stretch",gap:4,children:[P&&(0,s.jsx)(xe,{isPending:E,response:r,onCancel:()=>v(!1)}),(0,s.jsx)(T.a,{background:"neutral0",padding:8,shadow:"filterShadow",hasRadius:!0,children:(0,s.jsxs)(u.s,{direction:"column",alignItems:"stretch",gap:6,children:[(0,s.jsxs)(o.x,{gap:6,children:[(0,s.jsx)(_.E,{col:6,children:(0,s.jsx)(C.D0,{as:G.k,name:"name",error:d.errors.name,label:i({id:"global.name",defaultMessage:"Name"}),required:!0})}),(0,s.jsx)(_.E,{col:12,children:(0,s.jsx)(C.D0,{as:G.k,name:"url",error:d.errors.url,label:i({id:"Settings.roles.form.input.url",defaultMessage:"Url"}),required:!0})})]}),(0,s.jsx)(ae,{}),(0,s.jsx)(c,{})]})})]})})]})}):null},Ue=/(^$)|(^[A-Za-z][_0-9A-Za-z ]*$)/,Be=/(^$)|((https?:\/\/.*)(d*)\/?(.*))/,Ke=({formatMessage:t})=>I.Ik().shape({name:I.Yj().required(t({id:"Settings.webhooks.validation.name.required",defaultMessage:"Name is required"})).matches(Ue,t({id:"Settings.webhooks.validation.name.regex",defaultMessage:"The name must start with a letter and only contain letters, numbers, spaces and underscores"})),url:I.Yj().required(t({id:"Settings.webhooks.validation.url.required",defaultMessage:"Url is required"})).matches(Be,t({id:"Settings.webhooks.validation.url.regex",defaultMessage:"The value must be a valid Url"})),headers:I.RZ(n=>{const a=I.YO();if(n.length===1){const{key:E,value:r}=n[0];if(!E&&!r)return a}return a.of(I.Ik().shape({key:I.Yj().required(t({id:"Settings.webhooks.validation.key",defaultMessage:"Key is required"})),value:I.Yj().required(t({id:"Settings.webhooks.validation.value",defaultMessage:"Value is required"}))}))}),events:I.YO()}),je=t=>({...t,headers:t.headers.reduce((n,{key:a,value:E})=>(a!==""&&(n[a]=E),n),{})}),oe=()=>{const n=(0,Z.W5)("/settings/webhooks/:id")?.params.id,a=n==="create",{replace:E}=(0,Z.W6)(),r=(0,R.hN)(),{_unstableFormatAPIError:h,_unstableFormatValidationErrors:i}=(0,R.wq)(),P=m.useCallback(h,[]),{isLoading:v}=(0,w.u)(),[c,f]=m.useState(!1),[d,l]=m.useState(),{isLoading:g,webhooks:B,error:re,createWebhook:be,updateWebhook:ke,triggerWebhook:$e}=(0,me.u)({id:n},{skip:a});m.useEffect(()=>{re&&r({type:"warning",message:P(re)})},[re,r,P]);const Ne=async()=>{try{f(!0);const $=await $e(n);if("error"in $){r({type:"warning",message:h($.error)});return}l($.data)}catch{r({type:"warning",message:{id:"notification.error",defaultMessage:"An error occurred"}})}finally{f(!1)}},Ve=async($,We)=>{try{if(a){const A=await be(je($));if("error"in A){(0,N.x)(A.error)&&A.error.name==="ValidationError"?We.setErrors(i(A.error)):r({type:"warning",message:h(A.error)});return}r({type:"success",message:{id:"Settings.webhooks.created"}}),E(`/settings/webhooks/${A.data.id}`)}else{const A=await ke({id:n,...je($)});if("error"in A){(0,N.x)(A.error)&&A.error.name==="ValidationError"?We.setErrors(i(A.error)):r({type:"warning",message:h(A.error)});return}r({type:"success",message:{id:"notification.form.success.fields"}})}}catch{r({type:"warning",message:{id:"notification.error",defaultMessage:"An error occurred"}})}};if(g||v)return(0,s.jsx)(R.Bl,{});const[He]=B??[];return(0,s.jsxs)(j.g,{children:[(0,s.jsx)(R.x7,{name:"Webhooks"}),(0,s.jsx)(Le,{data:He,handleSubmit:Ve,triggerWebhook:Ne,isCreating:a,isTriggering:c,triggerResponse:d})]})},Se=Object.freeze(Object.defineProperty({__proto__:null,EditPage:oe,ProtectedEditPage:()=>{const t=(0,N.j)(_e.s);return(0,s.jsx)(R.kz,{permissions:t.settings?.webhooks.update,children:(0,s.jsx)(oe,{})})}},Symbol.toStringTag,{value:"Module"}))},52466:(X,L,e)=>{e.d(L,{ProtectedCreatePage:()=>ne});var s=e(92132),m=e(55506),K=e(43543),T=e(66248),U=e(46153),S=e(15126),b=e(63299),O=e(67014),M=e(59080),o=e(79275),_=e(14718),D=e(21272),y=e(82437),k=e(61535),j=e(5790),H=e(12083),x=e(35223),F=e(5409),z=e(74930),J=e(2600),u=e(48940),ie=e(41286),G=e(56336),W=e(13426),de=e(84624),R=e(77965),le=e(54257),Z=e(71210),N=e(51187),w=e(39404),_e=e(58692),Ee=e(501),he=e(57646),q=e(23120),ee=e(44414),ce=e(25962),ge=e(14664),Me=e(42588),C=e(90325),p=e(62785),I=e(87443),Pe=e(41032),Q=e(22957),me=e(93179),ue=e(73055),se=e(15747),De=e(85306),Oe=e(26509),ve=e(32058),fe=e(81185),Ae=e(82261),Ce=e(55151),te=e(79077),Te=e(98898),V=e(28026);const ne=()=>{const ae=(0,K.j)(T.s);return(0,s.jsx)(m.kz,{permissions:ae.settings?.webhooks.create,children:(0,s.jsx)(U.E,{})})}},98898:(X,L,e)=>{e.d(L,{u:()=>b});var s=e(21272),m=e(55506),K=e(43543);const T=K.n.injectEndpoints({endpoints:O=>({getComponents:O.query({query:()=>({url:"/content-manager/components",method:"GET"}),transformResponse:M=>M.data}),getContentTypes:O.query({query:()=>({url:"/content-manager/content-types",method:"GET"}),transformResponse:M=>M.data})}),overrideExisting:!1}),{useGetComponentsQuery:U,useGetContentTypesQuery:S}=T;function b(){const{_unstableFormatAPIError:O}=(0,m.wq)(),M=(0,m.hN)(),o=U(),_=S();s.useEffect(()=>{_.error&&M({type:"warning",message:O(_.error)})},[_.error,O,M]),s.useEffect(()=>{o.error&&M({type:"warning",message:O(o.error)})},[o.error,O,M]);const D=o.isLoading||_.isLoading,y=s.useMemo(()=>(_?.data??[]).filter(j=>j.kind==="collectionType"&&j.isDisplayed),[_?.data]),k=s.useMemo(()=>(_?.data??[]).filter(j=>j.kind!=="collectionType"&&j.isDisplayed),[_?.data]);return{isLoading:D,components:s.useMemo(()=>o?.data??[],[o?.data]),collectionTypes:y,singleTypes:k}}}}]);
