"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[2301],{92301:(D,a,s)=>{s.r(a),s.d(a,{InternalErrorPage:()=>l});var t=s(92132),n=s(53563),E=s(4198),o=s(55356),e=s(25815),d=s(38413),r=s(55506),i=s(72417),M=s(89092),P=s(54894);const l=()=>{const{formatMessage:_}=(0,P.A)();return(0,r.L4)(),(0,t.jsxs)(d.g,{labelledBy:"title",children:[(0,t.jsx)(o.Q,{id:"title",title:_({id:"content-manager.pageNotFound",defaultMessage:"Page not found"})}),(0,t.jsx)(E.s,{children:(0,t.jsx)(n.p,{action:(0,t.jsx)(e.z,{variant:"secondary",endIcon:(0,t.jsx)(i.A,{}),to:"/",children:_({id:"app.components.NotFoundPage.back",defaultMessage:"Back to homepage"})}),content:_({id:"notification.error",defaultMessage:"An error occured"}),hasRadius:!0,icon:(0,t.jsx)(M.A,{width:"10rem"}),shadow:"tableShadow"})})]})}}}]);
