"use strict";(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[6323],{96323:(P,_,s)=>{s.d(_,{PurchaseAuditLogs:()=>M});var t=s(92132),i=s(94061),e=s(53563),n=s(42455),d=s(55356),l=s(38413),o=s(49654),r=s(3047),E=s(14595),g=s(54894);const M=()=>{const{formatMessage:a}=(0,g.A)();return(0,t.jsx)(n.P,{children:(0,t.jsxs)(l.g,{children:[(0,t.jsx)(d.Q,{title:a({id:"global.auditLogs",defaultMessage:"Audit Logs"}),subtitle:a({id:"Settings.permissions.auditLogs.listview.header.subtitle",defaultMessage:"Logs of all the activities that happened in your environment"})}),(0,t.jsx)(i.a,{paddingLeft:10,paddingRight:10,children:(0,t.jsx)(e.p,{icon:(0,t.jsx)(r.A,{width:"10rem"}),content:a({id:"Settings.permissions.auditLogs.not-available",defaultMessage:"Audit Logs is only available as part of a paid plan. Upgrade to get a searchable and filterable display of all activities."}),action:(0,t.jsx)(o.z,{variant:"default",endIcon:(0,t.jsx)(E.A,{}),href:"https://strp.cc/45mbAdF",isExternal:!0,target:"_blank",children:a({id:"global.learn-more",defaultMessage:"Learn more"})})})})]})})}}}]);
