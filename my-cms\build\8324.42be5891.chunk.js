(self.webpackChunkmy_cms=self.webpackChunkmy_cms||[]).push([[8324,8595],{18324:(g,u,s)=>{"use strict";s.d(u,{ProtectedCreateView:()=>cs});var t=s(92132),l=s(55506),d=s(82437),c=s(66248),R=s(28595),A=s(21272),x=s(55151),I=s(79077),o=s(43543),P=s(15126),h=s(63299),H=s(67014),Q=s(59080),L=s(79275),v=s(14718),E=s(61535),j=s(5790),y=s(12083),k=s(35223),V=s(5409),b=s(74930),$=s(2600),S=s(48940),os=s(41286),is=s(56336),ls=s(13426),_s=s(84624),ds=s(77965),J=s(54257),Es=s(71210),z=s(51187),rs=s(39404),ss=s(58692),As=s(501),hs=s(57646),Ts=s(23120),ms=s(44414),gs=s(25962),ps=s(14664),Cs=s(42588),Is=s(90325),vs=s(62785),us=s(87443),Ls=s(41032),Rs=s(22957),ys=s(93179),Us=s(73055),Bs=s(15747),fs=s(85306),xs=s(26509),Ws=s(32058),Ks=s(81185),js=s(82261),Ss=s(30740),Ns=s(81590),ks=s(34542),Vs=s(61468),$s=s(24092),zs=s(92173);const cs=()=>{const Ps=(0,d.d4)(c.s);return(0,t.jsx)(l.kz,{permissions:Ps.settings?.["api-tokens"].create,children:(0,t.jsx)(R.w,{})})}},20920:(g,u,s)=>{var t=s(87864),l=s(80151),d=s(29327),c=s(52689),R=s(76299),A=Array.prototype,x=A.splice;function I(o,P,h,H){var Q=H?d:l,L=-1,v=P.length,E=o;for(o===P&&(P=R(P)),h&&(E=t(o,c(h)));++L<v;)for(var j=0,y=P[L],k=h?h(y):y;(j=Q(E,k,j,H))>-1;)E!==o&&x.call(E,j,1),x.call(o,j,1);return o}g.exports=I},28595:(g,u,s)=>{"use strict";s.d(u,{ProtectedEditView:()=>Et,w:()=>Os});var t=s(92132),l=s(21272),d=s(57237),c=s(44604),R=s(60888),A=s(94061),x=s(85963),I=s(4181),o=s(90151),P=s(68074),h=s(4198),H=s(55356),Q=s(38413),L=s(83997),v=s(30893),E=s(55506),j=s(61535),y=s(54894),k=s(17703),V=s(43543),b=s(30740),$=s(99831),S=s(81590),os=s(89032),is=s(54514),ls=s(20415),_s=s(34542),ds=s(61468),J=s(63891),Es=s(24092),z=s(12083),rs=s(88761),ss=s(92173),As=s(15126),hs=s(63299),Ts=s(67014),ms=s(59080),gs=s(79275),ps=s(14718),Cs=s(82437),Is=s(5790),vs=s(35223),us=s(5409),Ls=s(74930),Rs=s(2600),ys=s(48940),Us=s(41286),Bs=s(56336),fs=s(13426),xs=s(84624),Ws=s(77965),Ks=s(54257),js=s(71210),Ss=s(51187),Ns=s(39404),ks=s(58692),Vs=s(501),$s=s(57646),zs=s(23120),cs=s(44414),Ps=s(25962),Tt=s(14664),mt=s(42588),gt=s(90325),pt=s(62785),Ct=s(87443),It=s(41032),vt=s(22957),ut=s(93179),Lt=s(73055),Rt=s(15747),yt=s(85306),Ut=s(26509),Bt=s(32058),ft=s(81185),xt=s(82261);const Fs=V.n.injectEndpoints({endpoints:n=>({getPermissions:n.query({query:()=>"/admin/content-api/permissions",transformResponse:e=>e.data}),getRoutes:n.query({query:()=>"/admin/content-api/routes",transformResponse:e=>e.data})}),overrideExisting:!1}),{useGetPermissionsQuery:Gs,useGetRoutesQuery:Ys}=Fs,[Hs,Qs]=(0,os.q)("ApiTokenPermissionsContext"),Js=({children:n,...e})=>(0,t.jsx)(Hs,{...e,children:n}),ts=()=>Qs("useApiTokenPermissions"),Xs=({errors:n={},onChange:e,canEditInputs:a,isCreating:r,values:i={},apiToken:O={},onDispatch:_,setHasChangedPermissions:K})=>{const{formatMessage:m}=(0,y.A)(),W=({target:{value:U}})=>{K(!1),U==="full-access"&&_({type:"SELECT_ALL_ACTIONS"}),U==="read-only"&&_({type:"ON_CHANGE_READ_ONLY"})},X=[{value:"read-only",label:{id:"Settings.tokens.types.read-only",defaultMessage:"Read-only"}},{value:"full-access",label:{id:"Settings.tokens.types.full-access",defaultMessage:"Full access"}},{value:"custom",label:{id:"Settings.tokens.types.custom",defaultMessage:"Custom"}}];return(0,t.jsx)(A.a,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:(0,t.jsxs)(L.s,{direction:"column",alignItems:"stretch",gap:4,children:[(0,t.jsx)(v.o,{variant:"delta",as:"h2",children:m({id:"global.details",defaultMessage:"Details"})}),(0,t.jsxs)(o.x,{gap:5,children:[(0,t.jsx)(P.E,{col:6,xs:12,children:(0,t.jsx)(S.T,{error:n.name,value:i.name,canEditInputs:a,onChange:e})},"name"),(0,t.jsx)(P.E,{col:6,xs:12,children:(0,t.jsx)(S.a,{error:n.description,value:i.description,canEditInputs:a,onChange:e})},"description"),(0,t.jsx)(P.E,{col:6,xs:12,children:(0,t.jsx)(S.L,{isCreating:r,error:n.lifespan,value:i.lifespan,onChange:e,token:O})},"lifespan"),(0,t.jsx)(P.E,{col:6,xs:12,children:(0,t.jsx)(S.b,{value:i.type,error:n.type,label:{id:"Settings.tokens.form.type",defaultMessage:"Token type"},onChange:U=>{W({target:{value:U}}),e({target:{name:"type",value:U}})},options:X,canEditInputs:a})},"type")]})]})})},Zs=({apiTokenName:n=null})=>{const{formatMessage:e}=(0,y.A)();return(0,E.L4)(),(0,t.jsxs)(Q.g,{"aria-busy":"true",children:[(0,t.jsx)(E.x7,{name:"API Tokens"}),(0,t.jsx)(H.Q,{primaryAction:(0,t.jsx)(x.$,{disabled:!0,startIcon:(0,t.jsx)(is.A,{}),type:"button",size:"L",children:e({id:"global.save",defaultMessage:"Save"})}),title:n||e({id:"Settings.apiTokens.createPage.title",defaultMessage:"Create API Token"})}),(0,t.jsx)(h.s,{children:(0,t.jsx)(E.Bl,{})})]})},bs=n=>{switch(n){case"POST":return{text:"success600",border:"success200",background:"success100"};case"GET":return{text:"secondary600",border:"secondary200",background:"secondary100"};case"PUT":return{text:"warning600",border:"warning200",background:"warning100"};case"DELETE":return{text:"danger600",border:"danger200",background:"danger100"};default:return{text:"neutral600",border:"neutral200",background:"neutral100"}}},ws=(0,J.Ay)(A.a)`
  margin: -1px;
  border-radius: ${({theme:n})=>n.spaces[1]} 0 0 ${({theme:n})=>n.spaces[1]};
`,qs=({route:n={handler:"Nocontroller.error",method:"GET",path:"/there-is-no-path"}})=>{const{formatMessage:e}=(0,y.A)(),{method:a,handler:r,path:i}=n,O=i?ds(i.split("/")):[],[_="",K=""]=r?r.split("."):[],m=bs(n.method);return(0,t.jsxs)(L.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,t.jsxs)(v.o,{variant:"delta",as:"h3",children:[e({id:"Settings.apiTokens.createPage.BoundRoute.title",defaultMessage:"Bound route to"}),"\xA0",(0,t.jsx)("span",{children:_}),(0,t.jsxs)(v.o,{variant:"delta",textColor:"primary600",children:[".",K]})]}),(0,t.jsxs)(L.s,{hasRadius:!0,background:"neutral0",borderColor:"neutral200",gap:0,children:[(0,t.jsx)(ws,{background:m.background,borderColor:m.border,padding:2,children:(0,t.jsx)(v.o,{fontWeight:"bold",textColor:m.text,children:a})}),(0,t.jsx)(A.a,{paddingLeft:2,paddingRight:2,children:_s(O,W=>(0,t.jsxs)(v.o,{textColor:W.includes(":")?"neutral600":"neutral900",children:["/",W]},W))})]})]})},st=()=>{const{value:{selectedAction:n,routes:e}}=ts(),{formatMessage:a}=(0,y.A)(),r=n?.split(".")[0];return(0,t.jsx)(P.E,{col:5,background:"neutral150",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,style:{minHeight:"100%"},children:n?(0,t.jsx)(L.s,{direction:"column",alignItems:"stretch",gap:2,children:r&&r in e&&e[r].map(i=>i.config.auth?.scope?.includes(n)||i.handler===n?(0,t.jsx)(qs,{route:i},i.handler):null)}):(0,t.jsxs)(L.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,t.jsx)(v.o,{variant:"delta",as:"h3",children:a({id:"Settings.apiTokens.createPage.permissions.header.title",defaultMessage:"Advanced settings"})}),(0,t.jsx)(v.o,{as:"p",textColor:"neutral600",children:a({id:"Settings.apiTokens.createPage.permissions.header.hint",defaultMessage:"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"})})]})})},Ms=(0,J.AH)`
  background: ${n=>n.theme.colors.primary100};
  svg {
    opacity: 1;
  }
`,tt=(0,J.Ay)(A.a)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  svg {
    opacity: 0;
    path {
      fill: ${n=>n.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${n=>n.isActive&&Ms}
  &:hover {
    ${Ms}
  }
`,et=J.Ay.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({theme:n})=>n.colors.neutral150};
`,nt=({controllers:n=[],label:e,orderNumber:a=0,disabled:r=!1,onExpanded:i=()=>null,indexExpandendCollapsedContent:O=null})=>{const{value:{onChangeSelectAll:_,onChange:K,selectedActions:m,setSelectedAction:W,selectedAction:X}}=ts(),[U,w]=l.useState(!1),{formatMessage:Z}=(0,y.A)(),p=()=>{w(T=>!T),i(a)};l.useEffect(()=>{O!==null&&O!==a&&U&&w(!1)},[O,a,U]);const Ds=T=>T===X;return(0,t.jsxs)(d.n,{expanded:U,onToggle:p,variant:a%2?"primary":"secondary",children:[(0,t.jsx)(R.P,{title:Es(e)}),(0,t.jsx)(c.u,{children:n?.map(T=>{const B=T.actions.every(C=>m.includes(C.actionId)),f=T.actions.some(C=>m.includes(C.actionId));return(0,t.jsxs)(A.a,{children:[(0,t.jsxs)(L.s,{justifyContent:"space-between",alignItems:"center",padding:4,children:[(0,t.jsx)(A.a,{paddingRight:4,children:(0,t.jsx)(v.o,{variant:"sigma",textColor:"neutral600",children:T?.controller})}),(0,t.jsx)(et,{}),(0,t.jsx)(A.a,{paddingLeft:4,children:(0,t.jsx)(I.S,{value:B,indeterminate:!B&&f,onValueChange:()=>{_({target:{value:[...T.actions]}})},disabled:r,children:Z({id:"app.utils.select-all",defaultMessage:"Select all"})})})]}),(0,t.jsx)(o.x,{gap:4,padding:4,children:T?.actions&&T?.actions.map(C=>(0,t.jsx)(P.E,{col:6,children:(0,t.jsxs)(tt,{isActive:Ds(C.actionId),padding:2,hasRadius:!0,children:[(0,t.jsx)(I.S,{value:m.includes(C.actionId),name:C.actionId,onValueChange:()=>{K({target:{value:C.actionId}})},disabled:r,children:C.action}),(0,t.jsx)("button",{type:"button","data-testid":"action-cog",onClick:()=>W({target:{value:C.actionId}}),style:{display:"inline-flex",alignItems:"center"},children:(0,t.jsx)(ls.A,{})})]})},C.actionId))})]},`${e}.${T?.controller}`)})})]})},at=({section:n=null,...e})=>{const[a,r]=l.useState(null),i=O=>r(O);return(0,t.jsx)(A.a,{padding:4,background:"neutral0",children:n&&n.map((O,_)=>(0,t.jsx)(nt,{label:O.label,controllers:O.controllers,orderNumber:_,indexExpandendCollapsedContent:a,onExpanded:i,...e},O.apiId))})},ot=({...n})=>{const{value:{data:e}}=ts(),{formatMessage:a}=(0,y.A)();return(0,t.jsxs)(o.x,{gap:0,shadow:"filterShadow",hasRadius:!0,background:"neutral0",children:[(0,t.jsxs)(P.E,{col:7,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:[(0,t.jsxs)(L.s,{direction:"column",alignItems:"stretch",gap:2,children:[(0,t.jsx)(v.o,{variant:"delta",as:"h2",children:a({id:"Settings.apiTokens.createPage.permissions.title",defaultMessage:"Permissions"})}),(0,t.jsx)(v.o,{as:"p",textColor:"neutral600",children:a({id:"Settings.apiTokens.createPage.permissions.description",defaultMessage:"Only actions bound by a route are listed below."})})]}),e?.permissions&&(0,t.jsx)(at,{section:e?.permissions,...n})]}),(0,t.jsx)(st,{})]})},it=z.Ik().shape({name:z.Yj().max(100).required(E.iW.required),type:z.Yj().oneOf(["read-only","full-access","custom"]).required(E.iW.required),description:z.Yj().nullable(),lifespan:z.ai().integer().min(0).nullable().defined(E.iW.required)}),lt=n=>{const e={allActionsIds:[],permissions:[]};return e.permissions=Object.entries(n).map(([a,r])=>({apiId:a,label:a.split("::")[1],controllers:Object.keys(r.controllers).map(i=>({controller:i,actions:i in r.controllers?r.controllers[i].map(O=>{const _=`${a}.${i}.${O}`;return a.includes("api::")&&e.allActionsIds.push(_),{action:O,actionId:_}}).flat():[]})).flat()})),e},_t={data:{allActionsIds:[],permissions:[]},routes:{},selectedAction:"",selectedActions:[]},dt=(n,e)=>(0,rs.Ay)(n,a=>{switch(e.type){case"ON_CHANGE":{a.selectedActions.includes(e.value)?ss(a.selectedActions,e.value):a.selectedActions.push(e.value);break}case"SELECT_ALL_IN_PERMISSION":{e.value.every(i=>a.selectedActions.includes(i.actionId))?e.value.forEach(i=>{ss(a.selectedActions,i.actionId)}):e.value.forEach(i=>{a.selectedActions.push(i.actionId)});break}case"SELECT_ALL_ACTIONS":{a.selectedActions=[...a.data.allActionsIds];break}case"ON_CHANGE_READ_ONLY":{const r=a.data.allActionsIds.filter(i=>i.includes("find")||i.includes("findOne"));a.selectedActions=[...r];break}case"UPDATE_PERMISSIONS_LAYOUT":{a.data=lt(e.value);break}case"UPDATE_ROUTES":{a.routes={...e.value};break}case"UPDATE_PERMISSIONS":{a.selectedActions=[...e.value];break}case"SET_SELECTED_ACTION":{a.selectedAction=e.value;break}default:return a}}),Os=()=>{(0,E.L4)();const{formatMessage:n}=(0,y.A)(),e=(0,E.hN)(),{lockApp:a,unlockApp:r}=(0,E.MA)(),{state:i}=(0,k.zy)(),O=(0,V.j)(M=>M.admin_app.permissions),[_,K]=l.useState(i?.apiToken?.accessKey?{...i.apiToken}:null),{trackUsage:m}=(0,E.z1)(),{setCurrentStep:W}=(0,E.Cx)(),{allowedActions:{canCreate:X,canUpdate:U,canRegenerate:w}}=(0,E.ec)(O.settings?.["api-tokens"]),[Z,p]=l.useReducer(dt,_t),T=(0,k.W5)("/settings/api-tokens/:id")?.params?.id,B=T==="create",{_unstableFormatAPIError:f,_unstableFormatValidationErrors:C}=(0,E.wq)(),rt=(0,k.W6)(),F=Gs(),G=Ys();l.useEffect(()=>{F.error&&e({type:"warning",message:f(F.error)})},[F.error,f,e]),l.useEffect(()=>{G.error&&e({type:"warning",message:f(G.error)})},[G.error,f,e]),l.useEffect(()=>{F.data&&p({type:"UPDATE_PERMISSIONS_LAYOUT",value:F.data})},[F.data]),l.useEffect(()=>{G.data&&p({type:"UPDATE_ROUTES",value:G.data})},[G.data]),l.useEffect(()=>{_&&(_.type==="read-only"&&p({type:"ON_CHANGE_READ_ONLY"}),_.type==="full-access"&&p({type:"SELECT_ALL_ACTIONS"}),_.type==="custom"&&p({type:"UPDATE_PERMISSIONS",value:_?.permissions}))},[_]),l.useEffect(()=>{m(B?"didAddTokenFromList":"didEditTokenFromList",{tokenType:$.A})},[B,m]);const{data:N,error:es,isLoading:ct}=(0,b.b)(T,{skip:!T||B||!!_});l.useEffect(()=>{es&&e({type:"warning",message:f(es)})},[es,f,e]),l.useEffect(()=>{N&&(K(N),N.type==="read-only"&&p({type:"ON_CHANGE_READ_ONLY"}),N.type==="full-access"&&p({type:"SELECT_ALL_ACTIONS"}),N.type==="custom"&&p({type:"UPDATE_PERMISSIONS",value:N?.permissions}))},[N]);const[Pt]=(0,b.c)(),[Mt]=(0,b.d)(),Ot=async(M,Y)=>{m(B?"willCreateToken":"willEditToken",{tokenType:$.A}),a();try{if(B){const D=await Pt({...M,lifespan:M?.lifespan||null,permissions:M.type==="custom"?Z.selectedActions:null});if("error"in D){(0,V.x)(D.error)&&D.error.name==="ValidationError"?Y.setErrors(C(D.error)):e({type:"warning",message:f(D.error)});return}e({type:"success",message:n({id:"notification.success.apitokencreated",defaultMessage:"API Token successfully created"})}),m("didCreateToken",{type:D.data.type,tokenType:$.A}),rt.replace(`/settings/api-tokens/${D.data.id}`,{apiToken:D.data}),W("apiTokens.success")}else{const D=await Mt({id:T,name:M.name,description:M.description,type:M.type,permissions:M.type==="custom"?Z.selectedActions:null});if("error"in D){(0,V.x)(D.error)&&D.error.name==="ValidationError"?Y.setErrors(C(D.error)):e({type:"warning",message:f(D.error)});return}e({type:"success",message:n({id:"notification.success.apitokenedited",defaultMessage:"API Token successfully edited"})}),m("didEditToken",{type:D.data.type,tokenType:$.A})}}catch{e({type:"warning",message:{id:"notification.error",defaultMessage:"Something went wrong"}})}finally{r()}},[Dt,ns]=l.useState(!1),At={...Z,onChange:({target:{value:M}})=>{ns(!0),p({type:"ON_CHANGE",value:M})},onChangeSelectAll:({target:{value:M}})=>{ns(!0),p({type:"SELECT_ALL_IN_PERMISSION",value:M})},setSelectedAction:({target:{value:M}})=>{p({type:"SET_SELECTED_ACTION",value:M})}},as=U&&!B||X&&B;return ct?(0,t.jsx)(Zs,{apiTokenName:_?.name}):(0,t.jsx)(Js,{value:At,children:(0,t.jsxs)(Q.g,{children:[(0,t.jsx)(E.x7,{name:"API Tokens"}),(0,t.jsx)(j.l1,{validationSchema:it,validateOnChange:!1,initialValues:{name:_?.name||"",description:_?.description||"",type:_?.type,lifespan:_?.lifespan},enableReinitialize:!0,onSubmit:(M,Y)=>Ot(M,Y),children:({errors:M,handleChange:Y,isSubmitting:D,values:q,setFieldValue:ht})=>(Dt&&q?.type!=="custom"&&ht("type","custom"),(0,t.jsxs)(E.lV,{children:[(0,t.jsx)(S.F,{backUrl:"/settings/api-tokens",title:{id:"Settings.apiTokens.createPage.title",defaultMessage:"Create API Token"},token:_,setToken:K,canEditInputs:as,canRegenerate:w,isSubmitting:D,regenerateUrl:"/admin/api-tokens/"}),(0,t.jsx)(h.s,{children:(0,t.jsxs)(L.s,{direction:"column",alignItems:"stretch",gap:6,children:[Boolean(_?.name)&&(0,t.jsx)(S.c,{token:_?.accessKey,tokenType:$.A}),(0,t.jsx)(Xs,{errors:M,onChange:Y,canEditInputs:as,isCreating:B,values:q,apiToken:_,onDispatch:p,setHasChangedPermissions:ns}),(0,t.jsx)(ot,{disabled:!as||q?.type==="read-only"||q?.type==="full-access"})]})})]}))})]})})},Et=()=>{const n=(0,V.j)(e=>e.admin_app.permissions.settings?.["api-tokens"].read);return(0,t.jsx)(E.kz,{permissions:n,children:(0,t.jsx)(Os,{})})}},29327:g=>{function u(s,t,l,d){for(var c=l-1,R=s.length;++c<R;)if(d(s[c],t))return c;return-1}g.exports=u},30740:(g,u,s)=>{"use strict";s.d(u,{a:()=>A,b:()=>c,c:()=>R,d:()=>x,u:()=>d});var t=s(43543);const l=t.n.injectEndpoints({endpoints:I=>({getAPITokens:I.query({query:()=>"/admin/api-tokens",transformResponse:o=>o.data,providesTags:(o,P)=>[...o?.map(({id:h})=>({type:"ApiToken",id:h}))??[],{type:"ApiToken",id:"LIST"}]}),getAPIToken:I.query({query:o=>`/admin/api-tokens/${o}`,transformResponse:o=>o.data,providesTags:(o,P,h)=>[{type:"ApiToken",id:h}]}),createAPIToken:I.mutation({query:o=>({url:"/admin/api-tokens",method:"POST",data:o}),transformResponse:o=>o.data,invalidatesTags:[{type:"ApiToken",id:"LIST"}]}),deleteAPIToken:I.mutation({query:o=>({url:`/admin/api-tokens/${o}`,method:"DELETE"}),transformResponse:o=>o.data,invalidatesTags:(o,P,h)=>[{type:"ApiToken",id:h}]}),updateAPIToken:I.mutation({query:({id:o,...P})=>({url:`/admin/api-tokens/${o}`,method:"PUT",data:P}),transformResponse:o=>o.data,invalidatesTags:(o,P,{id:h})=>[{type:"ApiToken",id:h}]})}),overrideExisting:!1}),{useGetAPITokensQuery:d,useGetAPITokenQuery:c,useCreateAPITokenMutation:R,useDeleteAPITokenMutation:A,useUpdateAPITokenMutation:x}=l},34542:(g,u,s)=>{var t=s(87864),l=s(45353),d=s(29884),c=s(82261);function R(A,x){var I=c(A)?t:d;return I(A,l(x,3))}g.exports=R},61468:(g,u,s)=>{var t=s(52196);function l(d){var c=d==null?0:d.length;return c?t(d,1,c):[]}g.exports=l},76610:(g,u,s)=>{var t=s(20920);function l(d,c){return d&&d.length&&c&&c.length?t(d,c):d}g.exports=l},92173:(g,u,s)=>{var t=s(39226),l=s(76610),d=t(l);g.exports=d}}]);
